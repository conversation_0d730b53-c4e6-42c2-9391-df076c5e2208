version: 1
frontend:
  phases:
    preBuild:
      commands:
        - nvm install v14.19.1
        - nvm use v14.19.1
        - node -v
        - npm -v
        - npm i node-sass@4.14.1 -g
        - npm i --legacy-peer-deps
    build:
      commands:
        - export NODE_OPTIONS="--max-old-space-size=8192"
        - npm run ng build --configuration=production
  artifacts:
    baseDirectory: dist/digitoryWebv2
    files:
      - '**/*'
  cache:
    paths:
      - node_modules/**/*