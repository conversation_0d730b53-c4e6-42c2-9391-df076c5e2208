#!/bin/bash
# arg 1 env
# arg 2 port #


fileName=environment.ts

# Switch case
# baseUrl="d"
case "$1" in
  "demo")
  baseUrl="http://rms-demo.digitory.com:$2/"
  ;;
  "prod")
  baseUrl="http://rms-prod.digitory.com:$2/"
  ;;
  "dev")
  baseUrl="http://rms-dev.digitory.com:$2/"
  ;;
  "dev1")
  baseUrl="http://*************:$2/"
  ;;
  *)
  baseUrl="http://localhost:$2/"
  ;;
esac
echo "$baseUrl"

# do npm install
echo "node should be 10.15.3 and npm should be 6.4.1"
npm -v
node -v

echo "running npm install now"
npm install

#Remove the current build folder
sed -i "/baseUrl/c\baseUrl : '$baseUrl'" src/environments/$fileName

echo Removing the lastest build folder
rm -rf dist/

#Build the project
echo "Building application"
ng build

#Move Built folder to /var/www
echo copying built folder

echo "removing earlier deployed code"
sudo rm -r /var/www/html/*

sudo cp -r dist/digitoryWebv2/* /var/www/html

echo copying htaccess

sudo cp htaccess /var/www/html/.htaccess

