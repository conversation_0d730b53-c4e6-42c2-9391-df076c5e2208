var gulp = require('gulp');
var ts = require('gulp-typescript');
const sass = require('gulp-sass')(require('node-sass'));
var cleanCSS = require('gulp-clean-css');
var uglify = require('gulp-uglify');
var rename = require('gulp-rename');

// Compile TypeScript files
gulp.task('compile-ts', function() {
  return gulp.src('src/**/*.ts')
    .pipe(ts({
      noImplicitAny: true,
      outFile: 'app.js'
    }))
    .pipe(gulp.dest('dist'));
});

// // Compile Sass files
// gulp.task('compile-sass', function() {
//   return gulp.src('src/**/*.scss')
//     .pipe(sass())
//     .pipe(gulp.dest('dist'));
// });

// Minify CSS files
gulp.task('minify-css', function() {
  return gulp.src('dist/**/*.css')
    .pipe(cleanCSS())
    .pipe(rename({suffix: '.min'}))
    .pipe(gulp.dest('dist'));
});

// Minify JavaScript files
gulp.task('minify-js', function() {
  return gulp.src('dist/**/*.js')
    .pipe(uglify())
    .pipe(rename({suffix: '.min'}))
    .pipe(gulp.dest('dist'));
});

// Watch for changes
gulp.task('watch', function() {
  gulp.watch('src/**/*.ts', gulp.series('compile-ts'));
  // gulp.watch('src/**/*.scss', gulp.series('compile-sass'));
  gulp.watch('dist/**/*.css', gulp.series('minify-css'));
  gulp.watch('dist/**/*.js', gulp.series('minify-js'));
});


gulp.task('scripts', function() {
  return gulp.src('src/js/*.js')
    .pipe(concat('app.js'))
    .pipe(gulp.dest('dist/js'))
    .pipe(uglify())
    .pipe(rename({suffix: '.min'}))
    .pipe(gulp.dest('dist/js'));
});

// Default task
gulp.task('default', gulp.parallel('compile-ts', 'minify-css', 'minify-js', 'watch' ,'scripts'));
