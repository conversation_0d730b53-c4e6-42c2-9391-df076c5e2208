<!-- Main Card Container -->
<mat-card>
  <!-- Info Banner -->
  <div class="info-banner">
    <mat-icon>info</mat-icon>
    <span>Only package items available for conversion to Purchase Request. Select items to convert.</span>
  </div>

  <!-- Header Section with Back Button and Title -->
  <div class="header-section">
    <div class="header-row">
      <button mat-raised-button class="button back-btn" (click)="goBack()">
        <mat-icon>keyboard_backspace</mat-icon> Back
      </button>
      <h3 class="page-title">Convert to Purchase Request</h3>
    </div>
  </div>

  <!-- Filter Section -->
  <div class="filter-section">
    <div class="filter-row-1">
      <div class="search-group">
        <label>Search</label>
        <div class="search-input-wrapper">
          <input type="text"
                 [(ngModel)]="searchText"
                 (keyup)="doFilter($event.target.value)"
                 placeholder="Search by Item Name">
          <mat-icon class="clear-btn" (click)="resetSearch()" *ngIf="searchText">close</mat-icon>
        </div>
      </div>

      <div class="button-group">
        <button mat-raised-button class="button" (click)="refreshData()">
          Reset
        </button>
        <button mat-raised-button class="button3" (click)="createPR()" [disabled]="selection.selected.length === 0">
          Convert ({{ selection.selected.length }})
        </button>
      </div>
    </div>
  </div>

  <!-- Table Container -->
  <div class="table-wrapper">
    <table mat-table [dataSource]="dataSource" matSort class="data-table">

      <!-- Checkbox Column -->
      <ng-container matColumnDef="select">
        <th mat-header-cell *matHeaderCellDef>
          <mat-checkbox (change)="$event ? masterToggle() : null"
                        [checked]="selection.hasValue() && isAllSelected()"
                        [indeterminate]="selection.hasValue() && !isAllSelected()">
          </mat-checkbox>
        </th>
        <td mat-cell *matCellDef="let row">
          <mat-checkbox (click)="$event.stopPropagation()"
                        (change)="$event ? selection.toggle(row) : null"
                        [checked]="selection.isSelected(row)">
          </mat-checkbox>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <!-- Item Name Column -->
      <ng-container matColumnDef="itemName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Item Name</th>
        <td mat-cell *matCellDef="let element">{{ extractLastPart(element.itemName) | titlecase }}</td>
        <td mat-footer-cell *matFooterCellDef><strong>Total:</strong></td>
      </ng-container>

      <!-- Requested Qty Column -->
      <ng-container matColumnDef="requestedQty">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Requested Qty</th>
        <td mat-cell *matCellDef="let element">{{ utils.truncateNew(element.requestedQty) }}</td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="inStock">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>In-Stock</th>
        <td mat-cell *matCellDef="let element">{{ utils.truncateNew(element.inStock) }}</td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <!-- Order Qty Column -->
      <ng-container matColumnDef="orderQty">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Order Qty</th>
        <td mat-cell *matCellDef="let element">
          <input type="number"
                 class="quantity-input"
                 [(ngModel)]="element.orderQty"
                 (input)="onOrderQtyInput($event, element)"
                 (focus)="focusFunctionWithOutForm(element,'orderQty')"
                 (focusout)="focusOutFunctionWithOutForm(element,'orderQty')"
                 [min]="0"
                 [max]="element.requestedQty"
                 [step]="getStepValue(element.uom)"
                 placeholder="0">
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <!-- UOM Column -->
      <ng-container matColumnDef="uom">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>UOM</th>
        <td mat-cell *matCellDef="let element">{{ element.uom | titlecase }}</td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <!-- Unit Price Column -->
      <ng-container matColumnDef="unitPrice">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Unit Price</th>
        <td mat-cell *matCellDef="let element">{{ utils.truncateNew(element.unitPrice, 2) }}</td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <!-- Total Value Column -->
      <ng-container matColumnDef="totalValue">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Total Value</th>
        <td mat-cell *matCellDef="let element">{{ utils.truncateNew(element.totalValue, 2) }}</td>
        <td mat-footer-cell *matFooterCellDef><strong>{{ utils.truncateNew(getTotalValue(), 2) }}</strong></td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"
          [class.selected]="selection.isSelected(row)">
      </tr>
      <tr mat-footer-row *matFooterRowDef="displayedColumns" class="table-footer"></tr>
    </table>
  </div>

  <!-- Pagination -->
  <div class="pagination-wrapper">
    <span class="items-per-page">Items per page:</span>
    <mat-paginator [pageSizeOptions]="[10, 25, 50]"
                   [pageSize]="10"
                   showFirstLastButtons>
    </mat-paginator>
  </div>
</mat-card>
