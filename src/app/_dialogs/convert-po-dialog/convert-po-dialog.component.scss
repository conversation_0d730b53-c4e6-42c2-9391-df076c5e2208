// Info Banner - styled to match the design
.info-banner {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  border: 1px solid #4a5568;
  border-radius: 6px;
  padding: 12px 16px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  color: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  mat-icon {
    color: #3498db;
    font-size: 20px;
    width: 20px;
    height: 20px;
    flex-shrink: 0;
  }

  span {
    font-size: 14px;
    line-height: 1.4;
    font-weight: 400;
  }
}

// Header Section
.header-section {
  margin-bottom: 20px;

  .header-row {
    display: flex;
    align-items: center;
    gap: 20px;

    .back-btn {
      flex-shrink: 0;
    }

    .page-title {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #3586ca;
    }
  }
}

// Filter Section
.filter-section {
  margin-bottom: 16px;
}

.filter-row-1 {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  gap: 16px;
  margin-bottom: 16px;
}

.search-group {
  display: flex;
  flex-direction: column;
  min-width: 300px;

  label {
    font-size: 14px;
    color: #666;
    margin-bottom: 4px;
    font-weight: normal;
  }
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;

  input {
    width: 100%;
    padding: 8px 32px 8px 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
    background-color: #2d2d2d;
    color: #fff;

    &::placeholder {
      color: #999;
    }

    &:focus {
      outline: none;
      border-color: #007bff;
    }
  }

  .clear-btn {
    position: absolute;
    right: 8px;
    cursor: pointer;
    color: #999;
    font-size: 18px;

    &:hover {
      color: #fff;
    }
  }
}

.button-group {
  display: flex;
  gap: 8px;
}

// Buttons will use standard classes from styles.scss:
// .button - standard button style
// .button3 - primary action button style

// Table Styles
.table-wrapper {
  background-color: #2d2d2d;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 16px;
}

.data-table {
  width: 100%;
  background-color: #2d2d2d;

  .mat-header-row {
    background-color: #3d3d3d;
    border-bottom: 1px solid #555;
  }

  .mat-header-cell {
    color: #ccc;
    font-size: 14px;
    font-weight: 500;
    padding: 12px 16px;
    border-right: 1px solid #555;

    &:last-child {
      border-right: none;
    }
  }

  .mat-row {
    background-color: #2d2d2d;
    border-bottom: 1px solid #555;
    cursor: pointer;

    &:hover {
      background-color: #3d3d3d;
    }

    &.selected {
      background-color: #4a4a4a;
    }
  }

  .mat-cell {
    color: #fff;
    font-size: 14px;
    padding: 12px 16px;
    border-right: 1px solid #555;

    &:last-child {
      border-right: none;
    }

    .quantity-input {
      width: 80px;
      padding: 4px 8px;
      border: 1px solid #555;
      border-radius: 4px;
      background-color: #3d3d3d;
      color: #fff;
      font-size: 14px;
      text-align: center;

      &:focus {
        outline: none;
        border-color: #007bff;
        background-color: #4a4a4a;
      }

      &::placeholder {
        color: #999;
      }

      // Remove spinner arrows for number input
      &::-webkit-outer-spin-button,
      &::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }

      &[type=number] {
        -moz-appearance: textfield;
      }
    }
  }

  // Table Footer Styles
  .mat-footer-row {
    background-color: #3d3d3d !important;
    border-top: 2px solid #007bff;

    .mat-footer-cell {
      color: #fff !important;
      font-size: 14px;
      padding: 12px 16px;
      border-right: 1px solid #555;

      &:last-child {
        border-right: none;
      }

      strong {
        color: #ffffff;
        font-weight: 600;
      }
    }
  }

  .table-footer {
    background-color: #3d3d3d !important;
    border-top: 2px solid #007bff;
  }

  // Checkbox styling
  .mat-checkbox {
    .mat-checkbox-frame {
      border-color: #ccc;
    }

    .mat-checkbox-background {
      background-color: transparent;
    }

    &.mat-checkbox-checked {
      .mat-checkbox-background {
        background-color: #007bff;
      }
    }
  }
}

// Pagination
.pagination-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #2d2d2d;
  padding: 8px 16px;
  border-top: 1px solid #555;

  .items-per-page {
    color: #ccc;
    font-size: 14px;
  }

  mat-paginator {
    background-color: transparent;
    color: #ccc;

    .mat-paginator-container {
      padding: 0;
    }

    .mat-paginator-page-size-label,
    .mat-paginator-range-label {
      color: #ccc;
      font-size: 14px;
    }

    .mat-paginator-navigation-previous,
    .mat-paginator-navigation-next,
    .mat-paginator-navigation-first,
    .mat-paginator-navigation-last {
      color: #ccc;

      &:hover {
        background-color: #3d3d3d;
      }

      &.mat-paginator-navigation-disabled {
        color: #666;
      }
    }

    .mat-select {
      color: #ccc;
    }

    .mat-select-arrow {
      color: #ccc;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .filter-section {
    .search-controls {
      flex-direction: column;
      align-items: stretch;

      mat-form-field {
        min-width: auto;
      }

      .rfBtn, .convert-button {
        width: 100%;
        margin-top: 8px;
      }
    }
  }

  table {
    .name-column {
      min-width: 150px;
      max-width: 200px;
    }

    .mat-cell, .mat-header-cell {
      padding: 8px 12px;
    }
  }
}
