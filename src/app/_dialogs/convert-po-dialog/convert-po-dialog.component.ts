import { Component, OnInit, Inject, ViewChild, Optional, ChangeDetectorRef } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { SelectionModel } from '@angular/cdk/collections';
import { UtilsService } from '../../_utils/utils.service';
import { ShareDataService } from '../../_services/share-data.service';
import { Router } from '@angular/router';
import { Location } from '@angular/common';
import { AuthService, PurchasesService } from 'src/app/_services';
import { PrPreviewDialogComponent } from '../pr-preview-dialog/pr-preview-dialog.component';
import { SimpleDialogComponent } from '../simple-dialog/simple-dialog.component';

@Component({
  selector: 'app-convert-po-dialog',
  templateUrl: './convert-po-dialog.component.html',
  styleUrls: ['./convert-po-dialog.component.scss']
})
export class ConvertPoDialogComponent implements OnInit {
  displayedColumns: string[] = ['select', 'itemName', 'requestedQty','inStock', 'orderQty', 'uom', 'unitPrice', 'totalValue'];
  dataSource: MatTableDataSource<any>;
  selection = new SelectionModel<any>(true, []);
  searchText: string = '';
  checkSelection: boolean = false;
  data: any = { items: [] };

  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  ibtDetails: any;
  user: any;

  constructor(
    @Optional() private dialogRef: MatDialogRef<ConvertPoDialogComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public dialogData: any,
    public utils: UtilsService,
    private shareDataService: ShareDataService,
    private router: Router,
    private auth: AuthService,
    private dialog: MatDialog,
    private purchases: PurchasesService,
    private location: Location,
    private cdr: ChangeDetectorRef
  ) {
    this.dataSource = new MatTableDataSource<any>();
    this.user = this.auth.getCurrentUser();
  }

  ngOnInit() {
    // Check if we have dialog data (dialog mode) or need to get data from service (standalone mode)
    if (this.dialogData && this.dialogData.items) {
      this.data = this.dialogData;
    } else {
      // Get data from shared service for standalone mode
      this.shareDataService.currentConvertPoData.subscribe(data => {
        if (data && data.items) {
          this.data = data;
          this.ibtDetails = data['ibt'];
          this.initializeData();
        }
      });
    }

    this.initializeData();
  }

  private initializeData() {
    if (!this.data || !this.data.items) return;

    // Filter data to show only items with entryType == 'package'
    const filteredData = this.data.items.filter((item: any) => item.entryType === 'package');

    // Set up new columns and calculate totals
    filteredData.forEach((item: any) => {
      // Set requestedQty from original quantity
      item.requestedQty = item.quantity || 0;

      // Set orderQty to same value as requestedQty by default
      item.orderQty = item.requestedQty;

      // Ensure quantities are positive numbers
      if (item.requestedQty < 0) item.requestedQty = 0;
      if (item.orderQty < 0) item.orderQty = 0;

      // Calculate total value based on orderQty
      item.totalValue = (item.orderQty || 0) * (item.unitPrice || 0);
    });

    this.dataSource.data = filteredData;
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;

    // Select all rows by default
    this.selectAllRows();
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  // Check if all rows are selected
  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  // Master toggle for select all/none
  masterToggle() {
    this.isAllSelected() ?
      this.selection.clear() :
      this.dataSource.data.forEach(row => this.selection.select(row));
  }

  // Get checkbox label
  checkboxLabel(row?: any): string {
    if (!row) {
      return `${this.isAllSelected() ? 'select' : 'deselect'} all`;
    }
    return `${this.selection.isSelected(row) ? 'deselect' : 'select'} row ${row.itemName}`;
  }

  // Filter data based on search text
  doFilter(value: string) {
    this.dataSource.filter = value.trim().toLowerCase();
  }

  // Reset search
  resetSearch() {
    this.searchText = '';
    this.dataSource.filter = '';
  }

  // Refresh data (Reset functionality)
  refreshData() {
    // Filter data to show only items with entryType == 'package'
    const filteredData = this.data.items.filter((item: any) => item.entryType === 'package');

    // Reset data to original state
    filteredData.forEach((item: any) => {
      // Reset requestedQty from original quantity
      item.requestedQty = item.quantity || 0;

      // Reset orderQty to same value as requestedQty
      item.orderQty = item.requestedQty;

      // Ensure quantities are positive numbers
      if (item.requestedQty < 0) item.requestedQty = 0;
      if (item.orderQty < 0) item.orderQty = 0;

      // Calculate total value based on orderQty
      item.totalValue = (item.orderQty || 0) * (item.unitPrice || 0);
    });

    this.dataSource.data = filteredData;

    // Clear search
    this.searchText = '';
    this.dataSource.filter = '';

    // Select all rows by default
    this.selectAllRows();
  }

  // Get total value of selected items
  getSelectedTotal(): number {
    return this.selection.selected.reduce((total, item) => total + (item.totalValue || 0), 0);
  }

  // Close dialog without action or navigate back
  close() {
    if (this.dialogRef) {
      this.dialogRef.close();
    } else {
      this.location.back();
    }
  }

  // Create PR with selected items
  createPR() {
    if (this.selection.selected.length === 0) {
      this.utils.snackBarShowWarning('Please select at least one item to create PR.');
      return;
    }

    const selectedData = this.selection.selected.map(item => ({
      ...item,
      quantity: item.orderQty, // Use orderQty as the final quantity
      totalValue: item.totalValue
    }));

    const result = {
      action: 'create',
      selectedItems: selectedData,
      totalValue: this.getTotalValue()
    };

    if (this.dialogRef) {
      // Dialog mode
      this.dialogRef.close(result);
    } else {
      // Standalone mode - handle the creation here or navigate back with data
      let requiredItem = []
      selectedData.forEach((item) => {
          let obj = {
            "itemName": item['itemName'],
            "brand": null,
            "unitPrice": item['unitPrice'],
            "orderQty": item['quantity'],
            "uom": item['uom'],
            "itemCode": item['itemCode'],
            "taxRate": 0,
            "itemDescription": null,
            "itemCategory":item['category'],
            "subCategory": item['itemName'],
            "ledger": "-",
            "pkgName": item['packageName'],
            "unitPerPkg": item['unitPerPkg'],
            "pkgQty":item['packageQty'],
            "totalExcTax": item['totalValue'],
            "taxAmount": 0,
            "stockConversion": false,
            "totalPrice": item['totalValue'],
            "isContract": false,
            "defaultQty":item['quantity']
          }
          requiredItem.push(obj)
      })
      let date = new Date();
      date.setHours(0, 0, 0, 0);

      let purchaseReq = {
        "tenantId": this.ibtDetails['tenantId'],
        "prItems": requiredItem,
        "eta": null,
        "deliveryDate": date,
        "uId": this.auth.getCurrentUser().mId,
        "rId": this.ibtDetails['fromBranch']['restaurantId'],
        "totalAmount": this.utils.truncateNew(this.getTotalValue(), 2),
        "tempPr": true,
        "poMadeBy": "Default",
        "role": [],
        "appCat": "DEFAULT",
        "senderEmail": [this.user.email],
        "stockType": "Stockable",
        "ibtId": this.ibtDetails.ibtId,
        "createdUser": this.user.email
      }
      this.purchases.createPr(purchaseReq).subscribe(data => {
        this.updateStatus(data['prId']);
      })
      this.handleCreatePR(selectedData, this.getTotalValue());
    }
  }

  private handleCreatePR(selectedItems: any[], totalValue: number) {
    // This method would handle the PR creation in standalone mode
    // For now, just show success message and navigate back
    this.utils.snackBarShowSuccess(`Selected ${selectedItems.length} items for PR creation with total value: ${this.utils.truncateNew(totalValue, 2)}`);
    this.location.back();
  }

  // Extract last part of item name for display
  extractLastPart(itemName: string): string {
    if (!itemName) return '';
    const parts = itemName.split('>>');
    return parts[parts.length - 1].trim();
  }

  // Go back to previous page
  goBack() {
    if (this.dialogRef) {
      this.dialogRef.close({ action: 'back' });
    } else {
      this.location.back();
    }
  }

  // Handle input event for Order Qty field
  onOrderQtyInput(event: any, element: any) {
    // Get the input value
    let inputValue = parseFloat(event.target.value) || 0;
    element.orderQty = inputValue;

    // Apply validation and update totals
    this.updateRowTotal(element);

    // If validation changed the value, update the input field
    if (element.orderQty !== inputValue) {
      event.target.value = element.orderQty;
    }
  }

  // Update row total when order quantity changes
  updateRowTotal(element: any) {
    // Ensure orderQty is a positive number and not null/undefined
    if (element.orderQty < 0 || element.orderQty === null || element.orderQty === undefined) {
      element.orderQty = 0;
    }

    // Ensure orderQty doesn't exceed requestedQty
    if (element.orderQty > element.requestedQty) {
      element.orderQty = element.requestedQty;
    }

    // For "Nos" UOM, ensure whole numbers only
    if (element.uom && element.uom.toLowerCase() === 'nos' && element.orderQty % 1 !== 0) {
      element.orderQty = Math.floor(element.orderQty);
    }

    // Calculate total value for this row based on orderQty
    element.totalValue = (element.orderQty || 0) * (element.unitPrice || 0);
  }

  // Get total value for selected rows only
  getTotalValue(): number {
    return this.selection.selected.reduce((total, item) => total + (item.totalValue || 0), 0);
  }

  // Select all rows by default
  selectAllRows() {
    this.selection.clear();
    this.dataSource.data.forEach(row => this.selection.select(row));
  }

  // Get step value based on UOM (whole numbers for "Nos", decimals for others)
  getStepValue(uom: string): string {
    return uom && uom.toLowerCase() === 'nos' ? '1' : '0.01';
  }

  // Handle focus in on Order Qty input
  onOrderQtyFocusIn(element: any) {
    // Store original value for potential restoration
    element._originalOrderQty = element.orderQty;
  }

  // Handle focus out on Order Qty input with validation
  onOrderQtyFocusOut(element: any) {
    // Handle empty, null, undefined, or negative values
    if (element.orderQty === null || element.orderQty === undefined ||
        element.orderQty === '' || element.orderQty < 0) {
      element.orderQty = 0;
    }

    // Ensure orderQty doesn't exceed requestedQty
    if (element.orderQty > element.requestedQty) {
      element.orderQty = element.requestedQty;
      this.utils.snackBarShowWarning(`Order quantity cannot exceed requested quantity (${element.requestedQty})`);
    }

    // For "Nos" UOM, ensure whole numbers only
    if (element.uom && element.uom.toLowerCase() === 'nos' && element.orderQty % 1 !== 0) {
      element.orderQty = Math.floor(element.orderQty);
      this.utils.snackBarShowWarning('Quantity must be a whole number for "Nos" items');
    }

    // Update row total after validation
    this.updateRowTotal(element);
  }


    // previewPr(obj) {
    //   this.dialog.open(PrPreviewDialogComponent, {
    //   data: obj
    //   });
    // }


    focusFunctionWithOutForm(element , value){
      if(Number(element[value]) === 0){
        element[value] = null;
      }
    }

focusOutFunctionWithOutForm(element , value){
  let originalValue = element[value];

  if(element.orderQty === null){
    element[value] = 0
  }

  // Apply validation logic
  this.updateRowTotal(element);

  // Force UI update if value was changed
  if (originalValue !== element[value]) {
    this.cdr.detectChanges();
  }
}

  updateStatus(prId): void {
      this.dialog.open(SimpleDialogComponent, {
        data: {
          title: 'PR Status',
          msg: `Purchase Request successfully created with ID: ${prId}.`,
          ok: function () {

          }.bind(this)
        }
      });
  }
}
