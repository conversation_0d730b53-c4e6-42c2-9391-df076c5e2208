<h1 mat-dialog-title class="managepermission_model_header_dialog_box header-bold"> Report Info
  <a class='btn' (click)="closeDialog()" style="cursor: pointer;float: right;padding: unset;"><i
      class="fa fa fa-times mat_icon_clear" aria-hidden="true"></i></a>
</h1>
<div mat-dialog-content class="reg_terms_mat_dialog_content create-team-dialog">
  <!-- <table>
    <ng-container *ngFor="let data of metaData">
      <ng-container *ngFor="let item of data | keyvalue">
        <tr>
          <th class="topItemkey small text-muted pr-2" style="font-weight: bold; width: auto !important;" scope="row">{{ item.key }}</th>
          <td>
            <div *ngIf="(isoDateTimeRegex.test(item.value))">
               {{ item.value | date:'MMM d, hh:mm:ss a'}} 
            </div>
            <div *ngIf="!(isoDateTimeRegex.test(item.value))">
              {{ item.value }} 
            </div>
          </td>
        </tr>
      </ng-container>
    </ng-container>
  </table> -->


  <section class="example-container-1 mat-elevation-z8">
  <table class="table">
    <tbody *ngIf="metaData">
      <tr>
        <th class="topItemkey" scope="row">Report Number</th>
        <td>{{ metaData[1].reportNo }}</td>
      </tr>
      <tr>
        <th class="topItemkey" scope="row">Report Type</th>
        <td>{{ metaData[2].type }}</td>
      </tr>
      <tr *ngIf="metaData[2].startDate">
        <th class="topItemkey" scope="row">Start Date</th>
        <td>{{ metaData[2].startDate | date: "mediumDate" }}</td>
      </tr>
      <tr *ngIf="metaData[2].endDate">
        <th class="topItemkey" scope="row">End Date</th>
        <td>{{ metaData[2].endDate | date: "mediumDate" : '-5.30' }}</td>
      </tr>
      <tr *ngIf="metaData[2].selectedRestaurants">
        <th class="topItemkey" scope="row">Selected Branch</th>
        <td>{{ metaData[2].selectedRestaurants }}</td>
      </tr>
      <tr *ngIf="metaData[2].selectedCategories && metaData[2].selectedCategories.length > 0">
        <th class="topItemkey" scope="row">Category</th>
        <td>{{ metaData[2].selectedCategories }}</td>
      </tr>
      <tr *ngIf="metaData[2].selectedSubCategories && metaData[2].selectedSubCategories.length > 0">
        <th class="topItemkey" scope="row">SubCategory</th>
        <td>{{ metaData[2].selectedSubCategories }}</td>
      </tr>
      <tr *ngIf="metaData[2].selectedWorkAreas && metaData[2].selectedWorkAreas.length > 0">
        <th class="topItemkey" scope="row">WorkArea</th>
        <td>{{ metaData[2].selectedWorkAreas }}</td>
      </tr>
      <tr *ngIf="metaData[2].startYear">
        <th class="topItemkey" scope="row">Start Year</th>
        <td>{{ metaData[2].startYear }}</td>
      </tr>
      <tr *ngIf="metaData[2].startMonth">
        <th class="topItemkey" scope="row">start Month</th>
        <td>{{ metaData[2].startMonth }}</td>
      </tr>
      <tr *ngIf="metaData[2].tax">
        <th class="topItemkey" scope="row">Tax</th>
        <td>{{ metaData[2].tax }}</td>
      </tr>
      <!-- <tr *ngIf="metaData[2].selectedVendors.length > 0 && metaData[2].selectedVendors">
        <th class="topItemkey" scope="row">Vendor</th>
        <td>{{ metaData[2].selectedVendors }}</td>
      </tr> -->
    </tbody>
  </table>
</section>

</div>