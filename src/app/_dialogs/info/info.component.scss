.managepermison_material_dialog {
    overflow: hidden;
    display: block;
    padding: 8px 24px;
    margin: 0;
    height: 185px;
    max-height: 53vh;
  }
  
  table {
    width: 100% !important;
    margin: 0px auto !important;
    table-layout: fixed;
  }
  
  th,td {
    word-wrap: break-word;
    overflow: hidden;
  }

  .managepermission_model_header_dialog_box {
    // background-color: #4b93e193;
    // background-color: #07edd89e;
    // background-color: #A52A2A;
    // background-color: teal;
    text-align:center;
    margin: 0;
    padding: 3px;
    color: white;
    font-weight: bold;
    font-size: 15px;
  }
  .svg-inline--fa {
    float: right;
    font-size: large;
    height: 1em;
    overflow: visible;
    vertical-align: -.125em;
    margin-top:5px;
}
  
  .reg_terms_mat_dialog_content {
    display: block;
    margin: 0;
    padding: 10px 10px;
    max-height: 65vh;
    overflow: unset;
  }
  
  ::ng-deep .team-description {
    height: 200px;
  }
  
  .cancel_button {
    background: grey;
    color: white;
  }
  
  .save_modal_button {
    float: right;
    margin-bottom: 14px;
  }
  
  .hint {
    // margin-left: 55%;
    margin-left: 82%;
  }
  
  .image-upload-button {
  margin-top: 24px;
  }

  .example-container-1{
    max-height: 310px;
    overflow-y: auto;
  }