import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { MenuItemService, GlobalsService } from '../../_services';
@Component({
  selector: 'app-info',
  templateUrl: './info.component.html',
  styleUrls: ['./info.component.scss']
})
export class InfoComponent implements OnInit {
  metaData :any ;
  isoDateTimeRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d+)?(([+-]\d{2}:\d{2})|Z)?$/;
  constructor(
    public dialogRef: MatDialogRef<InfoComponent>,
    private menuItems: MenuItemService,
    @Inject(MAT_DIALOG_DATA) public data: any) { 
      let obj = {}
      obj['reportNo'] = data.reportData.name.replace(".xlsx","");
      this.menuItems.getMetaData(obj).subscribe(res => {
        if(res['result'] == 'success'){
          this.metaData =res['reportData'];
        }
    }, err => {console.log(
        err
      )});
    }

    
    closeDialog(){
      this.dialogRef.close();
    }
  ngOnInit() {
  }


}

