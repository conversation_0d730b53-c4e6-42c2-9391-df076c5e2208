<h2 mat-dialog-title>
  <button mat-icon-button class="CloseBtn" matTooltip="close">
    <mat-icon class="closeIcon" (click)="close()">close</mat-icon>
  </button>
  <b>{{data.title}}</b>
</h2>

<mat-dialog-content class="mat-typography">

  <div fxLayout fxLayoutAlign="space-between center" *ngIf="data.title != 'Stock Conversion' && data.title != 'Shortage'">
    <span class="dialogTitleData"><b class="dialogTitle">{{data.title}}</b> {{data.id}}</span> <br>
  </div>
  <div fxLayout fxLayoutAlign="space-between center" *ngIf="data.title != 'Stock Conversion' && data.title != 'Shortage'">
    <span class="dialogTitleData" *ngIf="data.order"><b class="dialogTitle">PO#</b> {{data.order.id}} </span>
    <span class="dialogTitleData invoiceNum" *ngIf="data.order"><b class="dialogTitle">Invoice num#</b>
      {{data.order.InvNo}}</span>
  </div>

  <!-- <div class="table-wrapper-scroll-y my-custom-scrollbar">
      <table class="table table-bordered table-striped mb-0" > -->
      <div *ngIf="data.title != 'Stock Conversion' && data.title != 'Shortage'">
        <table class="table">
          <thead>
            <tr>
              <th>#</th>
              <th>Name</th>
              <th>Received Quantity</th>
              <th>Unit Cost</th>
              <th>Tax Amount</th>
              <th>Total Amount</th>
            </tr>
          </thead>
          <tr *ngFor="let item of data.items; let i = index">
            <td>{{ i + 1 }}</td>
            <td style="text-align: left">{{ item.itemName }}</td>
            <td>{{ item.receivedQty }}</td>
            <td>{{ this.utils.truncateNew(item.packages[0].packagePrice) }}</td>
            <td>{{ this.utils.truncateNew(item.taxAmount) }}</td>
            <td>{{ this.utils.truncateNew(item.totalPrice) }}</td>
          </tr>
          <tbody>
            <tr>
              <td></td>
              <td>Transportation Charges</td>
              <td></td>
              <td></td>
              <td></td>
              <td>{{ data.otherCharges }}</td> 
            </tr>
            <!-- <tr>
              <td></td>
              <td>Labour Charges</td>
              <td></td>
              <td></td>
              <td></td>
              <td>{{ data.labourCharges }}</td> 
            </tr> -->
            <tr *ngFor="let item of data.otherTax; let i = index">
              <td></td>
              <td>{{ item.taxName }}</td>
              <td></td>
              <td></td>
              <td></td>
              <td>{{ item.value }}</td> 
            </tr>
            <tr>
              <th></th>
              <th>Total</th>
              <th>{{ getTotal('receivedQty') }}</th>
              <th></th>
              <th></th>
              <th>{{ this.utils.truncateNew(data.grandTotal) }}</th>
            </tr>
          </tbody>
        </table>
      </div>

      <div *ngIf="data.title == 'Shortage'">
        <table class="table">
          <thead>
            <tr>
              <th>#</th>
              <th>Item Code</th>
              <th>Name</th>
              <th>Package</th>
              <th>Exception</th>
            </tr>
          </thead>
          <tr *ngFor="let item of data.items; let i = index">
            <td>{{ i + 1 }}</td>
            <td>{{ item.itemCode }}</td>
            <td style="text-align: left">{{ item.itemName }}</td>
            <td>{{ item.packageName }}</td>
            <td style="text-align: left">{{ item.exceptionReason }}</td>
          </tr>
          <tbody>
          </tbody>
        </table>
      </div>


      <div *ngIf="data.title == 'Stock Conversion'">
        <table class="table">
          <thead>
            <tr>
              <th>ItemCode</th>
              <th>Item Name</th>
              <th>UOM</th>
              <th>Current Stock</th>
              <th>Weight</th>
              <th>Unit Cost</th>
              <th>Total Price</th>
            </tr>
          </thead>
          <tr *ngFor="let item of data.parentItems">
            <td>{{ item.itemCode }}</td>
            <td>{{ item.itemName | titlecase }}</td>
            <td>{{ item.uom }}</td>
            <td>{{ this.utils.truncateNew(item.inStock) }}</td>
            <td>{{ this.utils.truncateNew(item.stockConversionQty) }}</td>
            <td>{{ this.utils.truncateNew(item.unitPrice) }}</td>
            <td>{{ this.utils.truncateNew(item.unitPrice * item.stockConversionQty) }}</td>
          </tr>
        </table>
      </div>

      <div *ngIf="data.title == 'Stock Conversion'">
        <table class="table">
          <thead>
            <tr>
              <th>#</th>
              <th>ItemCode</th>
              <th>Item Name</th>
              <th>UOM</th>
              <th>Current Stock</th>
              <th>Weight</th>
              <th>Unit Cost</th>
              <th>Total Price</th>
            </tr>
          </thead>
            <tr *ngFor="let item of data.items; let i = index">
              <ng-container *ngIf="item.weight > 0">
                <td>{{ i + 1 }}</td>
                <td>{{ item.itemCode }}</td>
                <td>{{ item.itemName | titlecase }}</td>
                <td>{{ item.uom }}</td>
                <td>{{ this.utils.truncateNew(item.inStock) }}</td>
                <td>{{ this.utils.truncateNew(item.weight) }}</td>
                <td>{{ this.utils.truncateNew(item.price) }}</td>
                <td>{{ this.utils.truncateNew(item.totalPrice) }}</td>
              </ng-container>
            </tr>
          <tbody>
            <tr>
              <th></th>
              <th>Total</th>
              <th></th>
              <th></th>
              <th></th>
              <th>{{ this.utils.truncateNew(getTotalSc('weight')) }}</th>
              <th></th>
              <th>{{ this.utils.truncateNew(getTotalSc('totalPrice')) }}</th>
            </tr>
          </tbody>
        </table>
      </div>

      <div fxLayout fxLayoutAlign="space-between center" *ngIf="data.title == 'Stock Conversion'">
        <span class="dialogTitleData scItemsClass" ><b class="dialogTitle">wastageQuantity</b> {{ this.utils.truncateNew(data.wastageQuantity) }} {{ data.uom }} </span>
        <span class="dialogTitleData scItemsClass" ><b class="dialogTitle">wastageAmount</b>₹ {{ this.utils.truncateNew(data.wastageAmount) }}</span>
      </div>

</mat-dialog-content>

<mat-dialog-actions align='center'>
  <div class="goToGrn">
    <button mat-button  *ngIf="data.title != 'Stock Conversion' && data.title != 'Shortage'" class="goGrn button3 mb-2" (click)="goGrn()" matTooltip="go to GRN">
      <mat-icon class="arrowIcon">arrow_circle_right_outline</mat-icon><b>GRN</b></button>
      <button mat-button *ngIf="data.title == 'Stock Conversion' && data.title != 'Shortage'" class="goGrn button3 mb-2" (click)="closeDialog()">
       <b>Stock Conversion List</b></button>
       <button mat-button *ngIf="data.title == 'Shortage'" class="goGrn button3 mb-2" (click)="close()">
        <b>OK</b></button>
  </div>
</mat-dialog-actions>