import { Component, OnInit, Inject} from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA} from  '@angular/material';
import { Router } from '@angular/router';
import { UtilsService } from '../../_utils/utils.service';
import { PurchasesService } from 'src/app/_services';

@Component({
  selector: 'app-invoice-dialog',
  templateUrl: './invoice-dialog.component.html',
  styleUrls: ['./invoice-dialog.component.scss']
})
export class InvoiceDialogComponent implements OnInit {
  pr: any;
  grnId: any;

  constructor(private  dialogRef:  MatDialogRef<InvoiceDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public  data:  any,
    private router : Router,
    private utils : UtilsService,
    public purchases: PurchasesService) { }

  ngOnInit() {
    if(this.data.title != 'Stock Conversion' && this.data.title != 'Shortage'){
      this.processData(this.data);
      if ('id' in this.data){
        this.grnId = this.data.id;
      }
    }
  }
  public close(){
    this.dialogRef.close();
  }

  public ok(){
    this.data.ok();
    this.close();
  }

  processData(data){
    data.items.map(item => {item.totalValue=item.totalPrice})
  }

  getTotal(str){
    let total = this.utils.getTotal(this.data.items,str);
    let grandTotal = this.data.labourCharges + this.data.otherCharges + total
    return grandTotal
  }

  getTotalSc(key: string) {
    return this.utils.getTotal(this.data.items, key); 
}

  goGrn(){
    this.dialogRef.close();
    this.router.navigate(['/home/<USER>',{grnNumber:this.grnId }]);
  }

  closeDialog(){
    this.dialogRef.close();
    this.router.navigate(['/home/<USER>']);
    // this.router.navigate(['/home/<USER>']);
    // this.router.navigate(['/home/<USER>']);
  }

}
