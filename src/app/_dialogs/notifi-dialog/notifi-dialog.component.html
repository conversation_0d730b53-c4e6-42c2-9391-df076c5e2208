<div class="notifiCloseBtn">
      <button mat-icon-button class="closenotifyDialog" (click)="closeDialog()" matTooltip="close">
        <mat-icon>close</mat-icon>
      </button> 
</div>
<br><br>

<div>

  <div class="svg-icon">
    <svg xmlns="http://www.w3.org/2000/svg" height="110" fill="green" class="bi bi-check-circle-fill"
      viewBox="0 0 16 16">
      <path
        d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z" />
    </svg>

    <!-- error icon  =>  <svg  height="130" viewBox="0 0 24 24" fill="red" xmlns="http://www.w3.org/2000/svg"><path d="M12 4a8 8 0 1 0 0 16 8 8 0 0 0 0-16zM2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12zm5.793-4.207a1 1 0 0 1 1.414 0L12 10.586l2.793-2.793a1 1 0 1 1 1.414 1.414L13.414 12l2.793 2.793a1 1 0 0 1-1.414 1.414L12 13.414l-2.793 2.793a1 1 0 0 1-1.414-1.414L10.586 12 7.793 9.207a1 1 0 0 1 0-1.414z" fill="red"/></svg>  </div> -->

    <!-- warning icon => <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" height="130" fill="yellow" > <g> <path fill="none" d="M0 0h24v24H0z"/> <path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm-1-7v2h2v-2h-2zm0-8v6h2V7h-2z"/> </g> </svg> -->
    <br><br>

    <div class="notifi">
      <h1 style="color:white; font-weight: bolder;">{{ reportNo }}</h1>
    </div>

    <div class="notifi message">
      <div>Requested report is scheduled now</div>
    </div>
    <br>

  </div>