import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
@Component({
  selector: 'app-notifi-dialog',
  templateUrl: './notifi-dialog.component.html',
  styleUrls: ['./notifi-dialog.component.scss']
})
export class NotifiDialogComponent implements OnInit {
  message: any;
  reportNo: any;

  constructor(
    public dialogRef: MatDialogRef<NotifiDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any) { }

  ngOnInit() {
    this.message = this.data.message;
    this.reportNo = this.data.reportNo;
  }

  closeDialog() {
    this.dialogRef.close();
  }
}
