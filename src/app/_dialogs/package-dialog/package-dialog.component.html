
<div *ngIf="packageItems.length > 0; else noPackage">

  <h2 mat-dialog-title >
    <button mat-icon-button class="CloseBtn">
      <mat-icon (click)="close()" matTooltip="close" class="closeIcon">close</mat-icon>
    </button>
  </h2>

  <mat-dialog-content class="mat-typography">
    <mat-grid-list cols="4" rowHeight="50px">
      <mat-grid-tile>Package Name </mat-grid-tile>
      <mat-grid-tile>Num Of Packages</mat-grid-tile>
      <mat-grid-tile>Package Quantity </mat-grid-tile>
      <mat-grid-tile>UOM</mat-grid-tile>
    </mat-grid-list>
    <mat-divider [inset]="true"></mat-divider>
    <mat-grid-list class="package_items" cols="4" *ngFor="let ps of packageItems; let i = index;" rowHeight="50px">
      <div>
        <mat-grid-tile>{{ps.pkgName}}</mat-grid-tile>
        <mat-grid-tile>
          <input matInput [(ngModel)]="ps.orderedPackages" type="number" min="0" placeholder="{{ps.orderedPackages || 0}}" step = "any" onkeypress = "return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)">
        </mat-grid-tile>
        <mat-grid-tile> {{ps.pkgQty}} </mat-grid-tile>
        <mat-grid-tile> {{this.data.uom}} </mat-grid-tile>
      </div>
    </mat-grid-list>
    <mat-divider [inset]="true"></mat-divider>
    <div *ngIf="displayFreePackage">
      <mat-grid-list cols="1" rowHeight="50px">
        <mat-grid-tile class="open_section">Open Packages</mat-grid-tile>
      </mat-grid-list>
      <div *ngIf="this.openPkgUom=='KG';else noWeightTemplate">
        <mat-grid-list cols="4" rowHeight="50px">
          <mat-grid-tile>Package Name </mat-grid-tile>
          <mat-grid-tile>Num Of Packages</mat-grid-tile>
          <mat-grid-tile>Total Weight </mat-grid-tile>
          <mat-grid-tile>UOM</mat-grid-tile>
        </mat-grid-list>
        <mat-divider [inset]="true"></mat-divider>
        <mat-grid-list class="package_items" cols="4" rowHeight="50px">
          <div *ngFor="let ps of packageItems; let i=index;">
            <div *ngIf="!ps.pkgName.toLowerCase().includes('case')">
              <mat-grid-tile>Open {{ps.pkgName}} Quantity</mat-grid-tile>
              <mat-grid-tile>
                <input matInput style="width:80px !important" [(ngModel)]="ps.numOfOpenPkg" min="0" type="number" step = "any" onkeypress = "return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)">
              </mat-grid-tile>
              <mat-grid-tile *ngIf="this.openPkgUom=='KG'" >
                <input matInput style="width:100px !important" [(ngModel)]="ps.otherPackages" min="0" type="number" step = "any" onkeypress = "return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)">
              </mat-grid-tile>
              <mat-grid-tile > {{this.openPkgUom}} </mat-grid-tile>
            </div>
          </div>
        </mat-grid-list>
      </div>
      <ng-template #noWeightTemplate>
        <mat-grid-list cols="3" rowHeight="50px">
          <mat-grid-tile>Package Name </mat-grid-tile>
          <mat-grid-tile>Quantity</mat-grid-tile>
          <mat-grid-tile>UOM</mat-grid-tile>
        </mat-grid-list>
        <mat-divider [inset]="true"></mat-divider>
        <mat-grid-list class="package_items" cols="3" rowHeight="50px">
          <div *ngFor="let ps of packageItems; let i=index;">
            <div *ngIf="!ps.pkgName.toLowerCase().includes('case')">
              <mat-grid-tile>Open {{ps.pkgName}} Quantity</mat-grid-tile>
              <mat-grid-tile>
                <input matInput style="width:100px !important" [(ngModel)]="ps.otherPackages" min="0" type="number" step = "any" onkeypress = "return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)">
              </mat-grid-tile>
              <mat-grid-tile > {{this.openPkgUom}} </mat-grid-tile>
            </div>
          </div>
        </mat-grid-list>
      </ng-template>
    </div>
    <mat-divider [inset]="true"></mat-divider>
    <mat-divider [inset]="true"></mat-divider>
  </mat-dialog-content>
  <mat-dialog-actions align='center'>
    <button class="mat-raised-button mat-primary mb-2" (click)="save()" matTooltip="click to save">Save</button>
  </mat-dialog-actions>
</div>
<ng-template #noPackage>
  No Packaging sizes available for this item. Please enter the quantity directly in closing stock Page.
</ng-template>
