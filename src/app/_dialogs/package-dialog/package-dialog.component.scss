mat-form-field {
  width: 100%;
}


mat-grid-list input {
      border: 1px solid;
      padding: 5px;
}

.mat-divider.mat-divider-inset {
  margin-left: 0px;
}

.package_items {
  margin-top: 10px;
}

.open_section{
  background-color:#f1f1f1;
  font-size : 30px;
}

// button {
//   padding-left: 20px;
//   padding-right: 20px;
//   padding-top: 10px;
//   padding-bottom: 10px;
// }
.CloseBtn{
  float: right;
  margin-top: -7px;
  margin-right: -7px;
  margin-bottom: -1px;
}

.mat-dialog-content {
  margin: 0px !important; 
 padding: 15px 24px;
 max-height: 65vh;
 overflow: auto;
}

// ::ng-deep .mat-dialog-actions {
// 	display: inherit !important;
// }
.mat-dialog-title {
  margin: 0px; 
}