import { Component, OnInit, Input, Inject} from '@angular/core';
import {MatDialogRef, MAT_DIALOG_DATA} from  '@angular/material';
import {UtilsService} from '../../_utils/utils.service'
import { FormBuilder, FormGroup, FormArray } from '@angular/forms';

@Component({
  selector: 'app-package-dialog',
  templateUrl: './package-dialog.component.html',
  styleUrls: ['./package-dialog.component.scss']
})
export class PackageDialogComponent implements OnInit {
  packageForm: FormGroup;
  openPkgUom: any;
  items: FormArray;
  packageItems: any[];
  freeQtyDesc : string;
  displayFreePackage: boolean;
  @Input() opts : any;
  weightConvertedCpt : number;
constructor(private fb: FormBuilder,
            private  dialogRef:  MatDialogRef<PackageDialogComponent>,
            @Inject(MAT_DIALOG_DATA) public  data:  any,
            private utils : UtilsService) {

}

ngOnInit() {
  this.packageItems = this.data.packagingSizes
  this.openPkgUom = this.data.closingUom.split('/')[1].toUpperCase()
  this.packageItems.forEach(element => {
    if(!element.otherPackages)
      element.otherPackages = 0
    if(!element.numOfOpenPkg)
      element.numOfOpenPkg = 0
  });
  this.displayFreePackage = this.data.displayFreePackage;
  if(!this.data.weightConvertedCpt)
  this.data.weightConvertedCpt = 0
  }


public close(){
  this.dialogRef.close();
}


public save(){
  this.data.packagingSizes = this.packageItems;
  this.data.packagingSizes.forEach(element => {
    let obj = {
      currentWeight : element.otherPackages,
      packageQty : this.data.moq,
      fullWeight :  this.data.fullBottleWeight,
      emptyWeight : element.emptyBottleWeight,
      cnvrtWeightToCpt : element.cnvrtWeightToCpt,
      numOfOpenPkg : element.numOfOpenPkg
    }
    element.cnvtdOtherPackages = this.utils.bottleWeightToCapacityConversion(obj)
    
  });
  this.dialogRef.close(this.data);
}

}
