<h1 mat-dialog-title>
  <button mat-icon-button class="btn-outline-white CloseBtn" matTooltip="close" (click)="close()">
    <mat-icon class="closeIcon">close</mat-icon>
  </button>
  <b>{{title}}</b>
</h1>

<mat-dialog-content>
  <div id="print-section">
    <div class="po-details">
      <div fxLayout fxLayoutAlign="space-between center">
        <span class="dialogTitleData"> <b class="dialogTitle">PR number</b> {{pr.prId}}</span>
        <span class="dialogTitleData"> <b class="dialogTitle"> Issue Date </b> {{ pr.createTs | date : 'EEEE, MMMM d, y' : 'GMT'}}</span>
      </div>
      <div fxLayout fxLayoutAlign="space-between center">
        <span class="dialogTitleData" *ngIf="pr.restaurantName"> <b class="dialogTitle">Ship To</b>{{pr.restaurantName}} </span>
        <span class="dialogTitleData"> <b class="dialogTitle">Expected Date</b> {{ pr.eta| date : 'EEEE, MMMM d, y'}}</span>
      </div>
      <div fxLayout fxLayoutAlign="space-between center">
        <span class="dialogTitleData" *ngIf="pr.vendorName"> <b class="dialogTitle">Vendor</b> {{pr.vendorName}}</span>
      </div>
    </div>
    <!-- <div fxLayout fxLayoutAlign="space-between center" id="contact-section">
        <div *ngIf="pr.vendorName">
          <h4><b>Vendor</b></h4>
          <b> {{pr.vendorName}}</b> <br />
          <address *ngIf="false">

            {{selectedVendor.address.line1}},
            <span *ngIf="selectedVendor.address.line2"> {{selectedVendor.address.line2}},</span>
            <span *ngIf="selectedVendor.address.line3"> {{selectedVendor.address.line3}},</span>
            <span *ngIf="selectedVendor.address.landmark"> {{selectedVendor.address.landmark}},</span><br>
            <span *ngIf="selectedVendor.address.city"> {{selectedVendor.address.city}},</span><br>
            <span *ngIf="selectedVendor.address.state"> {{selectedVendor.address.state}},</span>
            <span *ngIf="selectedVendor.address.country"> {{selectedVendor.address.country}},</span><br>
            <span *ngIf="selectedVendor.address.contact.person"> {{selectedVendor.address.contact.person}},</span><br>
            <span *ngIf="selectedVendor.address.contact.number"> {{selectedVendor.address.contact.number}},</span><br>
          </address>
        </div>
        <div *ngIf="pr.restaurantName">
          <h4><b>Ship To</b></h4>
          <b>{{pr.restaurantName}}</b><br />
          <address *ngIf="false">
            {{pr.customer.address.line1}},
            <span *ngIf="pr.customer.address.line2"> {{pr.customer.address.line2}},</span>
            <span *ngIf="pr.customer.address.line3"> {{pr.customer.address.line3}},</span>
            <span *ngIf="pr.customer.address.landmark"> {{pr.customer.address.landmark}},</span><br>
            <span *ngIf="pr.customer.address.city"> {{pr.customer.address.city}},</span><br>
            <span *ngIf="pr.customer.address.state"> {{pr.customer.address.state}},</span>
            <span *ngIf="pr.customer.address.country"> {{pr.customer.address.country}},</span><br>
            <span *ngIf="pr.customer.address.contact.person"> {{pr.customer.address.contact.person}},</span><br>
            <span *ngIf="pr.customer.address.contact.number"> {{pr.customer.address.contact.number}},</span><br>
          </address>
        </div>
      </div> -->

    <table class="table">
      <thead>
        <tr *ngIf="displayedColumns">
          <!-- <th *ngFor="let col of displayedColumns">{{col}}</th> -->
          <th>#</th>
          <th>Name</th>
          <th>Pkg Name</th>
          <th>Units/Pkg</th>
          <!-- <th>
                  Item Code
                  </th> -->
          <!-- <th >Type</th> -->
          <th>Order Qty</th>
          <!-- <th>
              UOM
            </th> -->
          <th>Unit Cost</th>
          <th>Total(excl.tax)</th>
          <th>Tax Amt</th>
          <th>Total(incl.tax)</th>
        </tr>
      </thead>

      <tr *ngFor="let item of pr.prDetails; let i = index">
        <td>{{i+1}}</td>
        <td style="text-align : left">{{item.itemName}}</td>
        <td style="text-align : left">{{item.packages[0].packageName}}</td>
        <td style="text-align : left">{{item.packages[0].unitPerPkg}}</td>
        <!-- <td>{{item.itemCode}}</td> -->
        <!-- <td>
              {{  item.brand.name}}
                </td> -->
        <td>{{item.quantity}}</td>
        <!-- <td>{{item.uom}}</td> -->
        <td *ngIf="!item.unitPriceExclTax">{{ this.utils.truncateNew(item.subTotal/item.quantity)}}</td>
        <td *ngIf="item.unitPriceExclTax">{{this.utils.truncateNew(item.unitPriceExclTax)}}</td>
        <td>{{this.utils.truncateNew(item.subTotal)}}</td>
        <td>{{this.utils.truncateNew(item.taxAmount)}}</td>
        <td *ngIf="!item.totalPrice">{{ this.utils.truncateNew(((item.unitPrice+(item.unitPrice*item.taxRate/100)) * item.quantity))}}</td>
        <td *ngIf="item.totalPrice">{{this.utils.truncateNew(item.totalPrice)}}</td>
      </tr>
      <tbody>
        <tr *ngFor="let item of pr.otherTax; let i = index">
          <td></td>
          <td>{{item.taxName}}</td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td></td>
          <td>{{item.value}}</td>
        </tr>
      </tbody>
      <tfoot>
        <tr>
          <td></td>
          <td>Total</td>
          <td></td>
          <td></td>
          <td>{{getTotal('quantity')}}</td>
          <td></td>
          <!-- <td>{{getTotal('unitPrice')}}</td> -->
          <td>{{this.utils.truncateNew(getTotal('subTotal'))}}</td>
          <td>{{this.utils.truncateNew(getTotal('taxAmount'))}}</td>
          <td>{{this.utils.truncateNew(pr.grandTotal)}}</td>
        </tr>
      </tfoot>
    </table>

  </div>

  <!-- <div fxLayout fxLayoutAlign="space-between center" class="action-btns">
          <button mat-raised-button color="primary" (click)="editPo()">Edit Order</button>
          <button mat-raised-button color="primary" (click)="print()">Print Order</button>
        </div> -->
</mat-dialog-content>
<!-- <mat-dialog-actions>
  <button  mat-raised-button color="primary" (click)="ok()">Yes</button>
</mat-dialog-actions> -->

<mat-dialog-actions align='center'>
  <div class="text-center" *ngIf=" title == 'Purchase Request' ">
    <button class="dialogBtn button3 mb-2" mat-button (click)="navigate1()" matTooltip="Go to Purchase Request">
      <mat-icon class="arrowIcon">arrow_circle_right_outline</mat-icon> <b>Purchase Request</b> </button>
  </div>
  <div class="text-center" *ngIf=" title == 'Purchase Order' ">
    <button class="dialogBtn button3 mb-2" mat-button (click)="navigate2()" matTooltip="Go to Purchase Orders">
      <mat-icon class="arrowIcon">arrow_circle_right_outline</mat-icon> <b>Purchase Orders</b> </button>
  </div>
  <div class="text-center" *ngIf="this.user.tenantId == '100045' || this.user.tenantId == '100054'">
    <button class="dialogBtn button3 mb-2" mat-button (click)="navigate3()" matTooltip="Go to Purchase Status">
      <mat-icon class="arrowIcon">arrow_circle_right_outline</mat-icon> <b>Purchase Status</b> </button>
  </div>
</mat-dialog-actions>