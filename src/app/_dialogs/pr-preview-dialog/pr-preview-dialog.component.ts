
import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { Router } from '@angular/router';
import { GlobalsService } from '../../_services/globals.service';
import { UtilsService } from '../../_utils/utils.service'
import { AuthService, PurchasesService } from 'src/app/_services';
@Component({
  selector: 'app-pr-preview-dialog',
  templateUrl: './pr-preview-dialog.component.html',
  styleUrls: ['./pr-preview-dialog.component.scss']
})
export class PrPreviewDialogComponent implements OnInit {
  displayedColumns = GlobalsService.createPoColumns;
  isShow: boolean;
  poNumber : any;
  user: any;
  uniqueModules: any[];
  constructor(
    private router : Router,
    private auth: AuthService,
    public purchases: PurchasesService,
    private dialogRef: MatDialogRef<PrPreviewDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    public utils: UtilsService) {
      this.user = this.auth.getCurrentUser() ;
     }

  strinData: string;
  pr: any;
  title : string = "Purchase Request";

  ngOnInit() {
    if (this.data.prId){
      if(this.user.tenantId == '100045' || this.user.tenantId == '100054'){
        this.title = "Purchase Requisiton"
        this.pr = this.data;
      }else{
        this.pr = this.data;
      }
    }
    else if(this.data.poId){      
      this.poNumber = this.data.poId;
      this.title = "Purchase Order"
      this.pr = {};
      this.pr.prId = this.data.poId;
      this.pr.prDetails = this.data.poDetails;
      this.pr.prType = this.data.poType;
      this.pr.eta = this.data.eta
      this.pr.createTs = this.data.createTs;
      this.pr.prDetails = this.data.poItems
      this.pr.otherTax = this.data.otherTax;
      this.pr.grandTotal = this.data.grandTotal;
    }

    this.pr.prDetails.map((item : any) => {
      if(item.totalPrice){
        item.totalValue=item.totalPrice;
      }
      else{
        item.totalValue =  (item.unitPrice+(item.unitPrice*item.taxRate/100)) * item.quantity;
      }
    });
    this.auth.getRolesList({
      tenantId: this.user.tenantId
    }).subscribe(data => {
      let da = data['rolesList'].find((user) => user.role == this.user.role);
      const modules = [].concat(...Object.values(da['modules']));
      this.uniqueModules = [...new Set(modules)];
    });
  }


  public close() {
    this.dialogRef.close();
  }

  navigate1(){
    this.dialogRef.close();
    let prId = this.pr.prId;
    this.uniqueModules.includes('Purchase Requests') ? this.router.navigate(['/home/<USER>',{prNumber:prId }]) : this.router.navigate(['/home'])
  }

  navigate2(){
    this.dialogRef.close();
    let prId = this.pr.prId;
    this.uniqueModules.includes('Purchase Orders') ?  this.router.navigate(['/home/<USER>',{poNumber:this.poNumber }]) : this.router.navigate(['/home'])
  }

  navigate3(){
    this.dialogRef.close();
    let prId = this.pr.prId;
    this.uniqueModules.includes('Purchase Status') ? this.router.navigate(['/home/<USER>',{prNumber:prId }]) : this.router.navigate(['/home'])
  }

  getTotal(key: string) {
    return this.utils.getTotal(this.pr.prDetails, key)
  }

}
