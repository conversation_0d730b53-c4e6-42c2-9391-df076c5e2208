<h2 mat-dialog-title>
  <button mat-icon-button class="CloseBtn" matTooltip="close" (click)="close()">
    <mat-icon>close</mat-icon>
  </button>
  <b *ngIf="this.data.title == 'IBT Id'"> IBT </b> 
  <b *ngIf="this.data.directIbtTitle == 'CSI'"> CSI </b> 
  <b *ngIf="this.data.component == 'Purchase Status'"> {{data.title}} </b> 
  <b *ngIf="this.data.title == 'Po Terms'"> {{data.title}} </b> 
</h2>

<mat-dialog-content class="mat-typography">
  <h4 class="dialogTitleData" *ngIf="this.data.title == 'IBT Id'"> <b class="dialogTitle"> {{data.title}} </b> {{data.id}} </h4>
  <div class ="d-flex justify-content-between flex-wrap" >
    <h4 class="dialogTitleData" *ngIf="this.data.directIbtTitle == 'CSI'"> <b class="dialogTitle"> IBT ID </b> {{data.id}} </h4>
    <h4 class="dialogTitleData" *ngIf="this.data.directIbtTitle == 'CSI'"> <b class="dialogTitle"> Creator </b> {{data.role}} </h4>
  </div>

  <div fxLayout fxLayoutAlign="space-between center" *ngIf="this.data.component != 'Purchase Status'  && this.data.title != 'Po Terms'">
    <span class="dialogTitleData"> <b class="dialogTitle">From Branch </b> {{ data.fromBranch.location}}</span>
    <span class="dialogTitleData"> <b class="dialogTitle">To Branch </b> {{ data.toBranch.location}}</span>
  </div>

  <div fxLayout fxLayoutAlign="space-between center" *ngIf="this.data.component != 'Purchase Status' && this.data.title != 'Po Terms'">
    <span class="dialogTitleData"><b class="dialogTitle">Issue Date </b> {{ data.createTs | date : 'EEEE, MMMM d,y'}}</span>
    <span class="dialogTitleData"><b class="dialogTitle">Expected Date</b> {{ data.eta| date : 'EEEE, MMMM d, y'}}</span>
  </div>
  
  <table class="table" *ngIf="this.data.component != 'Purchase Status' && this.data.title != 'Po Terms'">
    <thead>
      <tr>
        <th>#</th>
        <th>Name</th>
        <th>Requested Quantity</th>
        <th>Uom</th>
        <th>WAC(incl.tax,etc)</th>
        <th>Total</th>
      </tr>
    </thead>
    <tfoot>
      <tr>
        <th></th>
        <th>Total</th>
        <th>{{this.utils.truncateNew(getTotal('quantity'))}}</th>
        <th></th>
        <th></th>
        <th>{{this.utils.truncateNew(getTotal('totalValue'))}}</th>
      </tr>
    </tfoot>
    <tbody>
      <tr *ngFor="let item of data.items; let i = index">
        <td>{{i+1}}</td>
        <td style="text-align : left">{{item.itemName}}</td>
        <td>{{this.utils.truncateNew(item.quantity)}} {{ getPortionCount(item) }}</td>
        <td>{{item.uom}}</td>
        <td>{{this.utils.truncateNew((item.unitPrice),2)}}</td>
        <td>{{this.utils.truncateNew((item.unitPrice * item.quantity),2)}}</td>
      </tr>
    </tbody>
  </table>

  <table class="table" *ngIf="this.data.title == 'Purchase Request'">
    <thead>
      <tr>
        <th>#</th>
        <th>Level</th>
        <th>Role</th>
        <th>Status</th>
        <th>Reason</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let item of this.data.items?.approvalDetail; let i = index">
        <td>{{i+1}}</td>
        <td>{{ item.level || '-'}}</td>
        <td>{{ item.role || '-'}}</td>
        <td>{{ item.status || '-'}}</td>
        <td>{{ item.reason || '-'}}</td>
      </tr>
    </tbody>
  </table>

  <table class="table" *ngIf="this.data.title == 'Purchase Order'">
    <thead>
      <tr>
        <th>#</th>
        <th>Level</th>
        <th>Role</th>
        <th>Status</th>
        <th>Reason</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let item of this.data.items?.poApprovalDetail; let i = index">
        <td>{{i+1}}</td>
        <td>{{ item.level || '-'}}</td>
        <td>{{ item.role || '-'}}</td>
        <td>{{ item.status || '-'}}</td>
        <td>{{ item.reason || '-'}}</td>
      </tr>
    </tbody>
  </table>

  <table class="table" *ngIf="this.data.title == 'GRN'">
    <thead>
      <tr>
        <th>#</th>
        <th>Level</th>
        <th>Role</th>
        <th>Status</th>
        <th>Reason</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let item of this.data.items?.grnApprovalSetting; let i = index">
        <td>{{i+1}}</td>
        <td>{{ item.level || '-'}}</td>
        <td>{{ item.role || '-'}}</td>
        <td>{{ item.status || '-'}}</td>
        <td>{{ item.reason || '-'}}</td>
      </tr>
    </tbody>
  </table>

  <table class="table" *ngIf="this.data.title == 'PI'">
    <thead>
      <tr>
        <th>#</th>
        <th>Level</th>
        <th>Role</th>
        <th>Status</th>
        <th>Reason</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let item of this.data.items?.approvalDetail; let i = index">
        <td>{{i+1}}</td>
        <td>{{ item.level || '-'}}</td>
        <td>{{ item.role || '-'}}</td>
        <td>{{ item.status || '-'}}</td>
        <td>{{ item.reason || '-'}}</td>
      </tr>
    </tbody>
  </table>


  <table class="table" *ngIf="this.data.title === 'Indents List' || this.data.title === 'CSI'">
    <thead>
      <tr>
        <th>#</th>
        <th>Level</th>
        <th>Role</th>
        <th>Status</th>
        <th>Reason</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let item of this.data.items?.indentApprovalDetail; let i = index">
        <td>{{i+1}}</td>
        <td>{{ item.level || '-'}}</td>
        <td>{{ item.role || '-'}}</td>
        <td>{{ item.status || '-'}}</td>
        <td *ngIf = "this.data.title === 'Indents List'">{{ item.rejectionReason || '-'}}</td>
        <td *ngIf = "this.data.title === 'CSI'">{{ item.reason || '-'}}</td>
      </tr>
    </tbody>
  </table>

 
  <div *ngIf="this.data.title == 'Po Terms'">
    <div class="mb-2">
      Po Terms : 
    </div>
    <div style="text-align: justify;">
      {{data.items}}
    </div>
  </div>

</mat-dialog-content>


<mat-dialog-actions align='center' *ngIf="this.data.component != 'Purchase Status' && this.data.title != 'Po Terms'">
<!-- <mat-dialog-actions align='center'>
  <button  mat-raised-button color="primary" (click)="ok()">Ok</button>
</mat-dialog-actions> -->
<div class="ibtBtn" *ngIf="data.title == 'IBT Id'">
  <button mat-raised-button class="dialogBtn button3 mb-2" (click)="goToIbt()" matTooltip="Go to IBT">
    <mat-icon class="arrowIcon">arrow_circle_right_outline</mat-icon> <b>IBT</b> </button>
</div>
<div class="ibtBtn" *ngIf="this.data.directIbtTitle == 'CSI'">
  <button mat-raised-button class="dialogBtn button3 mb-2" (click)="goToIbt()" matTooltip="Go to IBT">
    <mat-icon class="arrowIcon">arrow_circle_right_outline</mat-icon> <b>CSI</b> </button>
</div>

</mat-dialog-actions>