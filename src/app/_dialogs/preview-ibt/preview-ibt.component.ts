import { Component, OnInit, Inject} from '@angular/core';
import {MatDialogRef, MAT_DIALOG_DATA} from  '@angular/material';
import { Router } from '@angular/router';
import { UtilsService } from '../../_utils/utils.service';

@Component({
  selector: 'app-preview-ibt',
  templateUrl: './preview-ibt.component.html',
  styleUrls: ['./preview-ibt.component.scss']
})
export class PreviewIbtComponent implements OnInit {
  ibtId: any;
  senderBranch: any;

  constructor(private  dialogRef:  MatDialogRef<PreviewIbtComponent>,
            @Inject(MAT_DIALOG_DATA) public  data:  any,
            private router : Router,
            private utils : UtilsService) { }

  ngOnInit() {

    if(this.data.component != 'Purchase Status' && this.data.title != 'Po Terms'){
      this.data.items.map(item => {item.totalValue = item.unitPrice*item.quantity});
      this.ibtId = this.data.id
      this.senderBranch = this.data.fromBranch.restaurantId
    }


  }

  getPortionCount(element){
    if (element.hasOwnProperty('selectedOption') && element['selectedOption'] === 'portion' && element.hasOwnProperty('portionWeight')){
      let conversionCoefficient =  element['uom'] == 'NOS' ? 1: 1000 ;
      let calculatedQuantity = this.utils.truncateNew((element['quantity'] * conversionCoefficient) / element.portionWeight);
      return `(${calculatedQuantity} portion)`
    }
    return ''
  }

  public close(){
    this.dialogRef.close();
  }

  public ok(){
    this.data.ok();
    this.close();
  }

  getTotal(key: string) {
    return this.utils.truncateNew((this.utils.getTotal(this.data.items, key)),2)
  }

  goToIbt(){
    this.dialogRef.close();
    this.router.navigate(['/home/<USER>',{senderBranch:this.senderBranch , ibtNumber:this.ibtId }]);
  }
}
