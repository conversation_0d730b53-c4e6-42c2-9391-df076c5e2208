<h2 mat-dialog-title>
  <button mat-icon-button class="CloseBtn">
    <mat-icon (click)="closeDialog()" matTooltip="close" class="closeIcon">close</mat-icon>
  </button>
  <b>{{data.title}}</b>
</h2>

<mat-dialog-content class="mat-typography">
  <div *ngIf="!danceEvent && !askForInputFlag && !askForInput" class="mb-2">
    <mat-dialog-content class="dialogMsg"> {{data.msg}} </mat-dialog-content>
  </div>

  <div *ngIf='askForInputFlag' style="text-align: center;">
    <div *ngFor="let inputField of objectKeys(data.inputFromUser)">
      <span style="font-size: larger;" *ngIf="inputField != 'Reason'">{{inputField}} </span>
      <input *ngIf="data.title != 'Reject Reason'" type="text" maxlength="20" minlength="10" class="dialogInput" [(ngModel)]="data.inputFromUser[inputField]"
        (keyup)="checkDupTmpName(data.inputFromUser[inputField])">
        <textarea *ngIf="data.title == 'Reject Reason'" id="w3review" name="w3review" rows="4" cols="30" (keyup)="checkDupTmpName(data.inputFromUser[inputField])"
         [(ngModel)]="data.inputFromUser[inputField]"  placeholder={{data.msg}}></textarea>
      <svg *ngIf="data.title != 'Reject Reason'" matTooltip={{data.msg}} xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor"
        class="bi bi-info-circle ml-2" viewBox="0 0 16 16">
        <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z" />
        <path
          d="m8.93 6.588-2.29.287-.082.38.45.083c.294.07.352.176.288.469l-.738 3.468c-.194.897.105 1.319.808 1.319.545 0 1.178-.252 1.465-.598l.088-.416c-.2.176-.492.246-.686.246-.275 0-.375-.193-.304-.533L8.93 6.588zM9 4.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0z" />
      </svg>
      <mat-error *ngIf="showDupAlert">
        Template Name already exists.
      </mat-error>
    </div>
  </div>

  <div *ngIf='askForInput' style="text-align: center;">
    <input type="text" class="dialogInput" [(ngModel)]="data.inputUser.name" (keyup.enter)="ok()">
  </div>

</mat-dialog-content>


<div *ngIf="danceEvent">
  <div class="container__item">
    <label>User Link</label><br>
    <input style="font-size: larger; line-height: 2em; min-width: 300px;" type="text" class="form__field"
      value={{data.msg.user}} #userinput1 readonly="readonly">
    <button class="btn btn--primary btn--inside uppercase" matTooltip="copy invite url"
      (click)="copyInputMessage(userinput1)" value="click to copy">
      <i class="material-icons">content_copy</i>
    </button>
  </div>
  <br>
  <div class="container__item">
    <label>Cashier Link</label><br>
    <input style="font-size: larger; line-height: 2em; min-width: 300px;" type="text" class="form__field"
      value={{data.msg.cashier}} #userinput2 readonly="readonly">
    <button class="btn btn--primary btn--inside uppercase" matTooltip="copy invite url"
      (click)="copyInputMessage(userinput2)" value="click to copy">
      <i class="material-icons">content_copy</i>
    </button>
  </div>
  <br>
  <div class="container__item">
    <label>Gate Link</label><br>
    <input style="font-size: larger; line-height: 2em; min-width: 300px;" type="text" class="form__field"
      value={{data.msg.gate}} #userinput3 readonly="readonly">
    <button class="btn btn--primary btn--inside uppercase" matTooltip="copy invite url"
      (click)="copyInputMessage(userinput3)" value="click to copy">
      <i class="material-icons">content_copy</i>
    </button>
  </div>
</div>

<mat-dialog-actions align='center'>
  <div *ngIf="danceEvent">
    <br>
    <button mat-raised-button class="dialogBtn button3 mb-2" matTooltip="click to Download QrCode"
      (click)="ok()">Download QrCode</button>
  </div>
  <div *ngIf="!danceEvent && !templateEvent && !approvalSetting && !alert && !partialPr" style="text-align: center;">
    <button mat-raised-button class="dialogBtn button3 mb-2" (click)="ok()">OK</button>
  </div>
  <div *ngIf="templateEvent && !approvalSetting && !partialPr" class="dialogOkBtn" style="text-align: center;">
    <button mat-raised-button class="dialogBtn button3 mb-2" (click)="ok()">OK</button>
  </div>

  <div style="text-align: center;" *ngIf="approvalSetting">
    <!-- <div *ngIf="approvalSetting" class="dialogOkBtn"> -->
      <button mat-raised-button class="dialogBtn button3 mb-2" (click)="approve()">Approve & Proceed</button>
    <!-- </div> -->

    <!-- <div *ngIf="approvalSetting" class="dialogOkBtn"> -->
      <button mat-raised-button class="dialogBtn button3 mb-2" (click)="reject()">Reject & Proceed</button>
    <!-- </div>   -->
  </div>


  <div style="text-align: center;" *ngIf="partialPr">
    <div style="display: flex; justify-content: center; gap: 20px;">
      <button mat-raised-button class="dialogBtn button3 mb-2" (click)="create()">Create</button>
      <button mat-raised-button class="dialogBtn button3 mb-2" (click)="cancel()">Cancel</button>
    </div>
  </div>

  <div *ngIf="alert" class="dialogOkBtn" style="text-align: center;">
    <button mat-raised-button class="dialogBtn button3 mb-2" (click)="ok()">Inventory List</button>
  </div>

</mat-dialog-actions>