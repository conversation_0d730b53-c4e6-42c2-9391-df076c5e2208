/* Copy paste */
$background: #f5f6fa;
$text: #9c9c9c;
$input-bg-color: #fff;
$input-text-color: #a3a3a3;
$button-bg-color: #7f8ff4;
$button-text-color: #fff;
a {
	color: inherit;
	&:hover {
		color: $button-bg-color;
	}
}

.container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.uppercase {
	text-transform: uppercase;
}

.btn {
	display: inline-block;
	background: transparent;
	color: inherit;
	font: inherit;
	border: 0;
	outline: 0;
	padding: 0;
	transition: all 200ms ease-in;
	cursor: pointer;
	&--primary {
		background: $button-bg-color;
		color: $button-text-color;
		box-shadow: 0 0 10px 2px rgba(0, 0, 0, .1);
		border-radius: 4px;
		padding: 7px 10px 7px 10px;
		height: 40px;
		
		&:hover {
			background: darken($button-bg-color, 4%);
		}
		
		&:active {
			background: $button-bg-color;
			box-shadow: inset 0 0 10px 2px rgba(0, 0, 0, .2);
		}
	}
}

.dialogMsg{
	font-size: large;
	white-space: pre-line;
}

.infoIcon{
	color: orange;
	margin-top: 3px;
}

.mat-dialog-content {
     margin: 0px !important; 
    padding: 15px 24px;
    max-height: 65vh;
    overflow: auto;
}

.mat-dialog-title {
 	margin: 0px; 
}

  .cdk-overlay-pane {
    position: absolute;
    pointer-events: auto;
    box-sizing: border-box;
    z-index: 1000;
    display: flex;
    max-width: 100% !important;
    max-height: 100% !important;
	width: none !important;
}

  .CloseBtn{
	float: right;
	margin-bottom: -1px;
	margin-top: -7px;
  	margin-right: -7px;
  }

.dialogInput{
	width: 80%; 
	margin-top: 10px;
	height: 30px !important;
}