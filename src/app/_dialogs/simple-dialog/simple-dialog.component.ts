import { Component, OnInit, Input, Inject} from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA} from  '@angular/material';
import { AuthService, PurchasesService } from '../../_services/'
import { NotificationService } from '../../_services/notification.service'
import { UtilsService } from 'src/app/_utils/utils.service';
@Component({
  selector: 'app-simple-dialog',
  templateUrl: './simple-dialog.component.html',
  styleUrls: ['./simple-dialog.component.scss']
})
export class SimpleDialogComponent implements OnInit {
   InvNo : any;
   danceEvent =false;
   objectKeys : any;
   askForInputFlag : boolean = false;
   askForInput : boolean = false;
   showDupAlert : boolean = false
   templateEvent : boolean = false;
   approvalSetting : boolean = false;
   alert : boolean = false;
  @Input() opts : any;
  cashier= "http://rms-qa.digitory.com/main?eventId=U2FsdGVkX19z3o4TNLmGJwlAnFkC7AwuDlLFSNceT3ke1Q2u3A4l&cashier=yes"
  partialPr: boolean;
constructor(
  private notifyService : NotificationService,
  private  dialogRef:  MatDialogRef<SimpleDialogComponent>,
  private purchases: PurchasesService, private auth : AuthService,
  private utils: UtilsService,
   @Inject(MAT_DIALOG_DATA) public  data:  any) { }

  ngOnInit() {        
    this.objectKeys = Object.keys;
    if (this.data.title == 'Invite Window'){
      this.danceEvent =true;
    }else{
      this.danceEvent =false;
    }
    this.alert = (this.data.title === 'Alert') ? true : false;
    this.approvalSetting = (this.data.from && this.data.from === 'approvalSetting') ? true : false ;
    this.partialPr = (this.data.from && this.data.from === 'partialPr') ? true : false ;
    if(Object.keys(this.data).includes('inputFromUser')){
      this.templateEvent = true;
      this.askForInputFlag = true;
      this.checkDupTmpName(this.data.inputFromUser["Template Name"])
    }
    if(Object.keys(this.data).includes('inputUser')){
      this.askForInput = true;
    }
  }

  checkDupTmpName(newTmpName){
    if(Object.keys(this.data.inputFromUser).includes('Template Name')){
      let params : any = {
        tenantId : this.auth.getCurrentUser().tenantId,
        rId : this.data.rId,
        tempName : this.data.inputFromUser['Template Name']
      }

      this.purchases.checkDupTmpName(params).subscribe(res => {
        this.showDupAlert = res? true :false;
      }
        , err => console.error(err))
    }
  }

public close() {
  const result = {
    inputFromUser: this.data.inputFromUser,
    inputUser: this.data.inputUser
  };
  this.dialogRef.close(result); 
}


public ok(){
  if(!this.showDupAlert){
    this.close();
    this.data.ok();
  }
}

public approve(){
  if(!this.showDupAlert){
    this.close();
    this.data.approve();
  }
}

public reject(){
  if(!this.showDupAlert){
    this.close();
    this.data.reject();
  }
}


public create() {
  if (!this.showDupAlert) {
    this.close();
    this.data.create();
  }
}

public cancel() {
  if (!this.showDupAlert) {
    this.close();
    this.data.cancel();
  }
}
closeDialog(){
  this.close();
}

copyInputMessage(inputElement){
  inputElement.select();
  document.execCommand('copy');
  inputElement.setSelectionRange(0, 0);
  this.utils.snackBarShowSuccess("Invite copied !!")
}

}
