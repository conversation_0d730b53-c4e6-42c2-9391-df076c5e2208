import { Injectable } from '@angular/core';
import { Router, CanActivate } from '@angular/router';
@Injectable({ providedIn: 'root' })
export class LoginGuard implements CanActivate {
    constructor(
        private router: Router
    ) { }

    canActivate() {
        const currentUser: any = JSON.parse(sessionStorage.getItem('user'));
        if (currentUser && currentUser.token) {
            this.router.navigate(['/home']);
            return false;
        }
        return true;
    }
}
