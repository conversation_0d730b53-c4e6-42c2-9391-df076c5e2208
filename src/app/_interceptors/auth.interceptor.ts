import { <PERSON>ttp<PERSON><PERSON>, <PERSON>ttp<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest, HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { AuthService } from '../_services/auth.service';
import { ErrorDialogService } from '../_services/error-dialog.service';
import { Router } from '@angular/router';
import { UtilsService } from '../_utils/utils.service';
import { NotificationService } from '../_services/notification.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  constructor(
    private auth: AuthService,
    private router: Router,
    private errorDialogService: ErrorDialogService,
    private utils: UtilsService,
    private notifyService: NotificationService
  ) { }

  intercept(req: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<any>> {
    if (this.auth.getCurrentUser()) {
      req = req.clone({
        setHeaders: {
          'Content-Type': 'application/json; charset=utf-8',
          'Accept': 'application/json',
          'Authorization': `${this.auth.getCurrentUser().token}`,
        },
      });
    }

    return next.handle(req).pipe(
      map(response => {
        let res: any = response;
        if (res.body && res.body.newObj) {
          if (Object.keys(res.body.newObj.obj).length === 0) {
            if (Object.keys(res.body.newObj).length === 0) {
              throwError({});
            }
          }
        }
        return res;
      }),
      catchError((error: HttpErrorResponse) => {
        let err: any = {};

        // Client Side Error
        if (error.error instanceof ErrorEvent) {
          err.msg = error.error.message;
          err.status = 'Client Side Error';
        } else {
          err.status = error.status;

          // ✅ Detect offline error
          if (err.status === 0 && !navigator.onLine) {
            err.msg = 'No internet. Please check your connection and try again.';
          } else {
            switch (err.status) {
              case 400:
                err.msg = 'No data found for the current selections';
                break;
              case 401:
                err.msg = 'Your session has expired. Please Login again'
                this.auth.logout();
                this.router.navigate(['/login']);
                break;
              case 404:
                err.msg = 'Invalid credentials';
                this.auth.logout();
                this.router.navigate(['/login']);
                break;
              case 500:
                err.msg = 'We seem to be having trouble right now. Please try again after sometime';
                break;
              case 409:
                err.msg = 'An internal error has occurred. Please report this issue to customer support for resolution ';
                break;
              case 0:
                err.msg = 'An internal error has occurred. Please report this issue to customer support for resolution ';
                break;
              case undefined:
                err.msg = 'No data found for the current selections';
                break;
              default:
                err.msg = 'Internal error';
            }
          }
        }

        this.utils.snackBarShowError(err.msg);
        return throwError(err);
      })
    );
  }
}
