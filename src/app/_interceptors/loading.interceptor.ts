import { Injectable } from "@angular/core";
import { <PERSON>ttp<PERSON><PERSON>, <PERSON>ttp<PERSON><PERSON><PERSON>, <PERSON>ttpInterceptor, HttpRequest } from "@angular/common/http";
import { Observable } from "rxjs";
import { LoadingScreenService } from "../_services/";
import { finalize } from "rxjs/operators";

@Injectable()
export class LoadingScreenInterceptor implements HttpInterceptor {
  activeRequests: number = 0;
  excludedEnpoint=[
    "/generateTemplateJob","/uploadClosingData","//getJobs","/fetchPriceList","/reportStatus", 
    "/retrieveTemplate" , "//downloadTemplates" , "/forcastStatusTable" ,"/getBranchInv" , 
    "//checkExportStatus" ,"//checkImportStatus","/retrieveInvoice" , "/retrievePoSetting" ,
    "/retrieveGrnSetting" , "/retrievePrSetting ", "/removeDraftDataBulK", "/permissionCheck" ,
    "/filterJobsData", "/getInvoice","/getLastGrnPrice","/retrieveUpdates","//retrieveUpdates",'/masterDataUpdateConfig',
    '/getSystemErrorLog'
  ]
  
  constructor(private loadingScreenService: LoadingScreenService) {
  }

  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    var pathname = new URL(request['url']).pathname;
    if (this.excludedEnpoint.includes(pathname)) {
      return next.handle(request);
    } else {
      if (this.activeRequests === 0) {
        this.loadingScreenService.startLoading();
      }
      this.activeRequests++;
      return next.handle(request).pipe(
        finalize(() => {
          this.activeRequests--;
          if (this.activeRequests === 0) {
            this.loadingScreenService.stopLoading();
          }
        })
      )
    }
  
  };
}