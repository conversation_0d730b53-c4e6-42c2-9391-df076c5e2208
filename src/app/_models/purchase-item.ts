import {Brand} from './brand'
export class PurchaseItem {
  mId? : string;
  onHand? : number;
  optStock? : number;
  openOrders? : number;
  itemName? : string;
  openToBuy? : number;
  leadTime? : number;
  orderQty? : number;
  receivedQty? : number;
  reqQty? : number;
  unitPrice? : number;
  totalValue? : number;
  unit? : string;
  itemCode? : string;
  customer? : any;
  vendor? : any;
  supplyDate? : string;
  deliverableQty?: number;
  status? : string;
  brand? : Brand;
  brands? :Brand[];
  quotedUnitPrice? : number;
  pkgName? : String;
  totalPrice? : number;
  taxRate? : number;
  approvalDetail? : any;
  packageName? : String;
  description? : String;
  packages?: any;
  subTotal?: number;
  taxAmount?: number;
  cessAmt?: number;
  extraAmt?: number;
  discAmt?: number;
  quantity?: any;
  totalExcTax?: number;
  
}
