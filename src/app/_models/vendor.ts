import {PurchaseItem} from './purchase-item';
import {Address} from './address'
export class Vendor {
  id? : string;
  name : string;
  inventoryItems? : PurchaseItem[];
  orderMethods? : any [];
  gstin? : string;
  pan? : string;
  email? : string;
  emailVerified? : boolean;
  password ?: string;
  locations? : Address[];
  openOrders? : any[];
  closedOrders? : any[];
  contactPerson? : any[];
  status? : any;
  statusHistory? : any[];
  items? : any[]
  address? : Address;
  mobile? : string;
  landline? : string;
  tenantId? :string
  vendorId: any;
}
