import { Injectable } from '@angular/core';
import { GlobalsService } from '../_services/globals.service';
import { environment } from '../../environments/environment';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject } from 'rxjs';
import { map } from 'rxjs/operators';
import { User } from '../_models';
import { TimeOutService } from './time-out.service';
@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject = new BehaviorSubject<any>(JSON.parse(sessionStorage.getItem(GlobalsService.user)));
  private currentUser = this.currentUserSubject.asObservable();
  private branchesSubject;
  private branches;
  private restaurantSubject = new BehaviorSubject<any>(null);
  private currRestaurant = this.restaurantSubject.asObservable();
  private loggedIn$ = new BehaviorSubject<boolean>(false);

  baseUrl: string = environment.baseUrl;
  constructor(private http: HttpClient ) {
  }
  getCurrentUser() {
    return this.currentUserSubject.value;
  }
  getBranchesValue() {
    return this.branchesSubject.value;
  }

  getCurrRest() {
    return this.restaurantSubject.value;
  }

  setCurrRest(val) {
    this.restaurantSubject.next(val)
  }


  login(reqObj: any) {
    return this.http.post(`${this.baseUrl}authenticate/`, reqObj).pipe(map((res: any) => {
      if (res.result === GlobalsService.success) {
        let user = new User();
        user = res.uRecord;
        if (res.auth_token) {
          user.token = res.auth_token
        }
        sessionStorage.setItem(GlobalsService.user, JSON.stringify(user));
        sessionStorage.setItem('tanetId', user.tenantId)
        this.currentUserSubject.next(user);
        this.loggedIn$.next(true);

        return true;
      }
      return false;
    }));
  }

  getBranches(obj) {
    return this.http.post(`${this.baseUrl}getBranches/`, obj).pipe(map((res: any) => {
      if (res.result && res.result === 'success') {
        sessionStorage.setItem(GlobalsService.branches, JSON.stringify(res.branches));
        this.branches = res.branches;
        return res.branches.sort((a, b) => a.branchName > b.branchName)
      }
      return false;
    }));
  }

  getDbRange(obj) {
    return this.http.get(`${this.baseUrl}getDbDateRange/${obj.tId}`).pipe(map(res => {
      return res;
    }))
  }

  logout() {
    sessionStorage.removeItem(GlobalsService.user);
    this.currentUserSubject.next(null);
    this.loggedIn$.next(false);
  }

  dummyLogin(reqObj) {
    if (reqObj.email === GlobalsService.purchaseController) {
      let user = new User();
      user = { "name": "PurchaseController", "tenantId": "100001", "mobile": null, "restaurantId": null, "company": null, "companyId": null, "email": "<EMAIL>", "mId": "5ca5c6d2e4c04c216311a722", "role": ["purchaseController"], "type": null, "restaurantAccess": [{ "branchName": "indiranagar", "branchLocation": "Indiranagar", "restaurantIdOld": "100001@indiranagar", "restaurantId": "900001" }], "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjp7IiRvaWQiOiI1Y2E1YzZkMmU0YzA0YzIxNjMxMWE3MjIifSwiZXhwIjoxNTU1NDExNDY2fQ.Fh96WAaFXTjt0AgSl5ZN5OHbQP2i7lzUQthwFPk6w2o" };
      sessionStorage.setItem(GlobalsService.user, JSON.stringify(user));
      this.currentUserSubject.next(user);
    }
    else {
      let user = new User();
      user = { "name": "Vendor", "tenantId": "100001", "mobile": null, "restaurantId": null, "company": null, "companyId": null, "email": "<EMAIL>", "mId": "5ca5c6d2e4c04c216311a722", "role": ["vendor"], "type": null, "restaurantAccess": [{ "branchName": "indiranagar", "branchLocation": "Indiranagar", "restaurantIdOld": "100001@indiranagar", "restaurantId": "900001" }], "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjp7IiRvaWQiOiI1Y2E1YzZkMmU0YzA0YzIxNjMxMWE3MjIifSwiZXhwIjoxNTU1NDExNDY2fQ.Fh96WAaFXTjt0AgSl5ZN5OHbQP2i7lzUQthwFPk6w2o" };
      sessionStorage.setItem(GlobalsService.user, JSON.stringify(user));
      this.currentUserSubject.next(user);
    }
    return true;

  }

  getUsersList(obj) {
    return this.http.post(`${this.baseUrl}getUsersList/`, obj).pipe(map((res: any) => {
      return res
    }));
  }

  getBranchesForDirectIndent(obj) {
    return this.http.post(`${this.baseUrl}getBranchesforDirectIndent/`, obj).pipe(map((res: any) => {
      return res
    }));
  }

  createNewUser(obj) {
    return this.http.post(`${this.baseUrl}createNewUser/`, obj).pipe(map((res: any) => {
      return res
    }))
  }
  
  createNewTenantId(obj) {
    return this.http.post(`${this.baseUrl}createNewTenantId/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  getRolesList(obj) {
    return this.http.post(`${this.baseUrl}getRolesList/`, obj).pipe(map((res: any) => {
      return res
    }));
  }

  createRole(obj) {
    return this.http.post(`${this.baseUrl}createRole/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  updateUser(obj) {
    return this.http.post(`${this.baseUrl}updateUser/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  updateRole(obj) {
    return this.http.post(`${this.baseUrl}updateRole/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  getUserNewTenantId(obj) {
    return this.http.post(`${this.baseUrl}getUserNewTenantId/`, obj).pipe(map((res: any) => {
      return res
    }))
  }
  updateUserDetails(obj) {
    return this.http.post(`${this.baseUrl}updateUserDetails/`, obj).pipe(map((res: any) => {
      return res
    }))
  }
  
  getTenantUserDetails(obj) {
    return this.http.post(`${this.baseUrl}getTenantUserDetails/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

}
