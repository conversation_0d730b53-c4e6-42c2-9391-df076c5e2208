import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from './auth.service';
import { PurchasesService } from './purchases.service';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class AutoGrnService {
  pr: any;
  user: any;
  constructor(private auth: AuthService,
    private router: Router,
    private purchases: PurchasesService) {
    this.user = this.auth.getCurrentUser();
  }
  autoGrn(prId) {
    var p1 = new Promise((resolve, _reject) => {
      this.purchases.getPrById({ 'prId': prId }).subscribe(data1 => {
        resolve(data1)
      })
    })
    p1.then((data1) => {
      let object = {
        pr: data1,
        userMId: this.user.mId,
        uType: this.user.uType,
        baseUrl: environment.baseUrl,
        userEmail:this.auth.getCurrentUser().email
      };
      this.purchases.createPo(object).subscribe(data => {
        this.router.navigate(['/home/<USER>', { poNumber: data.poId }]);
      }, err => console.error(err));
    }).catch((err) => {
      console.log(err)
    })
  }

}
