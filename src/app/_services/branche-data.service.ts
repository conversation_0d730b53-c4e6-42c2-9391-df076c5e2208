import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs/operators';


@Injectable({
  providedIn: 'root'
})
export class BrancheDataService {
  baseUrl: string = environment.baseUrl;

  constructor(private http: HttpClient) { }

  getDataBranches(obj) {
    return this.http.post(`${this.baseUrl}getDmBranches`, obj).pipe(map((res: any) =>
      res
    ))
  }

  createDataBranches(obj) {
    return this.http.post(`${this.baseUrl}createDmBranches`, obj).pipe(map((res: any) =>
      res
    ))
  }

  updateDataBranches(obj) {
    return this.http.post(`${this.baseUrl}updateDmBranches`, obj).pipe(map((res: any) =>
      res
    ))
  }

  deleteDataBranches(obj) {
    return this.http.post(`${this.baseUrl}deleteDmBranches`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getBranchesDataList(obj) {
    return this.http.post(`${this.baseUrl}getBranchesDataList`, obj).pipe(map((res: any) =>
      res
    ))
  }
  getRowDataDmBranches(obj) {
    return this.http.post(`${this.baseUrl}getRowDataDmBranches`, obj).pipe(map((res: any) =>
      res
    ))
  }
  getDmDeletedBranch(obj) {
    return this.http.post(`${this.baseUrl}getDmDeletedBranch`, obj).pipe(map((res: any) =>
      res
    ))
  }

  generateBranchesTemplateJob(obj) {
    return this.http.post(`${this.baseUrl}generateBranchesTemplateJob`, obj).pipe(map((res: any) =>
      res
    ))
  }

  retrieveBranchesTemplate(obj) {
    return this.http.post(`${this.baseUrl}retrieveBranchesTemplate`, obj).pipe(map((res: any) =>
      res
    ))
  }

  revertBranchDeletedData(obj) {
    return this.http.post(`${this.baseUrl}revertBranchDeletedData`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getBranchTallyData(obj) {
    return this.http.post(`${this.baseUrl}getBranchTallyData`, obj).pipe(map((res: any) =>
      res
    ))
  }

  updateBranchTallyData(obj) {
    return this.http.post(`${this.baseUrl}updateBranchTallyData`, obj).pipe(map((res: any) =>
      res
    ))
  }
}




