import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs/operators';



@Injectable({
  providedIn: 'root'
})
export class CategoryService {
  baseUrl: string = environment.baseUrl;

  constructor(private http: HttpClient) { }

  getDmMasterCategory(obj) {
    return this.http.post(`${this.baseUrl}getDmMasterCategory`, obj).pipe(map((res: any) =>
      res
    ))
  }
  createDmMasterCategory(obj) {
    return this.http.post(`${this.baseUrl}createDmMasterCategory`, obj).pipe(map((res: any) =>
      res
    ))
  }
  updateDmMasterCategory(obj) {
    return this.http.post(`${this.baseUrl}updateDmMasterCategory`, obj).pipe(map((res: any) =>
      res
    ))
  }

  deleteDmMasterCategory(obj) {
    return this.http.post(`${this.baseUrl}deleteDmMasterCategory`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getMasterCategoryDataList(obj) {
    return this.http.post(`${this.baseUrl}getMasterCategoryDataList`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getRowMasterCategory(obj) {
    return this.http.post(`${this.baseUrl}getRowMasterCategory`, obj).pipe(map((res: any) =>
      res
    ))
  }

  generateCategoryTemplateJob(obj) {
    return this.http.post(`${this.baseUrl}generateCategoryTemplateJob`, obj).pipe(map((res: any) =>
      res
    ))
  }

  retrieveCategoryTemplate(obj) {
    return this.http.post(`${this.baseUrl}retrieveCategoryTemplate`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getDmDeletedCategory(obj) {
    return this.http.post(`${this.baseUrl}getDmDeletedCategory`, obj).pipe(map((res: any) =>
      res
    ))
  }

  revertCategoryDeletedData(obj) {
    return this.http.post(`${this.baseUrl}revertCategoryDeletedData`, obj).pipe(map((res: any) =>
      res
    ))
  }
  
}
