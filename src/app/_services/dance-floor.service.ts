import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class DanceFloorService {
  baseUrl: string = environment.baseUrl;
  constructor(private http: HttpClient) { }
  
  uploadImage(obj) {
    return this.http.post(`${this.baseUrl}uploadImage`, obj).pipe(map((res: any) => res))
  }

  launchEvent(obj) {
    return this.http.post(`${this.baseUrl}launchEvent`, obj).pipe(map((res: any) => res))
  }
  
  getEventsList(obj) {
    return this.http.post(`${this.baseUrl}getEventsList`, obj).pipe(map((res: any) => res))
  }

  createQrCode(obj) {
    return this.http.post(`${this.baseUrl}createQrCode`, obj).pipe(map((res: any) => res))
  }
  
  downloadQrCode(obj) {
    return this.http.post(`${this.baseUrl}downloadQrCode`, obj).pipe(map((res: any) => res))
  }
  
  
  
}
