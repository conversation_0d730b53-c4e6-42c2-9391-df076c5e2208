import { Injectable } from '@angular/core';
import * as moment from 'moment';
@Injectable({
  providedIn: 'root'
})
export class DateUtilsService {

  constructor() { }

  getFormattedDate  (date : any){

      date  = `${date.getFullYear()}-${date.getMonth()+1}-${date.getDate()}`;

    return date;
  }

  getDateParam(date){
    let obj : any = {};
    if(!date.hasOwnProperty('startDate'))
    {
      obj.startDate = obj.endDate= this.getFormattedDate(date);
    }
    else
    {
      obj.startDate = moment(date.startDate).format('YYYY-MM-DD');
      obj.endDate = moment(date.endDate).format('YYYY-MM-DD');
    }

    return obj;
  }
}
