import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class HistoryDataServiceService {
  baseUrl: string = environment.baseUrl;

  constructor(private http: HttpClient) { }

  getInventoryModifiedData(obj) {
    return this.http.post(`${this.baseUrl}getInventoryModifiedData`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getPackagingModifiedData(obj) {
    return this.http.post(`${this.baseUrl}getPackagingModifiedData`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getVendorModifiedData(obj) {
    return this.http.post(`${this.baseUrl}getVendorModifiedData`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getServingSizeConversionModifiedData(obj) {
    return this.http.post(`${this.baseUrl}getServingSizeConversionModifiedData`, obj).pipe(map((res: any) =>
      res
    ))
  }
  getSubrecipeMasterModifiedData(obj) {
    return this.http.post(`${this.baseUrl}getSubrecipeMasterModifiedData`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getMenuMasterModifiedData(obj) {
    return this.http.post(`${this.baseUrl}getMenuMasterModifiedData`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getMenuWorkMappingModifiedData(obj) {
    return this.http.post(`${this.baseUrl}getMenuWorkMappingModifiedData`, obj).pipe(map((res: any) =>
      res
    ))
  }
  getSubrecipeRecipesModifiedData(obj) {
    return this.http.post(`${this.baseUrl}getSubrecipeRecipesModifiedData`, obj).pipe(map((res: any) =>
      res
    ))
  }
  getMenuRecipeModifiedData(obj) {
    return this.http.post(`${this.baseUrl}getMenuRecipeModifiedData`, obj).pipe(map((res: any) =>
      res
    ))
  }
  getMasterWorkareaModifiedData(obj) {
    return this.http.post(`${this.baseUrl}getMasterWorkareaModifiedData`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getRoleModifiedData(obj) {
    return this.http.post(`${this.baseUrl}getRoleModifiedData`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getBranchesModifiedData(obj) {
    return this.http.post(`${this.baseUrl}getBranchesModifiedData`, obj).pipe(map((res: any) =>
      res
    ))
  }
  getUsersModifiedData(obj) {
    return this.http.post(`${this.baseUrl}getUsersModifiedData`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getAuditLogData(obj) {
    return this.http.post(`${this.baseUrl}getAuditLogData`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getpublishScenarioData(obj) {
    return this.http.post(`${this.baseUrl}getpublishScenarioData`, obj).pipe(map((res: any) =>
      res
    ))
  }

  publishScenario(obj) {
    return this.http.post(`${this.baseUrl}publishScenario`, obj).pipe(map((res: any) =>
      res
    ))
  }

  publishLogUserAction(obj) {
    return this.http.post(`${this.baseUrl}publishLogUserAction`, obj).pipe(map((res: any) =>
      res
    ))
  }

}
