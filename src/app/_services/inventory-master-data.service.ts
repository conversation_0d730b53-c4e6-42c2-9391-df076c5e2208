import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class InventoryMasterDataService {
  baseUrl: string = environment.baseUrl;
  constructor(private http: HttpClient) { }

  masterDataUpdateConfig(obj) {
    return this.http.post(`${this.baseUrl}getInventoryMaster`, obj).pipe(map((res: any) =>
      res
    ))
  }

  createDataInventory(obj) {
    return this.http.post(`${this.baseUrl}createInventoryMaster`, obj).pipe(map((res: any) =>
      res
    ))
  }

  updateDataInventory(obj) {
    return this.http.post(`${this.baseUrl}updateInventoryMaster`, obj).pipe(map((res: any) =>
      res
    ))
  }

  deleteDataInventory(obj) {
    return this.http.post(`${this.baseUrl}deleteInventoryMaster`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getInventoryDataList(obj) {
    return this.http.post(`${this.baseUrl}getInventoryDataList`, obj).pipe(map((res: any) =>
      res
    ))
  }
  getRowDataInventoryMaster(obj) {
    return this.http.post(`${this.baseUrl}getRowDataInventoryMaster`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getInventoryForTenantId(obj) {
    return this.http.post(`${this.baseUrl}getInventoryForTenantId`, obj).pipe(map((res: any) =>
      res
    ))
  }

  generateInventoryTemplateJob(reqObj: any) {
    return this.http.post(`${this.baseUrl}generateInventoryTemplateJob/`, reqObj).pipe(map((res: any) => {
      return res;
    }));
  }

  retrieveInventoryTemplate(reqObj: any) {
    return this.http.post(`${this.baseUrl}retrieveInventoryTemplate/`, reqObj).pipe(map((res: any) => {
      return res;
    }));
  }

  getDmDeletedInventoryMaster(obj) {
    return this.http.post(`${this.baseUrl}getDmDeletedInventoryMaster`, obj).pipe(map((res: any) =>
      res
    ))
  }

  revertInventoryDeletedData(obj) {
    return this.http.post(`${this.baseUrl}revertInventoryDeletedData`, obj).pipe(map((res: any) =>
      res
    ))
  }

}
