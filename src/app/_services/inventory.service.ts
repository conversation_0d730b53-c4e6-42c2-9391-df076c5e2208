import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class InventoryService {
  baseUrl: string = environment.baseUrl;

  constructor(private http: HttpClient) { }

  getActiveUser(obj) {
    return this.http.post(`${this.baseUrl}getActiveUser`, obj).pipe(map((res: any) => res))
  }
  
  updateClosingStock(obj) {
    return this.http.post(`${this.baseUrl}updateClosingStock`, obj).pipe(map((res: any) => res))
  }  
  adjustInventory(obj) {
    return this.http.post(`${this.baseUrl}adjustInventory`, obj).pipe(map((res: any) => res))
  }

  saveTempClosing(obj) {
    return this.http.post(`${this.baseUrl}saveTempClosing`, obj).pipe(map((res: any) => res))
  }

  inactiveTempData(obj) {
    return this.http.post(`${this.baseUrl}inactiveTempData`, obj).pipe(map((res: any) => res))
  }

  saveSubCat(obj) {
    return this.http.post(`${this.baseUrl}saveSubCat`, obj).pipe(map((res: any) => res))
  }

  saveUserSubCat(obj) {
    return this.http.post(`${this.baseUrl}saveUserSubCat`, obj).pipe(map((res: any) => res))
  }

  fetchClosingData(obj) {
    return this.http.post(`${this.baseUrl}fetchClosingData`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  saveClosingItem(obj) {
    return this.http.post(`${this.baseUrl}saveClosingItem`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  cancelSubCat(obj) {
    return this.http.post(`${this.baseUrl}cancelSubCat`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  updateItemsClosing(obj) {
    return this.http.post(`${this.baseUrl}updateItemsClosing`, obj).pipe(map((res: any) => {
      return res
    }))
  }

}
