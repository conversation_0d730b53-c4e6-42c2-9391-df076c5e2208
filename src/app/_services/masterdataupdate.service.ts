import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs/operators';
@Injectable({
  providedIn: 'root'
})
export class MasterdataupdateService {
  baseUrl: string = environment.baseUrl;
  constructor(private http: HttpClient) { }

  
  forcastStatusTable(obj){
    return this.http.post(`${this.baseUrl}forcastStatusTable`, obj).pipe(map((res: any) =>
    res 
  ))
  }
  
  createPriceList(obj){
    return this.http.post(`${this.baseUrl}/createPriceList`, obj).pipe(map((res: any) =>
    res 
  ))
  }

  createTaxStructure(obj){
    return this.http.post(`${this.baseUrl}/createTaxStructure`, obj).pipe(map((res: any) =>
    res 
  ))
  }

  getTaxStructure(params){
    return this.http.get(`${this.baseUrl}/getTaxStructure`, { params: params }).pipe(map((res: any) =>
    res 
  ))
  }

  getRoles(params) {
    return this.http.get(`${this.baseUrl}/getRoles`, { params: params }).pipe(map((res: any) =>
    res 
  ))  
}

  updateAccess(obj) {
    return this.http.post(`${this.baseUrl}updateAccess/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  masterDataIdAccess(obj) {
    return this.http.post(`${this.baseUrl}masterDataIdAccess/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  updateTaxStructure(obj){
    return this.http.post(`${this.baseUrl}/updateTaxStructure`, obj).pipe(map((res: any) =>
    res 
  ))
  }

  deleteTaxStructure(obj){
    return this.http.post(`${this.baseUrl}/deleteTaxStructure`, obj).pipe(map((res: any) =>
    res 
  ))
  }

  getTaxData(params){
    return this.http.get(`${this.baseUrl}/getTaxData`, { params: params }).pipe(map((res: any) =>
    res 
  ))
  }

  // getContractDateByVendor(obj){
  //   return this.http.post(`${this.baseUrl}/getContractDateByVendor`, obj).pipe(map((res: any) =>
  //   res 
  // ))
  // }

  getContractStartDateByVendor(obj){
    return this.http.post(`${this.baseUrl}/getContractStartDateByVendor`, obj).pipe(map((res: any) =>
    res 
  ))
  }

  getContractEndDateByVendor(obj){
    return this.http.post(`${this.baseUrl}/getContractEndDateByVendor`, obj).pipe(map((res: any) =>
    res 
  ))
  }

  createScheduler(obj){
    return this.http.post(`${this.baseUrl}/createScheduler`, obj).pipe(map((res: any) =>
    res 
  ))
  }

  fetchScheduler(obj){
    return this.http.post(`${this.baseUrl}/fetchScheduler`, obj).pipe(map((res: any) =>
    res 
  ))
  }

  updatePl(obj){
    return this.http.post(`${this.baseUrl}/updatePl`, obj).pipe(map((res: any) =>
    res 
  ))
  }

  updateContract(obj){
    return this.http.post(`${this.baseUrl}/updateContract`, obj).pipe(map((res: any) =>
    res 
  ))
  }
  deletePl(obj){
    return this.http.post(`${this.baseUrl}/deletePl`, obj).pipe(map((res: any) =>
    res 
  ))
  }

  createVendor(obj){
    return this.http.post(`${this.baseUrl}/createVendor`, obj).pipe(map((res: any) =>
    res 
  ))
  }

  // setkeysInPriceList(obj){
  //   return this.http.get(`${this.baseUrl}/setkeysInPriceList`, obj).pipe(map((res: any) =>
  //   res 
  // ))
  // }
  
  fetchPriceList(params){
    return this.http.get(`${this.baseUrl}/fetchPriceList`, { params: params }).pipe(map((res: any) =>
    res 
  ))
  }

  filterByVendor(params){
    return this.http.get(`${this.baseUrl}/filterByVendor`, { params: params }).pipe(map((res: any) =>
    res 
  ))
  }

  getVendorList(params){
    return this.http.get(`${this.baseUrl}/getVendorList`, { params: params }).pipe(map((res: any) =>
    res 
  ))
  }

  checkExportAndImportStatus(params){
    return this.http.get(`${this.baseUrl}/checkExportAndImportStatus`, { params: params }).pipe(map((res: any) =>
    res 
  ))
  }

  checkVendorContractStatus(params){
    return this.http.get(`${this.baseUrl}/checkVendorContractStatus`, { params: params }).pipe(map((res: any) =>
    res 
  ))
  }

  checkImportStatus(params){
    return this.http.get(`${this.baseUrl}/checkImportStatus`, { params: params }).pipe(map((res: any) =>
    res 
  ))
  }

  checkExportStatus(params){
    return this.http.get(`${this.baseUrl}/checkExportStatus`, { params: params }).pipe(map((res: any) =>
    res 
  ))
  }
  
  generatePriceList(params){
    return this.http.get(`${this.baseUrl}/generatePriceList`, { params: params }).pipe(map((res: any) =>
    res 
  ))
  }

  storePriceList(obj){
    return this.http.post(`${this.baseUrl}/updatePriceListData`, obj).pipe(map((res: any) =>
    res 
  ))
  }

  deleteItems(obj){
    return this.http.post(`${this.baseUrl}/deleteItems`, obj).pipe(map((res: any) =>
    res 
  ))
  }

  restoreItems(obj){
    return this.http.post(`${this.baseUrl}/restoreItems`, obj).pipe(map((res: any) =>
    res 
  ))
  }


  fetchDetailedPriceList(obj){
    return this.http.post(`${this.baseUrl}/fetchDetailedPriceList`, obj).pipe(map((res: any) =>
    res 
  ))
  }
  
  getVendorByid(params){
    return this.http.get(`${this.baseUrl}/getVendorByid`, { params: params }).pipe(map((res: any) =>
    res 
  ))
  }
  
  updatePriceList(obj){
    return this.http.post(`${this.baseUrl}/updatePriceList`, obj).pipe(map((res: any) =>
    res 
  ))
  }

  downloadPriceList(obj){
    return this.http.post(`${this.baseUrl}/downloadPriceList`, obj).pipe(map((res: any) =>
    res 
  ))
  }

  generateVendorContract(obj){
    return this.http.post(`${this.baseUrl}/generateVendorContract`, obj).pipe(map((res: any) =>
    res 
  ))
  }

  export(obj){
    return this.http.post(`${this.baseUrl}/export`, obj).pipe(map((res: any) =>
    res 
  ))
  }

  retrievePlTemplate(reqObj : any){
    return this.http.post(`${this.baseUrl}retrievePlTemplate/`,reqObj).pipe(map((res : any) => {
      return res;
    }));
  }

  processPriceList(reqObj : any){
    return this.http.post(`${this.baseUrl}processPriceList/`,reqObj).pipe(map((res : any) => {
      return res;
    }));
  }

  processVendorContract(reqObj : any){
    return this.http.post(`${this.baseUrl}processVendorContract/`,reqObj).pipe(map((res : any) => {
      return res;
    }));
  }


  generateQr(inputObj){
    return this.http.post(`${this.baseUrl}/launch`, inputObj).pipe(map((res: any) =>
    res 
  ))
  }

  downloadQr(obj){
    return this.http.post(`${this.baseUrl}/download`, obj).pipe(map((res: any) =>
    res 
  ))
  }

  fetchQrList(obj){
    return this.http.post(`${this.baseUrl}/fetch`, obj).pipe(map((res: any) =>
    res 
  ))
  }

  masterDataUpdateConfig(obj) {
    return this.http.post(`${this.baseUrl}masterDataUpdateConfig`, obj).pipe(map((res: any) =>
      res 
    ))
  }

  retrieveUpdates(obj) {
    return this.http.post(`${this.baseUrl}retrieveUpdates`, obj).pipe(map((res: any) =>
      res 
    ))
  }

  getJobs(obj){
    return this.http.post(`${this.baseUrl}/getJobs`, obj).pipe(map((res: any) =>
      res 
    ))
  }

  closingReports(obj){
    return this.http.post(`${this.baseUrl}/closingReports`, obj).pipe(map((res: any) =>
      res 
    ))
  }

  getJobsEvent(obj){
    return this.http.post(`${this.baseUrl}/getJobsEvent`, obj).pipe(map((res: any) =>
      res 
    ))
  }

  downloadTemplates(obj){
    return this.http.post(`${this.baseUrl}/downloadTemplates`, obj).pipe(map((res: any) =>
      res 
    ))
  }

  getFileFormatForClosing(obj){
    return this.http.post(`${this.baseUrl}/getFileFormatForClosing`, obj).pipe(map((res: any) =>
      res 
    ))
  }

  getErrorLog(obj) {
    return this.http.post(`${this.baseUrl}getErrorLog`, obj).pipe(map((res: any) =>
      res 
    ))
  }
  getSystemErrorLog(obj) {
    return this.http.post(`${this.baseUrl}/getSystemErrorLog`, obj).pipe(map((res: any) =>
      res 
    ))
  }

  createUpdateJob(obj) {
    return this.http.post(`${this.baseUrl}createUpdateJob`, obj).pipe(map((res: any) =>
      res 
    ))
  }

  checkSystemLog(obj) {
    return this.http.post(`${this.baseUrl}/checkSystemLog`, obj).pipe(map((res: any) =>
      res 
    ))
  }
  
  getCategories(params){
    return this.http.get(`${this.baseUrl}/getCategories`, { params: params }).pipe(map((res: any) =>
    res 
    ))
  }

  // getCategories(obj){
  //   return this.http.get(`${this.baseUrl}getCategories/`,obj).pipe(map((res : any) => {
  //     return res;
  //   }));
  // }

}

