import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../environments/environment';
import { map } from 'rxjs/operators';
import { BehaviorSubject } from 'rxjs';
@Injectable({
  providedIn: 'root'
})
export class MenuItemService {
  public notificationDta : BehaviorSubject<any[]> = new BehaviorSubject([]);
  baseUrl : string;
  constructor(private http: HttpClient) {
    this.baseUrl = environment.baseUrl;
 }

 reportStatus(params) {
  return this.http.get(`${this.baseUrl}reportStatus`, { params: params }).pipe(map((res: any) => {
      return res
  }))
}

changePassword(obj){
  return this.http.post(`${this.baseUrl}updateUser/`,obj).pipe(map((res : any) => {
    return res;
  }));
}

filterJobsData(params) {
  return this.http.get(`${this.baseUrl}filterJobsData`, { params: params }).pipe(map((res: any) => {
      return res
  }))
}

 retrieveTemplate(reqObj : any){
  return this.http.post(`${this.baseUrl}retrieveTemplate/`,reqObj).pipe(map((res : any) => {
    return res;
  }));
}

generateTemplateJob(reqObj : any){
  return this.http.post(`${this.baseUrl}generateTemplateJob/`,reqObj).pipe(map((res : any) => {
    return res;
  }));
}

  retrieveNotification(reqObj : any){
    return this.http.post(`${this.baseUrl}retrieveNotification/`,reqObj).pipe(map((res : any) => {
      return res;
    }));
  }

  getFileFormat(reqObj : any){
    return this.http.post(`${this.baseUrl}getFileFormat/`,reqObj).pipe(map((res : any) => {
      return res;
    }));
  }
  
  deleteNotification(reqObj : any){
    return this.http.post(`${this.baseUrl}deleteNotification/`,reqObj).pipe(map((res : any) => {
      return res;
    }));
  }

  getItems(reqObj : any){
    return this.http.post(`${this.baseUrl}getItems/`,reqObj).pipe(map((res : any) => {
      return res;
    }));
  }

  getCategories(obj){
    return this.http.post(`${this.baseUrl}getCategories/`,obj).pipe(map((res : any) => {
      return res;
    }));
  }
  getSubCategories(obj){
    return this.http.post(`${this.baseUrl}getSubCategories/`,obj).pipe(map((res : any) => {
      return res;
    }));
  }

  addReportDraftData(obj){
    return this.http.post(`${this.baseUrl}addReportDraftData/`,obj).pipe(map((res : any) => {
      return res;
    }));
  }

  getReportDraft(params) {
    return this.http.get(`${this.baseUrl}getReportDraft`, { params: params }).pipe(map((res: any) => {
        return res
    }))
  }

  getPOSPriceTires(params) {
    return this.http.get(`${this.baseUrl}getPOSPriceTires`, { params: params }).pipe(map((res: any) => {
        return res
    }))
  }

  updateItems(obj : any){
    return this.http.post(`${this.baseUrl}updateItems/`,obj).pipe(map((res : any) => {
      return res;
    }));
  }

  getKitchen(obj){
    return this.http.post(`${this.baseUrl}getKitchen/`,obj).pipe(map((res : any) => {
      return res;
    }));
  }

  printKitchen(obj){
    return this.http.post(`${this.baseUrl}printKitchen/`,obj).pipe(map((res : any) => {
      return res;
    }));
  }

  printProductionPlanning(obj){
    return this.http.post(`${this.baseUrl}printProductionPlanning/`,obj).pipe(map((res : any) => {
      return res;
    }));
  }

  exportProductionPlanning(obj){
    return this.http.post(`${this.baseUrl}exportProductionPlanning/`,obj).pipe(map((res : any) => {
      return res;
    }));
  }

  printComparisionReport(obj){
    return this.http.post(`${this.baseUrl}printComparisionReport/`,obj).pipe(map((res : any) => {
      return res;
    }));
  }
  requestReport(obj){
    return this.http.post(`${this.baseUrl}generateReportJob/`,obj).pipe(map((res : any) => {
      return res;
    }));
  }
  retrieveReportList(obj){
    return this.http.post(`${this.baseUrl}retrieveReportList/`,obj).pipe(map((res : any) => {
      return res;
    }));
  }
  retrieveReport(obj){
    return this.http.post(`${this.baseUrl}retrieveReport/`,obj).pipe(map((res : any) => {
      return res;
    }));
  }
  getMetaData(obj){
    return this.http.post(`${this.baseUrl}getMetaData/`,obj).pipe(map((res : any) => {
      return res;
    }));
  }
  deleteReport(obj){
    return this.http.post(`${this.baseUrl}deleteReport/`,obj).pipe(map((res : any) => {
      return res;
    }));
  }

  fetchReportList(obj){
    return this.http.post(`${this.baseUrl}fetchReportList/`,obj).pipe(map((res : any) => {
      return res;
    }));
  }

}
