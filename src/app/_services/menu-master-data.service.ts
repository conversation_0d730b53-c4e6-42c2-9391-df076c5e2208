import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class MenuMasterDataService {

  baseUrl: string = environment.baseUrl;
  constructor(private http: HttpClient) { }

  getDataMenuMaster(obj) {
    return this.http.post(`${this.baseUrl}getMenuMaster`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getMenuMasterDataList(obj) {
    return this.http.post(`${this.baseUrl}getMenuMasterDataList`, obj).pipe(map((res: any) =>
      res
    ))
  }

  createDataMenuMaster(obj) {
    return this.http.post(`${this.baseUrl}createMenuMaster`, obj).pipe(map((res: any) =>
      res
    ))
  }

  updateDataMenuMaster(obj) {
    return this.http.post(`${this.baseUrl}updateMenuMaster`, obj).pipe(map((res: any) =>
      res
    ))
  }

  deleteDataMenuMaster(obj) {
    return this.http.post(`${this.baseUrl}deleteMenuMaster`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getRowDataMenuMasterOrg(obj) {
    return this.http.post(`${this.baseUrl}getRowDataMenuMasterOrg`, obj).pipe(map((res: any) =>
      res
    ))
  }
  generateMasterMenuTemplateJob(obj) {
    return this.http.post(`${this.baseUrl}generateMasterMenuTemplateJob`, obj).pipe(map((res: any) =>
      res
    ))
  }
  retrieveMasterMenuTemplate(obj) {
    return this.http.post(`${this.baseUrl}retrieveMasterMenuTemplate`, obj).pipe(map((res: any) =>
      res
    ))
  }
  getDmDeletedMenuMaster(obj) {
    return this.http.post(`${this.baseUrl}getDmDeletedMenuMaster`, obj).pipe(map((res: any) =>
      res
    ))
  }
  revertMenuMasterDeletedData(obj) {
    return this.http.post(`${this.baseUrl}revertMenuMasterDeletedData`, obj).pipe(map((res: any) =>
      res
    ))
  }
}
