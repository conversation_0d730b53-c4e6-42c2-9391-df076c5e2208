import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class MenuRecipeDataService {

  baseUrl: string = environment.baseUrl;
  constructor(private http: HttpClient) { }

  getDataMenuRecipe(obj) {
    return this.http.post(`${this.baseUrl}getMenuRecipe`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getMenuRecipesDataList(obj) {
    return this.http.post(`${this.baseUrl}getMenuRecipesDataList`, obj).pipe(map((res: any) =>
      res
    ))
  }

  createDataMenuMaster(obj) {
    return this.http.post(`${this.baseUrl}createMenuRecipe`, obj).pipe(map((res: any) =>
      res
    ))
  }

  updateDataMenuMaster(obj) {
    return this.http.post(`${this.baseUrl}updateMenuRecipe`, obj).pipe(map((res: any) =>
      res
    ))
  }

  deleteDataMenuMaster(obj) {
    return this.http.post(`${this.baseUrl}deleteMenuRecipe`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getRowDataMenuRecipe(obj) {
    return this.http.post(`${this.baseUrl}getRowDataMenuRecipe`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getRowDataMenuMaster(obj) {
    return this.http.post(`${this.baseUrl}getRowDataMenuMaster`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getMenuRecipeFiltered(obj) {
    return this.http.post(`${this.baseUrl}getMenuRecipeFiltered`, obj).pipe(map((res: any) =>
      res
    ))
  }

  generateMenuMasterRecipeTemplateJob(obj) {
    return this.http.post(`${this.baseUrl}generateMenuMasterRecipeTemplateJob`, obj).pipe(map((res: any) =>
      res
    ))
  }

  retrieveMenuMasterRecipeTemplate(obj) {
    return this.http.post(`${this.baseUrl}retrieveMenuMasterRecipeTemplate`, obj).pipe(map((res: any) =>
      res
    ))
  }


}
