import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class MenuWorkMappingDataService {

  baseUrl: string = environment.baseUrl;
  constructor(private http: HttpClient) { }

  getDataMenuWorkMapping(obj) {
    return this.http.post(`${this.baseUrl}getMenuWorkMapping`, obj).pipe(map((res: any) =>
      res
    ))
  }

  createDataMenuWorkMappingr(obj) {
    return this.http.post(`${this.baseUrl}createMenuWorkMapping`, obj).pipe(map((res: any) =>
      res
    ))
  }

  updateDataMenuWorkMapping(obj) {
    return this.http.post(`${this.baseUrl}updateMenuWorkMapping`, obj).pipe(map((res: any) =>
      res
    ))
  }

  deleteDataMenuWorkMapping(obj) {
    return this.http.post(`${this.baseUrl}deleteMenuWorkMapping`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getMenuToWorkAreaMappingDataList(obj) {
    return this.http.post(`${this.baseUrl}getMenuToWorkAreaMappingDataList`, obj).pipe(map((res: any) =>
      res
    ))
  }
  getRowDataMenuWorkMapping(obj) {
    return this.http.post(`${this.baseUrl}getRowDataMenuWorkMapping`, obj).pipe(map((res: any) =>
      res
    ))
  }

  generateMenuToWorkareaTemplateJob(obj) {
    return this.http.post(`${this.baseUrl}generateMenuToWorkareaTemplateJob`, obj).pipe(map((res: any) =>
      res
    ))
  }

  retrieveMenuToWorkareaTemplate(obj) {
    return this.http.post(`${this.baseUrl}retrieveMenuToWorkareaTemplate`, obj).pipe(map((res: any) =>
      res
    ))
  }
  getDmDeletedMenuToWorkarea(obj) {
    return this.http.post(`${this.baseUrl}getDmDeletedMenuToWorkarea`, obj).pipe(map((res: any) =>
      res
    ))
  }

  revertMenuToWorkareaDeletedData(obj) {
    return this.http.post(`${this.baseUrl}revertMenuToWorkareaDeletedData`, obj).pipe(map((res: any) =>
      res
    ))
  }
}
