import { Injectable } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
  
@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  
  constructor(public toastr : ToastrService) { }



  showSuccess(message: any, _title :any) {
    this.toastr.success(message,'',{'toastClass':'successtoast ngx-toastr','closeButton':true , 'timeOut':3000})
  }

  showError(message: any, _title :any) {
    this.toastr.error(message,'',{'toastClass':'errortoast ngx-toastr','closeButton':true , 'timeOut':3000})
  }

  showWarning(message: any, _title :any) {
    this.toastr.warning(message,'',{'toastClass':'warningtoast ngx-toastr','closeButton':true , 'timeOut':3000})
  }

  showInfo(message: any, _title :any) {
    this.toastr.info(message,'',{'toastClass':'infotoast ngx-toastr','closeButton':true , 'timeOut':3000})
  }
  

  
}