import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs/operators';


@Injectable({
  providedIn: 'root'
})
export class PackagingMasterDataService {
  baseUrl: string = environment.baseUrl;
  constructor(private http: HttpClient) { }

  getDataPackagingMaster(obj) {
    return this.http.post(`${this.baseUrl}getPackagingMaster`, obj).pipe(map((res: any) =>
      res
    ))
  }
  getPackagingMasterDataList(obj) {
    return this.http.post(`${this.baseUrl}getPackagingMasterDataList`, obj).pipe(map((res: any) =>
      res
    ))
  }



  createDataPackagingMaster(obj) {
    return this.http.post(`${this.baseUrl}createPackagingMaster`, obj).pipe(map((res: any) =>
      res
    ))
  }

  updateDataPackagingMaster(obj) {
    return this.http.post(`${this.baseUrl}updatePackagingMaster`, obj).pipe(map((res: any) =>
      res
    ))
  }

  deleteDataPackagingMaster(obj) {
    return this.http.post(`${this.baseUrl}deletePackagingMaster`, obj).pipe(map((res: any) =>
      res
    ))
  }
  getRowDataPackagingMaster(obj) {
    return this.http.post(`${this.baseUrl}getRowDataPackagingMaster`, obj).pipe(map((res: any) =>
      res
    ))
  }

  generatePackagingTemplateJob(obj) {
    return this.http.post(`${this.baseUrl}generatePackagingTemplateJob`, obj).pipe(map((res: any) =>
      res
    ))
  }

  retrievePackagingTemplate(reqObj: any) {
    return this.http.post(`${this.baseUrl}retrievePackagingTemplate/`, reqObj).pipe(map((res: any) => {
      return res;
    }));
  }

  getDmDeletedPackagingMaster(obj) {
    return this.http.post(`${this.baseUrl}getDmDeletedPackagingMaster`, obj).pipe(map((res: any) =>
      res
    ))
  }

  revertPackagingDeletedData(obj) {
    return this.http.post(`${this.baseUrl}revertPackagingDeletedData`, obj).pipe(map((res: any) =>
      res
    ))
  }

}
