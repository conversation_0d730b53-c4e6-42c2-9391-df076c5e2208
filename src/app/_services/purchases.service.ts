import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { environment } from '../../environments/environment';
import { map } from 'rxjs/operators';
import { GlobalsService } from '../_services/globals.service';
@Injectable({
  providedIn: 'root'
})
export class PurchasesService {
  baseUrl: string;
  constructor(private http: HttpClient) {
    this.baseUrl = environment.baseUrl;
  }

  truncateValue(num: string | number): number {
    if (typeof num !== 'number' && typeof num !== 'string') {
      return 0;
    }
    const number = parseFloat(num.toString()); 
  
    if (isNaN(number) || Math.abs(number) < 0.01) {
      return 0;
    }
    const match = number.toString().match(/^-?\d+(?:\.\d{0,2})?/);
    const truncatedNumber = match ? match[0] : '0';
    return parseFloat(truncatedNumber);
  }


  createPr(obj) {
    return this.http.post(`${this.baseUrl}createPr`, obj).pipe(map((res: any) => {
      if (res.result === 'success')
        return res.prObj;
    }
    ))
  }

  createPrV2(obj) {
    return this.http.post(`${this.baseUrl}createPrV2`, obj).pipe(map((res: any) => {
      if (res.result === 'success')
        return res.prObj;
    }
    ))
  }

  getPrById(obj) {
    return this.http.post(`${this.baseUrl}getPrById`, obj).pipe(map((res: any) => {
        return res.pObj;
    }
    ))
  }

  emailStatus(obj) {
    return this.http.post(`${this.baseUrl}emailStatus`, obj).pipe(map((res: any) => {
        return res.data;
    }
    ))
  }

  getDraftData(obj) {
    return this.http.post(`${this.baseUrl}getDraftData`, obj).pipe(map((res: any) => {
        return res;
    }
    ))
  }

  fetchCategory(obj) {
    return this.http.post(`${this.baseUrl}fetchCategory`, obj).pipe(map((res: any) => {
        return res;
    }
    ))
  }

  fetchCategoryWithoutVendor(obj) {
    return this.http.post(`${this.baseUrl}fetchCategoryWithoutVendor`, obj).pipe(map((res: any) => {
        return res;
    }
    ))
  }

  deletePurchaseIndent(obj){
    return this.http.post(`${this.baseUrl}deletePr`, obj).pipe(map((res: any) => res))
  }
  
  removeDraftData(obj) {
    return this.http.post(`${this.baseUrl}removeDraftData`, obj).pipe(map((res: any) => {
        return res.data;
    }
    ))
  }

  removeDraftDataBulK(obj) {
    return this.http.post(`${this.baseUrl}removeDraftDataBulK`, obj).pipe(map((res: any) => {
        return res.data;
    }
    ))
  }

  removeDraft(obj) {
    return this.http.post(`${this.baseUrl}clearDraft`, obj).pipe(map((res: any) => {
        return res;
    }
    ))
  }

  removeAllData(obj) {
    return this.http.post(`${this.baseUrl}removeAllData`, obj).pipe(map((res: any) => {
        return res.data;
    }
    ))
  }

  addDraftData(obj) {
    return this.http.post(`${this.baseUrl}addDraftData`, obj).pipe(map((res: any) => {
      return res.data;
    }
    ))
  }

  exportDraftData(obj) {
    return this.http.post(`${this.baseUrl}exportTemplate`, obj).pipe(map((res: any) => {
        return res;
    }
    ))
  }


  poEmail(obj) {
    return this.http.post(`${this.baseUrl}poEmail`, obj).pipe(map((res: any) => {
        return res;
    }
    ))
  }

  getCreatePurchaseOrderV2data(obj) {
    return this.http.post(`${this.baseUrl}getCreatePurchaseOrderV2data`, obj).pipe(map((res: any) => {
        return res;
    }
    ))
  }

  getVendorInventoryNew(obj) {
    return this.http.post(`${this.baseUrl}getVendorInventoryNew`, obj).pipe(map((res: any) => {
        return res;
    }
    ))
  }

  getInventoryNew(obj) {
    return this.http.post(`${this.baseUrl}getInventoryNew`, obj).pipe(map((res: any) => {
        return res;
    }
    ))
  }

  createRequisition(obj) {
    return this.http.post(`${this.baseUrl}createRequistion`, obj).pipe(map((res: any) => {
        return res;
    }
    ))
  }

  getBranchDetails(obj) {
    return this.http.post(`${this.baseUrl}getBranchData`, obj).pipe(map((res: any) => {
        return res;
    }
    ))
  }

  getApprovalDetails(obj) {
    return this.http.post(`${this.baseUrl}getApprovalData`, obj).pipe(map((res: any) => {
        return res;
    }
    ))
  }

  deleteGrn(obj) {
    return this.http.post(`${this.baseUrl}deleteGrn`, obj).pipe(map((res: any) => {
        return res;
    }
    ))
  }

    checkItemAvailability(obj) {
        return this.http.post(`${this.baseUrl}checkItemAvailability`, obj).pipe(map((res: any) => {
            return res;
        }
      ))
    }

    updateStatus(obj) {
      return this.http.post(`${this.baseUrl}updateOrderStatus`, obj).pipe(map((res: any) => {
          return res;
      }
    ))
   }

   updateIndentStatus(obj) {
    return this.http.post(`${this.baseUrl}updateIndentStatus`, obj).pipe(map((res: any) => {
        return res;
    }
  ))
  }

  updateIbtStatus(obj) {
    return this.http.post(`${this.baseUrl}updateIbtStatus`, obj).pipe(map((res: any) => {
        return res;
    }
  ))
  }

   updatePrStatus(obj) {
    return this.http.post(`${this.baseUrl}updatePrStatus`, obj).pipe(map((res: any) => {
        return res;
    }
  ))
 }


  getPrs(obj):any {
    !obj.hasOwnProperty('startDate') ? obj['startDate'] = null : null ;
    !obj.hasOwnProperty('endDate') ? obj['endDate'] = null : null ;
    !obj.hasOwnProperty('vendorId') ? obj['vendorId'] = ['All'] : null ;
    // !obj.hasOwnProperty('approvalStatus') ? obj['approvalStatus'] = ['All'] : null ;
    let params = new HttpParams()
    .set(GlobalsService.tenantId, obj.tenantId)
    .set(GlobalsService.restaurantId, obj.restaurantId)
    .set(GlobalsService.startDate, obj.startDate)
    .set(GlobalsService.endDate, obj.endDate)
    .set(GlobalsService.prVendorId, obj.vendorId.join(','))
    // .set(GlobalsService.prApprovalStatus, obj.approvalStatus)
  
  if (obj.userEmail) {
    params = params.set(GlobalsService.userEmail, obj.userEmail);
  }

  return this.http.get(`${this.baseUrl}getPrs`, { params }).pipe(
    map((res: any) => res.purchaseRequests)
  );
    }
  
  prGroupApproval(obj) {
    return this.http.post(`${this.baseUrl}bulkPrApproval`, obj).pipe(map((res: any) => {
        return res;
    }
    ))
  }

  piGroupApproval(obj) {
    return this.http.post(`${this.baseUrl}bulkPiApproval`, obj).pipe(map((res: any) => {
        return res;
    }
    ))
  }

  grnGroupApproval(obj) {
  	return this.http.post(`${this.baseUrl}bulkGrnApproval`, obj).pipe(map((res: any) => {
  		return res;
  	}))
  }

  grnStatusGroupApproval(obj) {
  	return this.http.post(`${this.baseUrl}bulkGrnStatusApproval`, obj).pipe(map((res: any) => {
  		return res;
  	}))
  }

  grnGroupRejection(obj) {
  	return this.http.post(`${this.baseUrl}bulkGrnRejection`, obj).pipe(map((res: any) => {
  		return res;
  	}))
  }

  grnStatusGroupRejection(obj) {
  	return this.http.post(`${this.baseUrl}bulkGrnStatusRejection`, obj).pipe(map((res: any) => {
  		return res;
  	}))
  }

  indentApproval(obj) {
  	return this.http.post(`${this.baseUrl}indentApproval`, obj).pipe(map((res: any) => {
  		return res;
  	}))
  }

  approvalPI(obj) {
  	return this.http.post(`${this.baseUrl}PIApproval`, obj).pipe(map((res: any) => {
  		return res;
  	}))
  }

  modifyIndent(obj) {
  	return this.http.post(`${this.baseUrl}modifyIndent`, obj).pipe(map((res: any) => {
  		return res;
  	}))
  }

  modifyCSI(obj) {
  	return this.http.post(`${this.baseUrl}modifyCSI`, obj).pipe(map((res: any) => {
  		return res;
  	}))
  }

  approveCSI(obj) {
  	return this.http.post(`${this.baseUrl}approveCSI`, obj).pipe(map((res: any) => {
  		return res;
  	}))
  }

  rejectCSI(obj) {
  	return this.http.post(`${this.baseUrl}rejectCSI`, obj).pipe(map((res: any) => {
  		return res;
  	}))
  }

  indentBulkApproval(obj) {
  	return this.http.post(`${this.baseUrl}bulkIndentApproval`, obj).pipe(map((res: any) => {
  		return res;
  	}))
  }

  indentBulkRejection(obj) {
  	return this.http.post(`${this.baseUrl}bulkIndentRejection`, obj).pipe(map((res: any) => {
  		return res;
  	}))
  }

  indentRejection(obj) {
  	return this.http.post(`${this.baseUrl}indentRejection`, obj).pipe(map((res: any) => {
  		return res;
  	}))
  }

  rejectPI(obj) {
  	return this.http.post(`${this.baseUrl}PIRejection`, obj).pipe(map((res: any) => {
  		return res;
  	}))
  }

  prGroupReject(obj) {
    return this.http.post(`${this.baseUrl}bulkPrRejection`, obj).pipe(map((res: any) => {
        return res;
    }
    ))
  }

  piGroupReject(obj) {
    return this.http.post(`${this.baseUrl}bulkPiRejection`, obj).pipe(map((res: any) => {
        return res;
    }
    ))
  }

  deletePrs(obj) {
    return this.http.post(`${this.baseUrl}/deletePrs`, obj).pipe(map((res: any) =>
      res 
    ))
  }
  
  approvePr(obj) {
    return this.http.post(`${this.baseUrl}updatePr`, obj);
  }


  createPo(obj) {
    return this.http.post(`${this.baseUrl}createPo`, obj).pipe(map((res: any) => {
      let newPo = res.newPo;
      return newPo;

    }))
  }

  getPermission(obj) {
    return this.http.post(`${this.baseUrl}permissionCheck`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  sendEmailForPreq(obj) {
    return this.http.post(`${this.baseUrl}sendEmailForPurRequisition`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  addPkge(obj) {
    return this.http.post(`${this.baseUrl}addPackages`, obj).pipe(map((res: any) => {
      let newPackage = res.newPackage;
      let success = res.success;
      return {"success": success, "newPackage": newPackage};

    }))
  }

  // getFilteredPo(obj) {
  //   return this.http.post(`${this.baseUrl}getFilteredPos`, obj).pipe(map((res: any) => {
  //     return res
  //   }))
  // }

  getFilteredPo(obj):any {
    let params = new HttpParams()
      .set(GlobalsService.tenantId, obj.tenantId)
      .set(GlobalsService.restaurantId, obj.restaurantId)
      .set(GlobalsService.startDate, obj.startDate)
      .set(GlobalsService.endDate, obj.endDate)
      .set(GlobalsService.uType, obj.uType)
      .set(GlobalsService.userEmail, obj.userEmail)
      .set(GlobalsService.GrnApprovalVendorId, obj.vendorId.join(','))
      // .set(GlobalsService.GrnApprovalStatus, obj.approvalStatus)
      .set(GlobalsService.GrnStatus, obj.status)
    return this.http.get(`${this.baseUrl}getFilteredPos`, { params: params }).pipe(map((res: any) => {
      return  res
    }))
  }

  // getFilteredGrns(obj) {
  //   return this.http.post(`${this.baseUrl}getFilteredGrns`, obj).pipe(map((res: any) => {
  //     return res
  //   }))
  // }

  getFilteredGrns(obj):any {
    let params = new HttpParams()
      .set(GlobalsService.tenantId, obj.tenantId)
      .set(GlobalsService.restaurantId, obj.restaurantId)
      .set(GlobalsService.startDate, obj.startDate)
      .set(GlobalsService.endDate, obj.endDate)
      .set(GlobalsService.uType, obj.uType)
      .set(GlobalsService.userEmail, obj.userEmail)
      .set(GlobalsService.postGrnVendorId, obj.vendorId.join(','))
      // .set(GlobalsService.postGrnStatus, obj.approvalStatus)
    return this.http.get(`${this.baseUrl}getFilteredGrns`, { params: params }).pipe(map((res: any) => {
      return  res
    }))
  }

  // getPo(obj) {
  //   return this.http.post(`${this.baseUrl}getPos`, obj).pipe(map((res: any) => {
  //     return res.purchaseOrder
  //   }))
  // }

  getPo(obj):any {
    let params = new HttpParams()
      .set(GlobalsService.tenantId, obj.tenantId)
      .set(GlobalsService.restaurantId, obj.restaurantId)
      .set(GlobalsService.startDate, obj.startDate)
      .set(GlobalsService.endDate, obj.endDate)
      .set(GlobalsService.poVendorId, obj.vendorId.join(','))
      .set(GlobalsService.postatus, obj.status.join(','))
    return this.http.get(`${this.baseUrl}getPos`, { params: params }).pipe(map((res: any) => {
      return  res.purchaseOrder
    }))
  }

  getChild(obj) {
    let params = new HttpParams()
    .set(GlobalsService.restaurantId, obj.restaurantId)
    .set(GlobalsService.itemCode, obj.itemCode)
    .set(GlobalsService.packageName, obj.packageName);
  return this.http.get(`${this.baseUrl}getChild`, { params: params }).pipe(map((res: any) => {
    return res;
  }))
  }

  stockConversionList(obj) {
    let params = new HttpParams()
    .set(GlobalsService.restaurantId, obj.restaurantId)
    .set(GlobalsService.startDate, obj.startDate)
    .set(GlobalsService.endDate, obj.endDate);
  return this.http.get(`${this.baseUrl}stockConversionList`, { params: params }).pipe(map((res: any) => {
    return res;
  }))
    // return this.http.get(`${this.baseUrl}stockConversionList`, { params: obj }).pipe(map((res: any) => {
    //   return res
    // }))
  }

  startConversion(obj){
    return this.http.post(`${this.baseUrl}startConversion`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  getLastGrnPrice(obj) {
    return this.http.get(`${this.baseUrl}getLastGrnPrice`, { params: obj }).pipe(map((res: any) => {
      return res
    }))
  }

  getGrnsforStockConversion(obj) {
    return this.http.get(`${this.baseUrl}getGrnsforStockConversion`, { params: obj }).pipe(map((res: any) => {
      return res
    }))
  }

  getPurchaseStatus(obj) {
    return this.http.post(`${this.baseUrl}getPurchaseStatusData`, obj ).pipe(map((res: any) => {
      return res
    }))
  }

  getData(obj) {
    return this.http.post(`${this.baseUrl}get_po_grn_data`, obj ).pipe(map((res: any) => {
      return res
    }))
  }

  getDetails(obj) {
    return this.http.get(`${this.baseUrl}getDetails`, { params: obj }).pipe(map((res: any) => {
      return res
    }))
  }

  getGrnbyInvoice(obj) {
    return this.http.get(`${this.baseUrl}getGrnbyInvoiceId`, { params: obj }).pipe(map((res: any) => {
      return res
    }))
  }

  getInvoice(obj) {
    return this.http.get(`${this.baseUrl}getInvoice`, { params: obj }).pipe(map((res: any) => {
      return res
    }))
  }


  getTempItems(obj) {
    return this.http.get(`${this.baseUrl}getTemplateItems`, { params: obj }).pipe(map((res: any) => {
      return res
    }))
  }

  deleteTemplate(obj) {
    return this.http.post(`${this.baseUrl}deleteTemplate`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  savePurchaseOrder(obj) {
    return this.http.post(`${this.baseUrl}savePurchaseOrder`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  saveGrn(obj) {
    return this.http.post(`${this.baseUrl}saveGrn`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  updateTemplate(obj) {
    return this.http.post(`${this.baseUrl}updatePrTemplate`, obj).pipe(map((res: any) => {
      return res
    }))
  }
  
  getUsers(obj) {
    return this.http.post(`${this.baseUrl}getUsers`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  createGrn(obj) {
    return this.http.post(`${this.baseUrl}createGrnForPo`, obj).pipe(map((res: any) => {
        return res;
    }))
  }

  createPi(obj) {
    return this.http.post(`${this.baseUrl}createPi`, obj).pipe(map((res: any) => {
        return res;
    }))
  }

  // getGrn(obj) {
  //   return this.http.post(`${this.baseUrl}getGrns`, obj).pipe(map((res: any) => {
  //     if (res.result === 'success')
  //       return res.grns
  //   }))
  // }

  getGrn(obj):any {
    !obj.hasOwnProperty('startDate') ? obj['startDate'] = null : null ;
    !obj.hasOwnProperty('endDate') ? obj['endDate'] = null : null ;
    !obj.hasOwnProperty('vendorId') ? obj['vendorId'] = ['All'] : null ;
    !obj.hasOwnProperty('grnDate') ? obj['grnDate'] = null : null ;
    !obj.hasOwnProperty('stockConversion') ? obj['stockConversion'] = null : null ;
    let params = new HttpParams()
      .set(GlobalsService.tenantId, obj.tenantId)
      .set(GlobalsService.restaurantId, obj.restaurantId)
      .set(GlobalsService.startDate, obj.startDate)
      .set(GlobalsService.endDate, obj.endDate)
      .set(GlobalsService.grnDate, obj.grnDate)
      .set(GlobalsService.piScreen, obj.piScreen)
      .set(GlobalsService.vendorIdData, obj.vendorIdData.join(','))
      .set(GlobalsService.stockConversion, obj.stockConversion)
      // .set(GlobalsService.grnType, obj.grnType)
    return this.http.get(`${this.baseUrl}getGrns`, { params: params }).pipe(map((res: any) => {
      if (res.result === 'success') {
        return res.grns
      }
      return null
    }))
  }

  getRtvList(obj):any {
    !obj.hasOwnProperty('startDate') ? obj['startDate'] = null : null ;
    !obj.hasOwnProperty('endDate') ? obj['endDate'] = null : null ;
    let params = new HttpParams()
      .set(GlobalsService.tenantId, obj.tenantId)
      .set(GlobalsService.restaurantId, obj.restaurantId)
      .set(GlobalsService.startDate, obj.startDate)
      .set(GlobalsService.endDate, obj.endDate)
    return this.http.get(`${this.baseUrl}rtvList`, { params: params }).pipe(map((res: any) => {
      return res
    }))
  }

  // getDataForPurchaseTable(obj) {
  //   return this.http.post(`${this.baseUrl}getDataForPurchaseTable`, obj).pipe(map((res: any) => {
  //       return res;
  //   }))
  // }

  

  getDataForPurchaseTable(obj) {
    let params = new HttpParams()
      .set(GlobalsService.tenantId, obj.tenantId)
      .set(GlobalsService.restaurantId, obj.restaurantId)
      .set(GlobalsService.startDate, obj.startDate)
      .set(GlobalsService.endDate, obj.endDate);
    return this.http.get(`${this.baseUrl}getDataForPurchaseTable`, { params: params }).pipe(map((res: any) => {
      return res;
    }))
  }

  getSsi(obj) {
    
    if (!obj.hasOwnProperty('prId')) {
      obj['prId'] = [];
    }
    !obj.hasOwnProperty('event') ? obj['event'] = null : null ;
    // !obj.hasOwnProperty('prId') ? obj['prId'] = null : null ;
    let params = new HttpParams()
      .set(GlobalsService.tenantId, obj.tenantId)
      .set(GlobalsService.restaurantId, obj.restaurantId)
      .set(GlobalsService.dCRprId, obj.prId.join(','))
      // .set(GlobalsService.prId, obj.prId)
      .set(GlobalsService.specialFlag, obj.specialFlag)
      .set(GlobalsService.event, obj.event)

      if (Array.isArray(obj.prId)) {
        params = params.set(GlobalsService.dCRprId, obj.prId.join(','));
      } else {
        params = params.set(GlobalsService.dCRprId, '');
      }
      
    return this.http.get(`${this.baseUrl}getSsi`, { params: params }).pipe(map((res: any) => {
      if (res.result === 'success')
        return res.items.filter(item => item.otb > 0)
    }))
  }

  createPrs(obj){
    return this.http.post(`${this.baseUrl}createPrs`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  savePrsDraft(obj){
    return this.http.post(`${this.baseUrl}savePrsDraft`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  getPrsDraft(obj) {
    return this.http.get(`${this.baseUrl}getPrsDraft`, { params: obj }).pipe(map((res: any) => {
      return res
    }))
  }

  deletePrsDraft(obj){
    return this.http.post(`${this.baseUrl}deletePrsDraft`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  printPdf(obj, type) {
    let params = new HttpParams()
      .set(GlobalsService.tenantId, obj.tenantId)
      .set('spoilage',obj);
      return this.http.post(`${this.baseUrl}print`+type, obj).pipe(map((res: any) => {
        return res
    }))
  }

  printpdfs(obj, type) {
    let params = new HttpParams()
      .set(GlobalsService.tenantId, obj.tenantId)
      .set('grn',obj);
      return this.http.post(`${this.baseUrl}print`+type, obj).pipe(map((res: any) => {
        return res
    }))
  }

  printpdf(obj, type) {
    let params = new HttpParams()
      .set(GlobalsService.tenantId, obj.tenantId)
      .set('rtv',obj);
      return this.http.post(`${this.baseUrl}print`+type, obj).pipe(map((res: any) => {
        return res
    }))
  }

  print(obj, type) {
    let params = new HttpParams()
      .set(GlobalsService.tenantId, obj.tenantId)
      .set('intra_branch',obj);
      return this.http.post(`${this.baseUrl}print`+type, obj).pipe(map((res: any) => {
        return res
    }))
  }

  exportToExcel(obj, type) {
    let params = new HttpParams()
      .set(GlobalsService.tenantId, obj.tenantId)
      .set('grn',obj);
      return this.http.post(`${this.baseUrl}export`+type, obj).pipe(map((res: any) => {
      if (res.result === 'success')
        return res
    }))
  }

  globalPrintPdf(data){
    var pdfData = atob(data);
    var arrayBuffer = new ArrayBuffer(pdfData.length);
    var uint8Array = new Uint8Array(arrayBuffer);
    for (var i = 0; i < pdfData.length; i++) {
      uint8Array[i] = pdfData.charCodeAt(i);
    }
    var blob = new Blob([arrayBuffer], { type: 'application/pdf' });
    var url = URL.createObjectURL(blob);
    window.open(url, '_blank');
  }

  getSortedPkg(obj){
    return this.http.post(`${this.baseUrl}getSortedPkg`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  retrieveInvoice(obj){
    return this.http.post(`${this.baseUrl}retrieveInvoice`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  uploadInvoice(obj){
    return this.http.post(`${this.baseUrl}uploadInvoice`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  deleteInvoice(obj){
    return this.http.post(`${this.baseUrl}deleteInvoice`, obj).pipe(map((res: any) => {
      return res
    }))
  }
  
  generateRtv(obj){
    return this.http.post(`${this.baseUrl}generateRtv`, obj).pipe(map((res: any) => {
      return res
    }))

  }

  createRtv(obj){
    return this.http.post(`${this.baseUrl}createRtv`, obj).pipe(map((res: any) => {
      return res
    }))

  }

  getRtv(obj) {
    return this.http.get(`${this.baseUrl}getRtv`, { params: obj }).pipe(map((res: any) => {
      return res
    }))
  }

  deleteRtv(obj){
    return this.http.post(`${this.baseUrl}deleteRtv`, obj).pipe(map((res: any) => res))
  }

  getIntraBranchList(obj) {
    return this.http.get(`${this.baseUrl}getIntraBranchList`, { params: obj }).pipe(map((res: any) => {
      return res
    }))
  }

  deleteIntraBranch(obj){
    return this.http.post(`${this.baseUrl}deleteIntraBranchList`, obj).pipe(map((res: any) => res))
  }

  getRtvs(obj){
    return this.http.post(`${this.baseUrl}getRtvs`, obj).pipe(map((res: any) => {
      if (res.result === 'success')
        return res.rtvs
    }))

  }

  getInstoreVal(obj){
    return this.http.post(`${this.baseUrl}getInstoreVal`, obj).pipe(map((res: any) => {
      if (res.result === 'success')
        return res.items
    }))
  }

  deletePO(obj){
    return this.http.post(`${this.baseUrl}deletePO`, obj).pipe(map((res: any) => {
      return res
    }))
  }



  createSetting(obj){
    return this.http.post(`${this.baseUrl}createSetting`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  approveSetting(obj){
    return this.http.post(`${this.baseUrl}approveSetting`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  createPartialPr(obj){
    return this.http.post(`${this.baseUrl}createPartialPr`, obj).pipe(map((res: any) => {
      return res
    }))
  }


  rejectSetting(obj){
    return this.http.post(`${this.baseUrl}rejectSetting`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  
  retrievePrSetting(obj){
    return this.http.post(`${this.baseUrl}retrievePrSetting`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  retrievePoSetting(obj){
    return this.http.post(`${this.baseUrl}retrievePoSetting`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  getUserRoles(obj){
    return this.http.post(`${this.baseUrl}getUsersPersonalEmail`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  retrieveGrnSetting(obj){
    return this.http.post(`${this.baseUrl}retrieveGrnSetting`, obj).pipe(map((res: any) => {
      return res
    }))
  }
  
  approvePo(obj){
    return this.http.post(`${this.baseUrl}approvePo`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  rejectPo(obj){
    return this.http.post(`${this.baseUrl}rejectPo`, obj).pipe(map((res: any) => {
      return res
    }))
  }


  getSelectedUsers(obj){
    return this.http.post(`${this.baseUrl}getSelectedUsers`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  // getContractItems(obj){
  //   return this.http.post(`${this.baseUrl}getContractItems`, obj).pipe(map((res: any) => {
  //     return res
  //   }))
  // }

  getTemplates(obj){
    return this.http.post(`${this.baseUrl}getPrTmpList`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  sendGrnReq(obj){
    return this.http.post(`${this.baseUrl}checkPoPermission`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  checkPoEmail(obj){
    return this.http.post(`${this.baseUrl}checkPoEmail`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  workAreaMapping(obj){
    return this.http.post(`${this.baseUrl}workAreaMapping`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  approveGrn(obj){
    return this.http.post(`${this.baseUrl}approveGrn`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  approveGrnStatus(obj){
    return this.http.post(`${this.baseUrl}approvePostGrnStatus`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  rejectGrnStatus(obj){
    return this.http.post(`${this.baseUrl}rejectPostGrnStatus`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  rejectGrn(obj){
    return this.http.post(`${this.baseUrl}rejectGrn`, obj).pipe(map((res: any) => {
      return res
    }))
  }


  modifyPr(obj){
    return this.http.post(`${this.baseUrl}modifyPr`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  getUserAccess(obj){
    return this.http.post(`${this.baseUrl}getUserAccess`, obj).pipe(map((res: any) => {
      return res
    }))
  }


  makePrTemplate(obj){
    return this.http.post(`${this.baseUrl}makePrTemplate`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  getPrTmp(obj){
    return this.http.post(`${this.baseUrl}getPrTmpList`, obj).pipe(map((res: any) => {
      if (res.result === 'success')
        return res.prTmpList
    }))
  }

  checkDupTmpName(obj){
    return this.http.post(`${this.baseUrl}checkDupTmpName`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  delPrTm(obj){
    return this.http.post(`${this.baseUrl}deleteTemplate`, obj).pipe(map((res: any) => {
      if (res.result === 'success')
        return res.prTmpList
    }))
  }

  editDraft(obj){
    return this.http.post(`${this.baseUrl}editDraft`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  
}
