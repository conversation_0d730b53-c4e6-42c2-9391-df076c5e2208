import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class RoleDataService {

  baseUrl: string = environment.baseUrl;

  constructor(private http: HttpClient) { }

  getDataRole(obj) {
    return this.http.post(`${this.baseUrl}getDmRole`, obj).pipe(map((res: any) =>
      res
    ))
  }

  createDataRole(obj) {
    return this.http.post(`${this.baseUrl}createDmRole`, obj).pipe(map((res: any) =>
      res
    ))
  }

  updateDataRole(obj) {
    return this.http.post(`${this.baseUrl}updateDmRole`, obj).pipe(map((res: any) =>
      res
    ))
  }

  deleteDataRole(obj) {
    return this.http.post(`${this.baseUrl}deleteDmRole`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getRoleDataList(obj) {
    return this.http.post(`${this.baseUrl}getRoleDataList`, obj).pipe(map((res: any) =>
      res
    ))
  }


  getRowDataDmRole(obj) {
    return this.http.post(`${this.baseUrl}getRowDataDmRole`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getDmDeletedRole(obj) {
    return this.http.post(`${this.baseUrl}getDmDeletedRole`, obj).pipe(map((res: any) =>
      res
    ))
  }

  generateRolesTemplateJob(obj) {
    return this.http.post(`${this.baseUrl}generateRolesTemplateJob`, obj).pipe(map((res: any) =>
      res
    ))
  }
  
  retrieveRolesTemplate(obj) {
    return this.http.post(`${this.baseUrl}retrieveRolesTemplate`, obj).pipe(map((res: any) =>
      res
    ))
  }

  revertRoleDeletedData(obj) {
    return this.http.post(`${this.baseUrl}revertRoleDeletedData`, obj).pipe(map((res: any) =>
      res
    ))
  }

}
