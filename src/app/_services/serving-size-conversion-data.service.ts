import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class ServingSizeConversionDataService {

  baseUrl: string = environment.baseUrl;

  constructor(private http: HttpClient) { }

  getDataServingSizeConversion(obj) {
    return this.http.post(`${this.baseUrl}/getDmServingSizeConversion`, obj).pipe(map((res: any) =>
      res
    ))
  }

  createDataServingSizeConversion(obj) {
    return this.http.post(`${this.baseUrl}/createDmServingSizeConversion`, obj).pipe(map((res: any) =>
      res
    ))
  }

  updateDataServingSizeConversion(obj) {
    return this.http.post(`${this.baseUrl}/updateDmServingSizeConversion`, obj).pipe(map((res: any) =>
      res
    ))
  }

  deleteDataServingSizeConversion(obj) {
    return this.http.post(`${this.baseUrl}/deleteDmServingSizeConversion`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getServingSizeConversionDataList(obj) {
    return this.http.post(`${this.baseUrl}/getServingSizeConversionDataList`, obj).pipe(map((res: any) =>
      res
    ))
  }
  getRowDataDmServingSizeConversion(obj) {
    return this.http.post(`${this.baseUrl}/getRowDataDmServingSizeConversion`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getDmDeletedServingSizeConversion(obj) {
    return this.http.post(`${this.baseUrl}getDmDeletedServingSizeConversion`, obj).pipe(map((res: any) =>
      res
    ))
  }

  revertServingSizeConversionDeletedData(obj) {
    return this.http.post(`${this.baseUrl}revertServingSizeConversionDeletedData`, obj).pipe(map((res: any) =>
      res
    ))
  }

  generateServingSizeConversionTemplateJob(obj) {
    return this.http.post(`${this.baseUrl}generateServingSizeConversionTemplateJob`, obj).pipe(map((res: any) =>
      res
    ))
  }

  retrieveServingSizeConversionTemplate(obj) {
    return this.http.post(`${this.baseUrl}retrieveServingSizeConversionTemplate`, obj).pipe(map((res: any) =>
      res
    ))
  }

}
