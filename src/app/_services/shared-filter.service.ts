import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
@Injectable({
  providedIn: 'root'
})
export class SharedFilterService {

    public getFilteredIbts = new BehaviorSubject<any>([]);
    public getFilteredGrn = new BehaviorSubject<any>([]);
    public getFilteredCreateReqList = new BehaviorSubject<any>([]);
    public getFilteredInvoice = new BehaviorSubject<any>([]);
    public getFilteredPurOrder = new BehaviorSubject<any>([]);
    public getFilteredPurReq = new BehaviorSubject<any>([]);
    public getFilteredPurStatus = new BehaviorSubject<any>([]);
    public getFilteredPurReqApproval = new BehaviorSubject<any>([]);
    public getFilteredIndentApproval = new BehaviorSubject<any>([]);
    public getFilteredCsi = new BehaviorSubject<any>([]);
    public getFilteredPGrnApproval = new BehaviorSubject<any>([]);
    public getFilteredGrnApproval = new BehaviorSubject<any>([]);
    public getFilteredInventory = new BehaviorSubject<any>([]);
    public getFilteredIndentsList = new BehaviorSubject<any>([]);
    public getLedgerFilter = new BehaviorSubject<any>([]);
    
    constructor(){
      
    }

}
