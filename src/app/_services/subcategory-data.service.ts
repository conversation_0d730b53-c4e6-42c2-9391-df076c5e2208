import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class SubcategoryDataService {
  baseUrl: string = environment.baseUrl;
  constructor(private http: HttpClient) { }

  getDataMasterSubCategory(obj) {
    return this.http.post(`${this.baseUrl}getDmMasterSubCategory`, obj).pipe(map((res: any) =>
      res
    ))
  }

  createDataMasterSubCategory(obj) {
    return this.http.post(`${this.baseUrl}createDmMasterSubCategory`, obj).pipe(map((res: any) =>
      res
    ))
  }

  updateDataMasterSubCategory(obj) {
    return this.http.post(`${this.baseUrl}updateDataMasterSubCategory`, obj).pipe(map((res: any) =>
      res
    ))
  }

  deleteDataMasterSubCategory(obj) {
    return this.http.post(`${this.baseUrl}deleteDmMasterSubCategory`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getMasterSubCategoryDataList(obj) {
    return this.http.post(`${this.baseUrl}getMasterSubCategoryDataList`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getRowMasterSubCategory(obj) {
    return this.http.post(`${this.baseUrl}getRowMasterSubCategory`, obj).pipe(map((res: any) =>
      res
    ))
  }

  generateSubCategoryTemplateJob(obj) {
    return this.http.post(`${this.baseUrl}generateSubCategoryTemplateJob`, obj).pipe(map((res: any) =>
      res
    ))
  }

  retrieveSubCategoryTemplate(obj) {
    return this.http.post(`${this.baseUrl}retrieveSubCategoryTemplate`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getDmDeletedSubCategory(obj) {
    return this.http.post(`${this.baseUrl}getDmDeletedSubCategory`, obj).pipe(map((res: any) =>
      res
    ))
  }

  revertSubCategoryDeletedData(obj) {
    return this.http.post(`${this.baseUrl}revertSubCategoryDeletedData`, obj).pipe(map((res: any) =>
      res
    ))
  }
}
