import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class SubrecipeMasterDataService {
  baseUrl: string = environment.baseUrl;
  constructor(private http: HttpClient) { }

  getDataSubrecipeMaster(obj) {
    return this.http.post(`${this.baseUrl}getSubrecipeMaster`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getSubrecipeMasterDataList(obj) {
    return this.http.post(`${this.baseUrl}getSubrecipeMasterDataList`, obj).pipe(map((res: any) =>
      res
    ))
  }

  createDataSubrecipeMaster(obj) {
    return this.http.post(`${this.baseUrl}createSubrecipeMaster`, obj).pipe(map((res: any) =>
      res
    ))
  }

  updateDataSubrecipeMaster(obj) {
    return this.http.post(`${this.baseUrl}updateSubrecipeMaster`, obj).pipe(map((res: any) =>
      res
    ))
  }

  deleteDataSubrecipeMaster(obj) {
    return this.http.post(`${this.baseUrl}deleteSubrecipeMaster`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getRowData(obj) {
    return this.http.post(`${this.baseUrl}getRowData`, obj).pipe(map((res: any) =>
      res
    ))
  }

  generateSubRecipeMasterTemplateJob(obj) {
    return this.http.post(`${this.baseUrl}generateSubRecipeMasterTemplateJob`, obj).pipe(map((res: any) =>
      res
    ))
  }

  retrieveSubRecipeMasterTemplate(obj) {
    return this.http.post(`${this.baseUrl}retrieveSubRecipeMasterTemplate`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getDmDeletedSubRecipeMaster(obj) {
    return this.http.post(`${this.baseUrl}getDmDeletedSubRecipeMaster`, obj).pipe(map((res: any) =>
      res
    ))
  }

  revertSubRecipeMasterDeletedData(obj) {
    return this.http.post(`${this.baseUrl}revertSubRecipeMasterDeletedData`, obj).pipe(map((res: any) =>
      res
    ))
  }
}
