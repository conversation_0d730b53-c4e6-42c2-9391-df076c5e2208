import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class SubrecipeRecipesDataService {
  baseUrl: string = environment.baseUrl;

  constructor(private http: HttpClient) { }

  getDataSubrecipeRecipes(obj) {
    return this.http.post(`${this.baseUrl}getSubrecipeRecipes`, obj).pipe(map((res: any) =>
      res
    ))
  }

  createDataSubrecipeRecipes(obj) {
    return this.http.post(`${this.baseUrl}createSubrecipeRecipes`, obj).pipe(map((res: any) =>
      res
    ))
  }

  updateDataSubrecipeRecipes(obj) {
    return this.http.post(`${this.baseUrl}updateSubrecipeRecipes`, obj).pipe(map((res: any) =>
      res
    ))
  }

  deleteDataSubrecipeRecipes(obj) {
    return this.http.post(`${this.baseUrl}deleteSubrecipeRecipes`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getSubrecipeRecipesDataList(obj) {
    return this.http.post(`${this.baseUrl}getSubrecipeRecipesDataList`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getRowDataSubrecipeRecipes(obj) {
    return this.http.post(`${this.baseUrl}getRowDataSubrecipeRecipes`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getRowDataSubrecipeMaster(obj) {
    return this.http.post(`${this.baseUrl}getRowData`, obj).pipe(map((res: any) =>
      // return this.http.post(`${this.baseUrl}getRowDataMenuMaster`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getSubRecipeFiltered(obj) {
    return this.http.post(`${this.baseUrl}getSubRecipeFiltered`, obj).pipe(map((res: any) =>
      res
    ))
  }
  generateSubRecipeMasterRecipeTemplateJob(obj) {
    return this.http.post(`${this.baseUrl}generateSubRecipeMasterRecipeTemplateJob`, obj).pipe(map((res: any) =>
      res
    ))
  }
  retrieveSubRecipeMasterRecipeTemplate(obj) {
    return this.http.post(`${this.baseUrl}retrieveSubRecipeMasterRecipeTemplate`, obj).pipe(map((res: any) =>
      res
    ))
  }

}
