import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../environments/environment';
import { map } from 'rxjs/operators';


@Injectable({
  providedIn: 'root'
})
export class TemplateStatusService {

  baseUrl: string = environment.baseUrl;

  constructor(private http: HttpClient) { }

  getTemplateStatus(obj) {
    return this.http.post(`${this.baseUrl}getTemplateStatus`, obj).pipe(map((res: any) =>
      res
    ))
  }
}
