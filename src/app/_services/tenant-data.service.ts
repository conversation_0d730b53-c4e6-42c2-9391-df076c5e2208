import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class TenantDataService {
  baseUrl: string = environment.baseUrl;

  constructor(private http: HttpClient) { }

  getDmtenant(obj) {
    return this.http.post(`${this.baseUrl}getDmtenant`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getDmtenantDetailsById(obj) {
    return this.http.post(`${this.baseUrl}getDmtenantDetailsById`, obj).pipe(map((res: any) =>
      res
    ))
  }
  deleteDataTenant(obj) {
    return this.http.post(`${this.baseUrl}deleteDataTenant`, obj).pipe(map((res: any) =>
      res
    ))
  }
  updateTenantDetail(obj) {
    return this.http.post(`${this.baseUrl}updateTenantDetail`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getDmDeletedTenant(obj) {
    return this.http.post(`${this.baseUrl}getDmDeletedTenant`, obj).pipe(map((res: any) =>
      res
    ))
  }

  revertTenantDeletedData(obj) {
    return this.http.post(`${this.baseUrl}revertTenantDeletedData`, obj).pipe(map((res: any) =>
      res
    ))
  }

}
