import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs/operators';


@Injectable({
  providedIn: 'root'
})
export class UserDataService {

  baseUrl: string = environment.baseUrl;

  constructor(private http: HttpClient) { }

  getDataUser(obj) {
    return this.http.post(`${this.baseUrl}getDmUser`, obj).pipe(map((res: any) =>
      res
    ))
  }

  createDataUser(obj) {
    return this.http.post(`${this.baseUrl}createDmUser`, obj).pipe(map((res: any) =>
      res
    ))
  }

  updateDataUser(obj) {
    return this.http.post(`${this.baseUrl}updateDmUser`, obj).pipe(map((res: any) =>
      res
    ))
  }

  deleteDataUser(obj) {
    return this.http.post(`${this.baseUrl}deleteDmUser`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getUsersDataList(obj) {
    return this.http.post(`${this.baseUrl}getUsersDataList`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getRowDataDmUser(obj) {
    return this.http.post(`${this.baseUrl}getRowDataDmUser`, obj).pipe(map((res: any) =>
      res
    ))
  }

  generateUsersTemplateJob(obj) {
    return this.http.post(`${this.baseUrl}generateUsersTemplateJob`, obj).pipe(map((res: any) =>
      res
    ))
  }

  retrieveUsersTemplate(obj) {
    return this.http.post(`${this.baseUrl}retrieveUsersTemplate`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getDmDeletedUser(obj) {
    return this.http.post(`${this.baseUrl}getDmDeletedUser`, obj).pipe(map((res: any) =>
      res
    ))
  }

  revertUserDeletedData(obj) {
    return this.http.post(`${this.baseUrl}revertUserDeletedData`, obj).pipe(map((res: any) =>
      res
    ))
  }
}
