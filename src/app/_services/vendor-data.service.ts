import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs/operators';


@Injectable({
  providedIn: 'root'
})
export class VendorDataService {

  baseUrl: string = environment.baseUrl;

  constructor(private http: HttpClient) { }

  getDataVendor(obj) {
    return this.http.post(`${this.baseUrl}getDmVendor`, obj).pipe(map((res: any) =>
      res
    ))
  }

  createDataVendor(obj) {
    return this.http.post(`${this.baseUrl}createDmVendor`, obj).pipe(map((res: any) =>
      res
    ))
  }

  updateDataVendor(obj) {
    return this.http.post(`${this.baseUrl}updateDmVendor`, obj).pipe(map((res: any) =>
      res
    ))
  }

  deleteDataVendor(obj) {
    return this.http.post(`${this.baseUrl}deleteDmVendor`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getVendorDataList(obj) {
    return this.http.post(`${this.baseUrl}getVendorDataList`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getRowDataDmVendor(obj) {
    return this.http.post(`${this.baseUrl}getRowDataDmVendor`, obj).pipe(map((res: any) =>
      res
    ))
  }

  generateVendorTemplateJob(obj) {
    return this.http.post(`${this.baseUrl}generateVendorTemplateJob`, obj).pipe(map((res: any) =>
      res
    ))
  }

  retrieveVendorTemplate(reqObj: any) {
    return this.http.post(`${this.baseUrl}retrieveVendorTemplate/`, reqObj).pipe(map((res: any) => {
      return res;
    }));
  }

  getDmDeletedVendorMaster(obj) {
    return this.http.post(`${this.baseUrl}getDmDeletedVendorMaster`, obj).pipe(map((res: any) =>
      res
    ))
  }

  revertVendorMasterDeletedData(obj) {
    return this.http.post(`${this.baseUrl}revertVendorMasterDeletedData`, obj).pipe(map((res: any) =>
      res
    ))
  }

}
