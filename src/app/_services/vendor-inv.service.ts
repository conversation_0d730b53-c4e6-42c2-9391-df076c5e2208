import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { environment } from '../../environments/environment';
import { map } from 'rxjs/operators';
import { GlobalsService } from '../_services/globals.service';
import { AuthService } from '../_services/auth.service';
@Injectable({
  providedIn: 'root'
})
export class VendorInvService {

  baseUrl : string;
  user : any;
  constructor(private http: HttpClient, private auth : AuthService) {
    this.baseUrl = environment.baseUrl;
    this.user = this.auth.getCurrentUser();
   }

  getVenInvItemByCat(obj){
    // let params = new HttpParams().set(GlobalsService.vendorId,obj.vendorId).set(GlobalsService.tenantId,obj.tenantId)
    // .set(GlobalsService.restaurantId,obj.restaurantId).set(GlobalsService.category,obj.category)
    // return this.http.get(`${this.baseUrl}getVendorInventory`,{params : params}).pipe(
    //   map((res : any) =>  {
    //     res.invItems.sort((a,b) => a.itemName > b.itemName)
    //     return res
    //   }))
    return this.http.post(`${this.baseUrl}getVendorInventory/`, obj).pipe(map((res: any) => {
      return res
    }))
  }


  getVenInvItemByCatNew(obj){
    return this.http.post(`${this.baseUrl}getVendorInventoryNew`, obj).pipe(map((res: any) => {
      return res
    }))
  }


  updateTemplate(obj) {
    return this.http.post(`${this.baseUrl}updatePrTemplate/`, obj).pipe(map((res: any) => {
      return res
    }))
  }

  getVendorData(obj) {
    return this.http.post(`${this.baseUrl}getVendoritems`, obj).pipe(map((res: any) => {
      return res
    }))
  }

}
