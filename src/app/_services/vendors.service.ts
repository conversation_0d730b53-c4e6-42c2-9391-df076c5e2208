import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { environment } from '../../environments/environment';
import { map } from 'rxjs/operators';
import { GlobalsService } from '../_services/globals.service';
@Injectable({
  providedIn: 'root'
})
export class VendorsService {
  baseUrl : string;
  constructor(private http: HttpClient) {
    this.baseUrl = environment.baseUrl;
   }

   getVendors(obj) {
    let tenantId = obj['tenantId'];
    let report = obj.hasOwnProperty('report') ? true : false
    return this.http.get(`${this.baseUrl}getVendors/?tenantId=${tenantId}&report=${report}`).pipe(
      map((res : any) =>  res = res.vendors.sort((a,b) =>
        a.name > b.name
      )))
   }

}
