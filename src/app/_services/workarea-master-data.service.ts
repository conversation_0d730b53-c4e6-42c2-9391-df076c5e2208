import { Injectable } from '@angular/core';
import { environment } from '../../environments/environment';
import { HttpClient } from '@angular/common/http';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class WorkareaMasterDataService {
  baseUrl: string = environment.baseUrl;
  constructor(private http: HttpClient) { }

  getDataMasterWorkarea(obj) {
    return this.http.post(`${this.baseUrl}getDmMasterWorkarea`, obj).pipe(map((res: any) =>
      res
    ))
  }

  createDataMasterWorkarea(obj) {
    return this.http.post(`${this.baseUrl}createDmMasterWorkarea`, obj).pipe(map((res: any) =>
      res
    ))
  }

  updateDmMasterWorkarea(obj) {
    return this.http.post(`${this.baseUrl}updateDmMasterWorkarea`, obj).pipe(map((res: any) =>
      res
    ))
  }

  deleteDataMasterWorkarea(obj) {
    return this.http.post(`${this.baseUrl}deleteDmMasterWorkarea`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getMasterWorkAreaDataList(obj) {
    return this.http.post(`${this.baseUrl}getMasterWorkAreaDataList`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getRowMasterWorkArea(obj) {
    return this.http.post(`${this.baseUrl}getRowMasterWorkArea`, obj).pipe(map((res: any) =>
      res
    ))
  }
  generateWorkareaTemplateJob(obj) {
    return this.http.post(`${this.baseUrl}generateWorkareaTemplateJob`, obj).pipe(map((res: any) =>
      res
    ))
  }
  retrieveWorkareaTemplate(obj) {
    return this.http.post(`${this.baseUrl}retrieveWorkareaTemplate`, obj).pipe(map((res: any) =>
      res
    ))
  }

  getDmDeletedWorkarea(obj) {
    return this.http.post(`${this.baseUrl}getDmDeletedWorkarea`, obj).pipe(map((res: any) =>
      res
    ))
  }

  revertWorkareaDeletedData(obj) {
    return this.http.post(`${this.baseUrl}revertWorkareaDeletedData`, obj).pipe(map((res: any) =>
      res
    ))
  }
  
}
