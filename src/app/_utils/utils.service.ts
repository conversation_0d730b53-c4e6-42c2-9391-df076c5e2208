import { Injectable, ElementRef } from '@angular/core';
import { MatSnackBar, MatSnackBarHorizontalPosition, MatSnackBarVerticalPosition } from '@angular/material';
import { User } from '../_models';
@Injectable({
  providedIn: 'root'
})
export class UtilsService {
  horizontalPosition: MatSnackBarHorizontalPosition = 'center';
  verticalPosition: MatSnackBarVerticalPosition = 'top'
  constructor(private snackBar: MatSnackBar) { }
  private codeToNameobj = {
    1: 'AMARAVATHI CHICKEN FRY BONELESS',
    7: 'ANDHRA NON VEG MEALS',
    13: 'CHICKEN 65 WITHBONE',
    15: 'CHICKEN FRY',
    16: 'CHICKEN HYDERABADI BIRIYANI',
    17: 'CHICKEN HYDERABADI BIRIYANI FAMILY PACK',
    19: 'CHICKEN KOTHIMEERA CURRY',
    20: 'CHICKE<PERSON>HATRIYA',
    21: 'CHICKEN MASALA',
    22: 'CHICKEN PEPPERDRYBONE',
    38: 'FISH CURRY',
    39: 'FISH FRY',
    49: 'LEMON CHICKEN',
    57: 'MUTTON CHOPS',
    60: 'MUTTON FRY',
    67: 'MUTTON NALLI ROAST',
    70: 'NANDHANA CHICKEN ROAST',
    80: 'NATI KODI BIRIYANI',
    84: 'NELLORE CHICKEN BIRIYANI',
    85: 'NELLORE CHICKEN BIRIYANI FAMILYPACK',
    87: 'NELLORE MUTTON BIRIYANI',
    88: 'NELLORE MUTTON BIRIYANI FAMILYPACK',
    106: 'SUPREM CHICKEN BONELESS BIRIYANI',
    203: 'ANDHRA VEG CARRIER MEALS',
    204: 'ANDHRA VEG MEALS',
    247: 'VEGETABEL BIRIYANI',
    248: 'VEGETABEL BIRIYANI FAMILYPACK',
    509: 'CHICKEN GUNTURDRYBONE',
    510: 'CHICKEN KABAB',
    519: 'CHILLY CHICKEN'
  };

  private nameToCodeObj = {
    'AMARAVATHI CHICKEN FRY BONELESS': 1,
    'ANDHRA NON VEG MEALS': 7,
    'CHICKEN 65 WITHBONE': 13,
    'CHICKEN FRY': 15,
    'CHICKEN HYDERABADI BIRIYANI': 16,
    'CHICKEN HYDERABADI BIRIYANI FAMILY PACK': 17,
    'CHICKEN KOTHIMEERA CURRY': 19,
    'CHICKEN KSHATRIYA': 20,
    'CHICKEN MASALA': 21,
    'CHICKEN PEPPERDRYBONE': 22,
    'FISH CURRY': 38,
    'FISH FRY': 39,
    'LEMON CHICKEN': 49,
    'MUTTON CHOPS': 57,
    'MUTTON FRY': 60,
    'MUTTON NALLI ROAST': 67,
    'NANDHANA CHICKEN ROAST': 70,
    'NATI KODI BIRIYANI': 80,
    'NELLORE CHICKEN BIRIYANI': 84,
    'NELLORE CHICKEN BIRIYANI FAMILYPACK': 85,
    'NELLORE MUTTON BIRIYANI': 87,
    'NELLORE MUTTON BIRIYANI FAMILYPACK': 88,
    'SUPREM CHICKEN BONELESS BIRIYANI': 106,
    'ANDHRA VEG CARRIER MEALS': 203,
    'ANDHRA VEG MEALS': 204,
    'VEGETABEL BIRIYANI': 247,
    'VEGETABEL BIRIYANI FAMILYPACK': 248,
    'CHICKEN GUNTURDRYBONE': 509,
    'CHICKEN KABAB': 510,
    'CHILLY CHICKEN': 519
  }

  objToArr(obj) {
    let arr = [];
    Object.keys(obj).forEach(key => {
      obj[key]['name'] = key;
      arr.push(obj[key]);
    });
    return arr;
  }

  arrtoObj(arr: Array<any>) {
    let obj: any = {};
    arr.forEach(ele => {
      obj[ele.name] = ele;
    });
    return obj;
  }

  openSnackBar(message, action, duration) {
    this.snackBar.open(message, action, {
      horizontalPosition: this.horizontalPosition,
      verticalPosition: this.verticalPosition,
      duration: duration
    })
  }

  snackBarShowSuccess(message: any) {
    // let action = '❗'
    let action = '❌'
    console.log(action);
    this.snackBar.open(message, action,{
      horizontalPosition: this.horizontalPosition,
      verticalPosition: this.verticalPosition,
      duration: 10000,
      panelClass : ['success-snackbar']
    })
  }

  snackBarShowError(message: any) {
    let action = '❌'
    this.snackBar.open(message, action,{
      horizontalPosition: this.horizontalPosition,
      verticalPosition: this.verticalPosition,
      duration: 10000,
      panelClass : ['error-snackbar']
    })
  }

  snackBarShowWarning(message: any) {
    let action = '❌'
    this.snackBar.open(message, action,{
      horizontalPosition: this.horizontalPosition,
      verticalPosition: this.verticalPosition,
      duration: 10000,
      panelClass : ['warning-snackbar']
    })
  }

  snackBarShowInfo(message: any) {
    let action = '❌'
    this.snackBar.open(message, action,{
      horizontalPosition: this.horizontalPosition,
      verticalPosition: this.verticalPosition,
      duration: 10000,
      panelClass : ['info-snackbar']
    })
  }

  getUserReqObj(user: User): any {
    let obj: any = {};
    obj.uId = user.mId;
    obj.tId = user.tenantId;
    return obj;
  }

  exportToExcel(ele: ElementRef, fileName: any) {

  }

  getTotal(arr: any[], key: string) {
    return arr.map(t => t[key]).reduce((acc, value) => acc + value, 0);
  }

  codeToName(arr) {
    arr.forEach(item => {
      if (this.codeToNameobj[item.name])
        item.name = this.codeToNameobj[item.name]
      else
        item.name = 0
    });
    return arr;
  }

  nameToCode(arr) {
    arr.forEach(item => {
      if(this.nameToCodeObj[item.name])
        item.name = this.nameToCodeObj[item.name];
      else
        item.name = 0;
    });
    return arr;
  }


  arrToObjUpdatingQuantites(arr : Array<any>){
    let obj: any = {};
    arr.forEach(ele => {
      if(obj[ele.name]){
        if(ele.quantity)
          obj[ele.name].quantity += ele.quantity
        if(ele.receivedQty)
          obj[ele.name].receivedQty += ele.receivedQty
      }
      else
      obj[ele.name] = ele;
    });
    return this.objToArr(obj);
  }

  filterArrByPropVal (obj){
    let arr = obj.arr;
    if(obj.val === "ALL")
      return arr;
    return arr.filter(item => item[obj.key1][obj.key2] === obj.val)
  }

  getUniqueItems(arr){
    return arr.filter( (item,index,self) => self.findIndex(itm => itm.name===item.name) === index)
  }

  getPageSizes(arr){
    // let pageSizes = [10,20]
    let pageSizes = [10]

    let arrLen = arr.length
    let i = 1
    while(i < this.truncateNew(arrLen/50)){
      pageSizes.push(50*i)
      i++
    }
    pageSizes.push(arr.length)
    return pageSizes
  }

  bottleWeightToCapacityConversion(obj){
    let density = ((obj.fullWeight - obj.emptyWeight) / (obj.packageQty * 1000))
    let return_weight = (obj.currentWeight - (obj.emptyWeight/1000 * obj.numOfOpenPkg))*1000 / density
    return (return_weight)
  }

  validateNumericField(event){ 
    return (event.charCode != 45)
  }

  getApprovalStatus(element){
    let flag = true
    if(element['approvalDetail'] != null){
      for(let level of Object.entries(element['approvalDetail'])){
        if(level[1] == 'pending'){
              element['approvalStatus'] = level[0] + ' Pending'
              flag = false
              break;
            }
        else if(level[1] == 'rejected'){
          element['approvalStatus'] = 'Rejected'
          flag = false
          break;
        }    
      }
      if(flag){
        element['approvalStatus'] = 'Approved'
      }
    }
    else{
      element['approvalStatus'] = 'NA'
    }
  }


  showSuccess(message,title, duration) {
     title = 'X';
    this.snackBar.open(message, title, {
      duration: duration,
      verticalPosition: 'top',
      horizontalPosition: 'right',
      panelClass: 'snackbar-show'
    })
  }

showError(message, title, duration) {
     title = 'X';
     this.snackBar.open(message, title, {
    duration: duration,
    verticalPosition: 'top',
    horizontalPosition: 'right',
    panelClass: 'snackbar-show'
    
  })
}

  showInfo(message, title,duration) {
     title = 'X';
    this.snackBar.open(message, title, {
      duration: duration,
      verticalPosition: 'top',
      horizontalPosition: 'right',
      panelClass: 'snackbar-show'
    })
  }

  // dateCorrection(date){
  //   console.log("🚀 ~ UtilsService ~ dateCorrection ~ date:", date)
  //   const changedDate = new Date(date);
  //   console.log("🚀 ~ UtilsService ~ dateCorrection ~ changedDate:", changedDate)
  //   changedDate.setHours(changedDate.getHours() + 5, changedDate.getMinutes() + 30);
  //   return changedDate.toISOString()
  // }


  dateCorrection(date) {
    
    // Create a new Date object from the input date
    const changedDate = new Date(date);

    // Get the time zone offset in minutes and convert it to milliseconds
    const timeZoneOffset = changedDate.getTimezoneOffset() * 60000;

    // Adjust the date for the time zone offset
    const adjustedDate = new Date(changedDate.getTime() - timeZoneOffset);

    // // Add 5 hours and 30 minutes to the adjusted date
    // adjustedDate.setHours(adjustedDate.getHours() + 5, adjustedDate.getMinutes() + 30);

    // Return the date in ISO string format
    return adjustedDate.toISOString();
}

  // Note: This is only applicable for timestamps created by the backend.  
  timesStampReduction(date){
    const changedDate = new Date(date);
    changedDate.setHours(changedDate.getHours() - 5, changedDate.getMinutes() - 30);
    return changedDate.toISOString()
  }


  formatDateToUTC(dateString: string | number | Date, time=undefined): string {
    const date = new Date(dateString);

    if (time){
      return new Intl.DateTimeFormat('en-US', {
        weekday: 'long', // EEEE
        month: 'long',   // MMMM
        day: 'numeric',  // d
        year: 'numeric', // y
        timeZone: 'UTC'  // Ensure it's UTC
      }).format(date);
    } else {
      return new Intl.DateTimeFormat('en-US', {
        weekday: 'long', // EEEE
        month: 'long',   // MMMM
        day: 'numeric',  // d
        year: 'numeric', // y
        hour: 'numeric', // h
        minute: '2-digit', // mm
        hour12: true,    // a (AM/PM format)
        timeZone: 'UTC'  // Ensure it's UTC
      }).format(date);

    }
  }

  truncateNew(number, precision = 3) {
    try {
        if (typeof number === 'string') {
            number = parseFloat(number);
        }
        // if (isNaN(number)) {
        //     number = 0;
        // }
        if (isNaN(number) || number <= 0) {  
          return 0;
        }
        const factor = Math.pow(10, precision);
        const truncatedNumber = Math.trunc(number * factor) / factor;
        return Math.floor(truncatedNumber * Math.pow(10, precision)) / Math.pow(10, precision);
    } catch (error) {
        console.error('Error occurred in truncateNew:', error);
        return 0;
    }
}


}
