<div class="title">
  <button mat-raised-button class="button" style="float: left; margin-left: 0px;" (click)=goBack()>
    <mat-icon>keyboard_backspace</mat-icon> Back to AdjustInv List
  </button>

  <button mat-button mat-raised-button class="button" (click)="exportToExcel()" style="float: right;">
    Export
  </button>
  <!-- <button mat-button mat-raised-button class="button" (click)="printToPdf()" style="float: right;">
    Print
  </button> -->
</div>
<div class="search-table-input fieldcontainer">
  <div class="fieldbox">
    <label> Id# </label> <span class="label5">{{ adjInv?.adjustId }}</span>
  </div>
  <div class="fieldbox4-a">
    <label style="font-size: 16px;">WorkArea </label>
    <span class="label5">{{ adjInv.workArea }}</span>
  </div>
  <div class="fieldbox4-a">
    <label style="font-size: 16px;"> Date </label>
    <span class="label5">{{ adjInv.createTs | date: "EEEE, MMMM d, y" }}</span>
  </div>
</div>

<mat-card class="matcontent">
  <table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort>
    <ng-container matColumnDef="index">
      <th mat-header-cell *matHeaderCellDef class="tableId"><b>#</b></th>
      <td mat-cell *matCellDef="let element; let i = index" class="name-cell tableId">
        {{ i + 1 }}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="itemName" sticky>
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell">
        <b> Item Name</b>
      </th>
      <td mat-cell *matCellDef="let element" class="name-cell">
        {{ element.itemName | titlecase }}
      </td>
      <td mat-footer-cell *matFooterCellDef>Total</td>
    </ng-container>

    <ng-container matColumnDef="pkgName">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Pkg Name</b>
      </th>
      <td mat-cell *matCellDef="let element">
        <ng-container *ngIf='element.packageName != null'>
          {{element.packageName | titlecase}}
        </ng-container>
        <ng-container *ngIf='element.packageName == null'>
          {{ element.uom | titlecase}}
        </ng-container>
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="entryType">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Entry Type</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ element.entryType |titlecase}}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="adjustType">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Adjust Type</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ element.adjustType |titlecase}}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="unitPrice">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b> WAC(incl.tax,etc) </b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ this.utils.truncateNew(element.price,2)}}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="totalPrice">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b> Total Price</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ this.utils.truncateNew((element.price * element.adjustQty),2)}}
      </td>
      <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getTotal(),2)}}</td>
    </ng-container>

    <ng-container matColumnDef="adjustQty">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b> Adjust Qty</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ this.utils.truncateNew(element.adjustQty)}}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="reason">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b> Reason </b>
      </th>
      <td mat-cell *matCellDef="let element">
        <textarea matInput rows="3" maxlength="200" wrap="soft" cols="50" [(ngModel)]=element.reason
          readonly></textarea>

      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
    <tr mat-footer-row *matFooterRowDef="displayedColumns"></tr>
  </table>

  <mat-card-actions> </mat-card-actions>
</mat-card>