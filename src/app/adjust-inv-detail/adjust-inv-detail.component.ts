import { Component, OnInit } from '@angular/core';
import { GlobalsService } from '../_services/globals.service';
import { AuthService, PurchasesService } from '../_services/';
import { MatTableDataSource, MatPaginator, MatSort } from '@angular/material';
import { ShareDataService } from '../_services/share-data.service';
import { RtsItem } from '../_models';
import { Location } from '@angular/common';
import { NotificationService } from '../_services/notification.service';
import { UtilsService } from '../_utils/utils.service';
@Component({
  selector: 'app-adjust-inv-detail',
  templateUrl: './adjust-inv-detail.component.html',
  styleUrls: ['./adjust-inv-detail.component.scss', "./../../common-dark.scss"]
})
export class AdjustInvDetailComponent implements OnInit {
  adjInv: any
  displayedColumns: any
  dataSource: MatTableDataSource<any>;
  disableRts: any = false

  constructor(private shareData: ShareDataService, private auth: AuthService,private notifyService: NotificationService,
    private purchases: PurchasesService,
    private loc: Location,
    private utils: UtilsService,
    ) { }

  ngOnInit() {
     this.shareData.currAdjInv.subscribe(
      values => {
        let adjInv = values
        if (!adjInv.adjustId)
          this.loc.back()
        else {
          this.adjInv = adjInv
          this.dataSource = new MatTableDataSource<RtsItem>();
          this.dataSource.data = this.adjInv.adjustInvItems;
          this.displayedColumns = GlobalsService.adjustInvDetailCol
        }
      })
  }

  goBack() {
    this.loc.back()
  }

  getTotal() {
    let totalPrice = 0
    this.dataSource.data.forEach(element => {
      totalPrice += element.adjustQty * element.price
    });
    return totalPrice
  }

  printToPdf() {
    let inventoryList = {};
    inventoryList['inventoryItems'] = [];
    inventoryList['inventoryItems'] = this.dataSource.data
    if (inventoryList['inventoryItems'].length == 0) {
      this.utils.snackBarShowWarning('please enter adjInv values');
      return;
    }
    inventoryList['user'] = this.auth.getCurrentUser();
    inventoryList['rtsListView'] = true
    inventoryList['status'] = this.adjInv.status
    inventoryList['recipientArea'] = this.adjInv.workArea
    inventoryList['adjustId'] = this.adjInv.adjustId
    this.purchases.printpdfs(inventoryList, 'Indent').subscribe(data => {
      // window.open('data:application/pdf;base64,' + data.eFile);
      this.purchases.globalPrintPdf(data.eFile);
    });
  }

  exportToExcel() {
    let inventoryList = {}
    inventoryList['indentArea'] = this.adjInv.indentArea;
    inventoryList['inventoryItems'] = this.dataSource.data;
    inventoryList['user'] = this.auth.getCurrentUser();
    this.purchases.exportToExcel(inventoryList, 'Indent').subscribe(data => {
      window.open('data:text/csv;base64,' + data.eFile);
    });
  }

}
