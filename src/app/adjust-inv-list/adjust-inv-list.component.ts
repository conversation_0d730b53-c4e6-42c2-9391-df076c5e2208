import { Component, OnInit, ViewChild } from '@angular/core';
import { GlobalsService } from '../_services/globals.service';
import { AuthService, BranchTransferService } from '../_services/';
import { MatTableDataSource, MatPaginator, MatSort } from '@angular/material';
import { UtilsService } from '../_utils/utils.service';
import { ShareDataService } from '../_services/share-data.service';
import { Router } from '@angular/router';
import { FormControl } from '@angular/forms';
import { IndentItem } from '../_models/';
import { NotificationService } from '../_services/notification.service';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material';
import { MomentDateModule, MomentDateAdapter } from '@angular/material-moment-adapter';

export const MY_FORMATS = {
  parse: {
      dateInput: 'LL'
  },
  display: {
      dateInput: 'DD-MM-YYYY',
      monthYearLabel: 'YYYY',
      dateA11yLabel: 'LL',
      monthYearA11yLabel: 'YYYY'
  }
};
@Component({
  selector: 'app-adjust-inv-list',
  templateUrl: './adjust-inv-list.component.html',
  styleUrls: ['./adjust-inv-list.component.scss', "./../../common-dark.scss"],
  providers:[{ provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
            { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }
          ]
})
export class AdjustInvListComponent implements OnInit {

  user: any;
  @ViewChild(MatPaginator) set paginator(value: MatPaginator) {
    if (this.dataSource) {
      this.dataSource.paginator = value;
    }
  };
  pageSizes = []
  multiBranchUser: any
  restaurantId: any
  branchSelected: any
  adjustInvListFlag: boolean
  adjustInvListUrl = encodeURI(GlobalsService.adjustInventoryList)
  dataSource: MatTableDataSource<IndentItem>;
  displayedColumns: any = GlobalsService.adjustInvListColoumns
  private startDate = new FormControl();
  private endDate = new FormControl();

  constructor(
    private notifyService: NotificationService,
    private auth: AuthService, private branchTransfer: BranchTransferService,
    private utils: UtilsService, private router: Router,
    private sharedData: ShareDataService) { }

  ngOnInit() {
    this.adjustInvListFlag = this.router.url.includes(this.adjustInvListUrl);
    this.user = this.auth.getCurrentUser();
    this.multiBranchUser = this.user.multiBranchUser
    if (!this.user.multiBranchUser) {
      this.restaurantId = this.user.restaurantId
      let obj = {
        tenantId: this.user.tenantId,
        restaurantId: this.restaurantId,
        userEmail: this.user.email,
        adjustInvListFlag: this.adjustInvListFlag
      }
      this.getAdjInvList(obj)
    }

  }

  getAdjInvList(obj) {
    this.branchTransfer.getAdjInvList(obj).subscribe(data => {
      if (data) {
        this.dataSource = new MatTableDataSource<IndentItem>();
        this.dataSource.data = data
        this.pageSizes = this.utils.getPageSizes(this.dataSource.data )
        this.dataSource.paginator = this.paginator;
      }
    });

  }

  filterByBranch(restId) {
    this.restaurantId = restId;
    this.branchSelected = true
    let obj = {
      tenantId: this.user.tenantId,
      restaurantId: this.restaurantId,
      userEmail: this.user.email,
      adjustInvListFlag: this.adjustInvListFlag
    }
    this.getAdjInvList(obj)
  }

  detailedAdjInv(obj) {
    this.sharedData.changeAdjInv(obj);
    this.router.navigate(['/home/<USER>']);

  }

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }

  filterByDate() {
    if (this.startDate.value && this.endDate.value) {
      let obj = {
        tenantId: this.user.tenantId,
        restaurantId: this.restaurantId,
        startDate: this.utils.dateCorrection(this.startDate.value),
        endDate: this.utils.dateCorrection(this.endDate.value),
        userEmail: this.user.email,
        adjustInvListFlag: this.adjustInvListFlag
      }
      this.getAdjInvList(obj)

    }
    else {
      this.utils.snackBarShowWarning('Please select start date and end date');
    }
  }

}
