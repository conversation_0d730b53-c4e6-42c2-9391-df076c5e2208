.typeButton{
    background-color: #41b158 !important;
    color: black !important;
    justify-content: center !important;
    display: flex !important;
    height: 30px;
    width: 30px;
}

.myIcon {
  font-size: 20px;
  margin-top: -6px;
}

input[type=number]::-webkit-inner-spin-button, 
input[type=number]::-webkit-outer-spin-button { 
  -webkit-appearance: none; 
}

input[type=number] {
  -moz-appearance: textfield;
}

.clrButton{
  margin-top: 38px;
  margin-bottom: 11px;
 }

.uIbutton {
  float: right !important;
}

.uIbutton:disabled {
  background-color:  #c2c2a3 !important;
  color: black !important;
}

.exprbutton {
  float: right;
}

.togglebutton{
  float: right;
  margin-top: 30px;
}

.amountValue{
  color: #ffffff;
  font-size: 20px;
  font-style: bold;
  float: right;
  margin-top: 35px;
}

.requiredField {
  color: #ff0000;
}

textarea {
  padding-top: 15px !important;
}

.example-container-1{
  max-height: 460px;
  overflow-y: auto;
}


.refreshBtnForAI{
  position: absolute;
  margin-top: 35px !important;
}

textarea.mat-input-element {
  padding: 0 !important;
  margin: 10px 0 !important;
}

td.mat-cell:last-of-type, td.mat-footer-cell:last-of-type, th.mat-header-cell:last-of-type {
  padding-right: 10px !important;
}