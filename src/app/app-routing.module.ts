import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { LoginLayoutComponent } from '../app/login-layout/login-layout.component';
import { LoginComponent } from '../app/login/login.component';
import { HomeLayoutComponent } from '../app/home-layout/home-layout.component';
import { HomeComponent } from '../app/home/<USER>';
import { ProductionPlanningComponent } from './production-planning/production-planning.component';
import { ForecastReportComponent } from './forecast-report/forecast-report.component';
import { PurchaseListComponent } from './purchase-list/purchase-list.component';
import { AuthGuard } from './_guards/';
import { LoginGuard } from './_guards/';
import { GlobalsService } from './_services'
import { PageNotFoundComponent } from './page-not-found/page-not-found.component'
import { PurchaseOrdersComponent } from './purchase-orders/purchase-orders.component';
import { VendorPurchaseOrdersComponent } from './vendor-purchase-orders/vendor-purchase-orders.component';
import { CreatePurchaseOrderComponent } from './create-purchase-order/create-purchase-order.component';
import { ReceiveOrderComponent } from './receive-order/receive-order.component';
import { PurchaseRequestsComponent } from './purchase-requests/purchase-requests.component';
import { DetailedPrComponent } from './detailed-pr/detailed-pr.component';
import { GrnComponent } from './grn/grn.component';
import { DetailedGrnComponent } from './detailed-grn/detailed-grn.component';
import { DetailedIbtComponent } from './detailed-ibt/detailed-ibt.component';
import { IbtComponent } from './ibt/ibt.component'
import { IbtsComponent } from './ibts/ibts.component'
import { InventoryListComponent } from './invenotry-list/invenotry-list.component'
import { IndentsComponent } from './indents/indents.component'
import { IssueIndentComponent } from './issue-indent/issue-indent.component'
import { IndentsDetailComponent } from './indents-detail/indents-detail.component'
import { IndentsListComponent } from './indents-list/indents-list.component'
import { IndentPredictionComponent } from './indent-prediction/indent-prediction.component'
import { InitiateRtvComponent } from './initiate-rtv/initiate-rtv.component'
import { RtvListComponent } from './rtv-list/rtv-list.component'
import { DetailedRtvComponent } from './detailed-rtv/detailed-rtv.component'
import { InitiateRtsComponent } from './initiate-rts/initiate-rts.component'
import { RtsListComponent } from './rts-list/rts-list.component'
import { RtsDetailComponent } from './rts-detail/rts-detail.component';
import { AdjustInventoryComponent } from './adjust-inventory/adjust-inventory.component';
import { AdjustInvListComponent } from './adjust-inv-list/adjust-inv-list.component';
import { AdjustInvDetailComponent } from './adjust-inv-detail/adjust-inv-detail.component';
import { PurchaseSettingComponent } from './purchase-setting/purchase-setting.component';
import { PurchaseApprovalComponent } from './purchase-approval/purchase-approval.component';
import { PoapprovalDetailComponent } from './poapproval-detail/poapproval-detail.component';
import { PythonScriptComponent } from './python-script/python-script.component';
import { IntraBranchComponent } from './intra-branch/intra-branch.component';
import { PurchaseInvoiceComponent } from './purchase-invoice/purchase-invoice.component';
import { DetailedPiComponent } from './detailed-pi/detailed-pi.component';
import { JobmonitorComponent } from './job-monitor/job-monitor.component';
import { CrmQrComponent } from './crm-qr/crm-qr.component';
import { EmailStatusComponent } from './email-status/email-status.component';
import { ClosingComponent } from './closing/closing.component';
import { PriceListComponent } from './price-list/price-list.component';
import { DetailedPlComponent } from './detailed-pl/detailed-pl.component';
import { PoApprovalComponent } from './po-approval/po-approval.component';
import { GrnapprovalDetailComponent } from './grnapproval-detail/grnapproval-detail.component';
import { SettingsComponent } from './settings/settings.component';
import { PostGrnApprovalComponent } from './post-grn-approval/post-grn-approval.component';
import { PostGrnApprovalDetailComponent } from './post-grn-approval-detail/post-grn-approval-detail.component';
import { IndentApprovalComponent } from './indent-approval/indent-approval.component';
import { IndentApprovalDetailComponent } from './indent-approval-detail/indent-approval-detail.component';
import { CreateRequisitionListComponent } from './create-requisition-list/create-requisition-list.component';
import { DetailedCreateRequisitionComponent } from './detailed-create-requisition/detailed-create-requisition.component';
import { PurchaseStatusComponent } from './purchase-status/purchase-status.component';
import { CreatePurchaseRequisitionComponent } from './create-purchase-requisition/create-purchase-requisition.component';
import { ConvertPoDialogComponent } from './_dialogs/convert-po-dialog/convert-po-dialog.component';
import { DirectIbtListComponent } from './direct-ibt-list/direct-ibt-list.component';
import { CsiApprovalComponent } from './csi-approval/csi-approval.component';
import { CsiApprovalDetailComponent } from './csi-approval-detail/csi-approval-detail.component';
import { StockConversionComponent } from './stock-conversion/stock-conversion.component';
import { StockConversionListComponent } from './stock-conversion-list/stock-conversion-list.component';
import { DetailedScListComponent } from './detailed-sc-list/detailed-sc-list.component';
import { PiApprovalListComponent } from './pi-approval-list/pi-approval-list.component';
import { TaxStructureComponent } from './tax-structure/tax-structure.component';
import { CreateUserControlComponent } from './create-user-control/create-user-control.component';
import { ReturnToStoreComponent } from './return-to-store/return-to-store.component';
import { SpoilageComponent } from './spoilage/spoilage.component';
import { LedgerDocumentsComponent } from './ledger-documents/ledger-documents.component';
import { DetailedLedgerComponent } from './detailed-ledger/detailed-ledger.component';
import { DetailedRtvListComponent } from './detailed-rtv-list/detailed-rtv-list.component';
import { DetailedRtvsComponent } from './detailed-rtvs/detailed-rtvs.component';
import { RtvsComponent } from './rtvs/rtvs.component';
import { IntraBranchListComponent } from './intra-branch-list/intra-branch-list.component';
import { DetailedIntraBranchComponent } from './detailed-intra-branch/detailed-intra-branch.component';

const routes: Routes = [
  {path: '', redirectTo: '/login', pathMatch: 'full'},
  {
    path: 'login', component: LoginLayoutComponent, canActivate: [LoginGuard],
    children: [
      { path: '', component: LoginComponent }
    ]
  },
  {
    path: 'home', component: HomeLayoutComponent, canActivate: [AuthGuard],
    children: [
      { path: '', redirectTo: 'welcome', pathMatch: 'full' },
      { path: 'welcome', component: HomeComponent },
      {
        path: GlobalsService.productionPlanning, component: ProductionPlanningComponent,
        data: { roles: [GlobalsService.superAdmin, GlobalsService.restaurantManager, GlobalsService.admin] }
      },
      {
        path: GlobalsService.forecastReport, component: ForecastReportComponent,
        data: { roles: [GlobalsService.superAdmin, GlobalsService.restaurantManager, GlobalsService.admin] }
      },
      {
        path: GlobalsService.listPurchases, component: PurchaseListComponent,
        data: { roles: [GlobalsService.superAdmin, GlobalsService.purchaseController, GlobalsService.admin] }
      },
      {
        path: GlobalsService.purchaseOrders, component: PurchaseOrdersComponent,
        data: { roles: [GlobalsService.superAdmin, GlobalsService.purchaseController, GlobalsService.vendorAdmin, GlobalsService.store] }
      },
      {
        path: GlobalsService.purchaseOrdersList, component: PurchaseOrdersComponent,
        data: { roles: [GlobalsService.superAdmin, GlobalsService.purchaseController, GlobalsService.vendorAdmin, GlobalsService.store] }
      },
      {
        path: GlobalsService.purchaseInvoice, component: PurchaseInvoiceComponent,
        data: { roles: [GlobalsService.superAdmin, GlobalsService.purchaseController, GlobalsService.vendorAdmin, GlobalsService.store] }
      },
      {
        path: GlobalsService.stockReceive, component: PurchaseOrdersComponent,
        data: { roles: [GlobalsService.superAdmin, GlobalsService.purchaseController, GlobalsService.vendorAdmin, GlobalsService.store] }
      },
      {
        path: GlobalsService.purchasheRequests, component: PurchaseRequestsComponent,
        data: { roles: [GlobalsService.superAdmin, GlobalsService.vendorAdmin, GlobalsService.admin, GlobalsService.purchaseController] }
      },
      {
        path: GlobalsService.purchasheRequestsList, component: PurchaseRequestsComponent
      },
      {
        path: GlobalsService.detailedPr, component: DetailedPrComponent,
        data: { roles: [GlobalsService.superAdmin, GlobalsService.vendorAdmin, GlobalsService.admin] }
      },
      {
        path: GlobalsService.vendorPurchaseOrders, component: VendorPurchaseOrdersComponent,
        data: { roles: [GlobalsService.superAdmin, GlobalsService.vendor, GlobalsService.admin] }
      },
      {
        path: GlobalsService.createPurchaseOrder, component: CreatePurchaseOrderComponent,
        data: { roles: [GlobalsService.superAdmin, GlobalsService.vendor, GlobalsService.admin] }
      },
      {
        path: GlobalsService.createPurchaseOrderUpdated, component: CreatePurchaseOrderComponent,
        data: { roles: [GlobalsService.superAdmin,GlobalsService.vendor, GlobalsService.admin] }
      },

      {
        path: GlobalsService.receivePurchaseOrder, component: ReceiveOrderComponent,
        data: { roles: [GlobalsService.superAdmin, GlobalsService.purchaseController, GlobalsService.admin] }
      },
      {
        path: GlobalsService.grns, component: GrnComponent,
        data: { roles: [GlobalsService.superAdmin, GlobalsService.purchaseController, GlobalsService.admin, GlobalsService.store] }
      },
      {
        path: GlobalsService.grnList, component: GrnComponent,
        data: { roles: [GlobalsService.superAdmin, GlobalsService.purchaseController, GlobalsService.admin, GlobalsService.store] }
      },
      {
        path: GlobalsService.detailedGrn, component: DetailedGrnComponent,
        data: { roles: [GlobalsService.superAdmin, GlobalsService.purchaseController, GlobalsService.admin, GlobalsService.store] }
      },
      {
        path: GlobalsService.detailedPi, component: DetailedPiComponent,
        data: { roles: [GlobalsService.superAdmin, GlobalsService.purchaseController, GlobalsService.admin, GlobalsService.store] }
      },
      {
        path: GlobalsService.detailedIbt, component: DetailedIbtComponent,
        data: { roles: [GlobalsService.superAdmin, GlobalsService.purchaseController, GlobalsService.admin, GlobalsService.store] }
      },
      {
        path: GlobalsService.ibt, component: IbtComponent,
        data: { roles: [GlobalsService.superAdmin, GlobalsService.purchaseController, GlobalsService.admin, GlobalsService.store] }
      },
      {
        path: GlobalsService.kitchenIndent, component: IssueIndentComponent,
        data: { roles: [GlobalsService.superAdmin, GlobalsService.purchaseController, GlobalsService.admin, GlobalsService.store] }
      },
      {
        path: GlobalsService.indents, component: IndentsComponent,
        data: { roles: [GlobalsService.superAdmin, GlobalsService.kitchenManager, GlobalsService.admin, GlobalsService.store, GlobalsService.kitchenManager] }
      },
      {
        path: GlobalsService.getIbts, component: IbtsComponent,
        data: { roles: [GlobalsService.superAdmin, GlobalsService.purchaseController, GlobalsService.admin, GlobalsService.store] }
      },
      {
        path: GlobalsService.CentralIndentList, component: IbtsComponent,
      },
      {
        path: GlobalsService.directIbts, component: DirectIbtListComponent,
      },
      {
        path: GlobalsService.getInventory, component: InventoryListComponent,
        data: { roles: [GlobalsService.superAdmin, GlobalsService.store] }
      },
      {
        path: GlobalsService.kitchenStock, component: InventoryListComponent,
        data: { roles: [GlobalsService.superAdmin, GlobalsService.store] }
      },
      {
        path: GlobalsService.intraBranchTransfer, component: IntraBranchComponent,
        data: { roles: [GlobalsService.superAdmin, GlobalsService.store] }
      },
      {
        path: GlobalsService.workAreaTransfer, component: IntraBranchComponent,
      },
      {
        path: GlobalsService.closingStock, component: ClosingComponent,
        data: { roles: [GlobalsService.superAdmin,GlobalsService.kitchenManager] }
      },
      {
        path: GlobalsService.storeclosing, component: ClosingComponent,
        data: { roles: [GlobalsService.superAdmin,GlobalsService.kitchenManager] }
      },
      {
        path: GlobalsService.transferClosing, component: ClosingComponent,
        data: { roles: [GlobalsService.superAdmin,GlobalsService.kitchenManager] }
      },
      {
        path: GlobalsService.stockClosure, component: ClosingComponent,
        data: { roles: [GlobalsService.superAdmin,GlobalsService.kitchenManager] }
      },
      {
        path: GlobalsService.workareaClosing, component: ClosingComponent,
        data: { roles: [GlobalsService.superAdmin,GlobalsService.kitchenManager] }
      },
      {
        path: GlobalsService.specialIndents, component: IssueIndentComponent,
        data: { roles: [GlobalsService.superAdmin, GlobalsService.admin, GlobalsService.store] }
      },
      {
        path: GlobalsService.storeIndents, component: IssueIndentComponent,
      },
      {
        path: GlobalsService.createStoreIndent, component: IssueIndentComponent,
      },
      {
        path: GlobalsService.returnToStore, component: ReturnToStoreComponent,
      },
      {
        path: GlobalsService.CreateStoreIndent, component: DirectIbtListComponent,
      },
      {
        path: GlobalsService.adjustInventory, component: AdjustInventoryComponent,
        data: { roles: [GlobalsService.superAdmin, GlobalsService.kitchenManager] }
      },
      {
        path: GlobalsService.indentsList, component: IndentsListComponent
      },
      {
        path: GlobalsService.storeIndentList, component: IbtComponent,
      },
      {
        path: GlobalsService.indentsdetail, component: IndentsDetailComponent
      },
      {
        path: GlobalsService.indentRequests, component: IndentsListComponent
      },
      {
        path: GlobalsService.indentPredictions, component: IndentPredictionComponent
      },
      {
        path: GlobalsService.initiateRtv, component: InitiateRtvComponent
      },
      {
        path: GlobalsService.rtvList, component: RtvsComponent
      },
      {
        path: GlobalsService.Rtvs, component: RtvsComponent
      },
      {
        path: GlobalsService.detailedRtv, component: DetailedRtvComponent
      },
      {
        path: GlobalsService.initiateRts, component: InitiateRtsComponent
      },
      {
        path: GlobalsService.rtsList, component: RtsListComponent
      },
      {
        path: GlobalsService.workAreaRts, component: RtsListComponent
      },
      {
        path: GlobalsService.rtsDetail, component: RtsDetailComponent
      },
      {
        path: GlobalsService.adjustInventoryList, component: AdjustInvListComponent
      },
      {
        path: GlobalsService.adjustInvRequests, component: AdjustInvListComponent
      },
      {
        path: GlobalsService.adjustInventoryDetail, component: AdjustInvDetailComponent
      },
      {
        path: GlobalsService.adjustWorkAreaInventory, component: AdjustInventoryComponent
      },
      {
        path: GlobalsService.purchaseSetting, component: PurchaseSettingComponent
      },
      {
        path: GlobalsService.poApproval, component: PoApprovalComponent
      },
      {
        path: GlobalsService.purchaseApproval, component: PurchaseApprovalComponent
      },
      {
        path: GlobalsService.purchaseIndentApproval, component: PurchaseApprovalComponent
      },
      {
        path: GlobalsService.purchaseApprovalDtl, component: PoapprovalDetailComponent
      },
      {
        path: GlobalsService.masterDataUpdate, component: PythonScriptComponent
      },
      {
        path: GlobalsService.jobmonitor, component: JobmonitorComponent,
      },
      {
        path: GlobalsService.qrGenerator, component: CrmQrComponent,
      },
      {
        path: GlobalsService.emailStatus, component: EmailStatusComponent,
        data: { roles: [GlobalsService.superAdmin,GlobalsService.store] }
      },
      {
        path: GlobalsService.PriceList, component: PriceListComponent
      },
      {
        path: GlobalsService.detailedPriceList, component: DetailedPlComponent
      },
      {
        path: GlobalsService.contract, component: PriceListComponent,
      },
      {
        path: GlobalsService.detailedPriceList, component: DetailedPlComponent
      }, 
      {
        path: GlobalsService.grnApproval, component: GrnapprovalDetailComponent
      },
      {
        path: GlobalsService.settings, component: SettingsComponent
      },
      {
        path: GlobalsService.postGrnApproval, component: PostGrnApprovalComponent,
      },
      {
        path: GlobalsService.postGrnApprovalDetail, component: PostGrnApprovalDetailComponent,
      },
      {
        path: GlobalsService.IndentApproval, component: IndentApprovalComponent,
      },
      {
        path: GlobalsService.piApproval, component: PiApprovalListComponent,
      },
      {
        path: GlobalsService.csiApproval, component: CsiApprovalComponent,
      },
      {
        path: GlobalsService.csiApprovalDetail, component: CsiApprovalDetailComponent,
      },
      {
        path: GlobalsService.IndentApprovalDetail, component: IndentApprovalDetailComponent,
      },
      {
        path: GlobalsService.CreateRequisitionList, component: CreateRequisitionListComponent,
      },
      {
        path: GlobalsService.DetailedCreateRequisition, component: DetailedCreateRequisitionComponent,
      },
      {
        path: GlobalsService.CreatePurchaseRequisition, component: CreatePurchaseRequisitionComponent,
      },
      {
        path: 'convert-po', component: ConvertPoDialogComponent,
      },
      {
        path: GlobalsService.PurchaseStatus, component: PurchaseStatusComponent,
      },
      {
        path: GlobalsService.StockConversion, component: StockConversionComponent,
      },
      {
        path: GlobalsService.StockConversionList, component: StockConversionListComponent,
      },
      {
        path: GlobalsService.DetailedStockConversionList, component: DetailedScListComponent,
      },
      {
        path: GlobalsService.taxStructure, component: TaxStructureComponent,
      },
      {
        path: GlobalsService.createUserControl, component: CreateUserControlComponent,
      },
      {
        path: GlobalsService.spoilage, component: SpoilageComponent,
      },
      {
        path: GlobalsService.spoilageList, component: LedgerDocumentsComponent,
      },
      {
        path: GlobalsService.detailedLedger, component: DetailedLedgerComponent,
      },
      {
        path: GlobalsService.detailedRtvList, component: DetailedRtvListComponent,
      },
      {
        path: GlobalsService.detailedRtvs, component: DetailedRtvsComponent,
      },
      {
        path: GlobalsService.intraBranchList, component: IntraBranchListComponent,
      },
      {
        path: GlobalsService.detailedIntraBranch, component: DetailedIntraBranchComponent,
      },
    ]
  },
  { path: GlobalsService.ibt, component: IbtComponent },
  { path: '**', component: PageNotFoundComponent }

];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
