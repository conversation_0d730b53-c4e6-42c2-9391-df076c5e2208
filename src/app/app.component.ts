import { Component, OnInit} from '@angular/core';
import { takeUntil } from 'rxjs/operators';
import { VERSION } from 'src/version';
import { AuthService } from './_services';
import { TimeOutService } from './_services/time-out.service';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit {
  version = VERSION;
  private destroyed$ = new Subject<void>();
  
  constructor(private sessionTimeoutService: TimeOutService, private authService: AuthService) {}

  ngOnInit() {
    console.log(`Version ${this.version.major}.${this.version.minor}.${this.version.patch}`);
    this.sessionTimeoutService.logoutTimer
    .pipe(takeUntil(this.destroyed$))
    .subscribe((timeLeft) => {
      if (timeLeft === 0) {
        this.authService.logout();
      }
    });
  }


  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }
}

