$primary: #3586ca;

.empty_select {
    margin: 35% auto;
    position: relative;
    opacity: 0.55;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.14;
    text-align: center;
    color: #ffffff;
      align-items: center;
      justify-content: center;
  }
  
  .containerout {
    background-color: #292929 !important;
    padding-top: 0px !important;
    padding-bottom: 0px !important;
    margin-bottom: 20px !important;
    ::ng-deep .mat-content {
      display: block !important;
    }
  }
  
  .progressbar {
    width: 225px;
    height: 20px;
    border-radius: 20px;
    border: 1px solid #191919;
    ::ng-deep .mat-progress-bar-buffer {
      background-color: #292929 !important;
    }
  
    ::ng-deep .mat-progress-bar-fill::after {
      border-radius: 20px !important;
      background-color: $primary !important;
    }
  }
  
  .progressbar_small {
    border-radius: 20px;
    height: 8px;
    width: 50px !important;
    margin-top: -13px !important;
    margin-left: 42% !important;
    ::ng-deep .mat-progress-bar-buffer {
      background-color: #191919 !important;
      border: 1px solid #191919;
    }
  
    ::ng-deep .mat-progress-bar-fill::after {
      border-radius: 20px !important;
      background-color: $primary !important;
    }
  }

  .success_text {
    color: #66bb6a;
    font-size: 10px;
    float: right !important;
    margin-left: 5px;
  }

  
  .fade_text {
    color: #ffffff;
    font-size: 10px;
    float: right !important;
    // margin-left: 5px;
  }

  .left {
    float: left;
    vertical-align: top;
    width: 40%;
  }
  
  .right {
    float: left;
    vertical-align: top;
    width: 60%;
    border-left: 1px solid #191919;
  }
  
  .subcat-container {
    padding: 0px;
    opacity: 0.8;
    float: left;
  }
  
  .subcat-box {
    width: 150px;
    height: 150px;
    background-color: #232323;
    padding: 5px 0px 5px 0px;
    margin-bottom: 13px;
    margin-right: 13px;
    ::ng-deep .mat-list-item-content {
      display: block !important;
    }
  }
  .subcat-box2 {
    width: 150px;
    height: 150px;
    background-color: #191919;
    padding: 5px 0px 5px 0px;
    margin-bottom: 13px;
    margin-right: 13px;
    ::ng-deep .mat-list-item-content {
      display: block !important;
    }
  }

  .ellipse {
    margin: 0 auto;
    width: 44px;
    height: 44px;
    background-color: $primary !important;
    margin: 0 auto;
    margin-top: 12px;
    padding: 5px;
    border-radius: 50%;
  }
  .ellipse1 {
    margin: 0 auto;
    width: 44px;
    height: 44px;
    margin: 0 auto;
    margin-top: 12px;
    padding: 2px;
    border: 1px solid red;
    border-radius: 25px;
  }
  .ellipse2 {
    margin: 0 auto;
    width: 44px;
    height: 44px;
    margin: 0 auto;
    margin-top: 12px;
    padding: 2px;
    border: 1px solid green;
    border-radius: 25px;
  }
  .arrow {
    text-align: center;
    margin: 0px auto;
    display: block;
    margin-top: 9px;
  }
  .arrow1 {
    text-align: center;
    margin: 0px auto;
    display: block;
    margin-top: 9px;
    color: red !important;
  }
  .arrow2 {
    text-align: center;
    margin: 0px auto;
    display: block;
    margin-top: 9px;
    color: green !important;
  }
  
  .inner_heading {
    font-size: 12px;
    margin: 18px auto;
    text-align: center;
  }
  .inner_heading2 {
    font-size: 9px;
    margin: 10px auto;
    text-align: center;
  }
  
  .mat-expansion-panel {
    background-color: #292929 !important;
    box-shadow: none !important;
    height: 100% !important;
    ::ng-deep .mat-expansion-panel-body {
      padding: 0 0 0 16px !important;
    }
  }
  .mat-expansion-panel-header {
    height: 40px !important;
    border-radius: 0 !important;
    border-bottom: 1px solid #191919 !important;
    padding-left: 16px;
  }
  
  .panel-header {
    height: 60px !important;
  }
  
  .catagory {
    ::ng-deep .mat-list-item-content {
      display: block !important;
      // padding-top: 10px;
      padding-left: 0px !important;
      font-size: 12px !important;
      // overflow-y: auto;
      max-height: auto;
    }
  }
  
  .catagory a {
    height: 36px;
    padding-left: 6px;
    margin-top: 5px;
    margin-bottom: 5px;
  }

  .cat {
    margin-top: 5px;
    font-size: 14px !important;
  }
  .inlspn5 {
    font-size: 13px !important;
    // opacity: 0.55;
    font-weight: normal;
  }
  .cat1 {
    margin-left: 18px;
    font-size: 14px !important;
    color: $primary  !important;
  }
  
  .left-body {
    height: 45%;
  }
  
  form #decrease {
    border-radius: 8px 0 0 8px;
  }
  
  form #increase {
    border-radius: 0 8px 8px 0;
  }
  
  form #input-wrap {
    margin: 0px;
    padding: 0px;
  }
  
  input[type="number"]::-webkit-inner-spin-button,
  input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  .btn-info {
    width: 150px;
    height: 40px;
    border-radius: 4px;
    background-color:$primary !important;
    float: right !important;
    margin-top: 20px;
  }
  .stockheading {
    height: 40px;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0 24px;
    border-radius: inherit;
    color: white;
    opacity: 0.55;
    font-size: 14px;
    border-bottom: 1px solid #191919;
  }
  .stockbody {
    padding-left: 15px;
    padding-right: 15px;
    padding-top: 15px;
    ::ng-deep .mat-grid-list:nth-child(event) {
      background-color: #363636 !important;
    }
    ::ng-deep .mat-grid-list:nth-child(odd),
    ::ng-deep .mat-grid-list:not(:first-child) {
      background-color: #464646 !important;
    }
    ::ng-deep .mat-grid-list:first-child {
      border-top: none;
      color: #ffffff;
      background-color: #191919 !important;
      padding-left: 10px !important;
    }
    ::ng-deep .mat-grid-tile .mat-figure {
      font-size: 12px !important;
      justify-content: left !important;
      padding-left: 10px !important;
      border-left: 1px solid #262626;
    }
  }
  
  input[type=number]::-webkit-inner-spin-button, 
  input[type=number]::-webkit-outer-spin-button { 
    -webkit-appearance: none; 
  }
  
  input[type=number] {
    -moz-appearance: textfield;
  }
  
  .scrollView {
    height: 432px;
  }

  #outer{
    width:100%;
    text-align: center;
  }
  
  .inner{
    display: inline-block;
    padding: 0px 10px 0px 10px;
  }
  
  .each{
    padding: 0% 1%;
  }
  
  .table-option-div{
    text-align: -webkit-center;
  }

  .mat-stepper-horizontal, .mat-stepper-vertical {
    display: block;
    margin: 1% !important;
}

.icon_col{
  margin-top: 2%;
  text-align: right;
  height: 41px;
}

// .button{
//   margin-left: 5px;
// }

.mat-form-field-suffix {
  bottom: 0px !important;
}

.icon_btn_col{
  margin-top: 1.6em;
  display: flex;
  height: 41px;
  justify-content: flex-end;
}

.first_four_inputs{
  margin-left: 5px;
}

.status_inputs{
  float: left;
}

::ng-deep .mat-step-header .mat-step-icon {
  display: none !important;
}

::ng-deep  .mat-step-header .mat-step-label.mat-step-label-active {
  color: $primary !important;
  font-weight: bolder;
  font-size: x-large;
  margin: 20px;
}

::ng-deep .mat-tab-label {
  width: -webkit-fill-available !important;
  height: 48px;
  padding: 0 24px;
  cursor: pointer;
  box-sizing: border-box;
  opacity: .6;
  min-width: 160px;
  text-align: center;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  white-space: nowrap;
  position: relative;
}

.check_circle{
  color:green;
  font-size: 15px;
}

::ng-deep .selectItem{
  margin-top: 1px !important;
  margin-left: 18px !important;
}

.inputAndButtons{
  margin-right: 1.4em;
  margin-left: 1.5em;
}

.three_inputs{
  margin-bottom: 10px;
}

.fourButtons{
  margin-top: 40px;
  float: right;
}

.loader{
    // text-align: center !important;
    // margin-top: 151px;
    display: flex !important;
    margin: auto !important;
}

  div .mat-spinner{
    // text-align: center;
    // margin-left: 37%;
    display: flex;
    margin: auto;
  }
  
.refreshBtnclosing{
	float : right;
  margin-top: 44px;
  }

  .statusInputDiv{
    margin-top: -28px;
  }

  .closeMsg{
    text-align: center;
    padding-top: 90px;
  }

  .closeMsgBtn{
    text-align: center;
  }

  .matCardForClosing{
    height: 230px;
  }

  .closingContainer{
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 92vh;
  }

  .closingContainerDatas{
    max-width: 80vw;
    pointer-events: auto;
    width: 550px;
    position: static;
  }

  .closingLoadingContainer{
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .closingLoadingContainerDatas{
    max-width: 80vw;
    pointer-events: auto;
    width: 460px;
    position: static;
  }

  .cloasingLabel{
    color: $primary !important;
    margin-bottom: 1px !important;
    margin-top: 0px;
  }

  .CloseBtn{
    float: right;
    margin-bottom: -1px;
  }

  .errorDialog{
    max-height: 75vh;
    overflow: auto;
}
  
.errorData{
    font-size: medium;
    margin: 15px;
}

.errorCheckIcon{
    margin-left: 8px;
}

.errormsg{
    color: crimson;
    font-size: 13px;
    margin: 25px;
    background: #1f1f1f;
}

  .cancelIcon{
    color: crimson !important;
  }
  
  .checkIcon{
    color: green !important;
  }

  .checkBoxClass{
    display: flex;
    // flex-direction: column;
    gap: 14px;
    // margin: 20px 0px;
    justify-content: center;
  }

  .topItemclass{
    display: flex;
    align-items: baseline;
    gap: 30px;
    margin: 20px 0px;
    padding: 5px 20px 5px 10px;
    border-radius: 5px;
    cursor: grab;
  }

  .topItemclass:hover{
    background: #373737;
  }

  .fade_text1{
    color: #ffffff;
    font-size: 10px; 
  }
  
  .success_text1 {
    color: #66bb6a;
    font-size: 10px;
    margin-left: 5px;
  }

  .progressbar_small1{
    border-radius: 20px;
    height: 8px;
    width: 200px !important;
    margin-top: -13px !important;
    // margin-left: 42% !important;
    ::ng-deep .mat-progress-bar-buffer {
      background-color: #191919 !important;
      border: 1px solid #191919;
    }
  
    ::ng-deep .mat-progress-bar-fill::after {
      border-radius: 20px !important;
      background-color: $primary !important;
    }
  }
  .input-down {
    display: flex;
    border: 1px solid #ccc;
  }

  .input-box1{
    text-align: center;
    width: 65px;
    height: 25px;
    background: white;
    border: #191919;
    color: black;
    font-size: 12px !important;
  }

  .value-buttons{
    border: 1px solid #191919;
    width: 25px;
    height: 28px;
    color: white;
    background: #2f2f2f;
  }

  .iconDiv{
    margin-top: 5px;
    margin-left: 5px;
  }

  .custom-tooltip {
    position: absolute;
    background-color: #333;
    color: #fff;
    padding: 5px;
    border-radius: 4px;
    font-size: 14px;
    z-index: 1000; 
    width: 250px;
    left: 707px;
    top: 127px;
  }
  
  .custom-tooltip-Heading{
    width: 150px;
  }

  .subCatSearchInput{
    display: flex;
    width: 250px;
    height: 30px;
    padding: 10px;
    margin-top: 15px;
    margin-bottom: 15px;
    border-bottom: 1px solid grey;
  }

  .fullWidth{
    width: 100% !important;
  }

  .catLabel:hover {
    color: gray;
  }

.exportDialogContent{
  display: flex;
  // flex-direction: column;
  align-items: center;
  justify-content: center;
}

.dialogMsg{
	font-size: large;
}

.ipText{
  text-align: center;
  font-size: medium;
  // font-weight: bold;
  font-size: 16px !important;
  margin-bottom: 10px !important;
  margin-right: 10px !important;
  opacity: 0.9 !important;
}

::ng-deep .mat-radio-label-content {
  padding-left: 8px;
  margin-right: 10px;
  font-size: 15px;
  font-weight: bold;
  opacity: 0.9 !important;
}

.gap{
  padding-right: 10px; 
  padding-bottom: 30px; 
}
