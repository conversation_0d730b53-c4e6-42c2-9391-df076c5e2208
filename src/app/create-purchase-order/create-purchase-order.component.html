<div *ngIf="!isShowTemplate">
  <form [formGroup]="createPurchaseOrderForm">
    <div class="topItem">
      <mat-form-field appearance="none" class="mt-1">
        <mat-select formControlName="rest" placeholder="Restaurant" class="outline" (selectionChange)="setRestaurant()">
          <mat-option *ngFor="let rest of branchesData" [value]="rest">
            {{ rest.branchName }}
          </mat-option>
        </mat-select>
      </mat-form-field>

      <button mat-button (click)="createPr()" *ngIf="addItems && !purReq?.prId" [disabled]="dataSource.data.length == 0"
        class="button3 bottomButtons ml-2">
        {{ !res?.poApproval ? 'Create PO' : 'Create PO Approval' }}
        <!-- <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-send ml-2"
          viewBox="0 0 16 16">
          <path
            d="M15.854.146a.5.5 0 0 1 .11.54l-5.819 14.547a.75.75 0 0 1-1.329.124l-3.178-4.995L.643 7.184a.75.75 0 0 1 .124-1.33L15.314.037a.5.5 0 0 1 .54.11ZM6.636 10.07l2.761 4.338L14.13 2.576 6.636 10.07Zm6.787-8.201L1.591 6.602l4.339 2.76 7.494-7.493Z" />
        </svg> -->
      </button>

      <button mat-button (click)="createPr(true)" *ngIf="addItems" [disabled]="dataSource.data.length == 0"
        class="button3 bottomButtons ml-2">
        {{ purReq?.prId ? 'Update PR' : (!res?.prApproval ? 'Create PR' : 'Create PR Approval') }}
      </button>

      <!-- <button mat-button (click)="makePrTemplate()" *ngIf='(!tempName && addItems) && this.contractName === "nonContract"'
        [disabled]="dataSource.data.length == 0" class="button bottomButtons ml-2">
        Make Template
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-plus-circle"
          viewBox="0 0 16 16">
          <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z" />
          <path
            d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z" />
        </svg>
      </button> -->

      <!-- <button mat-button (click)="viewTemplate()" class="button bottomButtons ml-2" *ngIf="addItems && this.contractName === 'nonContract'">
        View Templates
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-eye"
          viewBox="0 0 16 16">
          <path
            d="M16 8s-3-5.5-8-5.5S0 8 0 8s3 5.5 8 5.5S16 8 16 8zM1.173 8a13.133 13.133 0 0 1 1.66-2.043C4.12 4.668 5.88 3.5 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13.133 13.133 0 0 1 14.828 8c-.058.087-.122.183-.195.288-.335.48-.83 1.12-1.465 1.755C11.879 11.332 10.119 12.5 8 12.5c-2.12 0-3.879-1.168-5.168-2.457A13.134 13.134 0 0 1 1.172 8z" />
          <path d="M8 5.5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5zM4.5 8a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0z" />
        </svg>
      </button> -->
<!-- 
      <button mat-button (click)="updateTemplate()" class="button bottomButtons ml-2" *ngIf='tempName && addItems'>
        Update Template
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-repeat"
          viewBox="0 0 16 16">
          <path
            d="M11.534 7h3.932a.25.25 0 0 1 .192.41l-1.966 2.36a.25.25 0 0 1-.384 0l-1.966-2.36a.25.25 0 0 1 .192-.41zm-11 2h3.932a.25.25 0 0 0 .192-.41L2.692 6.23a.25.25 0 0 0-.384 0L.342 8.59A.25.25 0 0 0 .534 9z" />
          <path fill-rule="evenodd"
            d="M8 3c-1.552 0-2.94.707-3.857 1.818a.5.5 0 1 1-.771-.636A6.002 6.002 0 0 1 13.917 7H12.9A5.002 5.002 0 0 0 8 3zM3.1 9a5.002 5.002 0 0 0 8.757 2.182.5.5 0 1 1 .771.636A6.002 6.002 0 0 1 2.083 9H3.1z" />
        </svg>
      </button> -->
    </div>

    <div *ngIf="addItems" class="search-table-input fieldcontainer" id="addItemForm">
      <mat-form-field appearance="none" class="firstCard">
        <label>Vendor</label>
        <mat-select placeholder="Vendor" formControlName="vendor" class="outline"
          (selectionChange)="selectVendor($event)" [(ngModel)]="vName" [ngModelOptions]="{standalone: true}">
          <mat-option>
            <ngx-mat-select-search placeholderLabel="Vendor Item..." noEntriesFoundLabel="'no Vendor Item found'"
              [formControl]="vendorFilterCtrl"></ngx-mat-select-search>
          </mat-option>
          <mat-option *ngFor="let vendor of vendorsBanks | async" [value]="vendor">
            {{ vendor.name }}
          </mat-option>
        </mat-select>
        <mat-error>
          Select a vendor
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="none" *ngIf='stockSeparation' class="firstCard">
        <label>Stock Type</label>
        <mat-select placeholder="Stock Type" formControlName="stockType" class="outline"
          (selectionChange)="stockChange($event.value)" [(ngModel)]="stockType" [ngModelOptions]="{standalone: true}">
          <mat-option value="Stockable">Stockable Items</mat-option>
          <mat-option value="Non-Stockable">Non-Stockable Items</mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field appearance="none" class="firstCard">
        <label>Select Category</label>
        <mat-select formControlName="category" [multiple]="true" #multiSelect placeholder="select Category"
          (selectionChange)="selectCategory($event.value)" class="outline" [(ngModel)]="category"
          [ngModelOptions]="{standalone: true}">
          <mat-option *ngFor="let cat of this.catItems" [value]="cat">
            {{ cat }}
          </mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field appearance="none" class="firstCard">
        <label>Item Type</label>
        <mat-select formControlName="itemType" placeholder="Item Type" class="outline"
          (selectionChange)="itemType($event)" [(ngModel)]="contractName" [ngModelOptions]="{standalone: true}">
          <mat-option value="contract">Contract Items</mat-option>
          <mat-option value="nonContract">Non-Contract Items</mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field appearance="none" *ngIf='stockSeparation && stockType == "Non-Stockable"' class="firstCard">
        <label>Work Area</label>
        <mat-select formControlName="workArea" multiple placeholder="select workArea" class="outline"
          [(ngModel)]="selectedWorkArea" [ngModelOptions]="{standalone: true}">
          <mat-option *ngFor="let area of workAreas" [value]="area" [required]="stockSeparation">
            {{ area }}
          </mat-option>
        </mat-select>
        <mat-error>
          Select workArea
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="none" *ngIf="addItems" class="firstCard">
        <label>Po Date</label>
        <input matInput [matDatepicker]="picker" class="outline" tabindex="-1" placeholder="Excpected Date"
          formControlName="expectedDate" readonly='true' />
        <mat-datepicker-toggle matSuffix [for]="picker" tabindex="-1">
          <mat-icon matDatepickerToggleIcon><img src="./../../assets/calender.png" /></mat-icon>
        </mat-datepicker-toggle>
        <mat-datepicker #picker disabled="true"></mat-datepicker>
        <mat-error>
          Select Po Date
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="none" *ngIf="addItems" class="firstCard">
        <label>Delivery Date</label>
        <input matInput class="outline" [matDatepicker]="picker1" tabindex="-1" placeholder="Validity Date"
          formControlName="validityDate" [(ngModel)]="delDate" [min]="startDate" />
        <mat-datepicker-toggle matSuffix [for]="picker1" tabindex="-1">
          <mat-icon matDatepickerToggleIcon><img src="./../../assets/calender.png" /></mat-icon>
        </mat-datepicker-toggle>
        <mat-datepicker #picker1 disabled="false"></mat-datepicker>
        <mat-error>
          Select Delivery Date
        </mat-error>
      </mat-form-field>

      <div style="margin-top: 15px; padding-right: 10px;">
        <span>Select Item Price History :</span>
        <mat-checkbox formControlName = "allVendorsChecked">All Vendors</mat-checkbox>
        <mat-checkbox formControlName = "currentVendorChecked">Current Vendor</mat-checkbox>
      </div>
    </div>
  </form>

  <div *ngIf="addItems" class="search-table-input fieldcontainer" id="addItemForm">
    <mat-expansion-panel>
      <mat-expansion-panel-header>
        <mat-panel-title class="topItemkey">
          Additional Details
        </mat-panel-title>
        <mat-panel-description>
          <i>Click Here </i> &nbsp;
          &nbsp; <mat-icon>add_shopping_cart</mat-icon>
        </mat-panel-description>
      </mat-expansion-panel-header>

      <div class="row expansionInnerContent">
        <div class="col">
          <mat-form-field appearance="outline" style="width: 210px !important">
            <mat-label style="font-size: larger; font-weight: bolder;"> <b>Remark</b> </mat-label>
            <textarea matInput [(ngModel)]="remarks"></textarea>
          </mat-form-field>
        </div>
        <div class="col">
          <mat-form-field appearance="outline" style="width: 210px !important">
            <mat-label style="font-size: larger; font-weight: bolder;"><b>Payment Terms</b></mat-label>
            <textarea matInput [(ngModel)]="paymentTerms"></textarea>
          </mat-form-field>
        </div>
        <div class="col">
          <mat-form-field appearance="outline" style="width: 210px !important">
            <mat-label style="font-size: larger; font-weight: bolder;"><b>Po Terms</b></mat-label>
            <textarea matInput [(ngModel)]="poTerms" #textInput (keydown)="onKeyDown($event)"></textarea>
          </mat-form-field>
        </div>
        <div class="col">
          <mat-form-field appearance="outline" style="width: 210px !important">
            <mat-label> <b>Payment Method</b> </mat-label>
            <mat-select [(value)]="paymentMethod" [(ngModel)]="paymentMethod">
              <mat-option *ngFor="let method of paymentMethods" [value]="method">
                {{method}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>
    </mat-expansion-panel>

    
    <form [formGroup]="addItemToPoForm">
      <div cdkTrapFocus (keydown.enter)="onEnterPressed()">
        <div>
          <mat-form-field appearance="none">
            <label>Item Name</label>
            <mat-select placeholder="Inventory Item" #singleSelect tabindex="0" #accTypeInventory
              (focus)="accTypeInventory.open()" class="outline" formControlName="inventoryItem"
              (selectionChange)="purchaseItemSelect($event)">
              <mat-option>
                <ngx-mat-select-search placeholderLabel="Inventory Item..."
                  noEntriesFoundLabel="'no Inventory Item found'"
                  [formControl]="bankFilterCtrl"></ngx-mat-select-search>
              </mat-option>
              <mat-option *ngFor="let item of filteredVendorItems | async" [value]="item">
                {{item.itemName}}
              </mat-option>
            </mat-select>
            <mat-error>
              Select an Item
            </mat-error>
          </mat-form-field>

          <mat-form-field *ngIf="showPkg" appearance="none" class="splOrderInputs">
            <label>Pkg Size</label>
            <mat-select #accTypepkgName (focus)="accTypepkgName.open()" focusOnInit placeholder="Pkg size"
              class="outline" formControlName="pkgName" (selectionChange)="pkgSelect($event)" [(ngModel)]="currentPKG">
              <mat-option *ngFor="let pkg of packagingSizes" [value]="pkg">
                {{pkg.packageName}}
              </mat-option>
            </mat-select>
            <mat-error>
              Select a package
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="none" class="splOrderInputs">
            <label>Item Code</label>
            <input matInput class="outline" type="text" placeholder="Item Code" formControlName="itemCode" disabled
              readonly />
          </mat-form-field>

          <mat-form-field appearance="none" class="splOrderInputs">
            <label>HSN Code</label>
            <input matInput class="outline" placeholder="HSN Code" formControlName="hsnCode" disabled readonly />
          </mat-form-field>

          <mat-form-field appearance="none" *ngIf="brand" class="splOrderInputs">
            <label>Brand</label>
            <input matInput class="outline" type="text" [(ngModel)]="brand" placeholder="Brand"
              formControlName="selectedBrand" [formControl]="selectedBrandControl" disabled readonly />
          </mat-form-field>

          <mat-form-field appearance="none" class="splOrderInputs">
            <label>In Stock</label>
            <input matInput class="outline"  placeholder="In Stock"
              formControlName="inStock" disabled readonly />
          </mat-form-field>

          <mat-form-field appearance="none" class="splOrderInputs">
            <label>Quantity</label>
            <input matInput type="number" min="0" class="outline" [(ngModel)]="quantity"
              (keyup)="getItemTotal()" (input)="checkNumericInput($event)" placeholder="Quantity" formControlName="orderQty"
              (focus)="focusFunction('orderQty')" (focusout)="focusOutFunction('orderQty')"
              [readonly]="!this.selectedPkg.packageName != 'N/A' && !this.selectedPkg.hasOwnProperty('packageName')">
              <!-- <input matInput type="number" min="0" class="outline" [(ngModel)]="quantity"
              (keyup)="getItemTotal()" placeholder="Quantity" formControlName="orderQty"
              (focus)="focusFunction('orderQty')" (focusout)="focusOutFunction('orderQty')"> -->
            <mat-error>
              Add Quantity
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="none" class="splOrderInputs" *ngIf="addItemToPoForm.get('contractDetails')?.value?.priceType && ['absolutePriceReduction', 'discountOnMRP'].includes(addItemToPoForm.get('contractDetails').value.priceType)">
            <label>MRP</label>
            <input matInput class="outline" placeholder="MRP"
              formControlName="convertedPrice" (focus)="focusFunction('convertedPrice')" (focusout)="focusOutFunction('convertedPrice')" (keyup)="setUnitPrice()"/>
          </mat-form-field>

          <mat-form-field appearance="none" style="max-width: 100px !important;">
            <label>Unit Cost</label>
            <input matInput type="number" step="0.01" min="0" class="outline"
            (keyup)="getItemTotal()" [(ngModel)]="unitCost"
            [matAutocomplete]="auto" placeholder="Unit Cost"
            [readonly]="contractName === 'contract' || !this.selectedPkg.packageName != 'N/A' && !this.selectedPkg.hasOwnProperty('packageName')"
            formControlName="unitPrice"
            [matTooltip]="isContract ? 'Contract price' : checkContractAccess?.priceType === 'fixedContractPrice' ? 'Fixed Contract Price' : ''"
            (focus)="focusFunction('unitPrice')" (focusout)="focusOutFunction('unitPrice')"
            [disabled]="contractName === 'contract'"
            [ngStyle]="{'opacity': contractName === 'contract' ? '0.5' : '1'}"
          />
              <mat-autocomplete #auto="matAutocomplete" class="autoComplete">
                <mat-grid-list cols="12" rowHeight="30px">
                  <mat-grid-tile colspan="2">GRN ID</mat-grid-tile>
                  <mat-grid-tile colspan="2">Date</mat-grid-tile>
                  <mat-grid-tile colspan="4">Vendor Name</mat-grid-tile>
                  <mat-grid-tile colspan="2">Pkg Name</mat-grid-tile>
                  <mat-grid-tile colspan="0.5">Rate</mat-grid-tile>
                  <mat-grid-tile colspan="0.5">Qty</mat-grid-tile>
                </mat-grid-list>
                <mat-divider [inset]="true"></mat-divider>
                <mat-option *ngFor="let val of filteredUnitPrice | async" [value]="val.unitPrice" 
                class="matOption" [disabled]="vendorName !== val.vendorName">
                  <mat-grid-list cols="12" rowHeight="20px">
                    <mat-grid-tile colspan="2">{{ val.grnId || '-' }}</mat-grid-tile>
                    <mat-grid-tile colspan="2">{{ val.createTs | date:'dd-MM-yyyy' || '-' }}</mat-grid-tile>
                    <mat-grid-tile colspan="4">{{ val.vendorName || '-' }}</mat-grid-tile>
                    <mat-grid-tile colspan="2">{{ val.packageName || '-' }}</mat-grid-tile>
                    <mat-grid-tile colspan="0.5">{{ val.unitPrice || '-' }}</mat-grid-tile>
                    <mat-grid-tile colspan="0.5">{{ val.quantity || '-' }}</mat-grid-tile>
                  </mat-grid-list>
                </mat-option>
              </mat-autocomplete>

            <div class="loaderSpinDiv" *ngIf="soLoader">
              <div class="spinner-border" role="status">
                <span class="sr-only">Loading...</span>
              </div>
            </div>
            <mat-error>
              Unit Price
            </mat-error>
          </mat-form-field>

          <div class="iconDiv" *ngIf = "this.contractName == 'contract'">
            <svg (click)="openToolTip()" style="cursor: grab;" xmlns="http://www.w3.org/2000/svg" width="21" height="21"
              fill="currentColor" class="bi bi-calculator calIcon" viewBox="0 0 16 16">
              <path
                d="M12 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1zM4 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2z" />
              <path
                d="M4 2.5a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-.5.5h-7a.5.5 0 0 1-.5-.5zm0 4a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5zm0 3a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5zm0 3a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5zm3-6a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5zm0 3a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5zm0 3a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5zm3-6a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5zm0 3a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5z" />
            </svg>
          </div>
          <mat-form-field appearance="none" class="splOrderInputs">
            <label>Tax Rate</label>
            <input matInput step="0.01" min="0" class="outline" autocomplete="off" [matAutocomplete]="auto1"
              (keyup)="taxRateChange()" [(ngModel)]="taxrate" placeholder="Tax rate"
              (keyup.enter)="addOption();" formControlName="taxRate"
              (focus)="focusFunction('taxRate')" (focusout)="focusOutFunction('taxRate')" />
            <mat-autocomplete #auto1="matAutocomplete" (optionSelected)="optionSelected($event.option)">
              <mat-option *ngFor="let tax of taxRateBank | async" [value]="tax">
                <span>{{ tax }}</span>
              </mat-option>
            </mat-autocomplete>
          </mat-form-field>
          <mat-form-field appearance="none" class="splOrderInputs" (mouseenter)="openDialog()">
            <label>Total Price</label>
            <input matInput type="number" class="outline " placeholder="Total Price" [value]="totalOfAllAmount" disabled
              [ngClass]="{'totalPriceInput': ((totalOfAllAmount !== null && totalOfAllAmount !== undefined) && addItemToPoForm.value.taxRate) && dialogTaxData}" />
          </mat-form-field>
          <mat-form-field appearance="none">
            <label> Add Description (Max. 50) </label>
            <textarea matInput style="font-size: 15px; height: 60px;" formControlName="itemDescription"
              [(ngModel)]="descriptionClear" class="outline" maxlength="50" required #descriptionTextarea></textarea>
          </mat-form-field>
          <div class="addItemBtnclss">
            <button [disabled]="createPurchaseOrderForm.invalid || this.selectedPkg.pkgName == 'N/A' || checkNegativeCost()" mat-button
              (click)="addItemtoPo()" class="addItemBtn button3" #addItemButton>
              Add Item
            </button>
          </div>
        </div>
      </div>
    </form>
  </div>


  <div *ngIf="dataSource.data.length > 0" class="search-table-input fieldcontainer">
    <section class="example-container-1 mat-elevation-z8">
      <mat-form-field appearance="none" style="float: left; margin-bottom: 0.1rem !important;">
        <input matInput type="text" (keyup)="doTableFilter($event.target.value)" placeholder="Search" class="outline"
          [(ngModel)]='searchText' />
        <mat-icon matSuffix class="closebtn">search</mat-icon>
      </mat-form-field>

      <mat-slide-toggle *ngIf='tempName'
        style="float: right; margin-top: 2rem !important; margin-bottom: 1rem !important;" [(ngModel)]="setOrderQty"
        (change)="setQtyToZero()">
        Set OrderQty to Zero
      </mat-slide-toggle>

      <mat-slide-toggle style="float: right !important;margin-top: 25px !important;" class="ml-2"
        [(ngModel)]="allExtraFieldFlag" (change)="addExtraFields()">
        <span>Show Cess</span>
      </mat-slide-toggle>

      <table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort>
        <ng-container matColumnDef="index">
          <th mat-header-cell *matHeaderCellDef class="tableId"><b>#</b></th>
          <td mat-cell *matCellDef="let element; let i = index" class="name-cell tableId">
            {{ dataSource.data.length - i }}
          </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="cessAmt">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Cess Amt</b>
          </th>
          <td mat-cell *matCellDef="let element">
            <input (keyup)="validateTotalPrice($event , element)" class="input1" type="number" step="any" min="0"
              [(ngModel)]="element.cessAmt" (focus)="focusFunctionWithOutForm(element,'cessAmt')"
              (focusout)="focusOutFunctionWithOutForm(element,'cessAmt')"
              onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)" />
          </td>
          <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getTotal('cessAmt'))}}</td>
        </ng-container>

        <ng-container matColumnDef="extraAmt">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Extra Charge</b>
          </th>
          <td mat-cell *matCellDef="let element">
            <input (keyup)="validateTotalPrice($event , element)" class="input1" type="number" step="any" min="0"
              [(ngModel)]="element.extraAmt" (focus)="focusFunctionWithOutForm(element,'extraAmt')"
              (focusout)="focusOutFunctionWithOutForm(element,'extraAmt')"
              onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)" />
          </td>
          <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getTotal('extraAmt'))}}</td>
        </ng-container>

        <ng-container matColumnDef="discnt">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Discount</b>
          </th>
          <td mat-cell *matCellDef="let element">
            <input (keyup)="validateTotalPrice($event , element)" class="input1" type="number" step="any" min="0"
              [(ngModel)]="element.discAmt" (focus)="focusFunctionWithOutForm(element,'discAmt')"
              (focusout)="focusOutFunctionWithOutForm(element,'discAmt')"
              onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)"
              [max]="element.totalExcTax" />
          </td>
          <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getTotal('discAmt'))}}</td>
        </ng-container>

        <ng-container matColumnDef="actionBtns">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Actions</b>
          </th>
          <td mat-cell *matCellDef="let element">
            <button mat-icon-button (click)="removeItemFromPo(element)">
              <mat-icon>delete</mat-icon>
            </button>
          </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="itemName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Inventory Item</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.itemName | titlecase }}
          </td>
          <td mat-footer-cell *matFooterCellDef> Total </td>
        </ng-container>

        <ng-container matColumnDef="itemDescription">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Item Description</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.itemDescription | titlecase }}
          </td>
          <td mat-footer-cell *matFooterCellDef> Total </td>
        </ng-container>

        <ng-container matColumnDef="pkgName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Pkg Name</b></th>
          <td mat-cell *matCellDef="let element"> {{element.pkgName | titlecase}} </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="itemCode">
          <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell"><b> Item Code</b> </th>
          <td mat-cell *matCellDef="let element" class="name-cell"> {{element.itemCode}} </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="hsnCode">
          <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell"><b>HSN Code</b> </th>
          <td mat-cell *matCellDef="let element" class="name-cell"> {{element.hsnCode || '-'}} </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="unitPerPkg">
          <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Units/Pkg</b></th>
          <td mat-cell *matCellDef="let element"> {{ this.utils.truncateNew(element.unitPerPkg) }} </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="orderQty">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Order Quantity</b>
          </th>
          <td mat-cell *matCellDef="let element">
            <input (keyup)="validateReceivedQty($event , element)" class="input1" type="number" step="0.01" min="0"
              [(ngModel)]=" element.orderQty" (focus)="focusFunctionWithOutForm(element,'orderQty')"
              (focusout)="focusOutFunctionWithOutForm(element,'orderQty')" />
          </td>
          <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getTotal('orderQty'))}}</td>
        </ng-container>

        <ng-container matColumnDef="unitPrice">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Unit Cost</b>
          </th>
          <td mat-cell *matCellDef="let element">
            <input (keyup)="validateReceivedQty($event , element)" class="input1" type="number" step="0.01" min="0"
              [(ngModel)]="element.unitPrice" 
              [readonly]="element.isContract && element.isContract === true"
              [disabled]="element.isContract && element.isContract === true"
              [ngStyle]="{'opacity': (element.isContract && element.isContract === true) ? '0.5' : '1'}"
              (focus)="focusFunctionWithOutForm(element,'unitPrice')"
              (focusout)="focusOutFunctionWithOutForm(element,'unitPrice')" />
          </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>
        
        <ng-container matColumnDef="totalValueExcTax">
          <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Amount(ex.tax)</b></th>
          <td mat-cell *matCellDef="let element"> {{this.utils.truncateNew(element.totalExcTax)}} </td>
          <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getTotal('totalExcTax'))}}</td>
        </ng-container>

        <ng-container matColumnDef="taxableAmt">
          <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Taxable Amt</b></th>
          <td mat-cell *matCellDef="let element"> {{ this.utils.truncateNew(element.orderQty * element.unitPrice)
            - element.discAmt }} </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="taxAmt">
          <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Tax Amt</b></th>
          <td mat-cell *matCellDef="let element"> {{this.utils.truncateNew(element.taxAmount)}} </td>
          <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getTotal('taxAmount'))}}</td>
        </ng-container>

        <ng-container matColumnDef="rate">
          <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Tax Rate</b></th>
          <td mat-cell *matCellDef="let element">
            <input (keyup)="validateReceivedQty($event, element)" class="input1" type="number" step="0.01" min="0"
              [(ngModel)]="element.taxRate"
              [readonly]="element.isContract && element.isContract === true"
              [disabled]="element.isContract && element.isContract === true"
              [ngStyle]="{'opacity': (element.isContract && element.isContract === true) ? '0.5' : '1'}"
              (focus)="focusFunctionWithOutForm(element, 'taxRate')"
              (focusout)="focusOutFunctionWithOutForm(element, 'taxRate')" />
          </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>
        

        <ng-container matColumnDef="totalValue">
          <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Amount(incl.tax)</b></th>
          <td mat-cell *matCellDef="let element"> {{this.utils.truncateNew(element.totalPrice)}} </td>
          <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getTotal('totalPrice'))}}</td>
        </ng-container>
        <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        <tr mat-footer-row *matFooterRowDef="displayedColumns"></tr>
      </table>
    </section>

    <div class="mt-3 mb-3">
      <div>         
        <mat-form-field appearance="none" style="float: right !important; margin-right: 0px !important;">
          <mat-select placeholder="Select Tax" [formControl]="otherTax"  
          class="outline" (selectionChange)="addTax($event.value)" multiple>
            <mat-option>      
              <ngx-mat-select-search [formControl]="taxFilterCtrl" placeholderLabel="Select Tax..."></ngx-mat-select-search>  
            </mat-option>    
            <mat-option class="hide-checkbox" (click)="toggleSelectAll()">
              Select All / Deselect All
            </mat-option>           
            <mat-option *ngFor="let tax of filteredTaxArray" [value]="tax">
              {{ tax }}
            </mat-option>
          </mat-select>
        </mat-form-field> 
      </div>
      <br>
      <span class="otherchargeHeading topItemkey">ADD TAX</span>
      <br><br><br>     
      <div *ngFor="let item of otherTaxes; let i = index" style="padding-bottom: 22px !important;">
        <span class="otherchargeHeading topItemkey">{{ item.taxName }}</span>
        <input *ngIf="item.taxName" matInput class="outline otherTax" [(ngModel)]="item.value" step="0.01" min="0"
        type="number" (focus)="focusFn(item)" (focusout)="focusOutFn(item)"
        onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)"/>
      </div>
      <div>
        <span class="otherchargeHeading topItemkey">Grand total ₹</span>
        <input matInput class="outline otherTax" [value]="getGrandTotal()" placeholder="Total ₹" disabled />
      </div>
    </div>
  </div>

  <div class="datacontainer">
    <mat-card *ngIf="toPrint">
      <div id="print-section">
        <h3>
          Purchase Order
        </h3>
        <div class="po-details">
          <div fxLayout fxLayoutAlign="space-between center">
            <span>Po number : {{ purchaseOrder.id }}</span>
          </div>
          <div fxLayout fxLayoutAlign="space-between center">
            <span>Issue Date :
              {{ purchaseOrder.orderedDate | date: "EEEE, MMMM d, y" }}</span>
            <span>Expected Date :
              {{
              createPurchaseOrderForm.value.expectedDate
              | date: "EEEE, MMMM d, y"
              }}</span>
            <span>Validity Date :
              {{
              createPurchaseOrderForm.value.validityDate
              | date: "EEEE, MMMM d, y"
              }}
            </span>
          </div>
        </div>

        <table>
          <thead>
            <tr *ngIf="displayedColumns">
              <th>#</th>
              <th>Name</th>
              <th>Order Quantity</th>
              <th>Unit Cost</th>
              <th>Total</th>
            </tr>
          </thead>

          <tbody>
            <tr *ngFor="let item of purchaseOrder.items; let i = index">
              <td>{{ i + 1 }}</td>
              <td style="text-align: left;">{{ item.itemName }}</td>
              <td>{{ item.orderQty }}</td>
              <td>{{ item.unitPrice }}</td>
              <td>{{ item.unitPrice * item.orderQty }}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div fxLayout fxLayoutAlign="space-between center" class="action-btns" style="margin-top: 10px;">
        <button mat-raised-button color="primary" (click)="editPo()">
          Edit Order
        </button>
        <button mat-raised-button color="primary" (click)="print()">
          Print Order
        </button>
      </div>
    </mat-card>
  </div>
</div>


<mat-card *ngIf="isShowTemplate">
  <span mat-card-title class="headTag">Templates</span>
  <div class="closeBtnForSO">
    <button mat-icon-button matTooltip="Back to special Order" (click)="goBack()"><mat-icon>close</mat-icon></button>
  </div>

  <div class="templateinputs">
    <mat-form-field appearance="none">
      <label>Search</label>
      <input matInput type="text" class="outline" (keyup)="doFilter($event.target.value)" placeholder="Search" />
    </mat-form-field>
    <mat-icon class="infoIconForSo" matTooltip="Filter By Template Name">info</mat-icon>
  </div>

  <table #table mat-table [dataSource]="templates" matSortActive="itemName" matSortDirection="asc" matSort>
    <ng-container matColumnDef="index">
      <th mat-header-cell *matHeaderCellDef class="tableId"><b>#</b></th>
      <td mat-cell *matCellDef="let element; let i = index" class="name-cell tableId">
        {{ i+1 }}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="delete">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Delete</b>
      </th>
      <td mat-cell *matCellDef="let element">
        <button mat-icon-button (click)="deleteTemplate(element)">
          <mat-icon>delete</mat-icon>
        </button>
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="tempName">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Template Name</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ element.templateName | titlecase }}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="vendorName">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Vendor Name</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ element.vendorName | titlecase }}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="updatedAt">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Updated Date</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ element.modTs | date: 'MMM d, y'}}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="actionBtns">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Actions</b>
      </th>
      <td mat-cell *matCellDef="let element">
        <button mat-icon-button class="m-2" (click)="getTemplate($event,element)" matTooltip="Click to Use Template">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor"
            class="bi bi-clipboard-check-fill" viewBox="0 0 16 16">
            <path
              d="M6.5 0A1.5 1.5 0 0 0 5 1.5v1A1.5 1.5 0 0 0 6.5 4h3A1.5 1.5 0 0 0 11 2.5v-1A1.5 1.5 0 0 0 9.5 0h-3Zm3 1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-1a.5.5 0 0 1 .5-.5h3Z" />
            <path
              d="M4 1.5H3a2 2 0 0 0-2 2V14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V3.5a2 2 0 0 0-2-2h-1v1A2.5 2.5 0 0 1 9.5 5h-3A2.5 2.5 0 0 1 4 2.5v-1Zm6.854 7.354-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 0 1 .708-.708L7.5 10.793l2.646-2.647a.5.5 0 0 1 .708.708Z" />
          </svg>
        </button>
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumnsForTemplate; sticky: true"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumnsForTemplate"></tr>
  </table>
  <div class="dataMessage" *ngIf="templates?.length == 0"> No Data Available </div>

</mat-card>


<ng-template #openTaxDialog>
  <h2 mat-dialog-title>
    <b>Tax Structure</b>
    <button mat-icon-button style="float: right;" (click)="closeDialog()"><mat-icon>close</mat-icon></button>
  </h2>

  <mat-dialog-content class="mat-typography">

    <div *ngIf="dialogTaxData" class="parentClass" style="margin: 30px;">
      <div class="childClass">
        <div class="headerChild">
          Central GST
        </div>
        <div class="valueChild">
          {{dialogTaxData.centralGST || '-' }} <mat-icon matSuffix class="pertIcons"
            *ngIf="dialogTaxData.centralGST ">%</mat-icon>
        </div>
      </div>
      <div class="childClass">
        <div class="headerChild">
          State GST
        </div>
        <div class="valueChild">
          {{dialogTaxData.stateGST || '-' }} <mat-icon matSuffix class="pertIcons"
            *ngIf="dialogTaxData.stateGST">%</mat-icon>
        </div>
      </div>
      <div class="childClass">
        <div class="headerChild">
          InterState GST
        </div>
        <div class="valueChild">
          {{dialogTaxData.interStateGST || '-' }} <mat-icon matSuffix class="pertIcons"
            *ngIf="dialogTaxData.interStateGST">%</mat-icon>
        </div>
      </div>
      <div class="childClass">
        <div class="headerChild">
          Total
        </div>
        <div class="valueChild">
          {{dialogTaxData.tax}} <mat-icon matSuffix class="pertIcons" *ngIf="dialogTaxData.tax">%</mat-icon>
        </div>
      </div>
    </div>

  </mat-dialog-content>
</ng-template>

<div class="custom-tooltip" [hidden]="!showTooltip">

  <div class="d-flex justify-content-end m-1">
    <mat-icon (click)="closeToolTip()" style="cursor: grab; font-size: 22px;" matTooltip="close">close</mat-icon>
  </div>

  <div *ngIf="dialogData" class="parentClass">
    <div class="d-flex m-2 topItems"
      *ngIf="dialogData.priceType === 'discountOnMRP' || dialogData.priceType === 'absolutePriceReduction'">
      <div class="custom-tooltip-Heading">
        Type
      </div>
      <div>
        {{ capitalizeWords(dialogData.priceType) || '-' }}
      </div>
    </div>

    <div class="d-flex m-2 topItems" *ngIf="dialogData.priceType === 'fixedContractPrice' && !this.contractTax">
      <div class="custom-tooltip-Heading">
        Type
      </div>
      <div>
        Fixed contract including Tax
      </div>
    </div>

    <div class="d-flex m-2 topItems" *ngIf="dialogData.priceType === 'fixedContractPrice' && this.contractTax">
      <div class="custom-tooltip-Heading">
        Type
      </div>
      <div>
        Fixed contract excluding Tax
      </div>
    </div>

    <div class="d-flex m-2 topItems" *ngIf="dialogData.priceType === 'discountOnMRP'">
      <div class="custom-tooltip-Heading">
        Discount
      </div>
      <div>
        {{dialogData.percentage || '-' }} % discount on MRP
      </div>
    </div>

    <div class="d-flex m-2 topItems" *ngIf="dialogData.priceType === 'absolutePriceReduction'">
      <div class="custom-tooltip-Heading">
        Discount
      </div>
      <div>
        {{dialogData.absolutePrice || '-' }} Rs. Reduction on MRP 
      </div>
    </div>

    <div class="d-flex m-2 topItems" *ngIf="dialogData.priceType === 'fixedContractPrice'">
      <div class="custom-tooltip-Heading">
        Contract Price
      </div>
      <div>
        {{dialogData.price}} Rs
      </div>
    </div>


    <div class="d-flex m-2 bottomItems border-top pt-2">
      <div class="custom-tooltip-Heading">
        Qty
      </div>
      <div class="d-flex">
        <div class="custom-tooltip-Value">
          1
        </div>
        <div class="custom-tooltip-Value">
          {{this.addItemToPoForm.value.orderQty || '-' }}
        </div>
      </div>
    </div>

    <div class="d-flex m-2 bottomItems"
      *ngIf="dialogData.priceType === 'discountOnMRP' || dialogData.priceType === 'absolutePriceReduction'">
      <div class="custom-tooltip-Heading">
        MRP
      </div>
      <div class="d-flex">
        <div class="custom-tooltip-Value">
          {{ this.addItemToPoForm.value.convertedPrice || '-' }}
        </div>
        <div class="custom-tooltip-Value">
          {{ this.addItemToPoForm.value.convertedPrice * this.addItemToPoForm.value.orderQty || '-' }}
        </div>
      </div>
    </div>


    <div class="d-flex m-2 bottomItems"
      *ngIf="dialogData.priceType === 'discountOnMRP'">
      <div class="custom-tooltip-Heading">
        Discount Value on MRP
      </div>
      <div class="d-flex">
        <div class="custom-tooltip-Value">
          {{ getDiscountAmount(1) || '-' }}
        </div>
        <div class="custom-tooltip-Value">
          {{ getDiscountAmount(this.addItemToPoForm.value.orderQty) || '-' }}
        </div>
      </div>
    </div>

    <div class="d-flex m-2 bottomItems"
    *ngIf="dialogData.priceType === 'absolutePriceReduction'">
    <div class="custom-tooltip-Heading">
      Price reduction  on MRP
    </div>
    <div class="d-flex">
      <div class="custom-tooltip-Value">
        {{ getAbsolutePrice(1) || '-' }}
      </div>
      <div class="custom-tooltip-Value">
        {{ getAbsolutePrice(this.addItemToPoForm.value.orderQty) || '-' }}
      </div>
    </div>
  </div>


    <div class="d-flex m-2 bottomItems"
    *ngIf="dialogData.priceType === 'discountOnMRP' || dialogData.priceType === 'absolutePriceReduction'|| dialogData.priceType === 'fixedContractPrice' && dialogData.hasOwnProperty('contractTax')">
    <div class="custom-tooltip-Heading">
      Final Value With Tax
    </div>
    <div class="d-flex">
      <div class="custom-tooltip-Value">
        {{ this.getFinalCost(1) || '-' }}
      </div>
      <div class="custom-tooltip-Value">
        {{ this.getFinalCost(this.addItemToPoForm.value.orderQty) || '-' }}
      </div>
    </div>
    </div>

    <div class="d-flex m-2 bottomItems" *ngIf="dialogData.priceType === 'fixedContractPrice' && !dialogData.hasOwnProperty('contractTax')">
      <div class="custom-tooltip-Heading">
        Final value with Tax
      </div>
      <div class="d-flex">
        <div class="custom-tooltip-Value">
          {{ getFinalCost(1) || '-' }}
        </div>
        <div class="custom-tooltip-Value">
          {{  getFinalCost(this.addItemToPoForm.value.orderQty) || '-'}}
        </div>
      </div>
    </div>

    <div class="d-flex m-2 bottomItems">
    <div class="custom-tooltip-Heading">
      Tax
    </div>
    <div class="d-flex">
      <div class="custom-tooltip-Value">
        {{this.utils.truncateNew(this.addItemToPoForm.value.taxRate) || '-' }} %
      </div>
      <div class="custom-tooltip-Value">
        {{this.utils.truncateNew(this.addItemToPoForm.value.taxRate) || '-' }} %
      </div>
    </div>
    </div>

    <div class="d-flex m-2 bottomItems"
    *ngIf="dialogData.priceType === 'discountOnMRP' || dialogData.priceType === 'absolutePriceReduction' || dialogData.priceType === 'fixedContractPrice' && dialogData.hasOwnProperty('contractTax')">
    <div class="custom-tooltip-Heading">
      Final value without Tax
    </div>
    <div class="d-flex">
      <div class="custom-tooltip-Value">
        {{ this.getFinalUnitCostWithoutTax(1) || '-' }}
      </div>
      <div class="custom-tooltip-Value">
        {{ this.getFinalUnitCostWithoutTax(this.addItemToPoForm.value.orderQty) || '-' }}
      </div>
    </div>
    </div>

    <div class="d-flex m-2 bottomItems" *ngIf="dialogData.priceType === 'fixedContractPrice' && !dialogData.hasOwnProperty('contractTax')">
      <div class="custom-tooltip-Heading">
        Final value without Tax
      </div>
      <div class="d-flex">
        <div class="custom-tooltip-Value">
          {{ utils.truncateNew(dialogData['price']) || '-' }}
        </div>
        <div class="custom-tooltip-Value">
          {{  utils.truncateNew(this.addItemToPoForm.value.orderQty * dialogData['price']) || '-'}}
        </div>
      </div>
    </div>




  </div>
</div>