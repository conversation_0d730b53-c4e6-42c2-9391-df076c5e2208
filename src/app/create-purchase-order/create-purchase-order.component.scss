.mat-form-field:not(.mat-form-field-appearance-legacy)
  .mat-form-field-prefix
  .mat-icon,
.mat-form-field:not(.mat-form-field-appearance-legacy)
  .mat-form-field-suffix
  .mat-icon {
  display: inline-block !important;
  // display: block;
  float: none !important;
  width: 180px !important;
}

.placeToggle{
    margin-right: 3%;
  }

  .testarea{
    // flex: 1 1 100%;
    // box-sizing: border-box;
    // max-width: 17% !important;
  }

.text{
  float: left;
  margin-left: 2%;
  margin-top: 6px;
  // font-style: italic;
  font-family: Public Sans;
  font-size: 12px;
}

#contractText{
  text-align: center;
}

.makeTemplateBtn{
  float: right;
}

.sendReqBtn{
  float: right;
}

.roleInput{
  float: right !important;
  margin-top : 24px !important;
}

.tableBtnAndInput{
  display: flow-root;
}

  .example-container-1{
    max-height: 310px;
    overflow-y: auto;
  }
  
.addItemBtnclss{
  border-radius: 4px;
  float: right;
  margin-top: 37px;
}

::ng-deep textarea.mat-input-element {
  padding: 0px 0 !important;
  margin: -8px 0 !important;
}

.matNavList{
  margin-left: 2%;
  margin-right: 2%;
  }

#vendorForm{
  display: flex !important;
}

.spinner-border{
  margin-left: 80% !important;
}

.loaderSpinDiv{
  margin-top : -29px;
}

.margin {
  margin-left : 10px;
}

mat-expansion-panel-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 !important;
  font-weight: bolder;
  border-radius: inherit;
}

example-headers-align .mat-expansion-panel-header-title, 
.example-headers-align .mat-expansion-panel-header-description {
  flex-basis: 0;
}

.example-headers-align .mat-expansion-panel-header-description {
  justify-content: flex-end !important;
}

::ng-deep span.mat-expansion-indicator.ng-trigger.ng-trigger-indicatorRotate.ng-star-inserted {
  display: none !important;
}

::ng-deep mat-panel-description.mat-expansion-panel-header-description {
  justify-content: flex-end !important;
}

::ng-deep .mat-expansion-panel-header-description, .mat-expansion-panel-header-title {
  margin-left: 10px !important;
}

.closeBtnForSO{
  float: right;
  margin-top: -45px !important;
}

.infoIconForSo{
  margin-top: 48px !important;
  margin-left: 5px !important;
}


.bottomButtons{
  // float: right;
  // margin-right: 10px;
  margin-top: 15px;
}


section.example-section {
  float: right !important;
}

.templateinputs{
  display: flex !important;
}

.btnInput{
  margin-top: -9px;
}

.test {
  display: inline-block; 
  width: 100%;
}
.left-div {
  float: left;
}

.right-div {
  float: right;
  display: flex;
}

mat-form-field.fit-content {
  .mat-select-placeholder {
    min-width: 180px;
  }

  .mat-form-field-infix {
    width: fit-content;
    min-width: fit-content;
  }

  .mat-form-field {
    width: auto !important;
  }

  .mat-select-value {
    max-width: 100%;
    width: auto;
  }
}

.container {
  display: flex;
  align-items: center;
}

.check_circle{
  color:green;
  font-size: 27px;
  margin-top: 16px;
}

.topItem{
  margin-left: 2%;
  margin-right: 2%;
}

#addItemForm{
  margin-top: 0px;
}

.bottomButtons{
  float: right;
}

.firstCard{
  margin-top: -10px;
}

.mat-expansion-panel-header-description, .mat-expansion-panel-header-title {
  margin-left: 10px !important;
}

.expansionInnerContent{
  margin-bottom: 10px;
  margin-left: 5px;
}

::ng-deep .mat-form-field-appearance-outline .mat-form-field-flex {
  padding: 5px 0.75em 5px 0.75em !important;
  margin-top: -.25em;
  position: relative;
}

.splOrderInputs{
  max-width: 100px !important;
}

.totalPriceInput{
  border: 1px solid !important;;
}

// .parentClass{
//   margin: 30px;
// }

.childClass{
  margin: 7px;
  font-size: 10px;
  display: flex;
  padding-bottom: 3px;
}

.headerChild{
  font-weight: bold;
  width: 80px;
  display: flex;
  align-items: center;
}

.valueChild{
  display: flex;
  align-items: center;
  gap: 5px;
}

.pertIcons{
  // font-size: 18px;
  font-size: 10px;
  position: relative;
  top: 7px;
  width: 8px;
}

.dropdownDate{
  font-size: 9px;
  margin-top: -28px;
}

.iconDiv{
  display: contents;
  position: absolute;
}

.sildeIcon{
  cursor: grab;
  position: relative;
  top: 42px;
  left: -10px;
}

.taxInput{
  margin-left: 15px;
}

.custom-tooltip {
  position: absolute;
  background-color: #333;
  color: #fff;
  padding: 5px;
  border-radius: 4px;
  font-size: 14px;
  z-index: 1000; /* Ensure it appears above other elements */
  // width: 250px;
  width: 335px;
  left: 750px;
  top: 68px;
}

.custom-tooltip-Heading{
  width: 135px;
}

.custom-tooltip-Value{
  width: 86px;
  // text-align: center;
}

.calIcon{
  margin-left: -12px;
  margin-right: 10px;
  position: relative;
  top: 34px;
  // height: 38px;
  cursor: grab;
}

.topItems{
  font-weight: bold;
  font-size: 11px;
}

.bottomItems{
  font-size: 11px;
}

::ng-deep .autoComplete {
  width: 750px !important;
  right: 310px !important;
  border-radius: 10px;
  background: #393f45;
}

::ng-deep .matOption {
  width: 100%;
  padding: 0 0px !important;
  height: 35px !important;
  font-size: 14px !important;
  background: #343639;
}

::ng-deep .mat-checkbox-layout {
  font-size: 16px !important;
  padding-left: 10px !important;
  margin-top: 7px !important;
}

::ng-deep .mat-autocomplete-panel .mat-option.mat-selected:not(.mat-active):not(:hover) {
  background: rgba(86, 89, 93, 0.87);
}

.othercharge{
  width: 120px;
  float: right;
  margin-right: 38px;
}

.otherTax{
  // width: 120px;
  width: 145px;
  float: right;
  margin-right: 33px;
}

.otherchargeHeading{
  margin-left: 600px;
}

.deleteForeverBtn{
  width: 30px;
  height: 38px;
  border-radius: 4px;
  float: right;
  padding-top: 7px;
}

::ng-deep .hide-checkbox .mat-pseudo-checkbox {
  display: none !important;
}
