import { Component, HostListener, OnInit, TemplateRef, ViewChild, ElementRef, ChangeDetectorRef} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatSort, MatTableDataSource } from '@angular/material';
import { PurchaseOrder } from '../_models';
import { PurchaseItem } from '../_models';
import { Brand } from '../_models';
import { UtilsService } from '../_utils/utils.service'
import { Vendor } from '../_models';
import { GlobalsService } from '../_services/globals.service';
import { MatDialog } from '@angular/material';
import { SimpleDialogComponent } from '../_dialogs/simple-dialog/simple-dialog.component';
import { PrPreviewDialogComponent } from '../_dialogs/pr-preview-dialog/pr-preview-dialog.component';
import { VendorsService, AuthService, VendorInvService, PurchasesService, ShareDataService, BranchTransferService } from '../_services/'
import { Observable, interval } from 'rxjs';
import { FormControl } from '@angular/forms';
import { MatSelect } from '@angular/material/select';
import { ReplaySubject, Subject } from 'rxjs';
import {map, startWith, takeUntil } from 'rxjs/operators';
import { AutoGrnService } from '../_services/auto-grn.service';
import { NotificationService } from '../_services/notification.service';
import { environment } from '../../environments/environment';
import { type } from 'os';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material';
import { MomentDateModule, MomentDateAdapter } from '@angular/material-moment-adapter';
import { Router } from '@angular/router';
import { MasterdataupdateService } from '../_services/masterdataupdate.service';
import { log } from 'console';

export const MY_FORMATS = {
  parse: {
      dateInput: 'LL'
  },
  display: {
      dateInput: 'DD-MM-YYYY',
      monthYearLabel: 'YYYY',
      dateA11yLabel: 'LL',
      monthYearA11yLabel: 'YYYY'
  }
};

@Component({
  selector: 'app-create-purchase-order',
  templateUrl: './create-purchase-order.component.html',
  styleUrls: ['./create-purchase-order.component.scss', "./../../common-dark.scss"],
  providers:[{ provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
            { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }
          ]
})
export class CreatePurchaseOrderComponent implements OnInit {
  @ViewChild('singleSelect') singleSelect: MatSelect;
  setOrderQty : boolean = false;
  public Bank: any[] = [];
  public VendorBank: any[] = [];
  public inventoryItem: FormControl = new FormControl();
  public bankFilterCtrl: FormControl = new FormControl();
  public vendorFilterCtrl: FormControl = new FormControl();
  public roleFilterCtrl: FormControl = new FormControl();
  public filteredVendorItems: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public vendorsBanks: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  protected _onDestroy = new Subject<void>();
  public intervalTimer = interval(500);
  private subscription;
  vendorSelected=false;
  allExtraFieldFlag: Boolean
  createPurchaseOrderForm: FormGroup;
  addItemToPoForm: FormGroup;
  submitAttempted: boolean = false;
  allowOrder: boolean ;
  purchaseOrder: PurchaseOrder = {};
  displayedColumns = ['index', 'itemCode',  'hsnCode', 'itemName', 'pkgName', 'unitPerPkg', 'orderQty', 'unitPrice', 'totalValueExcTax','rate', 'taxAmt', 'totalValue', 'actionBtns'];
  displayedColumnsForTemplate : any = ['index','tempName','vendorName','updatedAt', 'actionBtns','delete'];
  startDate: any;
  itemTotalDiaplay: number;
  itemSearchText: any;
  vendors: Vendor[];
  purchaseItems: PurchaseItem[];
  itemBrands: Brand[];
  @ViewChild(MatSort) sort: MatSort;
  orderMethods: any[];
  selectedVendor: Vendor;
  selectedInvCategory: any;
  selectedInventoryItem: PurchaseItem;
  selectedBrand: Brand;
  vendorItems: PurchaseItem[] = [];
  packagingSizes: any;
  selectedItem: any;
  displayedFooterColumns: any = ['itemName']
  showPkg: boolean = true
  selectedPkg: any = { 'pkgName': 'N/A' };
  dataSource: MatTableDataSource<PurchaseItem>;
  toPrint: boolean;
  units: string[];
  restaurants: any[];
  totalAmount: number = 0;
  taxFlag: boolean = false;
  lastSeen: boolean = false;
  descriptionFlag: boolean = false;
  dialogRef: any;
  selectedBranch: any;
  totalExcTax: any;
  templateName: String;
  public setHeight: string = '0px';
  public filteredItemList: Array<Object>;
  public contractItems: Array<Object>;
  public searchText:any;
  unitCost: any;
  quantity: any;
  taxrate: any;
  data: any;
  poData: string;
  deliveryData: string;
  podate: number;
  delDate: any;
  descriptionClear: string;
  lastUpdate: any;
  selectedRole: any;
  contractData: any =[];
  invItems: any =[];
  roles: any =[];
  appCategories: any =[];
  founContractPrice: boolean = false;
  isContract: boolean = false;
  passive: false;
  @ViewChild('err') vc: any;
  addItems: boolean = false;
  showItemDropDown: boolean = false;
  trapFocus: boolean = true;
  paymentMethods =[
    "Cash",
    "NEFT",
    "Other"
  ];
  remarks: any;
  paymentTerms: any;
  poTerms: any;
  paymentMethod: any;
  appCat: any;
  totalRoles: any;
  contractType: any;
  contractName:any;
  stockType:any;
  selectedWorkArea:any;
  workAreas= [];
  vendorName: any;
  soLoader: boolean = false;
  isTemplate: boolean = false;
  isCloned: boolean = false;
  catItems: any;
  category: any = [];
  vendorData: any;
  vendorDetail: Vendor;
  vName: any;
  templateData: any;
  prtemp: any;
  user: any;
  multiBranchUser; branchSelected: boolean;
  getBranchData: any[]
  branchesData: any[]
  templates: any;
  tempName: any;
  panelOpenState: boolean = false;
  showTemplateName: any;
  isShowTemplateName: boolean = false;
  selectedBrandControl = new FormControl();
  isShowTemplate: boolean = false;
  checked = false;
  getAllTemplatesData : any
  contractApproval: boolean;
  stockSeparation: boolean;
  brand: any;
  private specialKeys: Array<string> = ['Backspace', 'Tab', 'End', 'Home', 'ArrowLeft', 'ArrowRight', 'Del', 'Delete'];
  private regex: RegExp = new RegExp(/^\d*\.?\d{0,2}$/g);
  totalOfAllAmount: number;
  createPurchaseOrderUrl = encodeURI(GlobalsService.createPurchaseOrder)
  private unsubscribe$ = new Subject<void>();
  taxRateBank: Observable<string[]>;
  taxData: any;
  question = 'Would you like to add "';
  taxWholeData: any;
  dialogTaxData: any;
  @ViewChild('openTaxDialog') openTaxDialog: TemplateRef<any>;
  @ViewChild('textInput') textInputRef!: ElementRef<HTMLTextAreaElement>;
  
  filteredUnitPrice: Observable<any[]>;
  unitCostData = [];
  dialogCostData: any;
  costData: any;
  @ViewChild('openPriceDetailsDialog') openPriceDetailsDialog: TemplateRef<any>;
  dialogData: any;
  enteredCost: number = 0;
  access: string;
  checkAccess: boolean;
  enableDialogIcon: boolean = false;
  currentPKG: string
  showTooltip: boolean = false;
  leftPosition: number = 0;
  topPosition: number = 0;
  originalCost: any;
  checkContractAccess: any;
  deleteAccess: boolean;
  editAccess: boolean = true;
  currentContractPrice: any;
  tempUnitCost: any;
  checkTax: boolean;
  absolutePriceData: number;
  absolutePriceTotal: void;
  enteredUnitPrice: any;
  contactPrice: any;
  contractTax: boolean = false;
  tempTotalPrice: number;
  revTotal: number;
  value: number;
  itemsAdd: any[] = [];
  grandTotal: number;
  taxArray = ["AROED", "EXCISE DUTY", "MISCELLANEOUS", "TCS", "VAT"];
  otherTax = new FormControl();
  taxFilterCtrl = new FormControl();
  filteredTaxArray: any[] = [];
  allSelected: boolean = false;
  purReq: any = {};
  otherTaxNames: any;
  otherTaxes: any;
  res: any;
  
  constructor(private fb: FormBuilder,  
    private autoGrnService:AutoGrnService,
    private notifyService: NotificationService,
    private dialog: MatDialog, 
    private vendorService: VendorsService,
    private auth: AuthService, 
    private vendorInv: VendorInvService,
    private purchases: PurchasesService, 
    private shareData: ShareDataService,
    private sharedData: ShareDataService,
    private utils: UtilsService,
    private router : Router,
    private masterDataService: MasterdataupdateService,
    private branchTransfer: BranchTransferService,
    private cdRef: ChangeDetectorRef
    ) {
      this.user = this.auth.getCurrentUser();
      this.access = sessionStorage.getItem('access');
      let dataArray = this.access.split(',');
      this.checkAccess = dataArray.includes(this.user.role)
      this.multiBranchUser = this.user.multiBranchUser;
    this.dataSource = new MatTableDataSource<PurchaseItem>();
    this.createPurchaseOrderForm = this.fb.group({
      rest: ['', Validators.required],
      vendor: ['', Validators.required],
      itemType: [''],
      stockType: [''],
      workArea: [''],
      category:['', Validators.required],
      invCategory: ['', Validators.required],
      expectedDate: ['', Validators.required],
      validityDate: ['', Validators.required],
      roles: [''],
      allVendorsChecked: [false],
      currentVendorChecked: [true],
    });
    this.addItemToPoForm = this.fb.group({
      inventoryItem: ['', Validators.required],
      brand: [''],
      selectedBrand: [''],
      subCategory:[''],
      ledger:[''],
      orderQty: [0, Validators.required],
      unitPrice: [0, Validators.required],
      convertedPrice: [0, Validators.required],
      uom: ['', Validators.required],
      taxRate: [0, Validators.required],
      cessAmt: [0, Validators.required],
      discAmt: [0, Validators.required],
      extraAmt: [0, Validators.required],
      pkgName: ['', Validators.required],
      itemCode: [''],
      hsnCode: [''],
      inStock : [''],
      stockConversion: [''],
      pkgQty: [0],
      unitPerPkg: [0],
      contractDetails: [{}],
      itemDescription : ['']
    });
    
      this.sharedData.sharedBranchData.pipe(takeUntil(this.unsubscribe$)).subscribe((val) => {
        this.getBranchData = val;
        if(this.getBranchData.length == 0 ){
          this.branchesData = this.user.restaurantAccess;
        }else if(this.getBranchData.length == 1){
          const toSelect = this.getBranchData.find(data => data.branchName == this.getBranchData[0].branchName);
          this.createPurchaseOrderForm.get('rest').setValue(toSelect);
          this.branchesData = this.getBranchData
          this.setRestaurant();
        }else{
          this.branchesData = this.getBranchData
        }
    });
  }

  ngAfterViewChecked(){
    this.cdRef.detectChanges();
  }

  ngOnInit() {    
    this.setupCheckboxToggle();
    this.taxStructureData()
    if (this.branchSelected) {
      this.setRestaurant()
    } else {
      if (!this.user.multiBranchUser) {
        this.createPurchaseOrderForm.value.rest = this.user.restaurantId
        this.branchSelected = true;
      }
    }
    this.addItemToPoForm.get('orderQty').setValue(0);
    this.addItemToPoForm.get('unitPrice').setValue(0);
    this.addItemToPoForm.get('taxRate').setValue(0);
    this.addItemToPoForm.get('cessAmt').setValue(0);
    this.addItemToPoForm.get('discAmt').setValue(0);
    this.addItemToPoForm.get('extraAmt').setValue(0);
    this.restaurants = this.auth.getCurrentUser().restaurantAccess
    this.itemTotalDiaplay = this.addItemToPoForm.get('orderQty').value * this.addItemToPoForm.get('unitPrice').value;
    this.startDate = new Date();
    let validityDate = new Date(this.startDate.getTime() + (24 * 60 * 60 * 1000));
    this.createPurchaseOrderForm.get('expectedDate').setValue(this.startDate);
    this.createPurchaseOrderForm.get('validityDate').setValue(validityDate);
    let date = new Date();
    date.setHours(0, 0, 0, 0);
    this.delDate = date;
    this.subscription = this.intervalTimer.subscribe(() => {
      if (this.createPurchaseOrderForm.value.rest.restaurantIdOld !="" && this.vendorSelected){
        this.subscription.unsubscribe();
      }
    });

    this.filteredTaxArray = this.taxArray.slice();
    this.taxFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
      this.filterTaxArray();
    });

  }

  dateFn(){
    this.startDate = new Date();
    let validityDate = new Date(this.startDate.getTime() + (24 * 60 * 60 * 1000));
    this.createPurchaseOrderForm.get('expectedDate').setValue(this.startDate);
    this.createPurchaseOrderForm.get('validityDate').setValue(validityDate);
  }

  validateReceivedQty(event , element){
    if(event.keyCode == 190){
      return
    }
    element.unitPrice < element.discAmt ? element.discAmt = 0 : undefined ;
    element.totalExcTax = element.unitPrice * element.orderQty
    let afterDiscount = 0;
    let tax = 0;
    afterDiscount = (element.orderQty * element.unitPrice) - element.discAmt
    tax = afterDiscount * (element.taxRate / 100)
    element.subTotal = afterDiscount;
    element.taxAmount = tax;
    element.totalPrice = afterDiscount + element.cessAmt + element.extraAmt + tax;
    return element
  }

  validateTotalPrice(event , element) {
    if(event.keyCode == 190){
      return
    }
    element['extraAmt'] === null ? element['extraAmt'] = 0 : undefined ;
    element['cessAmt'] === null ? element['cessAmt'] = 0 : undefined ;
    element['discAmt'] === null ? element['discAmt'] = 0 : undefined ;
    if(element.discAmt > element.totalExcTax){
      element.discAmt = 0
    }
    let afterDiscount = 0;
    let tax = 0;
    afterDiscount = (element.orderQty * element.unitPrice) - element.discAmt
    tax = afterDiscount * (element.taxRate / 100)
    element.subTotal = afterDiscount;
    element.taxAmount = tax;
    element.totalPrice = afterDiscount + element.cessAmt + element.extraAmt + tax;

  }

  ngOnDestroy() {
    this.sharedData.clearPrData();
    this._onDestroy.next();
    this._onDestroy.complete();
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  private filterTaxArray() {
    const search = this.taxFilterCtrl.value ? this.taxFilterCtrl.value.toLowerCase() : '';
    this.filteredTaxArray = this.taxArray.filter(tax => tax.toLowerCase().includes(search));
  }

  toggleSelectAll() {
    this.allSelected = !this.allSelected;
    if (this.allSelected) {
      this.otherTax.setValue(this.taxArray);
    } else {
      this.otherTax.setValue([]);
    }
    this.addTax(this.otherTax.value || []);  
    this.cdRef.detectChanges();
  }

  protected filterBanks() {
    if (!this.Bank) {
      return;
    }
    let search = this.bankFilterCtrl.value;
    if (!search) {
      this.filteredVendorItems.next(this.Bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.filteredVendorItems.next(
      this.Bank.filter(bank => bank.itemName.toLowerCase().indexOf(search) > -1)
    );
  }

  protected vendorfilterBanks() {
    if (!this.VendorBank) {
      return;
    }
    let search = this.vendorFilterCtrl.value;
    if (!search) {
      this.vendorsBanks.next(this.VendorBank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.vendorsBanks.next(
      this.VendorBank.filter(VendorBank => VendorBank.name.toLowerCase().indexOf(search) > -1)
    );
  }

  isFieldInvalid(field: any) {
    let isInvalid: boolean = (
      !this.createPurchaseOrderForm.get(field).valid && this.createPurchaseOrderForm.get(field).touched
    ) || (this.createPurchaseOrderForm.get(field).untouched && this.submitAttempted);
    return isInvalid;
  }

  vendorSelect(v) {
    this.selectedVendor = v.value;
    this.orderMethods = v.value.orderMethods;
    this.vendorItems = v.value.items;
    this.filteredItemList = this.vendorItems;
    this.addItemToPoForm.get('inventoryItem').setValue('');
    this.addItemToPoForm.get('brand').setValue('');
    this.itemBrands = [];
    this.dataSource.data = [];
    this.dataSource.data = [...this.dataSource.data];
  }

  purchaseItemSelect(i) {
    this.dialogData = ''
    this.enableDialogIcon = false;
    this.contractTax = false;
    this.enteredUnitPrice = '';
    this.tempUnitCost = '';
    this.contactPrice = '';
    this.contactPrice = '';
    this.soLoader = true
    this.taxFlag = false
    this.selectedPkg = {}
    this.addItemToPoForm.get('unitPerPkg').setValue('');
    this.addItemToPoForm.get('orderQty').setValue(0);
    this.addItemToPoForm.get('pkgName').setValue('');
    this.addItemToPoForm.get('unitPrice').setValue(0);
    this.addItemToPoForm.get('convertedPrice').setValue(0);
    this.addItemToPoForm.get('contractDetails').setValue({});
    this.addItemToPoForm.get('taxRate').setValue(0)
    this.selectedItem = i.value
    this.packagingSizes = i.value.packagingSizes
    if (this.packagingSizes.length <= 0) {
      this.addItemToPoForm.get('unitPrice').setValue(i.value.unitPrice);
      this.utils.snackBarShowError('No packaging size available for this item.Please check');
    }
    else if (this.packagingSizes.length == 1) {
      this.addItemToPoForm.get('pkgName').setValue(this.packagingSizes[0])
      this.pkgSelect({ 'value': this.packagingSizes[0] })
    }
    this.addItemToPoForm.get('uom').setValue(i.value.uom);
    this.addItemToPoForm.get('itemCode').setValue(i.value.itemCode);
    this.addItemToPoForm.get('hsnCode').setValue(i.value.hsnCode);
    this.addItemToPoForm.get('taxRate').setValue(i.value.taxRate);    
    this.addItemToPoForm.get('cessAmt').setValue(0);
    this.addItemToPoForm.get('discAmt').setValue(0);
    this.addItemToPoForm.get('extraAmt').setValue(0);
    this.addItemToPoForm.get('stockConversion').setValue(i.value.stockConversion); 
    this.enteredUnitPrice = i.value.unitPrice;
  }

  selectContractPrice(itemCode: any , pkgName: any){
    if (this.contractData){
      let foundContract = this.contractData.find(item => item.itemCode === itemCode && item.packageName === pkgName);
      if (foundContract) {
        this.addItemToPoForm.get('contractDetails').setValue(foundContract);
        this.checkContractAccess = foundContract;
        this.currentContractPrice = this.checkContractAccess ? this.checkContractAccess['price'] : 0 ;
        if(this.checkContractAccess && !this.checkContractAccess.contractTax){
          this.contractTax = true;
        }
        this.addItemToPoForm.get('unitPrice').setValue(foundContract['price']);
        this.quantity = 0 ;
        this.taxrate = 0 ;
        this.totalOfAllAmount = 0 ;
        this.isContract = foundContract['price'] > 0 ? true : false;
        this.soLoader = false
      } else {
        this.addItemToPoForm.get('contractDetails').setValue({});
        this.isContract = false;
        this.checkContractAccess = undefined ;
        this.checkContractAccess = undefined;
        this.getLastGrnPrice();
      }
   } else {
    this.isContract = false;   
    this.addItemToPoForm.get('contractDetails').setValue({});
    this.checkContractAccess = undefined ;
    this.getLastGrnPrice();
   }
  }

  setupCheckboxToggle(): void {
    const allVendorsControl = this.createPurchaseOrderForm.get('allVendorsChecked');
    const currentVendorControl = this.createPurchaseOrderForm.get('currentVendorChecked');
  
    if (allVendorsControl && currentVendorControl) {
      allVendorsControl.valueChanges.subscribe(value => {
        if (value) {
          currentVendorControl.setValue(false, { emitEvent: false });
          this.addItemToPoForm.reset();
        }
      });
  
      currentVendorControl.valueChanges.subscribe(value => {
        if (value) {
          allVendorsControl.setValue(false, { emitEvent: false });
          this.addItemToPoForm.reset();
        }
      });
    }
  }  

  getLastGrnPrice(){
    const allVendorsChecked = this.createPurchaseOrderForm.get('allVendorsChecked').value;
    let params={
      'restaurantId':this.createPurchaseOrderForm.value.rest.restaurantIdOld,
      'vendorId':this.selectedVendor,
      'itemCode':this.selectedItem.itemCode,
      'packageName':this.selectedPkg.packageName,
      ...(allVendorsChecked && { 'allVendors': true })
    }
    this.purchases.getLastGrnPrice(params).subscribe(data => {
      if (data['success']){
        this.unitCostData = data.priceList
        this.costData = data.priceList
        this.filteredUnitPrice = this.addItemToPoForm.get('unitPrice').valueChanges.pipe(startWith(''),
          // map(state => state ? this.filterStates(state) : this.unitCostData.slice()));
          map(state => this.unitCostData.slice()));
        this.addItemToPoForm.get('unitPrice').setValue(data.priceList[0].unitPrice)
        this.addItemToPoForm.get('subCategory').setValue(data['ledger'][0]['subCategory']);
        this.addItemToPoForm.get('ledger').setValue(data['ledger'][0]['ledger']);
        this.soLoader = false
      }else{
        this.addItemToPoForm.get('subCategory').setValue(data['ledger'][0]['subCategory']);
        this.addItemToPoForm.get('ledger').setValue(data['ledger'][0]['ledger']);
        this.soLoader = false
        return null
      }
    }) 
  }

  // filterStates(unitPrice) {
  //   return this.unitCostData.filter(state =>
  //     state.unitPrice === Number(unitPrice)
  //   );
  // }

  pkgSelect(pkg) {  
    this.selectedPkg = pkg.value
    if (this.selectedPkg.packageName != 'N/A') {
      this.brand =  pkg.value.brand ? pkg.value.brand : null ;
      this.brand ? this.selectedBrandControl.disable() : null ;
      this.addItemToPoForm.get('unitPerPkg').setValue((pkg.value.packageQty/pkg.value.unitQty));
      this.addItemToPoForm.get('unitPrice').setValue(pkg.value.unitPrice);
      this.addItemToPoForm.get('inStock').setValue(this.utils.truncateNew(pkg.value.inStock));
      this.selectContractPrice(this.selectedItem['itemCode'] , this.selectedPkg.packageName )
    }
  }

  changed() {
    this.selectedPkg = {}
  }

  brandSelect(b) {
    this.addItemToPoForm.get('unitPrice').setValue(b.value.unitPrice);
    this.units = b.value.units;
  }

  addItemtoPo() {
    let itemExisted = false
    this.syncTableEdits();
    if (this.selectedPkg.packageName != 'N/A' && this.selectedPkg.hasOwnProperty('packageName')) {
      if (this.addItemToPoForm.value.orderQty > 0) {
        if (this.addItemToPoForm.value.unitPrice > 0) {
        if (this.selectedItem.packagingSizes.length > 0) {
          if (!this.showPkg) {
            this.purchases.getSortedPkg({
              item: this.selectedItem,
              orderQty: this.addItemToPoForm.value.orderQty
            }).subscribe(data => {
              data.sortedPkg.forEach(element => {
                let dataArr = this.dataSource.data;
                for (let i = 0; i < dataArr.length; i++) {
                  if (dataArr[i].itemCode === this.addItemToPoForm.get('inventoryItem').value.itemCode && element.pkg.pkgName == dataArr[i].packageName) {
                    dataArr[i].orderQty += element.orderQty;
                    itemExisted = true
                    break;
                  }
                }
                if (itemExisted == false) {
                  let sourceData=this.addPurchaseItem(element)
                  this.dataSource.data.push(sourceData)
                  this.addItemToDraft(sourceData);
                }
                itemExisted = false
              });
              this.dataSource.data = [...this.dataSource.data];
              this.dataSource.data.map((element : any) => {element.defaultQty = element.orderQty; return element});
              this.addItemToPoForm.reset();
              this.addItemToPoForm.clearAsyncValidators();
            }, err => console.error(err))
          }else {            
            let pkgName = this.addItemToPoForm.get('pkgName').value.packageName;
            let code =  this.addItemToPoForm.get('inventoryItem').value.itemCode;          
            let dataArrFiltered :any;
            dataArrFiltered = this.dataSource.data.filter(function(v, i) {
              return (v["itemCode"] == code && v['pkgName'] == pkgName);
            })
            if(dataArrFiltered.length > 0){
              dataArrFiltered = dataArrFiltered[0]
              this.dialog.open(SimpleDialogComponent, {
                data: {
                  title: 'Duplicate Item Alert',
                  msg: 'This item is already added.Do you want to add some more?',
                  ok: function () {
                    dataArrFiltered['orderQty'] = dataArrFiltered['orderQty'] + this.addItemToPoForm.get('orderQty').value;
                    dataArrFiltered.unitPrice = this.addItemToPoForm.get('unitPrice').value
                    dataArrFiltered.taxRate = this.addItemToPoForm.get('taxRate').value
                    dataArrFiltered['totalExcTax'] = dataArrFiltered.unitPrice * dataArrFiltered.orderQty;
                    dataArrFiltered['hsnCode'] = dataArrFiltered.hsnCode ? dataArrFiltered.hsnCode : '-';
                    let afterDiscount = 0;
                    let tax = 0;
                    afterDiscount = (dataArrFiltered.orderQty * dataArrFiltered.unitPrice) - dataArrFiltered.discAmt
                    tax = afterDiscount * (dataArrFiltered.taxRate / 100)
                    dataArrFiltered.subTotal = afterDiscount;
                    dataArrFiltered.taxAmount = tax;
                    dataArrFiltered.totalPrice = afterDiscount + dataArrFiltered.cessAmt + dataArrFiltered.extraAmt + tax;
                    this.addItemToDraft(dataArrFiltered);
                  }.bind(this)
                }
              });
            }else{
              let sourceData=this.formValtoPurchaseItem(this.addItemToPoForm.value)
              this.addItemToDraft(sourceData);
            }
            this.dataSource.data = [...this.dataSource.data];
            this.dataSource.data.map((element : any) => {element.defaultQty = element.orderQty; return element});
          }
        }else {
          let pkgName = this.addItemToPoForm.get('pkgName').value.pkgName;
          let code =  this.addItemToPoForm.get('inventoryItem').value.itemCode;
          let dataArrFiltered :any;
          dataArrFiltered = this.dataSource.data.filter(function(v, i) {
            return (v["itemCode"] == code&& v['pkgName'] == pkgName);
          })
          if(dataArrFiltered.length > 0){
            dataArrFiltered =dataArrFiltered[0]
            dataArrFiltered['orderQty'] += this.addItemToPoForm.get('orderQty').value;
          }else{
            this.dataSource.data.unshift(this.formValtoPurchaseItem(this.addItemToPoForm.value));
          }
          this.dataSource.data = [...this.dataSource.data];
          this.dataSource.data.map((element : any) => {element.defaultQty = element.orderQty; return element});
        }
      } else {
        this.utils.snackBarShowInfo('Please add required unit price');
      }
    } else {
      this.utils.snackBarShowInfo('Please add required quantity');
    }
    }else {
      this.utils.snackBarShowInfo('Please select package name');
    }
    this.descriptionClear = ''
  }

  syncTableEdits() {
    this.dataSource.data = this.dataSource.data.map(item => {
      item.orderQty = item.orderQty || 0; 
      item.unitPrice = item.unitPrice || 0;
      item.taxRate = item.taxRate || 0;
      item.discAmt = item.discAmt || 0;
      item.cessAmt = item.cessAmt || 0;
      item.extraAmt = item.extraAmt || 0;
      return item;
    });
  }

  addPurchaseItem(sortedPkg) {
    let purItem: any = {};
    purItem.itemName = this.selectedItem.itemName;
    purItem.unitPrice = sortedPkg.pkg.unitPrice;
    purItem.orderQty = sortedPkg.orderQty;
    purItem.uom = this.selectedItem.uom;
    purItem.itemCode = this.selectedItem.itemCode;
    purItem.taxRate = this.selectedItem.taxRate;
    purItem.pkgName = sortedPkg.pkg.pkgName;
    purItem.unitPerPkg = sortedPkg.pkg.unitPerPkg;
    purItem.pkgQty = sortedPkg.pkg.pkgQty;
    purItem.itemDescription = sortedPkg.pkg.itemDescription;
    purItem.subCategory = sortedPkg.subCategory;
    purItem.ledger = sortedPkg.ledger;
    purItem.hsnCode = sortedPkg.pkg.hsnCode;
    purItem.totalPrice = ((sortedPkg.pkg.unitPrice + (sortedPkg.pkg.unitPrice * this.selectedItem.taxRate / 100)) * sortedPkg.orderQty) + (sortedPkg['extraAmt'] + sortedPkg['cessAmt'] - sortedPkg['discAmt'])
    return purItem;
  }

  private formValtoPurchaseItem(formVal: any) {
    let purItem: any = {};
    purItem.itemName = formVal.inventoryItem.itemName;
    purItem.brand = formVal.brand;
    purItem.unitPrice = formVal.unitPrice;
    purItem.orderQty = formVal.orderQty;
    purItem.uom = formVal.uom;
    purItem.itemCode = formVal.inventoryItem.itemCode;
    purItem.taxRate = formVal.taxRate;
    purItem.cessAmt = formVal.cessAmt;
    purItem.discAmt = formVal.discAmt;
    purItem.extraAmt = formVal.extraAmt;
    purItem.subCategory = formVal.subCategory;
    purItem.ledger = formVal.ledger;
    purItem.itemDescription = formVal.itemDescription;
    purItem.hsnCode = formVal.hsnCode;
    purItem.itemCategory = formVal.inventoryItem.invCategory;
    purItem.stockConversion = formVal.stockConversion;
    if (formVal.pkgName) {
      purItem.pkgName = formVal.pkgName.packageName;
      purItem.unitPerPkg = formVal.unitPerPkg;
      purItem.pkgQty = this.selectedPkg.packageQty;
    }
    else {
      purItem.totalQty = formVal.orderQty
      purItem.pkgName = 'N/A'
      purItem.pkgQty = 'N/A'
      purItem.unitPerPkg = 'N/A'
    }
    purItem.totalExcTax = formVal.unitPrice * formVal.orderQty

    let afterDiscount = 0;
    let tax = 0;
    afterDiscount = (purItem.orderQty * purItem.unitPrice) - purItem.discAmt
    tax = afterDiscount * (purItem.taxRate / 100)
    purItem.subTotal = afterDiscount;
    purItem.taxAmount = tax;
    purItem.totalPrice = afterDiscount + formVal.cessAmt + formVal.extraAmt + tax;
    if (this.isContract)
    purItem.isContract = true
    else 
    purItem.isContract = false
    this.contractData
    if(this.contractData.length > 0){
      const contractValue = this.contractData.find(item => item.itemName === this.addItemToPoForm.value.inventoryItem.itemName);
      if(contractValue){
        purItem.priceType = contractValue.priceType
        purItem.priceTypeValue = contractValue.absolutePrice ? contractValue.absolutePrice : contractValue.percentage ?  contractValue.percentage : contractValue.price
        purItem.contractTax = contractValue.tax ? contractValue.tax : 0;
      }
    }
    return purItem;
  }


  removeItemFromPo(item) {
    this.dialog.open(SimpleDialogComponent, {
      data: {
        title: 'Place Order',
        msg: 'Are you sure you want to remove the item?',
        ok: function () {
          if (this.dataSource.data.indexOf(item) == -1) return null;
          this.dataSource.data.splice(this.dataSource.data.indexOf(item), 1);
          this.dataSource.data = [...this.dataSource.data];
          this.removeItemFromDraft(item['itemCode'], item['pkgName']);
        }.bind(this)
      }
    });
    this.tempName = ''
  }

  createPr(isPreview: boolean = false) {
    if (this.stockType == 'Non-Stockable' && this.selectedWorkArea == undefined){
    this.utils.snackBarShowWarning('Please select workArea');
    } else {

    if (this.purReq && this.purReq.hasOwnProperty('otherTax')){   
      this.otherTaxNames = [...this.otherTaxes];
    } else {
      this.otherTaxNames = this.otherTaxes.map(item => ({
        taxName: item.taxName,
        value: parseFloat(item.value) || 0
      }));
    }

    this.purchaseOrder.prItems = this.dataSource.data
    this.purchaseOrder.orderedDate = new Date();
    let purchaseReq = {
      vendorId: this.selectedVendor,
      tenantId: this.auth.getCurrentUser().tenantId,
      prItems: this.purchaseOrder.prItems.reverse(),
      eta: this.utils.dateCorrection(this.createPurchaseOrderForm.value.expectedDate),
      validityDate: this.utils.dateCorrection(this.createPurchaseOrderForm.value.validityDate),
      uId: this.auth.getCurrentUser().mId,
      rId: this.createPurchaseOrderForm.value.rest.restaurantIdOld,
      totalAmount: this.totalAmount,
      otherTax: this.otherTaxNames,
      grandTotal: this.grandTotal,
      remarks : this.remarks,
      paymentTerms : this.paymentTerms,
      poTerms : this.poTerms,
      paymentMethods : this.paymentMethod
    }
    
    if (this.purReq && this.purReq.hasOwnProperty('prId')){
      purchaseReq['prId'] = this.purReq.prId
    }
    if ("userName" in this.auth.getCurrentUser()) {
      purchaseReq['poMadeBy'] = this.auth.getCurrentUser().userName
    } else {
      purchaseReq['poMadeBy'] = "NA"
    }

    if (this.roles.length > 0){
      const uniqueItemCategories = [...new Set(purchaseReq['prItems'].map(obj => obj.itemCategory))] ;
      const getAppCatRoles = (data, appCat) => {
        const filteredObj = data.find(obj => obj.appCat === appCat);
        return filteredObj ? filteredObj.roles.map(role => role.value) : [];
      };
      let rolesArray = [],appCat;
      if (uniqueItemCategories.length > 1){
        appCat = "DEFAULT";
        rolesArray = getAppCatRoles(this.roles, appCat);
      } else if (uniqueItemCategories.length === 1) {
        appCat = uniqueItemCategories[0]
        rolesArray = getAppCatRoles(this.roles, appCat);
        if (rolesArray.length == 0){
          appCat = "DEFAULT";
          rolesArray = getAppCatRoles(this.roles, appCat);  
        }
      } else {
        rolesArray = [] ;
        appCat = undefined ;
      }
      this.selectedRole = rolesArray ; 
      this.appCat = appCat
    }

    if(this.selectedRole && this.selectedRole.length > 0){
      purchaseReq['role'] = this.selectedRole
    } else {
      purchaseReq['role'] = [];
    }
    purchaseReq['appCat'] = this.appCat;
    purchaseReq['contractType'] = this.contractType;
    purchaseReq['senderEmail'] = [this.user.email]
    purchaseReq['selectedWorkArea'] = this.selectedWorkArea;

    this.dialog.open(SimpleDialogComponent, {
      data: {
        title: 'Place Request',
        msg: 'Are you sure you want to send Purchase Request?',
        ok: async function () {
          let inputData: any = {
            vendorId: this.selectedVendor,
            tenantId: this.auth.getCurrentUser().tenantId,
            uId: this.auth.getCurrentUser().mId,
            rId: this.createPurchaseOrderForm.value.rest.restaurantIdOld,
            // cType: this.contractType
          }    
          inputData['stockType'] = this.stockType ? this.stockType : undefined ;
          this.purchases.removeDraftDataBulK(inputData).subscribe(() => {            
            this.purchases.createPr(purchaseReq).subscribe(data => {
              let obj: any = {}
              obj['tenantId'] = this.auth.getCurrentUser().tenantId,
              obj['tenantName'] = this.auth.getCurrentUser().name,
              obj['rId'] = this.createPurchaseOrderForm.value.rest.restaurantIdOld,
              obj['prId'] = data.prId,
              obj['createTs'] = data.modTs,
              obj['totalAmount'] = this.totalAmount,
              obj['vendorName'] = this.vendorName,
              obj['baseUrl'] = environment.baseUrl,
              obj['contractType'] = this.contractType,
              obj['userEmail'] = this.auth.getCurrentUser().email,
              obj['appCat'] = this.appCat
              if(this.selectedRole && this.selectedRole.length > 0){
                obj['role'] = this.selectedRole
              } else {
                obj['role'] = [];
              }
              this.router.navigate(['/home'])           
              this.purchases.getPermission(obj).subscribe(res => {
                if (res.result === true) {
                  if (isPreview) {
                    if(res.permission){
                      this.previewPr(data);
                    } else {
                      this.utils.snackBarShowSuccess('Approval email sent successfully');
                      this.previewPr(data);
                    }
                  } else if(res.permission){
                    if(this.contractType == 'contract'){
                      this.utils.snackBarShowSuccess('Order belongs to contract, No approvals required');
                    }
                    this.autoGrnService.autoGrn(data.prId);
                  }else {
                    this.utils.snackBarShowSuccess('Approval email sent successfully');
                    this.previewPr(data);
                  }
                }
              })
              this.createPurchaseOrderForm.reset();
              this.addItemToPoForm.reset();
              this.dataSource.data = [];
              this.selectedRole = [] ;
              this.dateFn();
            }, err => console.error(err));
          })
          
        }.bind(this)
      }
    });
  } 
  }

  print() {
    let popupWin, printContents;
    printContents = document.getElementById('print-section').innerHTML;
    popupWin = window.open('', '_blank', `top = 0 ; left = 0 ; height = 100%, width=auto`);
    popupWin.document.open();
    popupWin.document.write(
      ` <html>
        <head>
          <title>Print tab</title>
          <style>

          *{
            margin : 1%;
          }
          #contact-section>div{
            border-bottom: 2px solid $primary !important;
            border-left: 2px solid $primary  !important;
            border-right: 2px solid $primary  !important;
            width : 45%;
            margin-top : 2%;

          }
          h4{
            background-color: $primary  !important;
            color: #fff;
            padding-left: 5%;
            margin : 0;
          }
          address{
            margin : 2%;
          }

          table {
            width : 100%;
            margin : 5% auto
          }
          tr {
            text-align : left;
            border: 1px solid #000000;

          }
          </style>
        </head>
    <body onload="window.print();window.close()">${printContents}</body>
      </html>`
    );

    popupWin.document.close();

  }

  editPo() {
    this.toPrint = false;
  }

  getVendors() {
    this.vendorService.getVendors(this.auth.getCurrentUser()).subscribe((data: Vendor[]) => {
      this.vendors = data;
      this.VendorBank = this.vendors;
      this.vendorsBanks.next(this.VendorBank.slice()); 
      this.purReq = this.sharedData.getEditPr();  
      this.otherTaxes = this.purReq && this.purReq.hasOwnProperty('otherTax') && this.purReq.otherTax ? this.purReq.otherTax : [];    
      this.otherTax.setValue(this.otherTaxes.map(tax => tax.taxName)); 
      if (this.purReq && Object.keys(this.purReq).length > 0 && this.purReq.vendorDetails.vendorName) {
        const vendor = this.vendors.find(vendor => vendor.name === this.purReq.vendorDetails.vendorName);
        this.createPurchaseOrderForm.get("vendor").setValue(vendor);
        this.createPurchaseOrderForm.get("vendor").disable();
        this.selectVendor(this.purReq);
      }
      this.vendorFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
        this.vendorfilterBanks();
      });
    });
  }  

  selectVendorCategory(e) {
    let obj = {
      tenantId: this.auth.getCurrentUser().tenantId,
      vendorId: this.selectedVendor,
      restaurantId: this.createPurchaseOrderForm.value.rest.restaurantIdOld
    } 
    this.purchases.fetchCategory(obj).subscribe(res => {
      if (res.result == 'success'){
        this.catItems =  res.data;
        this.category = res.data ;
        this.selectCategory(res.data);
      } else {
        this.utils.snackBarShowInfo('category items not found');
      }
    })
  }

  selectVendor(e) { 
    this.vendorName = this.purReq && this.purReq.vendorDetails && this.purReq.vendorDetails.vendorName 
    ? this.purReq.vendorDetails.vendorName : e.value.name;

    this.selectedVendor = this.purReq && this.purReq.vendorDetails && this.purReq.vendorDetails.vendorId 
    ? this.purReq.vendorDetails.vendorId : e.value.tenantId; 

    if(this.purReq && this.purReq.hasOwnProperty('poTerms')){
      this.poTerms = this.purReq.poTerms; 
    } else {
      this.poTerms = e.value.POTerms; 
    } 
    this.poTerms= this.poTerms.replace(/(\d\.\s.*?)(?=\d\.)/g, '$1\n');  
    // this.vendorData = e.value;         
    this.tempName = undefined ;
    this.selectedWorkArea = undefined ;
    this.dataSource.data = [];
    this.itemSearchText = '';
    this.addItemToPoForm.reset();
    this.addItemToPoForm.clearAsyncValidators();
    this.vendorSelected=true;
    this.createPurchaseOrderForm.get('itemType').setValue('');  
    this.selectedInvCategory = [];
    this.selectedInvCategory.push({ 'invCategoryName': 'All' })
    this.createPurchaseOrderForm.controls['invCategory'].setValue(this.selectedInvCategory[this.selectedInvCategory.length - 1]);
    this.stockSeparation ? this.stockChange('Stockable') : this.selectVendorCategory({ value: { 'invCategoryName': 'All' } }) ;
    this.createPurchaseOrderForm.get('currentVendorChecked').setValue(true);
  }

  selectCategory(event){
    let obj: any = {
      tenantId: this.auth.getCurrentUser().tenantId,
      vendorId: this.selectedVendor,
      restaurantId: this.createPurchaseOrderForm.value.rest.restaurantIdOld,
      category : event
    }
    obj['stockType'] = this.stockType ? this.stockType : undefined ;
    this.vendorInv.getVenInvItemByCatNew(obj).subscribe(data => {      
        this.vendorItems = data.invItems
        this.filteredItemList = this.vendorItems;
        this.contractItems = this.vendorItems;
        this.invItems = this.vendorItems ;
      this.Bank = this.filteredItemList;
      this.filteredVendorItems.next(this.Bank.slice());
      this.bankFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
          this.filterBanks();
      });
      this.selectVendorItems();
      this.setMyStyles();
    })

  }

  selectVendorItems() {
    var today = new Date();
    let obj: any = {
      tenantId:  this.auth.getCurrentUser().tenantId,
      restaurantId: [this.createPurchaseOrderForm.value.rest.restaurantIdOld],
      vendorName: this.vendorName,
      day: today,
    }
    this.vendorInv.getVendorData(obj).subscribe(data => {
      this.contractData =  data['result'] ? data['result'] : [];      
      if (this.contractData.length > 0){
        this.contractName = 'contract'
        let obj = {}
        obj['value'] = 'contract'
        this.itemType(obj)
      } else {
        this.contractName = 'nonContract'
        let obj = {}
        obj['value'] = 'nonContract'
        this.itemType(obj)
      }
    }, err => { console.error() })
  }

  previewPr(obj) {
    this.dialog.open(PrPreviewDialogComponent, {
    data: obj
    });
  }

  getTotal(key: string) {
    if (key === 'totalPrice') {
        this.totalAmount = this.utils.getTotal(this.dataSource.data, key) 
        return this.utils.truncateNew(this.totalAmount)
    }
    else {
        return this.utils.getTotal(this.dataSource.data, key)
    }
  }
  
  description(){
    this.descriptionFlag = !this.descriptionFlag;
  }

  convertDate(inputFormat) {
    function pad(s) { return (s < 10) ? '0' + s : s; }
    var d = new Date(inputFormat)
    return [pad(d.getDate()), pad(d.getMonth()+1), d.getFullYear()].join('/')
  }

  modifyValidityDate() {
    let tmpExpDate = this.createPurchaseOrderForm.get('expectedDate').value;
    let validityDate = new Date(tmpExpDate.getTime() + (24 * 60 * 60 * 1000));
    let validityDateString = validityDate.getDate() + '/' + (validityDate.getMonth() + 1) + '/' + validityDate.getFullYear()
    this.createPurchaseOrderForm.value.get('validityDate').setValue(validityDateString);
  }

  makePrTemplate() {
    this.purchaseOrder.prItems = this.dataSource.data;
    if (this.purchaseOrder.prItems.length > 0) {
      let inputData: any = {
        vendorId: this.selectedVendor,
        vendorName : this.vendorName,
        restaurantId: this.createPurchaseOrderForm.value.rest.restaurantIdOld,
        tenantId: this.auth.getCurrentUser().tenantId,
        prItems: this.dataSource.data,
        uId: this.auth.getCurrentUser().mId,
        contractType : this.contractType,
        totalAmount: this.totalAmount,
        remarks: this.remarks,
        paymentTerms: this.paymentTerms,
        poTerms: this.poTerms,
        paymentMethods: this.paymentMethod,
        category:this.category,
        vendorData :this.vendorData,
        cType: this.contractType
      }
      inputData['stockType'] = this.stockType ? this.stockType : undefined ;
      this.dialogRef = this.dialog.open(SimpleDialogComponent, {
        data: {
          title: 'Make PR Template',
          msg: 'Enter Template Name(min 5 and max 20 characters allowed)',
          inputFromUser: { 'Template Name': '' },
          rId: this.createPurchaseOrderForm.value.rest.restaurantIdOld,
          ok: function () {
            this.dialogRef.afterClosed().subscribe(res => {
              this.templateName = res['Template Name'];
              if(res['Template Name'] == ''){
                this.utils.snackBarShowInfo('Please enter Template Name' );
              }else{
                inputData['templateName'] = this.templateName;
                this.purchases.makePrTemplate(inputData).subscribe(data => {
                  if (data.result == 'success') {
                    this.utils.snackBarShowSuccess('Template created successfully');
                    this.getTemplates() ;
                  }
                  else {
                    this.utils.snackBarShowError('Error :' + data.exception);
                  }
                }
                  , err => console.error(err))
              }
            })
          }.bind(this)
        }
      });
    }
    else {
      this.utils.snackBarShowWarning('No items to make template');
    }
  }

  setMyStyles() {
    if (Object.keys(this.vendorItems).length >= 6) {
      this.setHeight = '200px'
    }
    else {
      this.setHeight = (Object.keys(this.vendorItems).length * 38) + 'px'

    }
  }

  setRestaurant(){
    this.dataSource.data = [];
    this.contractData = [];
    this.getVendors();
    this.addItemToPoForm.reset();
    this.roles = [];
    this.getUsers();
    this.appCat = undefined;
    this.createPurchaseOrderForm.get('itemType').setValue('');
    this.category = ''
    this.addItemToPoForm.reset();
    this.addItems = true;
  }

// ----------------------------------------DRAFT---------------------------------------------
async getDraftData(){
  let inputData: any = {
    vendorId: this.selectedVendor,
    tenantId: this.auth.getCurrentUser().tenantId,
    uId: this.auth.getCurrentUser().mId,
    rId: this.createPurchaseOrderForm.value.rest.restaurantIdOld,
    // cType: this.contractType
  }
  inputData['stockType'] = this.stockType ? this.stockType : undefined ;
  this.purchases.getDraftData(inputData).subscribe(response => {
    this.lastSeen = true; 
    let currentData = [...this.dataSource.data]; 
    let incomingData = (response.data) ? response.data.reverse() : [];

    incomingData.forEach((draftItem: any) => {
      const existingItem = currentData.find(item => item.itemCode === draftItem.itemCode && item.pkgName === draftItem.pkgName);
      if (existingItem) {
        draftItem.orderQty = existingItem.orderQty;
        draftItem.unitPrice = existingItem.unitPrice;
        draftItem.taxRate = existingItem.taxRate;
        draftItem.totalExcTax = existingItem.unitPrice * existingItem.orderQty
        draftItem.discAmt = existingItem.discAmt;
        draftItem.cessAmt = existingItem.cessAmt;
        draftItem.extraAmt = existingItem.extraAmt;
        let afterDiscount = 0;
        let tax = 0;
        afterDiscount = (existingItem.orderQty * existingItem.unitPrice) - existingItem.discAmt
        tax = afterDiscount * (existingItem.taxRate / 100)
        draftItem.subTotal = afterDiscount;
        draftItem.taxAmount = tax;
        draftItem.totalPrice = afterDiscount + existingItem.cessAmt + existingItem.extraAmt + tax;
      }
    });
    this.dataSource.data = incomingData;

    this.dataSource.data.map((element : any) => {element.defaultQty = element.orderQty; return element});
      if(response.data.length == 0){
        this.remarks = '';
        this.paymentTerms = '';
        this.paymentMethod = '';
      }else{
        response.remarks
        response.paymentTerms 
        response.poTerms 
        response.paymentMethods 
        this.remarks = response.remarks,
        this.paymentTerms = response.paymentTerms ,
        this.poTerms= response.poTerms,
        this.paymentMethod = response.paymentMethods   
      }
      if (response.lastUpdatedaAt != null){
        this.lastUpdate = response.lastUpdatedaAt;
      } else {
        this.lastUpdate = new Date() ;
      }      
  }, err => console.error(err))
}

removeItemFromDraft(itemCode,pkgName){
  let inputData: any = {
    vendorId: this.selectedVendor,
    tenantId: this.auth.getCurrentUser().tenantId,
    itemCode:itemCode,
    pkgName : pkgName,
    uId: this.auth.getCurrentUser().mId,
    rId: this.createPurchaseOrderForm.value.rest.restaurantIdOld,
    // cType: this.contractType
  }
  inputData['stockType'] = this.stockType ? this.stockType : undefined ;
  this.purchases.removeDraftData(inputData).subscribe(async ()=> { await this.getDraftData(); }, err => console.error(err));
}

async removeDraftDataBulK(){
  let inputData: any = {
    vendorId: this.selectedVendor,
    tenantId: this.auth.getCurrentUser().tenantId,
    uId: this.auth.getCurrentUser().mId,
    rId: this.createPurchaseOrderForm.value.rest.restaurantIdOld,
    // cType: this.contractType
  }
  inputData['stockType'] = this.stockType ? this.stockType : undefined ;
  this.purchases.removeDraftDataBulK(inputData);
}

addItemToDraft(source){
  this.dataSource.data.push(source)
  this.purchaseOrder.prItems = source
  this.purchaseOrder.orderedDate = new Date();
  let inputData: any = {
    vendorId: this.selectedVendor,
    tenantId: this.auth.getCurrentUser().tenantId,
    prItems: this.purchaseOrder.prItems,
    itemList: this.dataSource.data,
    uId: this.auth.getCurrentUser().mId,
    rId: this.createPurchaseOrderForm.value.rest.restaurantIdOld,
    itemCode:source.itemCode,
    pkgName : source.pkgName,
    // cType: this.contractType,
    remarks : this.remarks,
    paymentTerms : this.paymentTerms,
    poTerms : this.poTerms,
    paymentMethods : this.paymentMethod
  }
  inputData['stockType'] = this.stockType ? this.stockType : undefined ;
  this.purchases.addDraftData(inputData).subscribe(async ()=> { await this.getDraftData(); }, err => console.error(err));
}

getUsers(){
  let inputData: any = {
    tenantId: this.auth.getCurrentUser().tenantId,
    restaurantId: this.createPurchaseOrderForm.value.rest.restaurantIdOld,
    type: "purchaseRequest"
  }
    this.purchases.getSelectedUsers(inputData).subscribe(data => {
      this.res = data;      
      let role = []
      data.data.forEach(function(element) {
          role.push(element.role)
      });
      this.contractApproval =  data.TenantDetails[0].permission.contractApprovalForPr ?  data.TenantDetails[0].permission.contractApprovalForPr : false ;
      this.stockSeparation = data.TenantDetails[0].permission.stockSeparation ?  data.TenantDetails[0].permission.stockSeparation : false  ;
      this.totalRoles = data.data;
      this.roles = this.totalRoles;
      if (this.roles.length === 0){
        this.allowOrder = true;
      }
      if (this.roles.length > 0 && this.contractType == 'nonContract'){
        this.allowOrder = false;
      }
  })
}

selectedRoles(val){
  if (val.value.length === 0){
    this.allowOrder = false;
  }else {
    this.allowOrder = true;
  }
  let role = [],appCat
  val.value.forEach(function(element) {
      role.push(element.value);
      appCat = element.cat
  });
  this.appCat = appCat ;
  this.selectedRole = role ;
if(this.appCat){
  this.roles = this.totalRoles.filter(item => item.appCat === this.appCat)
} else {
  this.roles = this.totalRoles
}
}

itemType(val) {
  this.addItemToPoForm.reset();
  this.contractType = val.value;
	this.addItems = true;
	let priceListData = [],
		contractPriceData = [],
		nonContractPriceData = [];
	this.contractData.forEach(item => {
		priceListData.push(item.itemCode)
	});
	this.contractItems.forEach(item => {
    if (priceListData.includes(item['itemCode'])) {
      let contractItem = JSON.parse(JSON.stringify(item)); 
      item['packagingSizes'].forEach((el) => {
          let contractedItem = this.contractData.find((data) => data['itemCode'] == item['itemCode'] && data['packageName'] == el['packageName']);
          if (contractedItem) {
              contractItem['contractItem'] = true;
              let index = contractPriceData.findIndex((ci) => ci['itemCode'] === item['itemCode']);
              if (index !== -1) {
                  contractPriceData[index]['packagingSizes'].push(el);
              } else {
                  contractItem['packagingSizes'] = [el];
                  contractPriceData.push(contractItem);
              }
          } else {
              let nonContractItem = JSON.parse(JSON.stringify(item)); 
              nonContractItem['contractItem'] = false;
              let index = nonContractPriceData.findIndex((ci) => ci['itemCode'] === item['itemCode']);
              if (index !== -1) {
                  nonContractPriceData[index]['packagingSizes'].push(el);
              } else {
                  nonContractItem['packagingSizes'] = [el];
                  nonContractPriceData.push(nonContractItem);
              }
          }
      });
    } else {
      let newItem = JSON.parse(JSON.stringify(item)); 
      nonContractPriceData.push(newItem);
    }
	});
  
	if (val.value == 'contract') {
    this.filteredItemList = contractPriceData ;
    this.Bank = this.filteredItemList;
    this.filteredVendorItems.next(this.Bank.slice());
    this.bankFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
        this.filterBanks();
    });
    this.allowOrder = (this.contractApproval && this.roles.length > 0) ? false : true ;
	} else if (val.value == 'nonContract') {
    if (this.roles.length > 0 && val.value == 'nonContract'){
      this.allowOrder = false;
    }
    this.filteredItemList = nonContractPriceData ;
    this.Bank = this.filteredItemList;
    this.filteredVendorItems.next(this.Bank.slice());
    this.bankFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
        this.filterBanks();
    });
	}
  this.getDraftData();
}

  getTemplates() {
    let obj = {
      "restaurantId":this.createPurchaseOrderForm.value.rest.restaurantIdOld,
      "tenantId": this.auth.getCurrentUser().tenantId,
      "vendorId": this.selectedVendor,
      "uId": this.auth.getCurrentUser().mId,
      "cType": this.contractType
    }
    obj['stockType'] = this.stockType ? this.stockType : undefined ;
    this.purchases.getTemplates(obj).subscribe(data => {
      this.templates = data.prTmpList;
      this.getAllTemplatesData = data.prTmpList;
    })
  }

  getTemplate(event,element) {
  	let inputData: any = {
  		vendorId: this.selectedVendor,
  		tenantId: this.auth.getCurrentUser().tenantId,
  		uId: this.auth.getCurrentUser().mId,
  		rId:this.createPurchaseOrderForm.value.rest.restaurantIdOld,
  		cType: this.contractType
  	}
    inputData['stockType'] = this.stockType ? this.stockType : undefined ;
  	this.purchases.removeDraftDataBulK(inputData).subscribe(() => {})
  	let obj = {
  		templateId: element._id.$oid
  	}
  	this.tempName = element.templateName;
  	this.purchases.getTempItems(obj).subscribe(data => {
  		let updatedItems = [];
  		if (data.prTmpList.cType == 'contract') {        
        this.showTemplateName = data.prTmpList.templateName
        this.remarks = data.prTmpList.remarks;
        this.paymentTerms = data.prTmpList.paymentTerms ;
        this.poTerms = data.prTmpList.poTerms;
        this.paymentMethod = data.prTmpList.paymentMethods; 
        this.isShowTemplateName = true
        this.isContract = true ;
  			data.prTmpList.prItems.forEach(el => {
  				let foundContract = this.contractData.find(item => item.itemCode === el.itemCode && item.packageName === el.pkgName);
  				if (foundContract['price']) {
  					el['unitPrice'] = foundContract['price'];
            this.validateReceivedQty(event ,el) ;
  					updatedItems.push(el);
  					this.addDraft(el);
  				}else if(el){
            this.validateReceivedQty(event ,el) ;
  					updatedItems.push(el);
  					this.addDraft(el);
          } 
          else {
  					updatedItems = [];
            this.utils.snackBarShowError(`item ${el.itemCode} not found in contract list please fix this issue`);
  				}
  			});
  			this.dataSource.data = updatedItems;
        this.dataSource.data.map((element : any) => {element.defaultQty = element.orderQty; return element});
        this.getDraftData();
  		} else if (data.prTmpList.cType == 'nonContract') {
        this.showTemplateName = data.prTmpList.templateName
        this.remarks = data.prTmpList.remarks;
        this.paymentTerms = data.prTmpList.paymentTerms;
        this.poTerms = data.prTmpList.poTerms;
        this.paymentMethod = data.prTmpList.paymentMethods; 
        this.isShowTemplateName = true
        this.isContract = false ;
  			data.prTmpList.prItems.forEach(el => {
  				let params = {
  					'restaurantId': this.createPurchaseOrderForm.value.rest.restaurantIdOld,
  					'vendorId': data.prTmpList.vendorId,
  					'itemCode': el.itemCode,
  					'packageName': el.pkgName
  				}
  				this.purchases.getLastGrnPrice(params).subscribe(data => {
  					if (data['success']) {
  						el['unitPrice'] = data['unitPrice'];
  						updatedItems.push(el);
  						this.addDraft(el);
  					} else {
  						let maserDataPrice = this.invItems.find(item => item.itemCode === el.itemCode && item.packagingSizes[0].packageName === el.pkgName);
  						if (maserDataPrice) {
  							el['unitPrice'] = maserDataPrice['unitPrice'];
                this.validateReceivedQty(event , el) ;
  							updatedItems.push(el);
  							this.addDraft(el);
  						} else {
                this.utils.snackBarShowError(`item ${el.itemCode} not found in master data please fix this issue`);
  						}
  					}
  					this.dataSource.data = updatedItems;
            this.dataSource.data.map((element : any) => {element.defaultQty = element.orderQty; return element});
  				})
  			})
  		}
  	})
    this.isShowTemplate = false
  }

  addDraft(el){
    this.dataSource.data.push(el)
    let inputData: any = {
      vendorId: this.selectedVendor,
      tenantId: this.auth.getCurrentUser().tenantId,
      prItems: el,
      itemList: this.dataSource.data,
      uId: this.auth.getCurrentUser().mId,
      rId: this.createPurchaseOrderForm.value.rest.restaurantIdOld,
      cType: this.contractType
    }
    inputData['stockType'] = this.stockType ? this.stockType : undefined ;
    this.purchases.addDraftData(inputData).subscribe(data => {
    })
  }
  
  deleteTemplate(event) {
    let obj = {
      "tempName": event.templateName,
      "tenantId": this.auth.getCurrentUser().tenantId,
      "restaurantId": this.createPurchaseOrderForm.value.rest.restaurantIdOld
    }
    obj['stockType'] = this.stockType ? this.stockType : undefined ;
  	this.purchases.deleteTemplate(obj).subscribe(data => {
      this.utils.snackBarShowSuccess('Template Removed Successfully');
      this.getTemplates()
    })
  }

  updateTemplate() {
  	let inputData: any = {
  		vendorId: this.selectedVendor,
  		vendorName: this.vendorName,
  		restaurantId: this.createPurchaseOrderForm.value.rest.restaurantIdOld,
  		tenantId: this.auth.getCurrentUser().tenantId,
  		prItems: this.dataSource.data,
  		uId: this.auth.getCurrentUser().mId,
  		templateName: this.tempName,
  		cType: this.contractType
  	}
    inputData['stockType'] = this.stockType ? this.stockType : undefined ;
  	this.purchases.updateTemplate(inputData).subscribe(data => {
      this.utils.snackBarShowSuccess('Template Updated Successfully');
  	})
  }

  viewTemplate(){
    if(this.isShowTemplate == false){
      this.isShowTemplate = true
    }else{
      this.isShowTemplate = false
    }
    this.getTemplates();
  }

  goBack(){
    this.isShowTemplate = false
  }

  doFilter = (value: any) => {
    let result = this.templates.filter(item => item.templateName.trim().includes(value));
    if(result.length > 0 && value != '' ){
      this.templates = result
    }else{
      if(value == ''){
        this.templates = this.getAllTemplatesData
      }else{
        let result = this.getAllTemplatesData.filter(item => item.templateName.trim().includes(value));
        this.templates = result
      }
    }
  }

  addExtraFields() {
    if (this.allExtraFieldFlag === true) {
      this.displayedColumns = ['index', 'itemCode', 'hsnCode', 'itemName', 'pkgName', 'unitPerPkg', 'orderQty', 'unitPrice', 'totalValueExcTax','taxableAmt','rate', 'taxAmt','extraAmt', 'discnt', 'cessAmt', 'totalValue', 'actionBtns']
    } else {
      this.displayedColumns = ['index', 'itemCode', 'hsnCode',  'itemName', 'pkgName', 'unitPerPkg', 'orderQty', 'unitPrice', 'totalValueExcTax','rate', 'taxAmt', 'totalValue', 'actionBtns']
    }
  }

  doTableFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }

  clearSearchText(){
    this.searchText = '';
  }
  setQtyToZero(){
    if(this.setOrderQty){
      this.dataSource.data.map((element : any) => {
        element.orderQty = 0; 
        return element
      })
    }else{
      this.dataSource.data.map((element : any) => {
        element.orderQty = element.defaultQty;        
        return element
      })
    }
  }

 stockChange(event){
  this.stockType = event ;
  this.selectedWorkArea = undefined ;
  this.selectVendorCategory({ value: { 'invCategoryName': 'All' } }) ;
  this.user.restaurantAccess.forEach(element => {
    if(element.restaurantIdOld == this.user.restaurantId){
      if (element.workAreas == undefined){
        this.workAreas = [];
      }else{
        this.workAreas = element.workAreas;
      }
    }
  });
  this.getDraftData() ;
 }

  onEnterPressed(event: KeyboardEvent){    
    if (!event || !(event instanceof KeyboardEvent)) {
      return;
    }
    const target = event.target as HTMLElement;
    if (target.tagName === 'TEXTAREA' || target.tagName === 'BUTTON') {
      return;
    }
  }

  checkNumericInput(event: any) {    
    if (this.addItemToPoForm.value.uom === 'NOS') {
      const input = event.target.value;
      event.target.value = input.replace(/[^0-9]/g, ''); 
      this.addItemToPoForm.get('orderQty').setValue(event.target.value);
    }
  }

taxRateChange() {
  let data = this.unitCostData.find(item => item.unitPrice === this.addItemToPoForm.get('unitPrice').value)
  if((data && this.addItemToPoForm.value.taxRate === data.taxRate)){
    this.addItemToPoForm.get('taxRate').setValue(data.taxRate)
  } else if(!this.addItemToPoForm.value.taxRate){
    this.addItemToPoForm.get('taxRate').setValue(0)
  }
  this.setUnitPrice() ;
}

  getItemTotal(){
    let total
    // const contractValue = this.contractData.find(item => item.itemName === this.addItemToPoForm.value.inventoryItem.itemName && item.packageName === this.currentPKG['packageName'] );
        total  = this.addItemToPoForm.get('orderQty').value *
        (Number(this.addItemToPoForm.get('unitPrice').value) +
          (Number(this.addItemToPoForm.get('unitPrice').value) *
          Number(this.addItemToPoForm.get('taxRate').value)) /
            100);
    this.totalOfAllAmount = this.utils.truncateNew(total)
  }

  focusFunction(formKey) {
    if(Number(this.addItemToPoForm.get(formKey).value) === 0){
      this.addItemToPoForm.get(formKey).setValue(null)
    }
  }
  
  focusOutFunction(formKey) {
    if(this.addItemToPoForm.get(formKey).value === null){
      this.addItemToPoForm.get(formKey).setValue(0)
    }
  }

  focusFunctionWithOutForm(element , value){
    if(Number(element[value]) === 0){
      element[value] = null;
    }
  }
  
  focusOutFunctionWithOutForm(element , value){
    if(element[value] === null){
      element[value] = 0
    }
  }

  cessAmount(element){
    let amt =  this.utils.truncateNew(element.orderQty * element.unitPrice) - element.discAmt
    if(element.discAmt > element.totalExcTax){
      element.discAmt = 0
    }
  }

  taxStructureData(){
    let obj = {}
    obj['tenantId'] = this.user.tenantId
    obj['location'] = this.createPurchaseOrderForm.value.rest.restaurantIdOld
      this.masterDataService.getTaxData(obj).subscribe((response: any) => {
        if (response.success === true) {
          this.taxWholeData = response.data
          this.taxData = response.data.map(item => item.taxName)
          this.taxRateBank = this.addItemToPoForm.get('taxRate').valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), this.taxData)));
        }
      }); 
  }

  optionSelected(option: any) {
    if (option.value.indexOf(this.question) === 0) {      
      this.addOption();
    }   
    this.taxRateBank = this.addItemToPoForm.get('taxRate').valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), this.taxData)));  
    this.dialogTaxData = this.taxWholeData.find(item => item.taxName === this.addItemToPoForm.value.taxRate)
    if(this.dialogTaxData){
      this.addItemToPoForm.get('taxRate').setValue(this.utils.truncateNew(this.dialogTaxData.tax))
    }
    this.getItemTotal();
  }
  
  addOption() {
    this.addItemToPoForm.controls['taxRate'].patchValue(this.removePromptFromOption(this.addItemToPoForm.value.taxRate));
  }

  removePromptFromOption(option) {
    if (option.startsWith(this.question)) {
      option = option.substring(this.question.length, option.length - 1);
    }
    return option;
  }

  protected _costFilter(value , input) {
    let filterValue = value;
    let filtered = input.filter(option => option.includes(filterValue));
        if (filtered.length == 0) {
      filtered = [this.question + value + '"'];
    }
        return filtered
  }

  protected _filter(value: string, input: string[]): string[] {
    let filterValue = value.toString().toLowerCase();
    let filtered = input.filter(option => option.toLowerCase().includes(filterValue));
        if (filtered.length == 0) {
      filtered = [this.question + value + '"'];
    }
        return filtered
  }

  openDialog(): void {
    if (((this.totalOfAllAmount !== null && this.totalOfAllAmount !== undefined) && this.addItemToPoForm.value.taxRate && this.dialogTaxData)) {
      const dialogRef = this.dialog.open(this.openTaxDialog, {
        width: '250px',
      });
    }
  }

  closeDialog(): void {
    this.dialog.closeAll();
  }

  onKeyDown(event: KeyboardEvent) {
    if (event.key === '.' && !event.shiftKey) {
      event.preventDefault(); // Prevent default dot behavior
      this.insertSpaceAfterDot();
    } else if (event.key === 'Enter') {
      event.preventDefault(); // Prevent default Enter behavior
      this.handleEnterKey();
    }
  }

  private insertSpaceAfterDot() {
    const textInput = this.textInputRef.nativeElement;
    const currentValue = textInput.value;
    const cursorPosition = textInput.selectionStart;
    const newText = currentValue.slice(0, cursorPosition) + '. ' + currentValue.slice(cursorPosition);
    textInput.value = newText;
    textInput.selectionStart = cursorPosition + 2; // Move cursor after the inserted dot and space
    textInput.selectionEnd = cursorPosition + 2;
  }

  private handleEnterKey() {
    const textInput = this.textInputRef.nativeElement;
    const currentValue = textInput.value;
    const lines = currentValue.split('\n');
    const lastLine = lines[lines.length - 1];
  
    // Check if the last line is a bullet point (e.g., "1. ", "2. ", etc.)
    const isBulletPoint = /^\d+\.\s/.test(lastLine.trim());
  
    if (isBulletPoint) {
      // If there is a bullet point, find the next number and add it with a bullet
      const nextNumber = parseInt(lastLine.trim().split('.')[0], 10) + 1;
      const newText = `${currentValue}\n${nextNumber}. `;
      textInput.value = newText;
    } else {
      // If there is no bullet point, simply add a new line
      textInput.value = `${currentValue}\n`;
    }
    textInput.scrollTop = textInput.scrollHeight;
  }

  openPriceDialog(): void {
    this.enableDialogIcon = true;
    const contractValue = this.contractData.find(item => item.itemName === this.addItemToPoForm.value.inventoryItem.itemName && item.packageName === this.currentPKG['packageName'] );
    this.dialogData = contractValue
      const dialogRef = this.dialog.open(this.openPriceDetailsDialog, {
      });
  }

  onMouseOver() {
    this.showTooltip = true;
    const contractValue = this.contractData.find(item => item.itemName === this.addItemToPoForm.value.inventoryItem.itemName && item.packageName === this.currentPKG['packageName'] );
    this.dialogData = contractValue
  }

  onMouseOut() {
    this.showTooltip = false;
  }

  capitalizeWords(input) {
    const hasMRP = input.toLowerCase().includes('mrp');
    if (hasMRP) {
      const parts = input.split('MRP');
      const firstPart = parts[0].split(/(?=[A-Z])/).map(word => word.toUpperCase()).join(' ');
      const secondPart = parts[1].split(/(?=[A-Z])/).map(word => word.toUpperCase()).join(' ');
      const capitalized = `${firstPart} MRP ${secondPart}`;
      return capitalized;
    } else {
      const words = input.split(/(?=[A-Z])/);
      const capitalizedWords = words.map(word => word.toUpperCase()).join(' ');
      return capitalizedWords;
    }
  }

  tenantDetail() {
    let obj = {
      tenantId: this.user.tenantId
    }
    this.branchTransfer.tenantDetails(obj).subscribe(res => {
      if (res.result == 'success') {
        let tenantDetails = res.data[0].permission 
        let deleteAccess = res.data[0] && res.data[0].permission && res.data[0].permission.POAccess ? res.data[0].permission.POAccess.delete : false;
        let editAccess = res.data[0] && res.data[0].permission && res.data[0].permission.POAccess ? res.data[0].permission.POAccess.edit : false;
        if (deleteAccess) {
          let rolesList = (res.data[0] && res.data[0].permission && res.data[0].permission.POAccess) ? res.data[0].permission.POAccess.deleteAccess : [];
          this.deleteAccess = (rolesList.some(role => role.toLowerCase() === this.user.role.toLowerCase())) ? true : false ;
        } else {
          this.deleteAccess = false ;
        }
        if (editAccess) {
          let rolesList = (res.data[0] && res.data[0].permission && res.data[0].permission.POAccess) ? res.data[0].permission.POAccess.editAccess : [];
          this.editAccess = (rolesList.some(role => role.toLowerCase() === this.user.role.toLowerCase())) ? true : false ;
        } else {
          this.editAccess = false ;
        }
      } else {
        this.deleteAccess = false ;
        this.editAccess = false ;
      }
    });
  }

  calAllAmount(){
    let total
    if(this.dialogData.priceType === 'absolutePriceReduction' || this.dialogData.priceType === 'discountOnMRP'){
      total = this.addItemToPoForm.get('orderQty').value *
        (Number(this.addItemToPoForm.get('unitPrice').value) +
        (Number(this.addItemToPoForm.get('unitPrice').value) *
        Number(this.addItemToPoForm.get('taxRate').value)) /
        100);
      return this.utils.truncateNew(total)
    }else{
      total =  this.addItemToPoForm.get('orderQty').value *
        (Number(this.contactPrice) +
        (Number(this.contactPrice) *
        Number(this.addItemToPoForm.get('taxRate').value)) /
          100);
      return this.utils.truncateNew(total)
    }
  }

  calAllTaxAmount(){
    let totalAmount = Number(this.addItemToPoForm.value.unitPrice) + (Number(this.addItemToPoForm.value.unitPrice) * (Number(this.addItemToPoForm.get('taxRate').value) / 100))
    return this.utils.truncateNew(totalAmount);
  }

  getTaxAmount(qty){
    let taxAmount = qty * (Number(this.addItemToPoForm.value.convertedPrice) * (Number(this.addItemToPoForm.get('taxRate').value) / 100))
    return this.utils.truncateNew(taxAmount);
  }



  getTotalAmount(qty){
    let total = qty * ( Number(this.addItemToPoForm.value.convertedPrice) + (Number(this.addItemToPoForm.value.convertedPrice) * (Number(this.addItemToPoForm.get('taxRate').value) / 100)))
    return this.utils.truncateNew(total);
  }

  getDiscountAmount(qty){
    let mrp = this.utils.truncateNew(this.addItemToPoForm.get('convertedPrice').value)  
    let contractDetails = this.addItemToPoForm.get('contractDetails').value
    let absolutePrice =  mrp * (contractDetails['percentage'] / 100 )
    return this.utils.truncateNew(qty * absolutePrice);
  }

  getAbsolutePrice(qty){
    let contractDetails = this.addItemToPoForm.get('contractDetails').value
    return this.utils.truncateNew(qty * contractDetails['absolutePrice']);
  }

  getFinalUnitCostWithoutTax(qty){
    let contractDetails = this.addItemToPoForm.get('contractDetails').value,unitPrice,mrp,absolutePrice,taxRate
    mrp = this.utils.truncateNew(this.addItemToPoForm.get('convertedPrice').value)
    taxRate = this.utils.truncateNew(this.addItemToPoForm.get('taxRate').value) 
    if(contractDetails.hasOwnProperty('priceType') && contractDetails['priceType'] === 'discountOnMRP') {
      absolutePrice = mrp - (mrp * (contractDetails['percentage'] / 100 ))
      unitPrice = (absolutePrice / (100 + taxRate)) * 100 ;
    } else if (contractDetails.hasOwnProperty('priceType') && contractDetails['priceType'] === 'absolutePriceReduction'){
      absolutePrice = mrp - contractDetails['absolutePrice']
      unitPrice = (absolutePrice / (100 + taxRate)) * 100 ;
    }else if (contractDetails.hasOwnProperty('priceType') && contractDetails['priceType'] === 'fixedContractPrice') {
      absolutePrice = contractDetails['price']
      if (contractDetails.hasOwnProperty('contractTax')) {
        unitPrice = (absolutePrice / (100 + taxRate)) * 100;
      }
    }
    return this.utils.truncateNew(qty * unitPrice);
  }


  getFinalCost(qty){
    let total  = qty *
    (Number(this.addItemToPoForm.get('unitPrice').value) +
      (Number(this.addItemToPoForm.get('unitPrice').value) *
      Number(this.addItemToPoForm.get('taxRate').value)) /
        100);
    return this.utils.truncateNew(total)
  }




  openToolTip(){
    this.onMouseOver();
  }

  closeToolTip(){
    this.onMouseOut();
  }

  onKeyUp(event: KeyboardEvent): void {
    if (event.key !== 'Tab') {
      this.getItemTotal();
    }
  }

  setUnitPrice() {
  let contractDetails = this.addItemToPoForm.get('contractDetails').value,unitPrice,mrp,absolutePrice,taxRate
  mrp = this.utils.truncateNew(this.addItemToPoForm.get('convertedPrice').value)
  taxRate = this.utils.truncateNew(this.addItemToPoForm.get('taxRate').value) 
  if(contractDetails.hasOwnProperty('priceType') && contractDetails['priceType'] === 'discountOnMRP') {
    absolutePrice = mrp - (mrp * (contractDetails['percentage'] / 100 ))
    unitPrice = (absolutePrice / (100 + taxRate)) * 100 ;
    this.addItemToPoForm.get('unitPrice').setValue(this.utils.truncateNew(unitPrice))
  } else if (contractDetails.hasOwnProperty('priceType') && contractDetails['priceType'] === 'absolutePriceReduction'){
    absolutePrice = mrp - contractDetails['absolutePrice']
    unitPrice = (absolutePrice / (100 + taxRate)) * 100 ;
    this.addItemToPoForm.get('unitPrice').setValue(this.utils.truncateNew(unitPrice))
  } else if (contractDetails.hasOwnProperty('priceType') && contractDetails['priceType'] === 'fixedContractPrice') {
    absolutePrice = contractDetails['price']
    if (contractDetails.hasOwnProperty('contractTax')) {
      unitPrice = (absolutePrice / (100 + taxRate)) * 100;
      this.addItemToPoForm.get('unitPrice').setValue(this.utils.truncateNew(unitPrice));
    }
  }
  this.getItemTotal();
  }

  checkNegativeCost() {
    return this.addItemToPoForm.get('unitPrice').value < 0  ? true : false ;
  }

  focusFn(item: any){
    if(Number(item.value) === 0){
      item.value = null;
    }
  }
  
  focusOutFn(item: any){
    if (item.value < 0) {
      item.value = 0;
    }
    if(item.value === null){
      item.value = 0
    }
  }

  getGrandTotal() {
    let itemsTotal = this.otherTaxes.reduce((sum, item) => {
        const value = parseFloat(item.value) || 0;
        return sum + value;
    }, 0);
    this.grandTotal = this.utils.truncateNew(this.totalAmount + itemsTotal);
    return this.grandTotal
  }

  addTax(selectedTaxes: string[]) {
    const updatedTaxes = [];  
    selectedTaxes.forEach((tax) => {
      const existingTax = this.otherTaxes.find(item => item.taxName === tax);
      if (existingTax) {
        updatedTaxes.push(existingTax);  
      } else {
        updatedTaxes.push({ taxName: tax, value: 0 });
      }
    });  
    this.otherTaxes = updatedTaxes;
  }

}


