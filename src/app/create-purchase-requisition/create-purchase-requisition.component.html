<div *ngIf="!isShowTemplate">
    <div>
      <form [formGroup]="createPurchaseOrderForm">

        <div class="topItem">
        <mat-form-field appearance="none">
          <label>Restaurant</label>
          <mat-select formControlName="rest" placeholder="Restaurant" class="outline"
            (selectionChange)="getBranchDetails()">
            <mat-option *ngFor="let rest of branchesData" [value]="rest">
              {{ rest.branchName }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <button mat-button *ngIf="showInputs" (click)="createPr()" [disabled]="dataSource.data.length == 0 || !delDate"
        class="button3 bottomButtons ml-2" style="float: right;">
        {{ approvalRequired ? 'Create PR Approval' : 'Create PR' }}
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-send ml-2"
          viewBox="0 0 16 16">
          <path
            d="M15.854.146a.5.5 0 0 1 .11.54l-5.819 14.547a.75.75 0 0 1-1.329.124l-3.178-4.995L.643 7.184a.75.75 0 0 1 .124-1.33L15.314.037a.5.5 0 0 1 .54.11ZM6.636 10.07l2.761 4.338L14.13 2.576 6.636 10.07Zm6.787-8.201L1.591 6.602l4.339 2.76 7.494-7.493Z" />
        </svg>
      </button>

          <button mat-button *ngIf="addItems" (click)="viewTemplate()" class="button bottomButtons ml-2" style="float: right;">
            View Template
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-eye"
              viewBox="0 0 16 16">
              <path
                d="M16 8s-3-5.5-8-5.5S0 8 0 8s3 5.5 8 5.5S16 8 16 8zM1.173 8a13.133 13.133 0 0 1 1.66-2.043C4.12 4.668 5.88 3.5 8 3.5c2.12 0 3.879 1.168 5.168 2.457A13.133 13.133 0 0 1 14.828 8c-.058.087-.122.183-.195.288-.335.48-.83 1.12-1.465 1.755C11.879 11.332 10.119 12.5 8 12.5c-2.12 0-3.879-1.168-5.168-2.457A13.134 13.134 0 0 1 1.172 8z" />
              <path d="M8 5.5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5zM4.5 8a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0z" />
            </svg>
          </button>
    
          <!-- <button mat-button (click)="updateTemplate()" class="button bottomButtons ml-2" *ngIf='tempName  && showInputs'
            style="float: right;">
            Update Template
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-arrow-repeat"
              viewBox="0 0 16 16">
              <path
                d="M11.534 7h3.932a.25.25 0 0 1 .192.41l-1.966 2.36a.25.25 0 0 1-.384 0l-1.966-2.36a.25.25 0 0 1 .192-.41zm-11 2h3.932a.25.25 0 0 0 .192-.41L2.692 6.23a.25.25 0 0 0-.384 0L.342 8.59A.25.25 0 0 0 .534 9z" />
              <path fill-rule="evenodd"
                d="M8 3c-1.552 0-2.94.707-3.857 1.818a.5.5 0 1 1-.771-.636A6.002 6.002 0 0 1 13.917 7H12.9A5.002 5.002 0 0 0 8 3zM3.1 9a5.002 5.002 0 0 0 8.757 2.182.5.5 0 1 1 .771.636A6.002 6.002 0 0 1 2.083 9H3.1z" />
            </svg>
          </button> -->
    
          <button mat-button (click)="makePrTemplate()" *ngIf="addItems" [disabled]="dataSource.data.length == 0 "
            class="button bottomButtons ml-2" style="float: right;">
            Add Template
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-plus-circle"
              viewBox="0 0 16 16">
              <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z" />
              <path
                d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z" />
            </svg>
          </button>
        </div>

  <!-- <mat-card class="soMatCard"> -->
    <div  *ngIf="showInputs" class="search-table-input fieldcontainer" >
        <mat-form-field appearance="none" class="ml-2">
          <label>Stock Type</label>
          <mat-select placeholder="Stock Type" formControlName="stockType" class="outline"
            (selectionChange)="stockChange($event.value)" [(ngModel)]="stockType" [disabled]="disableStock ">
            <mat-option value="Stockable">Stockable Items</mat-option>
            <mat-option value="Non-Stockable">Non-Stockable Items</mat-option>
          </mat-select>
        </mat-form-field>

          <!-- <mat-form-field appearance="none" class="ml-2">
            <label>Contract Type</label>
            <mat-select placeholder="Contract Type" formControlName="contract" class="outline"
            [(ngModel)]="contractType" (selectionChange)="contractChange($event.value)">
              <mat-option value="contract">Contract Items</mat-option>
              <mat-option value="nonContract">Non-Contract Items</mat-option>
            </mat-select>
          </mat-form-field> -->
  
        <mat-form-field appearance="none" class="ml-2">
          <label>Select Category</label>
          <mat-select formControlName="category" placeholder="select Category" class="outline"
            (selectionChange)="categorySelection()" [(ngModel)]="selectedCategory">
            <mat-option *ngFor="let cat of this.category" [value]="cat">
              {{ cat }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="none" class="ml-2">
          <label>Delivered To</label>
          <mat-select formControlName="workArea" placeholder="Delivered To" class="outline"
            [(ngModel)]="selectedWorkArea">
            <mat-option *ngFor="let area of workAreas" [value]="area" [required]="stockSeparation">
              {{ area }}
            </mat-option>
          </mat-select>
          <mat-error>
            Select workArea
          </mat-error>
        </mat-form-field>

        <!-- <mat-form-field appearance="outline" class="ml-2 find1" style="font-size: 12px;">
          <mat-label> Delivery Date </mat-label>
            <input matInput [matDatepicker]="picker1" tabindex="-1" [(ngModel)]="delDate" placeholder="Delivery Date"
            [formControl]="issueDate"/>
          <mat-datepicker-toggle matSuffix [for]="picker1" tabindex="-1">
            <mat-icon matDatepickerToggleIcon><img src="./../../assets/calender.png" /></mat-icon>
          </mat-datepicker-toggle>
          <mat-datepicker #picker1 disabled="false"></mat-datepicker>
          <mat-error>
            Select Delivery Date
          </mat-error>
        </mat-form-field> -->

        <mat-form-field appearance="none" class="ml-2">
          <label> Delivery Date </label>
            <input matInput [matDatepicker]="picker1" tabindex="-1" [(ngModel)]="delDate" placeholder="Delivery Date"
            [formControl]="issueDate" class="outline"/>
          <mat-datepicker-toggle matSuffix [for]="picker1" tabindex="-1">
            <mat-icon matDatepickerToggleIcon><img src="./../../assets/calender.png" /></mat-icon>
          </mat-datepicker-toggle>
          <mat-datepicker #picker1 disabled="false"></mat-datepicker>
          <mat-error>
            Select Delivery Date
          </mat-error>
        </mat-form-field>

        <button mat-button class="button3 find ml-2" (click)="getDatas()" [disabled]="createPurchaseOrderForm.invalid ">
          find
        </button>
      </div>
      </form>
    </div>


  <div *ngIf="addItems" class="search-table-input fieldcontainer" id="addItemForm">
    <div class="my-2">
      <section class="example-section">
        <mat-checkbox class="example-margin" [(ngModel)]="checked" [ngModelOptions]="{standalone: true}">Add
          Description</mat-checkbox>
      </section>
    </div>
    <form [formGroup]="addItemToPoForm">
      <div cdkTrapFocus>
        <div>
          <mat-form-field appearance="none">
            <label>Item Name</label>
            <mat-select placeholder="Inventory Item" #singleSelect tabindex="0" #accTypeInventory
              (focus)="accTypeInventory.open()" class="outline" formControlName="inventoryItem"
              (selectionChange)="purchaseItemSelect($event)">
              <mat-option>
                <ngx-mat-select-search placeholderLabel="Inventory Item..."
                  noEntriesFoundLabel="'no Inventory Item found'"
                  [formControl]="bankFilterCtrl"></ngx-mat-select-search>
              </mat-option>
              <mat-option *ngFor="let item of filteredVendorItems | async" [value]="item">
                {{item.itemName}}
              </mat-option>
            </mat-select>
            <mat-error>
              Select an Item
            </mat-error>
          </mat-form-field>

          <mat-form-field *ngIf="showPkg" appearance="none">
            <label>Pkg size</label>
            <mat-select #accTypepkgName (focus)="accTypepkgName.open()" focusOnInit placeholder="Pkg size"
              class="outline" formControlName="pkgName" (selectionChange)="pkgSelect($event)">
              <mat-option *ngFor="let pkg of packagingSizes" [value]="pkg">
                {{pkg.packageName}}
              </mat-option>
            </mat-select>
            <mat-error>
              Select a package
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="none" *ngIf="brand">
            <label>Brand</label>
            <input matInput class="outline" type="text" [(ngModel)]="brand" placeholder="Brand"
              formControlName="selectedBrand" [formControl]="selectedBrandControl" readonly />
          </mat-form-field>

          <mat-form-field appearance="none" fxFlex="10%">
            <label>Quantity</label>
            <input matInput type="number" step="0.01" min="0" class="outline" [(ngModel)]="quantity"
              (keyup)="getTotalofAllamount()" (input)="checkNumericInput($event)" (focus)="focusFunction('orderQty')" (focusout)="focusOutFunction('orderQty')" placeholder="Quantity"
              formControlName="orderQty">
              <!-- onkeypress="return (event.charCode != 45 && (event.charCode >= 48 && event.charCode <= 57) || event.charCode == 46)" -->
            <mat-error>
              Add Quantity
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="none" fxFlex="10%">
            <label>unit cost</label>
            <input matInput type="number" step="0.01" min="0" class="outline"
              (keyup)="makeZero($event , element); getTotalofAllamount()" [(ngModel)]="unitCost" placeholder="Unit Cost"
              readonly formControlName="unitPrice" [matTooltip]="isContract  ? 'Contract price' : ''" />
            <div class="loaderSpinDiv" *ngIf="soLoader">
              <div class="spinner-border" role="status">
                <span class="sr-only">Loading...</span>
              </div>
            </div>
            <mat-error>
              Unit Price
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="none" fxFlex="8%">
            <label>Tax rate</label>
            <input matInput type="number" step="0.01" min="0" class="outline"
              (keyup)="makeZero($event , element); getTotalofAllamount();" [(ngModel)]="taxrate" placeholder="Tax rate"
              formControlName="taxRate" readonly [matTooltip]="isContract  ? 'Contract' : ''" />
            <span matSuffix class="closebtn4">%</span>
          </mat-form-field>

          <mat-form-field appearance="none" fxFlex="10%">
            <label>Total Price</label>
            <input matInput type="number" class="outline" placeholder="Total Price" [value]="totalOfAllAmount"
              disabled />
          </mat-form-field>

          <mat-form-field appearance="none" class="testarea" *ngIf="checked == true">
            <label> Add Description (Max. 50) <b class="descriptionStar">*</b> </label>
            <textarea matInput style="font-size: 15px; height: 60px;" formControlName="itemDescription"
              [(ngModel)]="descriptionClear" class="outline" maxlength="50" required></textarea>
          </mat-form-field>
          <div class="addItemBtnclss">
            <button mat-button (click)="addItemtoPo()" class="addItemBtn button3">
              Add Item
            </button>
          </div>
        </div>
      </div>
    </form>
  </div>

  <div *ngIf="dataSource.data.length > 0 && addItems" class="search-table-input fieldcontainer">
    <section class="example-container-1 mat-elevation-z8">
      <mat-form-field appearance="none" style="float: left; margin-bottom: 0.1rem !important;">
        <input matInput type="text" (keyup)="doTableFilter($event.target.value)" placeholder="Search" class="outline"
          [(ngModel)]='searchText' />
        <mat-icon matSuffix class="closebtn">search</mat-icon>
      </mat-form-field>

      <button mat-button class="buttonForRefresh refreshBtnForAI" style="float: right; margin-top: 2rem !important; margin-bottom: 1rem !important; margin-right: 15px; " (click)="removeAllDraftData()">Clear Items</button>
      
      <mat-slide-toggle *ngIf = 'dataSource.data.length > 0'
        style="float: right; margin-top: 2rem !important; margin-bottom: 1rem !important; margin-right: 45px;" [(ngModel)]="setOrderQty"
        (change)="setQtyToZero()">
        Set OrderQty to Zero
      </mat-slide-toggle>

      <table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort>
        <ng-container matColumnDef="index">
          <th mat-header-cell *matHeaderCellDef class="tableId"><b>#</b></th>
          <td mat-cell *matCellDef="let element; let i = index" class="name-cell tableId">
            {{ dataSource.data.length - i }}
          </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="actionBtns">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Actions</b>
          </th>
          <td mat-cell *matCellDef="let element">
            <button mat-icon-button (click)="removeItemFromPo(element)">
              <mat-icon>delete</mat-icon>
            </button>
          </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="itemName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Inventory Item</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.itemName | titlecase }}
          </td>
          <td mat-footer-cell *matFooterCellDef> Total </td>
        </ng-container>

        <ng-container matColumnDef="itemDescription">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Item Description</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.itemDescription | titlecase }}
          </td>
          <td mat-footer-cell *matFooterCellDef> Total </td>
        </ng-container>

        <ng-container matColumnDef="pkgName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Pkg Name</b></th>
          <td mat-cell *matCellDef="let element"> {{element.pkgName | titlecase}} </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="unitPerPkg">
          <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Units/Pkg</b></th>
          <td mat-cell *matCellDef="let element"> {{element.unitPerPkg}} </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="orderQty">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Order Quantity</b>
          </th>
          <td mat-cell *matCellDef="let element">
            <input (keyup)="validateReceivedQty($event , element)" class="input1" type="number" step="0.01" min="0"
              [(ngModel)]="element.orderQty" (focus)="focusFunctionWithOutForm(element)" (focusout)="focusOutFunctionWithOutForm(element)"/>
              <!-- onkeypress="return (event.charCode != 45 && (event.charCode >= 48 && event.charCode <= 57) || event.charCode == 46)" -->
          </td>
          <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getTotal('orderQty'))}}</td>
        </ng-container>

        <ng-container matColumnDef="unitPrice">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Unit Cost</b>
          </th>
          <td mat-cell *matCellDef="let element">{{ this.utils.truncateNew(element.unitPrice)}}
          </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="totalValueExcTax">
          <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Amount(ex.tax)</b></th>
          <td mat-cell *matCellDef="let element"> {{ this.utils.truncateNew(element.totalExcTax)}} </td>
          <td mat-footer-cell *matFooterCellDef>{{ this.utils.truncateNew(getTotal('totalExcTax'))}}</td>
        </ng-container>

        <ng-container matColumnDef="taxAmt">
          <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Tax Amt</b></th>
          <td mat-cell *matCellDef="let element"> {{ this.utils.truncateNew(element.taxAmount)}} </td>
          <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getTotal('taxAmount'))}}</td>
        </ng-container>

        <ng-container matColumnDef="rate">
          <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Tax Rate</b></th>
          <td mat-cell *matCellDef="let element">{{ this.utils.truncateNew(element.taxRate)}}
          </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="totalValue">
          <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Amount(incl.tax)</b></th>
          <td mat-cell *matCellDef="let element"> {{ this.utils.truncateNew(element.totalPrice)}} </td>
          <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getTotal('totalPrice'))}}</td>
        </ng-container>
        <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        <tr mat-footer-row *matFooterRowDef="displayedColumns"></tr>
      </table>
    </section>
  </div>

  <div class="datacontainer">
    <mat-card *ngIf="toPrint">
      <div id="print-section">
        <h3>
          Purchase Order
        </h3>
        <div class="po-details">
          <div fxLayout fxLayoutAlign="space-between center">
            <span>Po number : {{ purchaseOrder.id }}</span>
          </div>
          <div fxLayout fxLayoutAlign="space-between center">
            <span>Issue Date :
              {{ purchaseOrder.orderedDate | date: "EEEE, MMMM d, y" }}</span>
            <span>Expected Date :
              {{
              createPurchaseOrderForm.value.expectedDate
              | date: "EEEE, MMMM d, y"
              }}</span>
            <span>Validity Date :
              {{
              createPurchaseOrderForm.value.validityDate
              | date: "EEEE, MMMM d, y"
              }}
            </span>
          </div>
        </div>

        <table>
          <thead>
            <tr *ngIf="displayedColumns">
              <th>#</th>
              <th>Name</th>
              <th>Order Quantity</th>
              <th>Unit Cost</th>
              <th>Total</th>
            </tr>
          </thead>

          <tbody>
            <tr *ngFor="let item of purchaseOrder.items; let i = index">
              <td>{{ i + 1 }}</td>
              <td style="text-align: left;">{{ item.itemName }}</td>
              <td>{{ item.orderQty }}</td>
              <td>{{ item.unitPrice }}</td>
              <td>{{ item.unitPrice * item.orderQty }}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div fxLayout fxLayoutAlign="space-between center" class="action-btns" style="margin-top: 10px;">
        <button mat-raised-button color="primary" (click)="editPo()">
          Edit Order
        </button>
        <button mat-raised-button color="primary" (click)="print()">
          Print Order
        </button>
      </div>
    </mat-card>
  </div>
  <!-- </div> -->
</div>


<mat-card *ngIf="isShowTemplate">
  <span mat-card-title class="headTag">Templates</span>
  <div class="closeBtnForSO">
    <button mat-icon-button matTooltip="Back to special Order" (click)="goBack()"><mat-icon>close</mat-icon></button>
  </div>

  <div class="templateinputs">
    <mat-form-field appearance="none">
      <label>Search</label>
      <input matInput type="text" class="outline" (keyup)="doFilter($event.target.value)" placeholder="Search" />
    </mat-form-field>
    <mat-icon class="infoIconForSo" matTooltip="Filter By Template Name">info</mat-icon>
  </div>

  <table #table mat-table [dataSource]="templates" matSortActive="itemName" matSortDirection="asc" matSort>
    <ng-container matColumnDef="index">
      <th mat-header-cell *matHeaderCellDef class="tableId"><b>#</b></th>
      <td mat-cell *matCellDef="let element; let i = index" class="name-cell tableId">
        {{ i+1 }}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="delete">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Delete</b>
      </th>
      <td mat-cell *matCellDef="let element">
        <button mat-icon-button (click)="deleteTemplate(element)">
          <mat-icon>delete</mat-icon>
        </button>
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="tempName">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Template Name</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ element.templateName | titlecase }}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="category">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Category</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ element.category | titlecase }}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="workArea">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>WorkArea</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ element.workArea | titlecase }}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>


    <ng-container matColumnDef="updatedAt">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Updated Date</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ element.modTs | date: 'MMM d, y'}}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="actionBtns">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Actions</b>
      </th>
      <td mat-cell *matCellDef="let element">
        <button mat-icon-button class="m-2" (click)="getTemplate(element)" matTooltip="Click to Use Template">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor"
            class="bi bi-clipboard-check-fill" viewBox="0 0 16 16">
            <path
              d="M6.5 0A1.5 1.5 0 0 0 5 1.5v1A1.5 1.5 0 0 0 6.5 4h3A1.5 1.5 0 0 0 11 2.5v-1A1.5 1.5 0 0 0 9.5 0h-3Zm3 1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-1a.5.5 0 0 1 .5-.5h3Z" />
            <path
              d="M4 1.5H3a2 2 0 0 0-2 2V14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V3.5a2 2 0 0 0-2-2h-1v1A2.5 2.5 0 0 1 9.5 5h-3A2.5 2.5 0 0 1 4 2.5v-1Zm6.854 7.354-3 3a.5.5 0 0 1-.708 0l-1.5-1.5a.5.5 0 0 1 .708-.708L7.5 10.793l2.646-2.647a.5.5 0 0 1 .708.708Z" />
          </svg>
        </button>
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumnsForTemplate; sticky: true"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumnsForTemplate"></tr>
  </table>
  <div class="dataMessage" *ngIf="templates?.length == 0"> No Data Available </div>
</mat-card>