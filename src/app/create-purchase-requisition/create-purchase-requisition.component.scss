.placeToggle{
    margin-right: 3%;
  }

  .testarea{
    flex: 1 1 100%;
    box-sizing: border-box;
    max-width: 17% !important;
  }

.text{
  float: left;
  margin-left: 2%;
  margin-top: 6px;
  // font-style: italic;
  font-family: Public Sans;
  font-size: 12px;
}

#contractText{
  text-align: center;
}

.makeTemplateBtn{
  float: right;
}

.sendReqBtn{
  float: right;
}

.roleInput{
  float: right !important;
  margin-top : 24px !important;
}

.tableBtnAndInput{
  display: flow-root;
}

  .example-container-1{
    max-height: 345px;
    overflow-y: auto;
  }
  
.addItemBtnclss{
  border-radius: 4px;
  margin-top: 37px;
}

::ng-deep textarea.mat-input-element {
  padding: 0px 0 !important;
  margin: -8px 0 !important;
}

.matNavList{
  margin-left: 2%;
  margin-right: 2%;
  }

#vendorForm{
  display: flex !important;
}

.spinner-border{
  margin-left: 80% !important;
}

.loaderSpinDiv{
  margin-top : -29px;
}

.margin {
  margin-left : 10px;
}

mat-expansion-panel-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 !important;
  font-weight: bolder;
  border-radius: inherit;
}

example-headers-align .mat-expansion-panel-header-title, 
.example-headers-align .mat-expansion-panel-header-description {
  flex-basis: 0;
}

.example-headers-align .mat-expansion-panel-header-description {
  justify-content: flex-end !important;
}

::ng-deep span.mat-expansion-indicator.ng-trigger.ng-trigger-indicatorRotate.ng-star-inserted {
  display: none !important;
}

::ng-deep mat-panel-description.mat-expansion-panel-header-description {
  justify-content: flex-end !important;
}

::ng-deep .mat-expansion-panel-header-description, .mat-expansion-panel-header-title {
  margin-left: 16px !important;
}

.closeBtnForSO{
  float: right;
  margin-top: -45px !important;
}

.infoIconForSo{
  margin-top: 48px !important;
  margin-left: 5px !important;
}


.bottomButtons{
  margin-top: 42px !important;
}


section.example-section {
  float: right !important;
}

.templateinputs{
  display: flex !important;
}

.btnInput{
  margin-top: -9px;
}

.test {
  display: inline-block; 
  width: 100%;
}
.left-div {
  float: left;
}

.right-div {
  float: right;
  display: flex;
}

mat-form-field.fit-content {
  .mat-select-placeholder {
    min-width: 180px;
  }

  .mat-form-field-infix {
    width: fit-content;
    min-width: fit-content;
  }

  .mat-form-field {
    width: auto !important;
  }

  .mat-select-value {
    max-width: 100%;
    width: auto;
  }
}

.container {
  display: flex;
  align-items: center;
}

.check_circle{
  color:green;
  font-size: 27px;
  margin-top: 16px;
}

.findBtn{
  display: initial;
  margin-top: 41px;
  position: absolute;
  margin-left: 565px;
}

.find{
  margin-top: 40px;
  position: absolute;
}
.find1{
  margin-top: 40px;
}


#addItemForm{
  margin-top: 0px !important;
}

// .findBtnBottom{
//   display: flow-root !important;
// }
.topItem{
  margin-left: 2%;
  margin-right: 2%;
}