import { Component, OnInit, ViewChild} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatSort, MatTableDataSource } from '@angular/material';
import { PurchaseOrder } from '../_models';
import { PurchaseItem } from '../_models';
import { Brand } from '../_models';
import { UtilsService } from '../_utils/utils.service'
import { Vendor } from '../_models';
import { GlobalsService } from '../_services/globals.service';
import { MatDialog } from '@angular/material';
import { SimpleDialogComponent } from '../_dialogs/simple-dialog/simple-dialog.component';
import { PrPreviewDialogComponent } from '../_dialogs/pr-preview-dialog/pr-preview-dialog.component';
import { VendorsService, AuthService,PurchasesService, ShareDataService } from '../_services/'
import { interval } from 'rxjs';
import { FormControl } from '@angular/forms';
import { MatSelect } from '@angular/material/select';
import { ReplaySubject, Subject } from 'rxjs';
import {takeUntil } from 'rxjs/operators';
import { AutoGrnService } from '../_services/auto-grn.service';
import { NotificationService } from '../_services/notification.service';
import { environment } from '../../environments/environment';
import { type } from 'os';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material';
import { MomentDateModule, MomentDateAdapter } from '@angular/material-moment-adapter';
import { Router } from '@angular/router';

export const MY_FORMATS = {
  parse: {
      dateInput: 'LL'
  },
  display: {
      dateInput: 'DD-MM-YYYY',
      monthYearLabel: 'YYYY',
      dateA11yLabel: 'LL',
      monthYearA11yLabel: 'YYYY'
  }
};

@Component({
  selector: 'app-create-purchase-requisition',
  templateUrl: './create-purchase-requisition.component.html',
  styleUrls: ['./create-purchase-requisition.component.scss', "./../../common-dark.scss"],
  providers:[{ provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
  { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }
]
})
export class CreatePurchaseRequisitionComponent implements OnInit {
  @ViewChild('singleSelect') singleSelect: MatSelect;
  setOrderQty : boolean = false;
  public Bank: any[] = [];
  public VendorBank: any[] = [];
  public inventoryItem: FormControl = new FormControl();
  public bankFilterCtrl: FormControl = new FormControl();
  public roleFilterCtrl: FormControl = new FormControl();
  public filteredVendorItems: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  protected _onDestroy = new Subject<void>();
  public intervalTimer = interval(500);
  private subscription;
  createPurchaseOrderForm: FormGroup;
  addItemToPoForm: FormGroup;
  approvalRequired: boolean = false;
  purchaseOrder: PurchaseOrder = {};
  displayedColumns = GlobalsService.createPoColumns1;
  displayedColumnsForTemplate : any = ['index','tempName','category','workArea','updatedAt', 'actionBtns','delete'];
  startDate: any;
  vendors: Vendor[];
  itemBrands: Brand[];
  @ViewChild(MatSort) sort: MatSort;
  selectedVendor: Vendor;
  selectedInvCategory: any;
  selectedBrand: Brand;
  vendorItems: PurchaseItem[] = [];
  packagingSizes: any;
  selectedItem: any;
  contractType: any;
  showPkg: boolean = true
  selectedPkg: any = { 'pkgName': 'N/A' };
  dataSource: MatTableDataSource<PurchaseItem>;
  toPrint: boolean;
  category: any = [];
  units: string[];
  totalAmount: number = 0;
  taxFlag: boolean = false;
  descriptionFlag: boolean = false;
  dialogRef: any;
  selectedCategory: any;
  totalExcTax: any;
  templateName: String;
  public setHeight: string = '0px';
  public filteredItemList: Array<Object>;
  public contractItems: Array<Object>;
  public searchText:any;
  unitCost: any;
  quantity: any;
  delDate : any;
  taxrate: any;
  data: any;
  descriptionClear: string;
  lastUpdate: any;
  selectedRole: any;
  contractData: any =[];
  roles: any =[];
  isContract: boolean = false;
  passive: false;
  @ViewChild('err') vc: any;
  addItems: boolean = false;
  showInputs: boolean = false;
  showItemDropDown: boolean = false;
  trapFocus: boolean = true;
  appCat: any;
  stockType:any;
  selectedWorkArea: any ;
  workAreas= [];
  vendorName: any;
  soLoader: boolean = false;
  disableStock: boolean ;
  user: any;
  multiBranchUser; branchSelected: boolean;
  getBranchData: any[]
  branchesData: any[]
  templates: any;
  tempName: any;
  selectedBrandControl = new FormControl();
  isShowTemplate: boolean = false;
  checked = false;
  getAllTemplatesData : any;
  stockSeparation: boolean;
  brand: any;
  private unsubscribe$ = new Subject<void>();
  totalOfAllAmount: number;
  issueDate = new FormControl();
  constructor(private fb: FormBuilder,  
    private autoGrnService:AutoGrnService,
    private notifyService: NotificationService,
    private dialog: MatDialog, 
    private vendorService: VendorsService,
    private auth: AuthService, 
    private purchases: PurchasesService, 
    private shareData: ShareDataService,
    private sharedData: ShareDataService,
    private utils: UtilsService,
    private router : Router,
    ) {
      this.user = this.auth.getCurrentUser();
      this.multiBranchUser = this.user.multiBranchUser;
    this.dataSource = new MatTableDataSource<PurchaseItem>();
    this.createPurchaseOrderForm = this.fb.group({
      rest: ['', Validators.required],
      stockType: ['',Validators.required],
      // contract: ['',Validators.required],
      category:['', Validators.required],
      workArea: ['',Validators.required],
    });
    this.addItemToPoForm = this.fb.group({
      inventoryItem: ['', Validators.required],
      brand: [''],
      selectedBrand: [''],
      subCategory:[''],
      ledger:[''],
      orderQty: [0, Validators.required],
      unitPrice: [0],
      uom: [''],
      taxRate: [''],
      pkgName: ['', Validators.required],
      pkgQty: [0],
      unitPerPkg: [0],
      itemDescription : [''],
      stockConversion: [''],
    });
    var windowLocation = window.location.href;
    let windowCurrentUrl = windowLocation.split('/')[4]
      this.sharedData.sharedBranchData.pipe(takeUntil(this.unsubscribe$)).subscribe((val) => {
        this.getBranchData = val;
        if(this.getBranchData.length == 0 ){
          this.branchesData = this.user.restaurantAccess;
        }else if(this.getBranchData.length == 1){
          const toSelect = this.getBranchData.find(data => data.branchName == this.getBranchData[0].branchName);
          this.createPurchaseOrderForm.get('rest').setValue(toSelect);
          this.branchesData = this.getBranchData;
          this.setRestaurant();
          this.getBranchDetails();
        }else{
          this.branchesData = this.getBranchData
        }
    });
  }

  ngOnInit() {    
    if (this.branchSelected) {
      this.setRestaurant()
    } else {
      if (!this.user.multiBranchUser) {
        this.createPurchaseOrderForm.value.rest = this.user.restaurantId
        this.branchSelected = true;
      }
    }
    this.unitCost = 0;
    this.quantity = 0;
    this.taxrate = 0;
    this.startDate = new Date();
    let validityDate = new Date(this.startDate.getTime() + (24 * 60 * 60 * 1000));
  }

dateFn(){
  this.startDate = new Date();
  let validityDate = new Date(this.startDate.getTime() + (24 * 60 * 60 * 1000));
}

  makeZero(event , element){
    if(event.keyCode == 190){
      return
    }
    this.unitCost = this.utils.truncateNew(this.unitCost)
    if(!this.unitCost){
      this.unitCost = 0;
    }
    this.quantity = this.utils.truncateNew(this.quantity)
    if(!this.quantity){
      this.quantity = 0;
    }
    this.taxrate = this.utils.truncateNew(this.taxrate)
    if(!this.taxrate){
      this.taxrate = 0;
    }
  }

  validateReceivedQty(event , element){
    element.totalExcTax = element.unitPrice * element.orderQty
    element.taxAmount = (element.unitPrice * element.taxRate / 100) * element.orderQty
    element.totalPrice = (element.unitPrice + (element.unitPrice * element.taxRate) / 100) * element.orderQty
    return element
  }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  protected filterBanks() {
    if (!this.Bank) {
      return;
    }
    let search = this.bankFilterCtrl.value;
    if (!search) {
      this.filteredVendorItems.next(this.Bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.filteredVendorItems.next(
      this.Bank.filter(bank => bank.itemName.toLowerCase().indexOf(search) > -1)
    );
  }

  isFieldInvalid(field: any) {
    let isInvalid: boolean = (
      !this.createPurchaseOrderForm.get(field).valid && this.createPurchaseOrderForm.get(field).touched
    ) || (this.createPurchaseOrderForm.get(field).untouched);
    return isInvalid;
  }

  vendorSelect(v) {
    this.selectedVendor = v.value;
    this.vendorItems = v.value.items;
    this.filteredItemList = this.vendorItems;
    this.addItemToPoForm.get('inventoryItem').setValue('');
    this.addItemToPoForm.get('brand').setValue('');
    this.itemBrands = [];
    this.dataSource.data = [];
    this.dataSource.data = [...this.dataSource.data];
  }

  purchaseItemSelect(i) {
    this.soLoader = true
    this.taxFlag = false
    this.selectedPkg = {}
    this.addItemToPoForm.get('unitPerPkg').setValue('');
    this.addItemToPoForm.get('orderQty').setValue(0);
    this.addItemToPoForm.get('pkgName').setValue('');
    this.addItemToPoForm.get('unitPrice').setValue(0);
    this.addItemToPoForm.get('taxRate').setValue(0)
    this.selectedItem = i.value
    this.packagingSizes = i.value.packagingSizes
    if (this.packagingSizes.length <= 0) {
      this.addItemToPoForm.get('unitPrice').setValue(i.value.unitPrice);
      this.utils.snackBarShowWarning('No packaging size available for this item.Please check')
    }
    else if (this.packagingSizes.length == 1) {
      this.addItemToPoForm.get('pkgName').setValue(this.packagingSizes[0])
      this.pkgSelect({ 'value': this.packagingSizes[0] })
    }
    this.addItemToPoForm.get('uom').setValue(i.value.uom);
    this.addItemToPoForm.get('taxRate').setValue(i.value.taxRate);    
    this.addItemToPoForm.get('stockConversion').setValue(i.value.stockConversion);    
  }

  selectContractPrice(itemCode: any , pkgName: any){
    if (this.contractData){
      let foundContract = this.contractData.find(item => item.itemCode === itemCode && item.packageName === pkgName);
      if (foundContract) {
        this.addItemToPoForm.get('unitPrice').setValue(foundContract['price']);
        this.isContract = true;
        this.soLoader = false
      } else {
        this.isContract = false;
        this.getLastGrnPrice();
      }
    } else {
    this.isContract = false;
    this.getLastGrnPrice();
   }
  }

  getLastGrnPrice(){
    let params={
      'restaurantId':this.createPurchaseOrderForm.value.rest.restaurantIdOld,
      'itemCode':this.selectedItem.itemCode,
      'packageName':this.selectedPkg.packageName
    }
    this.purchases.getLastGrnPrice(params).subscribe(data => {
      if (data['success']){
        this.soLoader = false
        if(data.priceList){
        this.addItemToPoForm.get('unitPrice').setValue(data.priceList[0].unitPrice);
        this.addItemToPoForm.get('taxRate').setValue(data.priceList[0].taxRate);
        }else{
          this.addItemToPoForm.get('unitPrice').setValue(data['unitPrice']);
          data['taxRate'] !== null ?  this.addItemToPoForm.get('taxRate').setValue(data['taxRate']) : undefined;
        }
        this.addItemToPoForm.get('subCategory').setValue(data['ledger'][0]['subCategory']);
        this.addItemToPoForm.get('ledger').setValue(data['ledger'][0]['ledger']);
      }else{
        this.soLoader = false ;
        this.addItemToPoForm.get('subCategory').setValue(data['ledger'][0]['subCategory']);
        this.addItemToPoForm.get('ledger').setValue(data['ledger'][0]['ledger']);
        return null
      }
    }) 
  }

  pkgSelect(pkg) {    
        this.selectedPkg = pkg.value
    if (this.selectedPkg.packageName != 'N/A') {
      this.brand =  pkg.value.brand ? pkg.value.brand : null ;
      this.brand ? this.selectedBrandControl.disable() : null ;
      this.addItemToPoForm.get('unitPerPkg').setValue((pkg.value.packageQty/pkg.value.unitQty));
      this.addItemToPoForm.get('unitPrice').setValue(pkg.value.unitPrice);
      this.selectContractPrice(this.selectedItem['itemCode'] , this.selectedPkg.packageName )
    }
  }

  changed() {
    this.selectedPkg = {}
  }

  brandSelect(b) {
    this.addItemToPoForm.get('unitPrice').setValue(b.value.unitPrice);
    this.units = b.value.units;
  }

  addItemtoPo() {
    let itemExisted = false;
    if (this.selectedPkg.packageName != 'N/A' && this.selectedPkg.hasOwnProperty('packageName')) {
      if (this.addItemToPoForm.value.orderQty > 0) {
        if ([this.selectedItem].length > 0) {
          if (!this.showPkg) {
            this.purchases.getSortedPkg({
              item: this.selectedItem,
              orderQty: this.addItemToPoForm.value.orderQty
            }).subscribe(data => {
              data.sortedPkg.forEach(element => {
                let dataArr = this.dataSource.data;
                for (let i = 0; i < dataArr.length; i++) {
                  if (dataArr[i].itemCode === this.addItemToPoForm.get('inventoryItem').value.itemCode && element.pkg.pkgName == dataArr[i].packageName) {
                    dataArr[i].orderQty += element.orderQty;
                    itemExisted = true;
                    break;
                  }
                }
                if (itemExisted == false) {
                  let sourceData = this.addPurchaseItem(element);
                  this.dataSource.data.push(sourceData);
                  this.addItemToDraft(sourceData);
                }
                itemExisted = false;
              });
              this.dataSource.data = [...this.dataSource.data];
              this.dataSource.data.map((element: any) => {
                element.defaultQty = element.orderQty;
                return element;
              });
              this.addItemToPoForm.reset();
              this.addItemToPoForm.clearAsyncValidators();
            }, err => console.error(err));
          } else {
            let pkgName = this.addItemToPoForm.get('pkgName').value.packageName;
            let code = this.addItemToPoForm.get('inventoryItem').value.itemCode;
            let dataArrFiltered: any;
            dataArrFiltered = this.dataSource.data.filter(function (v, i) {
              return (v["itemCode"] == code && v['pkgName'] == pkgName);
            });
            if (dataArrFiltered.length > 0) {
              dataArrFiltered = dataArrFiltered[0];
              this.dialog.open(SimpleDialogComponent, {
                data: {
                  title: 'Duplicate Item Alert',
                  msg: 'This item is already added. Do you want to add some more?',
                  ok: function () {
                    dataArrFiltered['orderQty'] = dataArrFiltered['orderQty'] + this.addItemToPoForm.get('orderQty').value;
                    dataArrFiltered['totalPrice'] = (dataArrFiltered['unitPrice'] + (dataArrFiltered['unitPrice'] * dataArrFiltered['taxRate'] / 100)) * dataArrFiltered['orderQty'];
                    this.addItemToDraft(dataArrFiltered);
                  }.bind(this)
                }
              });
            } else {
              let sourceData = this.formValtoPurchaseItem(this.addItemToPoForm.value);
              this.addItemToDraft(sourceData);
            }
            this.dataSource.data = [...this.dataSource.data];
            this.dataSource.data.map((element: any) => {
              element.defaultQty = element.orderQty;
              return element;
            });
          }
        } else {
          let pkgName = this.addItemToPoForm.get('pkgName').value.pkgName;
          let code = this.addItemToPoForm.get('inventoryItem').value.itemCode;
          let dataArrFiltered: any;
          dataArrFiltered = this.dataSource.data.filter(function (v, i) {
            return (v["itemCode"] == code && v['pkgName'] == pkgName);
          });
          if (dataArrFiltered.length > 0) {
            dataArrFiltered = dataArrFiltered[0];
            dataArrFiltered['orderQty'] += this.addItemToPoForm.get('orderQty').value;
          } else {
            this.dataSource.data.unshift(this.formValtoPurchaseItem(this.addItemToPoForm.value));
          }
          this.dataSource.data = [...this.dataSource.data];
          this.dataSource.data.map((element: any) => {
            element.defaultQty = element.orderQty;
            return element;
          });
        }
      } else {
        // this.utils.showInfo('Please add required quantity', ' ', 3000);
      this.utils.snackBarShowInfo('Please add required quantity')

      }
    } else {
      this.utils.snackBarShowInfo('Please select package name')
    }
    this.descriptionClear = '';
  }
    
  addPurchaseItem(sortedPkg) {
    let purItem: any = {};
    purItem.itemName = this.selectedItem.itemName;
    purItem.unitPrice = sortedPkg.pkg.unitPrice;
    purItem.orderQty = sortedPkg.orderQty;
    purItem.uom = this.selectedItem.uom;
    purItem.itemCode = this.selectedItem.itemCode;
    purItem.taxRate = this.selectedItem.taxRate;
    purItem.pkgName = sortedPkg.pkg.pkgName;
    purItem.unitPerPkg = sortedPkg.pkg.unitPerPkg;
    purItem.pkgQty = sortedPkg.pkg.pkgQty;
    purItem.itemDescription = sortedPkg.pkg.itemDescription;
    purItem.subCategory = sortedPkg.subCategory;
    purItem.ledger = sortedPkg.ledger;
    purItem.totalPrice = (sortedPkg.pkg.unitPrice + (sortedPkg.pkg.unitPrice * this.selectedItem.taxRate / 100)) * sortedPkg.orderQty
    return purItem;
  }

  private formValtoPurchaseItem(formVal: any) {
    let purItem: any = {};
    purItem.itemName = formVal.inventoryItem.itemName;
    purItem.brand = formVal.brand;
    purItem.unitPrice = formVal.unitPrice;
    purItem.orderQty = formVal.orderQty;
    purItem.uom = formVal.uom;
    purItem.itemCode = formVal.inventoryItem.itemCode;
    purItem.taxRate = formVal.taxRate;
    purItem.itemDescription = formVal.itemDescription;
    purItem.itemCategory = formVal.inventoryItem.invCategory;
    purItem.subCategory = formVal.subCategory;
    purItem.ledger = formVal.ledger;
    if (formVal.pkgName) {
      purItem.pkgName = formVal.pkgName.packageName;
      purItem.unitPerPkg = formVal.unitPerPkg;
      purItem.pkgQty = this.selectedPkg.packageQty;
    }
    else {
      purItem.totalQty = formVal.orderQty
      purItem.pkgName = 'N/A'
      purItem.pkgQty = 'N/A'
      purItem.unitPerPkg = 'N/A'
    }
    purItem.totalExcTax = formVal.unitPrice * formVal.orderQty
    purItem.taxAmount = (formVal.unitPrice * formVal.taxRate / 100) * formVal.orderQty
    purItem.stockConversion = formVal.stockConversion;
    purItem.totalPrice = (formVal.unitPrice + (formVal.unitPrice * formVal.taxRate / 100)) * formVal.orderQty
    if (this.isContract)
    purItem.isContract = true
    else 
    purItem.isContract = false
    return purItem;
  }


  removeItemFromPo(item) {
    this.dialog.open(SimpleDialogComponent, {
      data: {
        title: 'Place Order',
        msg: 'Are you sure you want to remove the item?',
        ok: function () {
          if (this.dataSource.data.indexOf(item) == -1) return null;
          this.dataSource.data.splice(this.dataSource.data.indexOf(item), 1);
          this.dataSource.data = [...this.dataSource.data];
          this.removeItemFromDraft(item['itemCode'], item['pkgName']);
        }.bind(this)
      }
    });
    this.tempName = ''
  }

  createPr() {
    if (this.stockType == 'Non-Stockable' && !this.selectedWorkArea){
    this.utils.snackBarShowWarning('Please select workArea')
    } else {
    this.purchaseOrder.prItems = this.dataSource.data
    this.purchaseOrder.orderedDate = new Date();
    let purchaseReq = {
      tenantId: this.auth.getCurrentUser().tenantId,
      prItems: this.purchaseOrder.prItems.reverse(),
      eta: null,
      deliveryDate:this.utils.dateCorrection(this.delDate),
      uId: this.auth.getCurrentUser().mId,
      rId: this.createPurchaseOrderForm.value.rest.restaurantIdOld,
      totalAmount: this.totalAmount,
      tempPr: true,
    }
    if ("userName" in this.auth.getCurrentUser()) {
      purchaseReq['poMadeBy'] = this.auth.getCurrentUser().userName
    } else {
      purchaseReq['poMadeBy'] = "NA"
    }
    
    if (this.roles.length > 0){
      const uniqueItemCategories = [...new Set(purchaseReq['prItems'].map(obj => obj.itemCategory))] ;
      const getAppCatRoles = (data, appCat) => {
        const filteredObj = data.find(obj => obj.appCat === appCat);
        return filteredObj ? filteredObj.roles.map(role => role.value) : [];
      };
      let rolesArray = [],appCat;
      if (uniqueItemCategories.length > 1){
        appCat = "DEFAULT";
        rolesArray = getAppCatRoles(this.roles, appCat);
      } else if (uniqueItemCategories.length === 1) {
        appCat = uniqueItemCategories[0]
        rolesArray = getAppCatRoles(this.roles, appCat);
        if (rolesArray.length == 0){
          appCat = "DEFAULT";
          rolesArray = getAppCatRoles(this.roles, appCat);  
        }
      } else {
        rolesArray = [] ;
        appCat = undefined ;
      }
      this.selectedRole = rolesArray ; 
      this.appCat = appCat
    }

    if(this.selectedRole && this.selectedRole.length > 0){
      purchaseReq['role'] = this.selectedRole
    } else {
      purchaseReq['role'] = [];
    }
    purchaseReq['appCat'] = this.appCat;
    purchaseReq['contractType'] = this.contractType;
    purchaseReq['senderEmail'] = [this.user.email]
    purchaseReq['selectedWorkArea'] =this.stockType == 'Non-Stockable' ?  [this.selectedWorkArea] : undefined;
    purchaseReq['stockType'] = this.stockType;
    purchaseReq['createdUser'] = this.user.email;
    this.dialog.open(SimpleDialogComponent, {
      data: {
        title: 'Place Request',
        msg: 'Are you sure you want to send Purchase Request?',
        ok: async function () {
          let inputData: any = {
            tenantId: this.auth.getCurrentUser().tenantId,
            uId: this.auth.getCurrentUser().mId,
            rId: this.createPurchaseOrderForm.value.rest.restaurantIdOld,
            cType: this.contractType
          }    
          inputData['stockType'] = this.stockType ? this.stockType : undefined ;
          this.purchases.removeDraftDataBulK(inputData).subscribe(() => {
            this.purchases.createPr(purchaseReq).subscribe(data => {
            this.getDraftData();
            let obj: any = {}
            obj['tenantId'] = this.auth.getCurrentUser().tenantId,
            obj['tenantName'] = this.auth.getCurrentUser().name,
            obj['rId'] = this.createPurchaseOrderForm.value.rest.restaurantIdOld,
            obj['prId'] = data.prId,
            obj['createTs'] = data.modTs,
            obj['totalAmount'] = this.totalAmount,
            obj['baseUrl'] = environment.baseUrl,
            obj['contractType'] = this.contractType,
            obj['userEmail'] = this.auth.getCurrentUser().email,
            obj['appCat'] = this.appCat
            if(this.selectedRole && this.selectedRole.length > 0){
              obj['role'] = this.selectedRole
            } else {
              obj['role'] = [];
            }
            this.utils.snackBarShowSuccess('PR created successfully')
            this.router.navigate(['/home'])
            this.purchases.sendEmailForPreq(obj).subscribe(res => {
              if (res.result === true) {
                if(res.permission){
                  if(this.contractType == 'contract'){
                    this.utils.snackBarShowSuccess('Order belongs to contract, No approvals required')
                  }
                }else {
                  this.utils.snackBarShowSuccess('Approval email sent successfully')
                  this.previewPr(data);
                }
              }
            })
            this.createPurchaseOrderForm.reset();
            this.addItemToPoForm.reset();
            this.dataSource.data = [];
            this.selectedRole = [] ;
            this.dateFn();
            }, err => console.error(err));
          })
          
        }.bind(this)
      }
    });
  } 
  }

  print() {
    let popupWin, printContents;
    printContents = document.getElementById('print-section').innerHTML;
    popupWin = window.open('', '_blank', `top = 0 ; left = 0 ; height = 100%, width=auto`);
    popupWin.document.open();
    popupWin.document.write(
      ` <html>
        <head>
          <title>Print tab</title>
          <style>

          *{
            margin : 1%;
          }
          #contact-section>div{
            border-bottom: 2px solid $primary !important;
            border-left: 2px solid $primary  !important;
            border-right: 2px solid $primary  !important;
            width : 45%;
            margin-top : 2%;

          }
          h4{
            background-color: $primary  !important;
            color: #fff;
            padding-left: 5%;
            margin : 0;
          }
          address{
            margin : 2%;
          }

          table {
            width : 100%;
            margin : 5% auto
          }
          tr {
            text-align : left;
            border: 1px solid #000000;

          }
          </style>
        </head>
    <body onload="window.print();window.close()">${printContents}</body>
      </html>`
    );

    popupWin.document.close();

  }

  editPo() {
    this.toPrint = false;
  }

  selectVendor(e) {
    this.tempName = undefined ;
    this.selectedWorkArea = undefined ;
    this.vendorName = e.value.name;  
    this.dataSource.data = [];
    this.addItemToPoForm.reset();
    this.addItemToPoForm.clearAsyncValidators();
    this.addItems = false;
    this.selectedVendor = e.value;
    this.selectedInvCategory = [];
    this.selectedInvCategory.push({ 'invCategoryName': 'All' })
    this.createPurchaseOrderForm.controls['invCategory'].setValue(this.selectedInvCategory[this.selectedInvCategory.length - 1]);
  }

  previewPr(obj) {
    this.dialog.open(PrPreviewDialogComponent, {
    data: obj
    });
  }

  getTotal(key: string) {
    if (key === 'totalPrice') {
        this.totalAmount = this.utils.getTotal(this.dataSource.data, key) 
        return this.totalAmount
    }
    else {
        return this.utils.getTotal(this.dataSource.data, key)
    }
  }
  
  description(){
    this.descriptionFlag = !this.descriptionFlag;
  }

  convertDate(inputFormat) {
    function pad(s) { return (s < 10) ? '0' + s : s; }
    var d = new Date(inputFormat)
    return [pad(d.getDate()), pad(d.getMonth()+1), d.getFullYear()].join('/')
  }

  makePrTemplate() {
    this.purchaseOrder.prItems = this.dataSource.data;
    if (this.purchaseOrder.prItems.length > 0) {
      let inputData: any = {
        restaurantId: this.createPurchaseOrderForm.value.rest.restaurantIdOld,
        tenantId: this.auth.getCurrentUser().tenantId,
        prItems: this.dataSource.data,
        uId: this.auth.getCurrentUser().mId,
        totalAmount: this.totalAmount,
        category: this.selectedCategory,
      }
      inputData['stockType'] = this.stockType ? this.stockType : undefined ;
      inputData['workArea'] = this.selectedWorkArea ? this.selectedWorkArea : undefined ;
      this.dialogRef = this.dialog.open(SimpleDialogComponent, {
        data: {
          title: 'Make PR Template',
          msg: 'Enter Template Name(min 5 and max 20 characters allowed)',
          inputFromUser: { 'Template Name': '' },
          rId: this.createPurchaseOrderForm.value.rest.restaurantIdOld,
          ok: function () {
            this.dialogRef.afterClosed().subscribe(res => {
              this.templateName = res['Template Name'];
              if(res['Template Name'] == ''){
                this.utils.snackBarShowInfo('Please enter Template Name')
              }else{
                inputData['templateName'] = this.templateName;
                this.purchases.makePrTemplate(inputData).subscribe(data => {
                  if (data.result == 'success') {
                    this.utils.snackBarShowSuccess('Template created successfully')
                    this.getTemplates() ;
                  }
                  else {
                    this.utils.snackBarShowError('Error :' + data.exception)
                  }
                }
                  , err => console.error(err))
              }
            })
          }.bind(this)

        }
      });
    }
    else {
      this.utils.snackBarShowWarning('No items to make template')
    }
  }

  setMyStyles() {
    if (Object.keys(this.vendorItems).length >= 6) {
      this.setHeight = '200px'
    }
    else {
      this.setHeight = (Object.keys(this.vendorItems).length * 38) + 'px'

    }
  }

  setRestaurant(){
    this.dataSource.data = [];
    this.contractData = [];
    this.addItemToPoForm.reset();
    this.roles = [];
    this.appCat = undefined;
    this.addItemToPoForm.reset();
  }


  
// ----------------------------------------DRAFT---------------------------------------------
async getDraftData(){
  let inputData: any = {
    tenantId: this.auth.getCurrentUser().tenantId,
    uId: this.auth.getCurrentUser().mId,
    rId: this.createPurchaseOrderForm.value.rest.restaurantIdOld,
    cType: this.contractType,
    category: this.selectedCategory,
  }
  inputData['stockType'] = this.stockType ? this.stockType : undefined ;
  inputData['workArea'] = this.selectedWorkArea ? this.selectedWorkArea : undefined ;
  this.purchases.getDraftData(inputData).subscribe(response => {
      this.dataSource.data = (response.data) ? response.data.reverse() : [] ;      
      this.dataSource.data.map((element : any) => {element.defaultQty = element.orderQty; return element});
      if (response.lastUpdatedaAt != null){
        this.lastUpdate = response.lastUpdatedaAt;
      } else {
        this.lastUpdate = new Date() ;
      }
  }, err => console.error(err))
}

removeItemFromDraft(itemCode,pkgName){
  let inputData: any = {
    tenantId: this.auth.getCurrentUser().tenantId,
    itemCode:itemCode,
    pkgName : pkgName,
    uId: this.auth.getCurrentUser().mId,
    rId: this.createPurchaseOrderForm.value.rest.restaurantIdOld,
    cType: this.contractType,
    category: this.selectedCategory,
  }
  inputData['stockType'] = this.stockType ? this.stockType : undefined ;
  inputData['workArea'] = this.selectedWorkArea ? this.selectedWorkArea : undefined ;
  this.purchases.removeDraftData(inputData).subscribe(async ()=> { await this.getDraftData(); }, err => console.error(err));
}

async removeDraftDataBulK(){
  let inputData: any = {
    vendorId: this.selectedVendor.tenantId,
    tenantId: this.auth.getCurrentUser().tenantId,
    uId: this.auth.getCurrentUser().mId,
    rId: this.createPurchaseOrderForm.value.rest.restaurantIdOld,
    cType: this.contractType,
    category: this.selectedCategory,
  }
  inputData['stockType'] = this.stockType ? this.stockType : undefined ;
  inputData['workArea'] = this.selectedWorkArea ? this.selectedWorkArea : undefined ;
  this.purchases.removeDraftDataBulK(inputData);
}

addItemToDraft(source){
  this.purchaseOrder.prItems = source
  this.purchaseOrder.orderedDate = new Date();
  let inputData: any = {
    tenantId: this.auth.getCurrentUser().tenantId,
    prItems: this.purchaseOrder.prItems,
    uId: this.auth.getCurrentUser().mId,
    rId: this.createPurchaseOrderForm.value.rest.restaurantIdOld,
    itemCode:source.itemCode,
    pkgName : source.pkgName,
    cType: this.contractType,
    category: this.selectedCategory,
  }
  inputData['stockType'] = this.stockType ? this.stockType : undefined ;
  inputData['workArea'] = this.selectedWorkArea ? this.selectedWorkArea : undefined ;
  this.purchases.addDraftData(inputData).subscribe(async ()=> { await this.getDraftData(); }, err => console.error(err));
}

getUsers(){
  let inputData: any = {
    tenantId: this.auth.getCurrentUser().tenantId,
    restaurantId: this.createPurchaseOrderForm.value.rest.restaurantIdOld,
    type: "purchaseRequest"
  }
    this.purchases.getSelectedUsers(inputData).subscribe(data => {
      this.roles = data.data;
  })
}

// getContractItems(){
//   let inputData: any = {
//     tenantId: this.auth.getCurrentUser().tenantId,
//     restaurantId: this.createPurchaseOrderForm.value.rest.restaurantIdOld
//   }
//     this.purchases.getContractItems(inputData).subscribe(data => {
//       this.contractData = data['success'] ? data['contractItems'] : []
//   })
// }

  getTemplates() {
    let obj = {
      "restaurantId":this.createPurchaseOrderForm.value.rest.restaurantIdOld,
      "tenantId": this.auth.getCurrentUser().tenantId,
      'category': this.selectedCategory,
    }
    obj['stockType'] = this.stockType ? this.stockType : undefined ;
    obj['workArea'] = this.selectedWorkArea ? this.selectedWorkArea : undefined ;
    this.purchases.getTemplates(obj).subscribe(data => {
      this.templates = data.prTmpList;
      this.getAllTemplatesData = data.prTmpList;
    })
  }

  getTemplate(element) {
  	let inputData: any = {
  		tenantId: this.auth.getCurrentUser().tenantId,
  		uId: this.auth.getCurrentUser().mId,
  		rId:this.createPurchaseOrderForm.value.rest.restaurantIdOld,
  		cType: this.contractType
  	}
    inputData['stockType'] = this.stockType ? this.stockType : undefined ;
  	this.purchases.removeDraftDataBulK(inputData).subscribe(() => {})
  	let obj = {
  		templateId: element._id.$oid
  	}
  	this.tempName = element.templateName;
  	this.purchases.getTempItems(obj).subscribe(data => {
      // data.prTmpList.prItems.length > 0 ? this.addDraft(data.prTmpList.prItems) : this.notifyService.showInfo('Items not found','')
      data.prTmpList.prItems.length > 0 ? this.addDraft(data.prTmpList.prItems) : this.utils.snackBarShowInfo('Items not found')
  	})
    this.isShowTemplate = false
  }

    removeAllDraftData(){
      let inputData: any = {
        tenantId: this.auth.getCurrentUser().tenantId,
        rId:this.createPurchaseOrderForm.value.rest.restaurantIdOld,
    		uId: this.auth.getCurrentUser().mId,
        category: this.selectedCategory,
      }
      inputData['stockType'] = this.stockType ? this.stockType : undefined ;
      inputData['workArea'] = this.selectedWorkArea ? this.selectedWorkArea : undefined ;
      this.purchases.removeDraft(inputData).subscribe((data) => {
        data.result ? (this.notifyService.showSuccess('Draft cleared successfully',''),this.getDraftData()) : this.utils.snackBarShowError('Something went wrong')
      })
    }

  addDraft(el){
    let inputData: any = {
      tenantId: this.auth.getCurrentUser().tenantId,
      prItems: el,
      uId: this.auth.getCurrentUser().mId,
      rId: this.createPurchaseOrderForm.value.rest.restaurantIdOld,
      category: this.selectedCategory,
    }
    inputData['stockType'] = this.stockType ? this.stockType : undefined ;
    inputData['workArea'] = this.selectedWorkArea ? this.selectedWorkArea : undefined ;
    this.purchases.exportDraftData(inputData).subscribe(data => {
      data.result ? this.getDraftData() : undefined;
    })
  }
  
  deleteTemplate(event) {
    let obj = {
      "tempName": event.templateName,
      "tenantId": this.auth.getCurrentUser().tenantId,
      "restaurantId": this.createPurchaseOrderForm.value.rest.restaurantIdOld
    }
    obj['stockType'] = this.stockType ? this.stockType : undefined ;
  	this.purchases.deleteTemplate(obj).subscribe(data => {
      this.utils.snackBarShowSuccess('Template Removed Successfully')
      this.getTemplates()
    })
  }

  updateTemplate() {
  	let inputData: any = {
  		vendorId: this.selectedVendor.tenantId,
  		vendorName: this.vendorName,
  		restaurantId: this.createPurchaseOrderForm.value.rest.restaurantIdOld,
  		tenantId: this.auth.getCurrentUser().tenantId,
  		prItems: this.dataSource.data,
  		uId: this.auth.getCurrentUser().mId,
  		templateName: this.tempName,
  		cType: this.contractType
  	}
    inputData['stockType'] = this.stockType ? this.stockType : undefined ;
  	this.purchases.updateTemplate(inputData).subscribe(data => {
      this.utils.snackBarShowSuccess('Template Updated Successfully')
  	})
  }

  viewTemplate(){
    if(this.isShowTemplate == false){
      this.isShowTemplate = true
    }else{
      this.isShowTemplate = false
    }
    this.getTemplates();
  }

  goBack(){
    this.isShowTemplate = false
  }

  doFilter = (value: any) => {
    let result = this.templates.filter(item => item.templateName.trim().includes(value));
    if(result.length > 0 && value != '' ){
      this.templates = result
    }else{
      if(value == ''){
        this.templates = this.getAllTemplatesData
      }else{
        let result = this.getAllTemplatesData.filter(item => item.templateName.trim().includes(value));
        this.templates = result
      }
    }
  }


  doTableFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }

  clearSearchText(){
    this.searchText = '';
  }
  setQtyToZero(){
    if(this.setOrderQty){
      this.dataSource.data.map((element : any) => {
        element.orderQty = 0; 
        this.validateReceivedQty(0,element) ;
        return element
      })
    }else{
      this.dataSource.data.map((element : any) => {
        element.orderQty = element.defaultQty;    
        this.validateReceivedQty(element.defaultQty,element) ;
        return element
      })
    }
  }

 stockChange(event){
  this.stockType = event ;
  this.selectedWorkArea = undefined ;
  if(this.stockType == "Stockable"){
    this.workAreas = [];
    let arr2 = ['store'];
    this.workAreas.push(...arr2);
    this.createPurchaseOrderForm.get('workArea').setValue(this.workAreas);
    this.selectedWorkArea = 'store' ;
  }else{
    this.createPurchaseOrderForm.get('workArea').setValue('');
    this.workAreas = [];
    this.user.restaurantAccess.forEach(element => {
      if (element.restaurantIdOld ==  this.createPurchaseOrderForm.value.rest.restaurantIdOld) {
        this.workAreas = element.workAreas;
      } 
    });
  }
  this.dataSource = new MatTableDataSource<any>();
  this.dataSource.data = [];
 }

  getTotalofAllamount(){
    let total= this.addItemToPoForm.get('orderQty').value *
    this.utils.truncateNew(this.addItemToPoForm.get('unitPrice').value + 
    (this.addItemToPoForm.get('unitPrice').value * this.addItemToPoForm.get('taxRate').value) / 100)
    this.totalOfAllAmount = this.utils.truncateNew(total)
  }

  // getDatas(){
  //   let obj = {}
  //   obj['tenantId'] = this.auth.getCurrentUser().tenantId ;
  //   obj['stockType'] = this.createPurchaseOrderForm.value.stockType ;
  //   obj['restaurantId'] = this.createPurchaseOrderForm.value.rest.restaurantIdOld ;
  //   obj['category'] = this.selectedCategory ;
  //   obj['withoutVendorFlow'] = true;
  //     obj['workArea'] = [this.createPurchaseOrderForm.value.workArea] ;
  //   this.purchases.getInventoryNew(obj).subscribe(res => {
  //     if(res.result == 'success'){
  //      if ( this.contractType == 'contract'){
  //       let contractItems = this.contractData.filter((el)=> el['category'] == this.selectedCategory)
  //       let selectedItems = res.invItems ;
  //       let filteredItems = [] ;
  //       selectedItems.forEach((el)=>{
  //         let requiredItem = contractItems.find((item)=> item['itemCode'] == el['itemCode'] && item['packageName'] == el['packageName'])
  //         requiredItem ? filteredItems.push(el) : undefined ;
  //       })
  //       this.Bank = filteredItems;
  //      } else {
  //       let nonContractItems = res.invItems
  //       nonContractItems.forEach((el,index)=> {
  //         let requiredItem = this.contractData.find((item)=> item['itemCode'] == el['itemCode'] && item['packageName'] == el['packageName'])
  //         requiredItem ?  nonContractItems.splice(index, 1) : undefined ;
  //       })
  //       this.Bank = nonContractItems;
  //      }
  //     //  this.Bank = res.invItems;
  //       this.filteredVendorItems.next(this.Bank.slice());
  //       this.bankFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
  //           this.filterBanks();
  //       });
  //       this.setMyStyles();
  //     }else{
  //     }
  //   })
  //   this.addItems = true;
  //   this.getDraftData();
  // }

  getDatas(){
    let obj = {}
    obj['tenantId'] = this.auth.getCurrentUser().tenantId ;
    obj['stockType'] = this.createPurchaseOrderForm.value.stockType ;
    obj['restaurantId'] = this.createPurchaseOrderForm.value.rest.restaurantIdOld ;
    obj['category'] = this.selectedCategory ;
    obj['withoutVendorFlow'] = true;
      obj['workArea'] = [this.createPurchaseOrderForm.value.workArea] ;
    this.purchases.getInventoryNew(obj).subscribe(res => {
      if(res.result == 'success'){
        this.Bank = res.invItems;
        this.filteredVendorItems.next(this.Bank.slice());
        this.bankFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
            this.filterBanks();
        });
        this.setMyStyles();
      }else{
      }
    })
    this.addItems = true;
    this.getDraftData();
  }

  getBranchDetails(){
    let obj = {}
    obj['tenantId'] = this.user.tenantId;
    obj['restaurantId'] = this.createPurchaseOrderForm.value.rest.restaurantIdOld,
    obj['type'] = 'purchaseRequest',
    this.purchases.getBranchDetails(obj).subscribe(data => {
      this.approvalRequired = data.approvalRequired ;
    if (data.branchType[0].storeAvailable){
      this.disableStock = false ;
      this.stockType = undefined ;
    } else {
      this.disableStock = true ;
      if (data.branchType[0].branchType == "outlet"){
        this.stockType = 'Non-Stockable';
        this.stockChange(this.stockType) ;
      } else{
        this.stockType = 'Stockable';
        this.stockChange(this.stockType) ;
      }
    }
      this.selectedCategory = undefined ;
      this.category = [] ;
      this.searchCategory();
      this.getUsers();
      // this.getContractItems() ;
    })
    this.showInputs = true;
  }

  searchCategory() {
    let obj = {
      tenantId: this.auth.getCurrentUser().tenantId,
      restaurantId: this.createPurchaseOrderForm.value.rest.restaurantIdOld
    } 
    this.purchases.fetchCategoryWithoutVendor(obj).subscribe(res => {
      if (res.result){
        this.category = res.data ;
      } else {
        this.utils.snackBarShowInfo('Category not found')
      }
    })
  }

  categorySelection(){
    this.dataSource.data = [] ;
    this.Bank = [] ;
  }

  // contractChange(){
  //   this.addItemToPoForm.reset();
  //   this.dataSource.data = [];
  //   this.selectedRole = [] ;
  //   this.dateFn();
    
  // }


focusFunction(formKey) {
  if(Number(this.addItemToPoForm.get(formKey).value) === 0){
    this.addItemToPoForm.get(formKey).setValue(null)
  }
}

focusOutFunction(formKey) {
  if(this.addItemToPoForm.get(formKey).value === null){
    this.addItemToPoForm.get(formKey).setValue(0)
  }
}

focusFunctionWithOutForm(element){
  if(Number(element.orderQty) === 0){
    element.orderQty = null;
  }
}

focusOutFunctionWithOutForm(element){
  if(element.orderQty === null){
    element.orderQty = 0
  }
}

checkNumericInput(event: any) {
  if (this.addItemToPoForm.value.pkgName.packageName === 'NOS') {
    const input = event.target.value;
    event.target.value = input.replace(/[^0-9]/g, ''); 
    this.addItemToPoForm.get('orderQty').setValue(event.target.value);
  }
}

}