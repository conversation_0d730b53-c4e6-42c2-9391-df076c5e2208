<mat-card class="soMatCard">
  <div class="infoMessage">
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
      class="bi bi-info-circle-fill infoSvgIcon" viewBox="0 0 16 16">
      <path
        d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm.93-9.412-1 4.705c-.07.34.029.533.304.533.194 0 .487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703 0-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381 2.29-.287zM8 5.5a1 1 0 1 1 0-2 1 1 0 0 1 0 2z" />
    </svg>
    <p class="ml-2 mb-0"> Only pending purchase indents shown here. Refer to purchase status for more details </p>
  </div>

    <form [formGroup]="purTablerForm" style="display: flex;">
      <!-- <div style="display: inline;"> -->
        <mat-form-field appearance="none">
          <label>Select Branch</label><br>
          <mat-select placeholder="Select Branch" class="outline" formControlName="branchSelection" (selectionChange)="filterByBranch($event.value)">
            <mat-option *ngFor="let rest of branches" [value]="rest">
              {{ rest.branchName }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      <!-- </div> -->


      <!-- <div style="display: inline;"> -->
    <mat-form-field appearance="none" class="ml-2">
      <label>Start Date</label>
      <input matInput class="outline" [matDatepicker]="picker1" placeholder="Start Date" [formControl]="startDate" />
      <mat-datepicker-toggle matSuffix [for]="picker1">
        <mat-icon matDatepickerToggleIcon>
          <img src="./../../assets/calender.png" />
        </mat-icon>
      </mat-datepicker-toggle>
      <mat-datepicker #picker1></mat-datepicker>
    </mat-form-field>
      <!-- </div> -->

    <!-- <div style="display: inline;"> -->
      <mat-form-field appearance="none" class="ml-2">
        <label>End Date</label>
        <input matInput class="outline" [matDatepicker]="picker2" [formControl]="endDate" placeholder="End Date" [readonly] = "!startDate.value" [disabled]=" !startDate.value" [min]="startDate.value"/>
        <mat-datepicker-toggle matSuffix [for]="picker2">
          <mat-icon matDatepickerToggleIcon>
            <img src="./../../assets/calender.png" />
          </mat-icon>
        </mat-datepicker-toggle>
        <mat-datepicker #picker2></mat-datepicker>
      </mat-form-field>
    <!-- </div> -->

    <!-- <div style="display: inline; position: relative; top: 34px;"> -->
      <button mat-button class="button3 actionBtns ml-2" (click)="filterByDate()"> Find </button>

    <!-- </div> -->

    <!-- <div style="display: inline; position: relative; top: 34px;"> -->
      <button mat-button class="button3 actionBtns ml-2" (click)="clear()"> Clear </button>

    <!-- </div> -->

    <!-- <div style="display: inline; float: right; position: relative; top: 34px;"> -->
      <button mat-button class="button3 actionBtns conrtBtn ml-2 " style="float: right !important;" (click)="convert()" [disabled]="this.selection.selected.length === 0"> Convert </button>

    <!-- </div> -->
</form>

  <div>
    <mat-form-field appearance="none">
      <label>Search</label>
      <input matInput type="text" class="outline" [(ngModel)]="searchTerm" (keyup)="filterData()" placeholder="Search by PrId" />
      <mat-icon matSuffix class="closebtn" (click)="resetForm()">close</mat-icon>
    </mat-form-field>
  
    <mat-form-field appearance="none" class="ml-3">
      <label>Purchase Status</label>
      <mat-select placeholder="Status" class="outline" [formControl]="purchaseStat">
        <mat-option *ngFor="let status of purchaseStatus" [value]="status" (click)="selectPurchaseStatus(status)">
          {{ status | titlecase }}
        </mat-option>
      </mat-select>
    </mat-form-field>
  
    <button mat-raised-button matTooltip="click to refresh" class="rfBtn buttonForRefresh mt-2" (click)="refresh()">
      Refresh
    </button>
  </div>

  <table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort
  class="mat-elevation-z8">

  <ng-container matColumnDef="select">
    <th mat-header-cell *matHeaderCellDef>
      <mat-checkbox (change)="$event ? masterToggle() : null"
                    [checked]="selection.hasValue() && isAllSelected()"
                    [indeterminate]="selection.hasValue() && !isAllSelected()"  [(ngModel)] = "checkSelection">
      </mat-checkbox>
    </th>
    <td mat-cell *matCellDef="let row; let element">
      <mat-checkbox (click)="$event.stopPropagation()"
                    (change)="$event ? selection.toggle(row) : null"
                    [checked]="selection.isSelected(row)"
                    [disabled]="isPendingOrReject(element)">
      </mat-checkbox>
    </td>
  </ng-container>

  <ng-container matColumnDef="RequestId" sticky>
    <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell">
      <b> Request Id</b>
    </th>
    <td mat-cell *matCellDef="let element" >
      <span class="name-cell links" matTooltip="Show Details" (click)="detailedCr(element)">{{ element.prId }}</span>
      <span *ngIf="element.parentId"  class="parentIdClass"> {{ element.parentId }} </span>
    </td>
    <mat-divider></mat-divider>
  </ng-container>

  <ng-container matColumnDef="createdUser" sticky>
    <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Created By</b></th>
    <td mat-cell *matCellDef="let element"> {{ element.createdUser || '-'}} </td>
  </ng-container>

  <ng-container matColumnDef="requestDate" sticky>
    <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Request Date</b></th>
    <td mat-cell *matCellDef="let element">{{adjustDate(element.createTs) | date: 'MMM d, y, h:mm:ss a' }}</td>
  </ng-container>

  <ng-container matColumnDef="status" sticky>
    <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Status</b></th>
    <!-- <td mat-cell *matCellDef="let element">{{ getPoStatus(element) element.isPoCreated ? 'Completed' : 'Pending' }}</td> -->
    <td mat-cell *matCellDef="let element">{{ getPoStatus(element) }}</td>
  </ng-container>

  <ng-container matColumnDef="approvalStatus">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>
      <b>Approval Status</b>
    </th>
    <td mat-cell *matCellDef="let element">
      <div *ngIf="element?.approvalDetail && element?.approvalDetail != ''">
        <span class="links" (click)="mouseEnter(element)">{{ getDynamicText(element) }}</span>
      </div>
      <div *ngIf="!element?.approvalDetail || element?.approvalDetail == ''">
        -
      </div>
    </td>
  </ng-container>
  


  <ng-container matColumnDef="edit" sticky>
    <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Edit</b></th>
    <td mat-cell *matCellDef="let element">
      <button mat-icon-button (click)="detailedCr(element)" matTooltip="Show Details" matTooltipPosition="left" class="action-btn">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-pencil-square svgEditIcon" viewBox="0 0 16 16">
          <path d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z"/>
          <path fill-rule="evenodd" d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5v11z"/>
        </svg>
      </button> 
    </td>
  </ng-container>

  <ng-container matColumnDef="delete">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>
      <b>Delete</b>
    </th>
    <td mat-cell *matCellDef="let element">
      <button mat-icon-button (click)="deleteIndent(element.prId)" class="delete-button"  matTooltip="Delete Indent"
        matTooltipPosition="left">
        <mat-icon>delete_outline</mat-icon>
      </button>
    </td>
  </ng-container>

  <ng-container matColumnDef="closePr">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>
      <b>Close</b>
    </th>
    <td mat-cell *matCellDef="let element">
      <button mat-icon-button (click)="updateStatus(element, 'closed')"
      [disabled]=" element.hasOwnProperty('closingStatus') && element.closingStatus.indentStatus === 'closed'"
      class="action-btn" >
      <mat-icon class="action-print-icon"
        [matTooltip]="element.hasOwnProperty('closingStatus') && element.closingStatus.indentStatus === 'closed' ? '' : 'Close Indent'" matTooltipPosition="right">
        lock
      </mat-icon>
      </button>
    </td>
  </ng-container>
  <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
  <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
</table>
<div class="dataMessage" *ngIf="dataSource?.data?.length == 0"> No Data Available </div>
<mat-paginator [showTotalPages]="5" [pageSize]='10' [pageSizeOptions]="pageSizes"></mat-paginator>

</mat-card>