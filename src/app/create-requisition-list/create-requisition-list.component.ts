import { Component, OnInit, ViewChild } from '@angular/core';
import { AuthService, GlobalsService, PurchasesService, ShareDataService } from '../_services';
import { FormControl, FormGroup, FormBuilder, Validators  } from '@angular/forms';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog } from '@angular/material';
import { UtilsService } from '../_utils/utils.service';
import { Router } from '@angular/router';
import { NotificationService } from '../_services/notification.service';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { SharedFilterService } from '../_services/shared-filter.service';
import { SelectionModel } from '@angular/cdk/collections';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material';
import { MomentDateModule, MomentDateAdapter } from '@angular/material-moment-adapter';
import { SimpleDialogComponent } from '../_dialogs/simple-dialog/simple-dialog.component';
import { PreviewIbtComponent } from '../_dialogs/preview-ibt/preview-ibt.component';

export const MY_FORMATS = {
  parse: {
      dateInput: 'LL'
  },
  display: {
      dateInput: 'DD-MM-YYYY',
      monthYearLabel: 'YYYY',
      dateA11yLabel: 'LL',
      monthYearA11yLabel: 'YYYY'
  }
};
@Component({
  selector: 'app-create-requisition-list',
  templateUrl: './create-requisition-list.component.html',
  styleUrls: ['./create-requisition-list.component.scss', './../../common-dark.scss'],
  providers:[{ provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
            { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }]
})
export class CreateRequisitionListComponent implements OnInit {
  getBranchData: any[]
  branches: any[]
  user: any;
  purTablerForm: FormGroup;
  restaurantId: any;
  branchSelected: boolean;
  inputObj: any;
  displayedColumns: string[];
  dataSource: MatTableDataSource<any>;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  inventoryItems: any[];
  pageSizes = [];
  selectedStartDate: any;
  selectedEndDate: any;
  public startDate = new FormControl();
  public endDate = new FormControl();
  CreateRequisitionListUrl = encodeURI(GlobalsService.CreateRequisitionList)
  private unsubscribe$ = new Subject<void>();
  sharedFilterData: any = {};
  DetailedCreateRequisitionUrl = encodeURI(GlobalsService.DetailedCreateRequisition)
  searchText: string;
  searchValue: string;
  tempdata: any;
  searchTerm: any;
  filteredItems: any;
  checkSelection : boolean ;
  selection = new SelectionModel<any>(true, []);
  restId: any;
  elementData: any;
  purchaseStatus: string[] = ['pending','closed'];
  purchaseStat = new FormControl();
  
  constructor(
    private sharedData: ShareDataService,
    private fb: FormBuilder,
    private purchases: PurchasesService,
    private utils: UtilsService,
    private auth: AuthService,
    private router: Router,
    private notifyService: NotificationService,
    public dialog: MatDialog,
    private sharedFilterService: SharedFilterService
  ) { 
    this.user = this.auth.getCurrentUser();

    this.purTablerForm = this.fb.group({
      branchSelection: [null, Validators.required]
    });
    this.purchaseStat.setValue('pending')
    var windowLocation = window.location.href;
    let windowCurrentUrl = windowLocation.split('/')[4]

    this.sharedFilterService.getFilteredCreateReqList.pipe(takeUntil(this.unsubscribe$)).subscribe(obj => 
      this.sharedFilterData = obj
    );    
      this.sharedData.sharedBranchData.pipe(takeUntil(this.unsubscribe$)).subscribe((val) => {
        this.getBranchData = val;
        if(this.getBranchData.length == 0 ){
          this.branches = this.user.restaurantAccess;
        }else if(this.getBranchData.length == 1){        
          const toSelect = this.getBranchData.find(data => data.branchName == this.getBranchData[0].branchName);
          if(toSelect != this.sharedFilterData.restaurantId){
            this.sharedFilterData = '';
            this.startDate.setValue(null);
            this.endDate.setValue(null);
          }
          this.purTablerForm.get('branchSelection').setValue(toSelect);
          this.branches = this.getBranchData
          this.filterByBranch(this.purTablerForm.value.branchSelection);
          if(this.sharedFilterData){
            this.sharedFilterData.branchFlag = false;
          }
        }else{
          if(this.sharedFilterData.branchFlag == true){
            this.filterByBranch(this.sharedFilterData.restaurantId);
            this.sharedFilterData.branchFlag = false;
          }
          this.branches = this.getBranchData
        }
    });
  }

  ngOnInit() {
    this.displayedColumns = ['select','RequestId','requestDate','createdUser','status','approvalStatus','edit','delete','closePr']
  }

  ngOnDestroy() {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
    if(!this.router.url.includes(this.DetailedCreateRequisitionUrl)){
      this.sharedFilterService.getFilteredCreateReqList['_value'] = ''
    }
  }

  filterByBranch(restId) {
    this.restId = restId.restaurantIdOld;
    this.dataSource = new MatTableDataSource<any>();
    this.restaurantId = restId.restaurantIdOld;
    this.branchSelected = true
    if(this.sharedFilterData != '' && this.sharedFilterData.restaurantId == restId){  
      this.purTablerForm.get('branchSelection').setValue(this.sharedFilterData.restaurantId);
      this.branches = this.getBranchData
      this.startDate.setValue(this.sharedFilterData.selectedStartDate) 
      this.endDate.setValue(this.sharedFilterData.selectedEndDate) 
      this.purchaseStat.setValue(this.sharedFilterData.status)
    }
    this.getDataForPurchaseTable();
  }

  getDataForPurchaseTable(){
    let obj = {}
    obj['tenantId'] = this.user.tenantId;
    obj['restaurantId'] = this.purTablerForm.value.branchSelection.restaurantIdOld

    if(this.startDate.value && this.endDate.value){
      obj['startDate'] = this.utils.dateCorrection(this.startDate.value);
      obj['endDate'] = this.utils.dateCorrection(this.endDate.value);
    }else{
      obj['startDate'] = null,
      obj['endDate'] = null
    }

    this.purchases.getDataForPurchaseTable(obj).subscribe((response: any) => {
      if (response.result == true) {
        this.dataSource = new MatTableDataSource<any>();
        this.dataSource.data = response.data
        this.tempdata = response.data
        this.selectPurchaseStatus('pending');
        this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
        this.dataSource.paginator = this.paginator;
      }
    });
  }

  detailedCr(obj){
    let editFlag = false
    let inputObj = {
      restaurantId : this.restId,
      selectedStartDate : this.startDate.value,
      selectedEndDate : this.endDate.value,
      branchFlag : true,
      status : this.purchaseStat.value,
    }    
    this.sharedFilterService.getFilteredCreateReqList.next(inputObj);
    this.sharedData.createReq(obj);
    this.router.navigate(['/home/<USER>']);
  }

  filterByDate() {
      this.getDataForPurchaseTable() ;
  }

  clear(){
    this.startDate.setValue(null);
    this.endDate.setValue(null);
    this.getDataForPurchaseTable();
  }

  refresh(){
    this.getDataForPurchaseTable();
  }

  resetForm() {
    this.searchText = ''
    this.searchValue = ''
    this.dataSource.data = this.tempdata
  }

  filterData() {
    const searchTerm = this.searchTerm.toLowerCase();
    this.dataSource.data = this.tempdata.filter(item =>
      item.prId.toLowerCase().includes(searchTerm)
    );
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;  
    const filteredData = this.dataSource.data.filter(item => {      
      return item.approvalDetail.length === 0 || item.approvalDetail.some(detail => detail.status === 'approved');
    });
    const numRows = filteredData.length;
    return numSelected === numRows;
  }

  masterToggle() {
    this.isAllSelected() ?
        this.selection.clear() :
        this.dataSource.data.forEach(row => {
          let data = row.approvalDetail.some(item => item.status === 'pending' || item.status === 'rejected')
          // !data && (!row.hasOwnProperty('closingStatus') || row.closingStatus.indentStatus != 'closed') ? this.selection.select(row) : undefined ;
          if(!data){
            if(!row.hasOwnProperty('closingStatus')){
              this.selection.select(row)
            } else {
              if(row.closingStatus.indentStatus != 'closed'){
                this.selection.select(row)
              }
            }
          }
        })
  }

  convert(){
    this.sharedData.createReq(this.selection.selected);
    this.router.navigate(['/home/<USER>']);
  }

  adjustDate(inputDate: string): Date {
    const date = new Date(inputDate);
    date.setHours(date.getHours() - 5);
    date.setMinutes(date.getMinutes() - 30);
    return date;
  }
  
  isPendingOrReject(element) {
    if(element.approvalDetail){}{
      return element.approvalDetail.some(item => item.status === 'pending' || item.status === 'rejected') || element.hasOwnProperty('closingStatus') && element.closingStatus.indentStatus === 'closed';
    }
  }

  deleteIndent(prId) {
    this.dialog.open(SimpleDialogComponent, {
      data: {
        title: 'Delete Indent',
        msg: 'Are you sure you want to delete?',
        ok: function () {
          let obj = {}
          obj['tenantId'] = this.user.tenantId
          obj['userName'] = this.user.name
          obj['prId'] = prId
          this.purchases.deletePurchaseIndent(obj).subscribe(res => {
            if (res['success']) {
              this.getDataForPurchaseTable();
              this.utils.snackBarShowSuccess('Indent deleted successfully!');
            } else {
              this.utils.snackBarShowError('Something went wrong,please try again');
            }
          })
        }.bind(this)
      }
    });
  }

  mouseEnter(element){
    this.elementData = element
    this.dialog.open(PreviewIbtComponent, {
      width: "600px",
      data: {
        title: "Purchase Request",
        component: "Purchase Status",
        items: this.elementData,
        ok: function () {
          this.location.back();
        }.bind(this),
      },
    });
  }

  getPrStatus(data) {
    if (Object.keys(data).length !== 0) {
      const levelOrder = data.map(item => item.level);
      let statusWithRole = "";
      for (const currentLevel of levelOrder) {
        const matchingData = data.find(item => item.level === currentLevel);
        
        if (matchingData) {
          const { level, status, role } = matchingData;
          
          if (status === "rejected") {
            statusWithRole = `${status.charAt(0).toUpperCase() + status.slice(1)} (${role})`;
            break;
          } else if (status === "pending" && !statusWithRole.includes("rejected")) {
            statusWithRole = `${status.charAt(0).toUpperCase() + status.slice(1)} (${role})`;
          } else if (status === "approved" && !statusWithRole.includes("rejected") && !statusWithRole.includes("pending")) {
            statusWithRole = `${status.charAt(0).toUpperCase() + status.slice(1)} (${role})`;
          }
        }
      }
      return statusWithRole;
    }
  }
  
  getDynamicText(element: any): string {
    if (element.approvalDetail && element.approvalDetail.length > 0) {
      return this.getPrStatus(element.approvalDetail);
    } else {
      return 'Click here';
    }
  }

  updateStatus(element,event){
    let obj = {}
    obj['tenantId'] = element.tenantId
    obj['restaurantId'] = element.restaurantId
    obj['prId'] = element.prId
    obj['uId'] = this.user.mId
    obj['indentStatus'] = event
    this.dialog.open(SimpleDialogComponent, {
      data: {
        title: 'Close Indent',
        msg: 'Are you sure you want to Close?',
        ok: function () {
          this.purchases.updatePrStatus(obj).subscribe(res => {
            res.result ? (this.utils.snackBarShowSuccess('Indent closed successfully'),this.refresh()) : this.utils.snackBarShowError('Something went wrong')
          })
        }.bind(this)
      }
    });
}


getPoStatus(element){
  if(!element.hasOwnProperty('closingStatus')){
    return element.isPoCreated ? 'Completed' : 'Pending' 
  } else {
    if(element.closingStatus.indentStatus != 'closed'){
      return element.isPoCreated ? 'Completed' : 'Pending' 
    }
    return 'closed'
  }
}

selectPurchaseStatus(status){
  if(status === 'pending'){
    this.dataSource.data = this.tempdata.filter(element => !element.closingStatus);
  }else{
    this.dataSource.data = this.tempdata.filter(element => element.closingStatus && element.closingStatus.indentStatus === "closed");
  }
}

}
