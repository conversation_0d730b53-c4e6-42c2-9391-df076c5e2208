<form [formGroup]="CreatePurchaseOrderV2Form" class="d-flex ml-4"> 

  <mat-form-field appearance="none">
    <label>Restaurant</label>
    <mat-select  placeholder="Restaurant" formControlName="rest" class="outline" (selectionChange)="setRestaurant($event.value)">
      <mat-option *ngFor="let rest of branchesData" [value]="rest">
        {{ rest.branchName }}
      </mat-option>
    </mat-select>
  </mat-form-field>

  <mat-form-field appearance="none" class="ml-2">
    <label>Stock Type</label>
    <mat-select placeholder="Stock Type" formControlName="stockType" class="outline" (selectionChange)="stockChange($event.value)">
      <mat-option value="Stockable">Stockable Items</mat-option>
      <mat-option value="Non-Stockable">Non-Stockable Items</mat-option>
    </mat-select>
  </mat-form-field>

  <mat-form-field appearance="none" class="ml-2">
    <label>Destination</label>
    <mat-select formControlName="workArea" multiple placeholder="select workArea" class="outline"
    [(ngModel)]="selectedWorkArea">
      <mat-option *ngFor="let area of workAreas" [value]="area">
        {{ area }}
      </mat-option>
    </mat-select>
    <mat-error>
      Select workArea
    </mat-error>
  </mat-form-field>

  <div class="findBtn">
    <button mat-button class="button3 ml-2" (click)="getDatas()"
    [disabled]="CreatePurchaseOrderV2Form.invalid">
      find
    </button>
  </div>


</form>

<br>

<mat-card *ngIf="isShowInventoryitemcard">
  <form [formGroup]="addItemToPoForm" class="itemsClass">
    <div cdkTrapFocus class="row">
        <div class="col">
          <mat-form-field appearance="none">
            <label>Item Name</label>
            <mat-select placeholder="Inventory Item" #singleSelect tabindex="0" #accTypeInventory
              (focus)="accTypeInventory.open()" class="outline" formControlName="inventoryItem"
              (selectionChange)="purchaseItemSelect($event)">
              <mat-option>
                <ngx-mat-select-search placeholderLabel="Inventory Item..."
                  noEntriesFoundLabel="'no Inventory Item found'"
                  [formControl]="bankFilterCtrl"></ngx-mat-select-search>
              </mat-option> 
              <mat-option *ngFor="let item of this.itemNames | async" [value]="item">
                {{item.itemName}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <div class="col">
          <mat-form-field appearance="none">
            <label>Pkg size</label>
            <mat-select #accTypepkgName (focus)="accTypepkgName.open()" focusOnInit placeholder="Pkg size"
              class="outline" formControlName="packageName" (selectionChange)="pkgSelect($event)">
              <mat-option *ngFor="let pkg of packagingSizes" [value]="pkg.packageName">
                {{pkg.packageName}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <div class="col">
          <mat-form-field appearance="none">
            <label>Quantity</label>
              <input matInput type="number" step="0.01" min="0"
              class="outline" [(ngModel)]="quantity" 
              placeholder="Quantity" formControlName="orderQty">
          </mat-form-field>
        </div>

        <!-- <div class="col">
          <mat-form-field appearance="none" class="testarea" *ngIf="checked == true">
            <label> Add Description (Max. 50) <b class="descriptionStar">*</b> </label>
            <textarea matInput style="font-size: 15px; height: 40px;" formControlName="itemDescription"
              [(ngModel)]="descriptionClear" class="outline" maxlength="50" required></textarea>
          </mat-form-field>
        </div> -->

        <div class="addItemBtnclss col">
          <button mat-button
            (click)="addItemtoPo()" class="addItemBtn button3">
            Add Item
          </button>
        </div>
    </div>
  </form>
</mat-card>

<mat-card *ngIf="this.dataSource?.data?.length > 0">
  <button mat-button class="button3 sendReqBtn my-2" (click)="sendRequest()">send Request</button>
  <table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort>
    <ng-container matColumnDef="itemName">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Inventory Item</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ element.itemName | titlecase }}
      </td>
      <!-- <td mat-footer-cell *matFooterCellDef> Total </td> -->
    </ng-container>

    <ng-container matColumnDef="pkgName">
      <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Package Name</b></th>
      <td mat-cell *matCellDef="let element"> {{element.packageName}} </td>
      <!-- <td mat-footer-cell *matFooterCellDef></td> -->
    </ng-container>


    <ng-container matColumnDef="quantity">
      <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Quantity</b></th>
      <td mat-cell *matCellDef="let element"> {{element.orderQty}} </td>
      <!-- <td mat-footer-cell *matFooterCellDef></td> -->
    </ng-container>

    <ng-container matColumnDef="description">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Item Description</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ element.description | titlecase }}
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
    <!-- <tr mat-footer-row *matFooterRowDef="displayedColumns"></tr> -->
  </table>


</mat-card>
