import { Component, OnInit, ViewChild } from '@angular/core';
import { AuthService, PurchasesService, ShareDataService } from '../_services';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import {CdkDragDrop, moveItemInArray, transferArrayItem} from '@angular/cdk/drag-drop';
import { ReplaySubject, Subject, interval } from 'rxjs';
import { take, takeUntil } from 'rxjs/operators';
import { MatDialog, MatPaginator, MatSort, MatTableDataSource } from '@angular/material';
import { UtilsService } from '../_utils/utils.service';
import { PurchaseOrder } from '../_models';
import { NotificationService } from '../_services/notification.service';
import { environment } from '../../environments/environment';
import { SimpleDialogComponent } from '../_dialogs/simple-dialog/simple-dialog.component';
import { Router } from '@angular/router';
import { AutoGrnService } from '../_services/auto-grn.service';

@Component({
  selector: 'app-create-requisition',
  templateUrl: './create-requisition.component.html',
  styleUrls: ['./create-requisition.component.scss', "./../../common-dark.scss"]
})
export class CreateRequisitionComponent implements OnInit {

  getBranchData: any[]
  branchesData: any[]
  user: any;
  CreatePurchaseOrderV2Form: FormGroup;

  workAreas: any[];
  stockType: any;
  selectedRest: any;
  // itemNames: any[];

  descriptionClear
  quantity
  addItemToPoForm: FormGroup;
  checked = true

  public Bank: any[] = [];
  public bankFilterCtrl: FormControl = new FormControl();
  public itemNames: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  protected _onDestroy = new Subject<void>();
  selectedItems: any[];

  dataSource: MatTableDataSource<any>;
  displayedColumns: string[];
  pageSizes: any[];
  @ViewChild(MatPaginator) paginator: MatPaginator;
  packagingSizes: string[];
  pushedData = [];
  isShowInventoryitemcard : boolean = false;
  startDate: any;
  purchaseOrder: PurchaseOrder = {};
  selectedWorkArea: any;
  multiBranchUser; branchSelected: boolean;
  private subscription;
  public intervalTimer = interval(500);

  constructor(
    private sharedData: ShareDataService,
    private fb: FormBuilder,
    private auth: AuthService, 
    private purchases: PurchasesService,
    private utils: UtilsService,
    private notifyService: NotificationService,
    private dialog: MatDialog,
    private router : Router,
    private autoGrnService:AutoGrnService,
  ) {

    this.user = this.auth.getCurrentUser();
    this.multiBranchUser = this.user.multiBranchUser;

    this.CreatePurchaseOrderV2Form = this.fb.group({
      rest: ['', Validators.required],
      stockType: ['',Validators.required],
      workArea: ['',Validators.required],
      expectedDate: [''],
      validityDate: [''],
    });

    this.addItemToPoForm = this.fb.group({
      inventoryItem: ['', Validators.required],
      packageName: ['', Validators.required],
      orderQty: [0, Validators.required],      
      itemDescription : ['']
    });

    this.sharedData.sharedBranchData.subscribe((val) => {
      this.getBranchData = val;
      if(this.getBranchData.length == 0 ){
        this.branchesData = this.user.restaurantAccess;
      }else if(this.getBranchData.length == 1){
        const toSelect = this.getBranchData.find(data => data.branchName == this.getBranchData[0].branchName);
        this.CreatePurchaseOrderV2Form.get('rest').setValue(toSelect);
        this.branchesData = this.getBranchData
        // this.setRestaurant(this.purchaseRequestForm.value.branchSelection);
        this.setRestaurant(this.branchesData[0]);
      }else{
        this.branchesData = this.getBranchData
      }
  });
   }

  ngOnInit() {
    if (!this.user.multiBranchUser) {
      this.CreatePurchaseOrderV2Form.value.rest = this.user.restaurantId
      this.branchSelected = true;
    }

    this.startDate = new Date();
    let validityDate = new Date(this.startDate.getTime() + (24 * 60 * 60 * 1000));
    this.CreatePurchaseOrderV2Form.get('expectedDate').setValue(this.startDate);
    this.CreatePurchaseOrderV2Form.get('validityDate').setValue(validityDate);
    this.subscription = this.intervalTimer.subscribe(() => {
      if (this.CreatePurchaseOrderV2Form.value.rest.restaurantIdOld !=""){
        this.subscription.unsubscribe();
      }
    });
  }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  protected filterBanks() {
    if (!this.Bank) {
      return;
    }
    // get the search keyword
    let search = this.bankFilterCtrl.value;
    if (!search) {
      this.itemNames.next(this.Bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    // filter the banks
    this.itemNames.next(
      this.Bank.filter(bank => bank.itemName.toLowerCase().indexOf(search) > -1)
    );
  }

  setRestaurant(val){    
    this.selectedRest = val.restaurantIdOld;
    this.CreatePurchaseOrderV2Form.value.rest.restaurantIdOld
  }

  stockChange(event){
    this.stockType = event ;
    if(this.stockType == "Stockable"){
      this.workAreas = [];
      let arr2 = ['store'];
      this.workAreas.push(...arr2);
      this.CreatePurchaseOrderV2Form.get('workArea').setValue(this.workAreas);
    }else{
      this.CreatePurchaseOrderV2Form.get('workArea').setValue('');
      this.workAreas = [];
      this.user.restaurantAccess.forEach(element => {
        if (element.restaurantIdOld ==  this.selectedRest) {
          this.workAreas = element.workAreas;
        } 
      });
    }
    this.dataSource = new MatTableDataSource<any>();
    this.dataSource.data = [];
  }

  getDatas(){
    let obj = {}
    obj['restaurantId'] = this.CreatePurchaseOrderV2Form.value.rest.restaurantIdOld;
    obj['stockType'] = this.CreatePurchaseOrderV2Form.value.stockType;
    if(this.stockType != "Stockable"){
      obj['workArea'] = this.CreatePurchaseOrderV2Form.value.workArea;
    }
    this.purchases.getCreatePurchaseOrderV2data(obj).subscribe(data => {
      if(data.result == true){
        // this.dataSource.data = data.data
        // const itemNames = data.data.map(item => item.itemName);
        // this.itemNames = itemNames
        this.Bank = data.data;
        this.itemNames.next(this.Bank.slice());
        this.bankFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
            this.filterBanks();
        });
        this.isShowInventoryitemcard = true;
      }else{
      }
    })
  }

  purchaseItemSelect(n){
    this.packagingSizes = [n.value];
    this.selectedItems = n.value;
  }


  createRequisition(){
    let obj = {}
    obj['tenantId'] = this.user.tenantId;
    obj['restaurantId'] = this.CreatePurchaseOrderV2Form.value.rest.restaurantIdOld
    obj['date'] = new Date();
    obj['stocktype'] = this.CreatePurchaseOrderV2Form.value.stockType;
    obj['workArea'] = this.CreatePurchaseOrderV2Form.value.workArea;

    const result = this.pushedData.map(item => {
      return {
        itemCode: item.itemCode,
        itemName: item.itemName,
        packageName: item.packageName,
        orderQty: item.orderQty
      };
    });
    obj['invItems'] = result;    
    this.purchases.createRequisition(obj).subscribe(data => {
      if(data.success == true){
        this.utils.snackBarShowSuccess('items has Sent successfully')
        this.router.navigate(['/home/<USER>']);
      }
    })
  }

  addItemtoPo(){
      this.selectedItems['orderQty'] = this.addItemToPoForm.value.orderQty
      this.selectedItems['packageName'] = this.addItemToPoForm.value.packageName
      this.pushedData.push(this.selectedItems)
      this.displayedColumns = ['itemName','pkgName' ,'quantity'];
      this.dataSource = new MatTableDataSource<any>();
      this.dataSource.data = this.pushedData;
      this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
      this.dataSource.paginator = this.paginator;
      this.addItemToPoForm.reset();
  }

  pkgSelect(pkg) {
    // this.selectedPkg = pkg.value
    // if (this.selectedPkg.packageName != 'N/A') {
    //   this.brand =  pkg.value.brand ? pkg.value.brand : null ;
    //   this.brand ? this.selectedBrandControl.disable() : null ;
    //   this.addItemToPoForm.get('unitPerPkg').setValue((pkg.value.packageQty/pkg.value.unitQty));
    //   this.addItemToPoForm.get('unitPrice').setValue(pkg.value.unitPrice);
    //   this.selectContractPrice(this.selectedItem['itemCode'] , this.selectedPkg.packageName )
    // }
  }

  sendRequest(){
    // if (this.stockType == 'Non-Stockable' && this.selectedWorkArea == undefined){
    //   this.notifyService.showWarning('Please select workArea' , '')
    //   } else {

    //   this.dataSource.data.map((element : any) => {
    //     element.pkgName = element.packageName,element.unitPerPkg = element.unitPerPkg , element.pkgQty = element.packageQty; 
    //     return element});
    //   this.purchaseOrder.prItems = this.dataSource.data

    //   this.purchaseOrder.orderedDate = new Date();
    //   let purchaseReq = {
    //     // vendorId: this.selectedVendor.tenantId,
    //     vendorId: '',
    //     tenantId: this.auth.getCurrentUser().tenantId,
    //     prItems: this.purchaseOrder.prItems.reverse(),
    //     eta: this.CreatePurchaseOrderV2Form.value.expectedDate,
    //     validityDate: this.CreatePurchaseOrderV2Form.value.validityDate,
    //     uId: this.auth.getCurrentUser().mId,
    //     rId: this.CreatePurchaseOrderV2Form.value.rest.restaurantIdOld,
    //     totalAmount: 0,
    //     remarks : '',
    //     paymentTerms : '',
    //     poTerms : '',
    //     paymentMethods : ''
    //   }
    //   if ("userName" in this.auth.getCurrentUser()) {
    //     purchaseReq['poMadeBy'] = this.auth.getCurrentUser().userName
    //   } else {
    //     purchaseReq['poMadeBy'] = "NA"
    //   }
    //   // if(this.selectedRole && this.selectedRole.length > 0){
    //   //   purchaseReq['role'] = this.selectedRole
    //   // } else {
    //     purchaseReq['role'] = [];
    //   // }
    //   purchaseReq['appCat'] = '';
    //   purchaseReq['contractType'] = '';
    //   purchaseReq['senderEmail'] = [this.user.email]
    //   purchaseReq['selectedWorkArea'] = this.selectedWorkArea;
    //   purchaseReq['key'] = 'createPurchaseOrderV2';
  
    //   this.dialog.open(SimpleDialogComponent, {
    //     data: {
    //       title: 'Place Request',
    //       msg: 'Are you sure you want to send Purchase Request?',
    //       ok: async function () {
    //         let inputData: any = {
    //           // vendorId: this.selectedVendor.tenantId,
    //           vendorId: '',
    //           tenantId: this.auth.getCurrentUser().tenantId,
    //           uId: this.auth.getCurrentUser().mId,
    //           rId: this.CreatePurchaseOrderV2Form.value.rest.restaurantIdOld,
    //           cType: this.contractType
    //         }    
    //         inputData['stockType'] = this.stockType ? this.stockType : undefined ;
    //           this.purchases.createPrV2(purchaseReq).subscribe(data => {
                
    //             let obj: any = {}
    //             obj['tenantId'] = this.auth.getCurrentUser().tenantId,
    //             obj['tenantName'] = this.auth.getCurrentUser().name,
    //             obj['rId'] = this.CreatePurchaseOrderV2Form.value.rest.restaurantIdOld,
    //             obj['prId'] = data.prId,
    //             obj['createTs'] = data.modTs,
    //             obj['totalAmount'] = 0,
    //             // obj['vendorName'] = this.selectedVendor,
    //             obj['vendorName'] = '',
    //             obj['baseUrl'] = environment.baseUrl,
    //             obj['contractType'] = this.contractType,
    //             obj['userEmail'] = this.auth.getCurrentUser().email,
    //             obj['appCat'] = this.appCat
    //             // if(this.selectedRole && this.selectedRole.length > 0){
    //             //   obj['role'] = this.selectedRole
    //             // } else {
    //             obj['role'] = [];
    //             // }
    //             console.log(obj);
                  
    //             this.router.navigate(['/home'])
    //             this.purchases.getPermission(obj).subscribe(res => {
    //               if (res.result === true) {
    //                 if(res.permission){
    //                   if(this.contractType == 'contract'){
    //                     this.notifyService.showSuccess('Order belongs to contract, No approvals required' , '')
    //                   }
    //                   // this.autoGrnService.autoGrn(data.prId);
    //                 }else {
    //                   if (obj['role'].length > 0){
    //                     this.notifyService.showSuccess('Order belongs to role based approval' , '')
    //                   }else {
    //                     this.notifyService.showSuccess('Order belongs to Amount based approval' , '')
    //                   }
    //                   this.previewPr(data);
    //                 }
    //               }
    //             })
    //             this.CreatePurchaseOrderV2Form.reset();
    //             // this.addItemToPoForm.reset();
    //             this.dataSource.data = [];
    //             // this.selectedRole = [] ;
    //             this.dateFn();
    //           }, err => console.error(err));

            
    //       }.bind(this)
    //     }
    //   });
    // } 

    this.createRequisition()
  }

  dateFn(){
    this.startDate = new Date();
    let validityDate = new Date(this.startDate.getTime() + (24 * 60 * 60 * 1000));
    this.CreatePurchaseOrderV2Form.get('expectedDate').setValue(this.startDate);
    this.CreatePurchaseOrderV2Form.get('validityDate').setValue(validityDate);
  }

}
