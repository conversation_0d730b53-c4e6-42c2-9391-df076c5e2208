<div class="mr-2 mt-2">
  <button mat-button mat-raised-button class="button3 uIbutton ml-2" *ngIf=buttonAccess (click)="issueIndentReq()" [disabled]='(!allowOrder)'>
    Request Indent
  </button>
  <button mat-button mat-raised-button class="exprbutton button ml-2" *ngIf=buttonAccess (click)="exportToExcel()">
    Export
  </button>
  <button mat-button mat-raised-button class="exprbutton button ml-2" *ngIf=buttonAccess (click)="printToPdf()">
    Print
  </button>
  <mat-slide-toggle *ngIf='buttonAccess' class="togglebutton ml-2" [(ngModel)]="indentPreview"
    (change)="preview()">Preview
  </mat-slide-toggle>
</div>

<div class="splIndentTopDiv mt-2">
  <form [formGroup]="indentForm" class="topHeadInputs">
    <mat-form-field *ngIf="multiBranchUser" appearance="none" class="topitem">
      <label>Source Branch</label>
      <mat-select placeholder="Source Branch" class="outline" formControlName="sourceBranch"
        (selectionChange)="filterByBranch($event.value)">
        <mat-option *ngFor="let rest of branches" [value]="rest" [disabled]="rest.branchName == this.sourceReceipient">
          {{ rest.branchName }}
        </mat-option>
      </mat-select>
    </mat-form-field>
    
    <mat-form-field *ngIf="multiBranchUser" appearance="none" class="topitem ml-2">
      <label>Receipient Branch</label>
      <mat-select placeholder="Receipient Branch" class="outline" formControlName="receipientBranch"
        (selectionChange)="filterByReceipientBranch($event.value)"  [disabled]="!enableReceipientBranch">
        <mat-option *ngFor="let rest of branches" [value]="rest" [disabled]="rest.branchName == this.sourceBranch">
          {{ rest.branchName }}
        </mat-option>
      </mat-select>
    </mat-form-field>
    <!-- *ngIf='stockSeparation' -->
  <mat-form-field appearance="none" class="topitem ml-2" >
    <label>Stock Type</label>
    <mat-select placeholder="Stock Type" class="outline"  formControlName="stockType"
      (selectionChange)="stockChange($event)" disabled>
      <mat-option value="Stockable">Stockable Items</mat-option>
      <!-- <mat-option value="Non-Stockable">Non-Stockable Items</mat-option>[(ngModel)]="stockType" -->
    </mat-select>
  </mat-form-field>
</form>

  <!-- *ngIf="specialFlag == true && viewAccess" -->
  <!-- <mat-form-field appearance="none" class="topitem ml-2">
    <label>Delivered To</label>
    <mat-select placeholder="Select Work Area" (selectionChange)="selectIndentArea($event)" class="outline"
      [(ngModel)]="currentWorkArea" [disabled]="!enableWorkArea">
      <mat-option *ngFor="let area of IndentAreas" [value]="area">
        {{ area }}
      </mat-option>
    </mat-select>
  </mat-form-field> -->

  <mat-form-field appearance="none" class="topitem ml-2">
    <label>Delivered To</label>
    <mat-select placeholder="Select Work Area" (selectionChange)="selectIndentArea($event)" class="outline" 
    [(ngModel)]="currentWorkArea" [disabled]="!enableWorkArea">
      <mat-option>
        <ngx-mat-select-search placeholderLabel="Work Area..."
          noEntriesFoundLabel="'Work Area Not Found'"
          [formControl]="workAreaFilterCtrl"></ngx-mat-select-search>
      </mat-option>
      <mat-option *ngFor="let area of workAreasBanks | async" [value]="area">
        {{ area }}
      </mat-option>
    </mat-select>
  </mat-form-field>

  <mat-form-field appearance="none" *ngIf=buttonAccess class="topitem ml-2">
    <label>Document Date</label>
    <input matInput [matDatepicker]="picker1" class="outline" placeholder="Indent Document Date"
      [formControl]="documentDate" [(ngModel)]="today" />
    <mat-datepicker-toggle matSuffix [for]="picker1">
      <mat-icon matDatepickerToggleIcon>
        <img class="datepickIcon" src="./../../assets/calender.png" />
      </mat-icon>
    </mat-datepicker-toggle>
    <mat-datepicker #picker1></mat-datepicker>
  </mat-form-field>
</div>


<!-- *ngIf="(branchSelected && multiBranchUser && workAreaSelected) || (!multiBranchUser && workAreaSelected)" -->
<div class="datacontainer" *ngIf="showTable">
  <mat-card>
    <mat-card-content>
      <div class="search-table-input">

        <mat-form-field appearance="none">
          <label>Search</label>
          <input matInput type="text" (keyup)="doFilter($event.target.value)" placeholder="Search" class="outline"
            [(ngModel)]='searchText' />
          <mat-icon matSuffix (click)="clear()" class="closebtn">close</mat-icon>
        </mat-form-field>

        <mat-form-field appearance="none">
          <label>Type</label>
          <mat-select placeholder="Item Type" [formControl]="ItemType" class="outline">
            <mat-option *ngFor="let itemType of ItemTypeList" [value]="itemType" (click)="selectItemType(itemType)">
              {{ itemType | titlecase }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="none">
          <label>Category</label>
          <mat-select placeholder="Category" class="outline" [formControl]="category">
            <mat-option *ngFor="let cat of categoryList" [value]="cat" (click)="selectCategory(cat)">
              {{ cat | titlecase}}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="none">
          <label>Sub Category</label>
          <mat-select placeholder="Subcategory" [formControl]="Subcategory" class="outline">
            <mat-option *ngFor="let subCat of subCategoryList" [value]="subCat" (click)="selectSubCat(subCat)">
              {{ subCat | titlecase }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort >

        <ng-container matColumnDef="index">
          <th mat-header-cell *matHeaderCellDef mat-sort-header class="tableId"><b>#</b></th>
          <td mat-cell *matCellDef="let element; let i = index" class="tableId">
            {{ i + 1 + paginator.pageIndex * paginator.pageSize }}
          </td>
        </ng-container>

        <ng-container matColumnDef="itemName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Item Name</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.itemName | titlecase }}
          </td>
        </ng-container>
        <ng-container matColumnDef="moq">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Minimum Issue Quantity</b>
          </th>
          <td mat-cell *matCellDef="let element">{{ element.moq }}</td>
        </ng-container>

        <ng-container matColumnDef="inStock">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> In Stock</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ this.utils.truncateNew(element.inStock) }}
          </td>
        </ng-container>
        <ng-container matColumnDef="unitPrice">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Unit Price </b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ this.utils.truncateNew(element.price ) }}
          </td>
        </ng-container>
        <ng-container matColumnDef="totalPrice">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Total Price </b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ this.utils.truncateNew(element.issueQty * element.price) }}
          </td>
        </ng-container>
        <ng-container matColumnDef="pkgName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Pkg Name</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.packageName }}
          </td>
        </ng-container>
        <ng-container matColumnDef="entryType">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Entry Type</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.entryType }}
          </td>
        </ng-container>

        <ng-container matColumnDef="projectedSales">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Projected Consumption</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ this.utils.truncateNew(element.projectedSales) }}
          </td>
        </ng-container>
        <ng-container matColumnDef="issueQty">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Indent Quantity</b>
          </th>
          <td mat-cell *matCellDef="let element">
            <input class="input1" type="number" step="0.01" min="0" (keyup)="getTotalIndentCost($event , element)"
              [(ngModel)]="element.issueQty" (focus)="focusFunctionWithOutForm(element,'issueQty')" (focusout)="focusOutFunctionWithOutForm(element,'issueQty')"
               />
               <!-- onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)" -->
          </td>

        </ng-container>
        <ng-container matColumnDef="uom">
          <th mat-header-cell *matHeaderCellDef mat-sort-header><b> Uom</b></th>
          <td mat-cell *matCellDef="let element">{{ element.uom }}</td>
        </ng-container>
        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>
      <div class="dataMessage" *ngIf="dataSource?.data.length == 0"> No Data Available </div>
    </mat-card-content>
  </mat-card>
  <mat-paginator [showTotalPages]="5" [pageSize]='10' [pageSizeOptions]="pageSizes"></mat-paginator>

</div>