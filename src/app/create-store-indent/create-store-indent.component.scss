input[type=number]::-webkit-inner-spin-button, 
input[type=number]::-webkit-outer-spin-button { 
  -webkit-appearance: none; 
}

input[type=number] {
  -moz-appearance: textfield;
}

.uIbutton {
  float: right !important;
}

.uIbutton:disabled {
  background-color:  #c2c2a3 !important;
  color: black !important;
}

.exprbutton {
  min-width: 78px ;
  float: right;
}

.togglebutton{
  float: right;
}

.splIndentTopDiv{
  overflow: hidden;
  width: 96%;
  margin-left: 2% !important;
  margin-right: 2% !important;
}

.bottomButtons{
  float: right;
}


