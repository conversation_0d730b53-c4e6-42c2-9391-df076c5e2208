import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService, PurchasesService, BranchTransferService, ShareDataService } from '../_services';
import { GlobalsService } from '../_services/globals.service';
import { UtilsService } from '../_utils/utils.service';
import { MatDialog } from '@angular/material';
import { SimpleDialogComponent } from '../_dialogs/simple-dialog/simple-dialog.component';
import { MatTableDataSource, MatPaginator, MatSort } from '@angular/material';
import { PackageDialogComponent } from '../_dialogs/package-dialog/package-dialog.component';
import { NotificationService } from '../_services/notification.service';
import { FormBuilder, FormControl, FormGroup, Validators } from "@angular/forms";
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material';
import { MomentDateModule, MomentDateAdapter } from '@angular/material-moment-adapter';
import { environment } from 'src/environments/environment';
import { Observable } from 'rxjs';
import { map, startWith } from 'rxjs/operators';
import { log } from 'console';
import { interval, Subscription,ReplaySubject , Subject} from 'rxjs';
import { takeUntil } from 'rxjs/operators';

export const MY_FORMATS = {
  parse: {
      dateInput: 'LL'
  },
  display: {
      dateInput: 'DD-MM-YYYY',
      monthYearLabel: 'YYYY',
      dateA11yLabel: 'LL',
      monthYearA11yLabel: 'YYYY'
  }
};

@Component({
  selector: 'app-create-store-indent',
  templateUrl: './create-store-indent.component.html',
  styleUrls: ['./create-store-indent.component.scss', './../../common-dark.scss'],
  providers:[{ provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
            { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }
          ]
})
export class CreateStoreIndentComponent implements OnInit {
  IndentAreas: string[] = [];
  indentArea: any;
  specialFlag: boolean;
  buttonAccess: boolean = false;
  all = "ALL";
  indentPreview: boolean = false;
  viewAccess: boolean = true;
  curDataSource: any[];
  specialIndentUrl = encodeURI(GlobalsService.specialIndents)
  CreateStoreIndentUrl = encodeURI(GlobalsService.CreateStoreIndent);
  fromBrachFilterOptions: Observable<any[]>;
  toBrachFilterOptions: Observable<any[]>;
  user: any;
  inventoryItems: any[];
  displayedColumns: any[];
  restaurantId: any;
  savedItems: any;
  workAreaSelected: boolean = false;
  stockSeparation: boolean;
  stockType:any;
  currentWorkArea:any;
  initData: any;
  branchSelected: any;
  multiBranchUser: boolean = false;
  categoryList = ['All'];
  subCategoryList = ['All'];
  ItemTypeList = ['All'];
  initSubCategoryList: any = [];
  initCategoryList: any = [];
  subCatList: any = [];
  searchText: any;
  selectedWorkArea: any;
  filterKeys = { ItemType: 'All', category: 'All', subCategory: 'All' }
  category = new FormControl('', [Validators.required]);
  Subcategory = new FormControl();
  documentDate = new FormControl();
  ItemType = new FormControl();
  dataSource: MatTableDataSource<any>;
  totalIndentCost: any = 0;
  branches: any[];
  indentForm: FormGroup;
  totalRoles: any;
  allowOrder: boolean ;
  roles: any =[];
  appCat: any;
  selectedRole: any;
  searchWorkAreaText: any;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  today: any;
  pageSizes: any;
  private specialKeys: Array<string> = ['Backspace', 'Tab', 'End', 'Home', 'ArrowLeft', 'ArrowRight', 'Del', 'Delete'];
  private regex: RegExp = new RegExp(/^\d*\.?\d{0,2}$/g);
  receipientRestaurantId: any;
  enableReceipientBranch: boolean = false;
  receipientBranch: string;
  enableWorkArea: boolean = false;
  showTable: boolean = false;
  sourceBranch: any;
  sourceReceipient: any;
  public workAreaFilterCtrl: FormControl = new FormControl();
  public workAreasBanks: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  protected _onDestroy = new Subject<void>();
  public workAreaBank: any[] = [];

  constructor(private auth: AuthService,
    private purchases: PurchasesService,
    private notifyService: NotificationService,
    private branchTransfer: BranchTransferService,
    private cdref: ChangeDetectorRef ,
    private dialog: MatDialog, private utils: UtilsService,
    public router: Router,
    private fb: FormBuilder) {
      this.today = new Date(); 
      this.user = this.auth.getCurrentUser()
      this.multiBranchUser = this.user.multiBranchUser;
      this.indentForm = this.fb.group({ 
        sourceBranch: [null, Validators.required],
        receipientBranch: [null, Validators.required],
        stockType: [null, Validators.required],
      });
      this.getBranches();
    }
  
  ngAfterContentChecked() {
    this.cdref.detectChanges();
  }

  ngOnInit() {

  }


  selectIndentArea(val) {
    this.indentPreview = false;
    this.selectedWorkArea = val.value;
    this.indentArea = val.value;
    this.workAreaSelected = true;
    this.getSpecialIndentData();
    this.showTable = true;
  }


  getSpecialIndentData() {
    let obj = {
      tenantId: this.user.tenantId,
      workArea: this.indentArea,
      restaurantId: this.indentForm.value.sourceBranch.restaurantIdOld,
      destinationId : this.indentForm.value.receipientBranch.restaurantIdOld,
      userEmail: this.user.email,
      uId: this.user.mId,
      specialFlag: this.specialFlag,
      reqQty: true
    }
    
    // obj['stockType'] = this.stockSeparation ? this.stockType : undefined ;
    obj['stockType'] = this.indentForm.value.stockType ;
    this.branchTransfer.getSpecialIndentData(obj).subscribe(data => {
      if (data) {
        if (!this.dataSource)
        this.dataSource = new MatTableDataSource<any>();

        this.initData = data.invItems;
        this.inventoryItems = data.invItems;
        this.inventoryItems.filter(item => {
          if (!this.router.url.includes(this.specialIndentUrl))
            return item.projectedSales > 0
          return true
        }).map((item: any) => {
          if (!item.hasOwnProperty('packageName')) {
            item.packageName = item.uom
            item.packageQty = 1
          }
          if (this.router.url.includes(this.specialIndentUrl))
            item.issueQty = 0
          else
            item.issueQty = item.projectedSales

          if (!this.router.url.includes(this.specialIndentUrl) && item.miq > item.issueQty)
            item.issueQty = item.miq

          if (!this.router.url.includes(this.specialIndentUrl) && item.issueQty > item.inStock) {
            item.issueQty = item.inStock
          }
          item.issueQty = this.utils.truncateNew(item.issueQty)
          return item
        });
        this.inventoryItems.forEach(item => {
          if (item.category == null) {
            item.category = 'N/A'
          }
          if (item.ItemType == null) {
            item.ItemType = 'N/A'
          }
          if (item.subCategory == null) {
            item.subCategory = 'N/A'
          }
          this.ItemTypeList.push(item.ItemType)
          this.categoryList.push(item.category)
          this.subCategoryList.push(item.subCategory)
        })
        this.categoryList = this.categoryList.filter((k, i, ar) => ar.indexOf(k) === i);
        this.ItemTypeList = this.ItemTypeList.filter((p, q, arrr) => arrr.indexOf(p) === q);
        this.subCategoryList = this.subCategoryList.filter((j, l, arr) => arr.indexOf(j) === l);
        this.initCategoryList = this.categoryList;
        this.initSubCategoryList = this.subCategoryList;
        this.displayedColumns = ['index', 'itemName', 'entryType', 'pkgName', 'unitPrice', 'inStock', 'issueQty','totalPrice'];
        // filter(item => Object.keys(item.workArea).includes(this.indentArea))
        this.dataSource.data = this.inventoryItems.map((item: any) => { item.issueQty = 0; return item })
        this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
        this.dataSource.paginator = this.paginator;
        this.curDataSource = this.dataSource.data
        this.buttonAccess = true;
      }
    },
      err => console.error(err))
  }


  displayPackages(data) {
    const dialogRef = this.dialog.open(PackageDialogComponent, {
      data: data
    });
    dialogRef.afterClosed().subscribe(
      data => {
        let dialogIssueQty = 0;
        data.packagingSizes.forEach((item) => {
          if (item.orderedPackages != undefined) {
            item.orderedPackages = parseInt(item.orderedPackages, 10)
            dialogIssueQty = dialogIssueQty + (item.orderedPackages * item.pkgQty)
          }

        });
        data.issueQty = dialogIssueQty;
        if (data.issueQty > data.inStock) {
          this.utils.snackBarShowError('Total Issue Qty(i.e \'' + data.issueQty + '\') is greater than instock value(i.e \'' + data.inStock + '\'). Issue Qty is set to inStock Value.')
          data.issueQty = data.inStock
        }
      });
  }

  issueIndent() {
    if (this.router.url.includes(this.specialIndentUrl) && this.indentArea == undefined) {
      this.utils.snackBarShowWarning("Please select an indent area");
    }
    else {
      let itemsToIssue = this.dataSource.data.filter(item => item.issueQty > 0)
      this.branchTransfer.issueIndent({
        invItems: itemsToIssue,
        uId: this.user.mId,
        tenantId: this.user.tenantId,
        indentArea: this.indentArea
      }).subscribe(data => {
        this.utils.openSnackBar('Indent Sent', null, 3000)
      }, err => console.error(err))
    }

  }

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }

  printToPdf() {
    let inventoryList = {};
    inventoryList['inventoryItems'] = [];
    this.dataSource.data.forEach(function (item) {
      if (item['issueQty'] > 0) {
        inventoryList['inventoryItems'].push(item);
      }
    });
    if (inventoryList['inventoryItems'].length == 0) {
      this.utils.snackBarShowWarning('please enter indent values');
      return;
    }
    inventoryList['user'] = this.user;
    inventoryList['recipientArea'] = this.indentArea
    this.purchases.printpdfs(inventoryList, 'Indent').subscribe(data => {
      // window.open('data:application/pdf;base64,' + data.eFile);
      this.purchases.globalPrintPdf(data.eFile);
    });
  }

  exportToExcel() {
    let inventoryList = {}
    inventoryList['indentArea'] = this.indentArea;
    inventoryList['inventoryItems'] = this.dataSource.data;
    inventoryList['user'] = this.user;
    this.purchases.exportToExcel(inventoryList, 'Indent').subscribe(data => {
      window.open('data:text/csv;base64,' + data.eFile);
    });
  }

  validateIssueQty(element) {
    element.inStock < element.issueQty ? element.issueQty = element.inStock : element;
  }

  filterByBranch(restId) {
    this.sourceBranch = restId.branchName
    this.restaurantId = restId.restaurantIdOld;
    this.branchSelected = true;
    // this.user.restaurantAccess.forEach(element => {
    //   if (element.restaurantIdOld == this.restaurantId) {
    //     this.IndentAreas = element.workAreas;
    //   }
    // });
    this.category.setValue('');
    this.getUsers() ;
    this.appCat = undefined ;
    this.stockType = undefined ;
    this.currentWorkArea = undefined ;
    this.enableReceipientBranch = true;
    this.receipientBranch = ''
  }

  filterByReceipientBranch(value){
    this.sourceReceipient = value.branchName;

    this.receipientRestaurantId = value.restaurantIdOld
    this.user.restaurantAccess.forEach(element => {
      if (element.restaurantIdOld == value.restaurantIdOld) {
        this.IndentAreas = element.workAreas;
        this.workAreaBank = this.IndentAreas;
        this.workAreasBanks.next(this.workAreaBank.slice());
        this.workAreaFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
            this.workAreafilterBanks();
        });
      }
    });
    this.indentForm.get('stockType').setValue("Stockable")
    this.specialFlag = true;
    this.enableWorkArea = true;
    this.currentWorkArea = ''
  }

  getBranches() {
    this.auth.getBranches({
        tenantId: this.user.tenantId,
      }).subscribe(
        (data: any) => {
          if (data) this.branches = data;
          this.intialiseFilters();
        },
        (err) => console.error(err)
      );
  }

  private intialiseFilters() {
    this.fromBrachFilterOptions = this.indentForm
      .get("sourceBranch")
      .valueChanges.pipe(
        startWith(""),
        map((value) => this._filter(value, this.branches, "branchName"))
      );

    this.toBrachFilterOptions = this.indentForm.get("receipientBranch").valueChanges.pipe(
      startWith(""),
      map((value) => this._filter(value, this.branches, "branchName"))
    );
  }

  private _filter(value: string, arr: any[], key: string): any[] {
    const filterValue = typeof value === 'string' ? value.toLowerCase() : '';
    return arr.filter((option) => 
      new RegExp(`${filterValue}`, "gi").test(option[key])
    );
  }


  allFilter() {
    let tmp = this.curDataSource
    let prev = this.curDataSource
    Object.keys(this.filterKeys).forEach(element => {
      if (this.filterKeys[element] != 'All') {
        tmp = prev.filter(item =>
          item[element].toLowerCase() === this.filterKeys[element].toLowerCase())
        prev = tmp
      }
    });
    this.dataSource.data = tmp
  }

  selectItemType(itemType) {
    let filteredCategoryList = []
    let filteredSubCategoryList = []
    if (itemType != 'All') {
      let filteredItem = this.curDataSource.filter(item => item['ItemType'].toUpperCase() === itemType.toUpperCase());
      filteredItem.forEach(element => {
        filteredCategoryList.push(element.category)
        filteredSubCategoryList.push(element.subCategory)
      });
      this.categoryList = filteredCategoryList.filter((k, i, ar) => ar.indexOf(k) === i);
      this.subCategoryList = filteredSubCategoryList.filter((k, i, ar) => ar.indexOf(k) === i);
      this.categoryList.splice(0, 0, 'All');
      this.subCategoryList.splice(0, 0, 'All');
      this.subCatList = this.subCategoryList
    }
    else {
      this.categoryList = this.initCategoryList;
      this.subCategoryList = this.initSubCategoryList;
    }
    this.filterKeys = { ItemType: itemType, category: 'All', subCategory: 'All' }
    this.allFilter()
  }

  selectCategory(cat) {
    let filteredSubCategoryList = []
    let filteredItem = []
    if (cat != 'All') {
      if (this.filterKeys.ItemType != 'All') {
        filteredItem = this.curDataSource.filter(item => item['category'].toUpperCase() === cat.toUpperCase()
          && item['ItemType'].toUpperCase() === this.filterKeys.ItemType.toUpperCase());
      }
      else {
        filteredItem = this.curDataSource.filter(item => item['category'].toUpperCase() === cat.toUpperCase());
      }
      filteredItem.forEach(element => {
        filteredSubCategoryList.push(element.subCategory)
      });
      this.subCategoryList = filteredSubCategoryList.filter((k, i, ar) => ar.indexOf(k) === i);
      this.subCategoryList.splice(0, 0, 'All')
    }
    else if (this.filterKeys.ItemType != 'All') {
      this.subCategoryList = this.subCatList
    }
    else {
      this.subCategoryList = this.initSubCategoryList
    }
    this.filterKeys.category = cat;
    this.filterKeys.subCategory = 'All'
    this.allFilter()
  }

  selectSubCat(subCat) {
    this.filterKeys.subCategory = subCat;
    this.allFilter()
  }

  saveItems() {
    let itemsToSave = this.inventoryItems.filter(item => item.issueQty > 0)
    this.branchTransfer.saveIndentReq({
      invItems: itemsToSave,
      userEmail: this.user.email,
      tenantId: this.user.tenantId,
      indentArea: this.indentArea,
      restaurantId: this.restaurantId
    }).subscribe(data => {
      this.savedItems = [data['savedItems']]
      this.utils.openSnackBar('Items Saved', null, 3000)
    }, err => console.error(err))
  }

  issueIndentReq() {
    let faultItemCount = 0
    //  && Object.keys(item.workArea).includes(this.indentArea)
    let itemsToIssue = this.inventoryItems.filter(item => item.issueQty > 0)
    itemsToIssue.forEach(element => {
      if (element.issueQty > element.inStock) {
        faultItemCount = faultItemCount + 1
      }
    });
    if (faultItemCount > 0) {
      this.dialog.open(SimpleDialogComponent, {
        data: {
          title: 'Indent Alert',
          msg: 'There are ' + faultItemCount + ' items with indent Qty greater than inStore Qty. Are you sure you want to raise Indent?',
          ok: function () {
            this.raiseIndent(itemsToIssue)
          }.bind(this)
        }
      });
    }
    else {
      this.raiseIndent(itemsToIssue)
    }
  }

  raiseIndent(itemsToIssue: any[]) {
    for (let i = 0; i < itemsToIssue.length; i++) {
      itemsToIssue[i].issueQty = this.utils.truncateNew(itemsToIssue[i].issueQty)
    }
    if (itemsToIssue.length > 0) {
      let obj = {
        invItems: itemsToIssue,
        userEmail: this.user.email,
        tenantId: this.user.tenantId,
        indentArea: this.indentArea,
        indentDocumentDate: this.utils.dateCorrection(this.today),
        restaurantId: this.restaurantId
      }
      if (this.roles.length > 0){
        const uniqueItemCategories = [...new Set(itemsToIssue.map(obj => obj.category))] ;
        const getAppCatRoles = (data, appCat) => {
          const filteredObj = data.find(obj => obj.appCat === appCat);
          return filteredObj ? filteredObj.roles.map(role => role.value) : [];
        };
        let rolesArray = [],appCat;
        if (uniqueItemCategories.length > 1){
          appCat = "DEFAULT";
          rolesArray = getAppCatRoles(this.roles, appCat);
        } else if (uniqueItemCategories.length === 1) {
          appCat = uniqueItemCategories[0]
          rolesArray = getAppCatRoles(this.roles, appCat);
          if (rolesArray.length == 0){
            appCat = "DEFAULT";
            rolesArray = getAppCatRoles(this.roles, appCat);  
          }
        } else {
          rolesArray = [] ;
          appCat = undefined ;
        }
        this.selectedRole = rolesArray ; 
        this.appCat = appCat
      }

      if(this.selectedRole && this.selectedRole.length > 0){
        obj['role'] = this.selectedRole ;
      } else {
        obj['role'] = [];
      }  
      obj['approvalCategory'] = this.appCat ; 
      obj['userRole'] = this.user.role ; 
      obj['user'] = this.user ; 
      obj['baseUrl'] = environment.baseUrl; 
      this.branchTransfer.issueIndentReq(obj).subscribe(data => {
        if (data.hasOwnProperty('updatedIndent')) {
          this.savedItems = [data.updatedIndent]
        }
        this.utils.openSnackBar('Indent Sent', null, 3000)
        // .filter(item =>  Object.keys(item.workArea).includes(this.indentArea))
        this.dataSource.data = this.inventoryItems.map(
            (item: any) => { item.issueQty = 0; return item }
          )
      }, err => console.error(err))
    }
    else {
      this.utils.snackBarShowWarning('No Items to issue. Please add Item');
    }
  }

  validateAndDcrsReqQty(element) {
    element.issueQty = element.issueQty - 1
    if (element.issueQty < 0)
      element.issueQty = 0
    this.getTotalIndentCost(null ,element)
  }
  validateAndIncrsReqQty(element) {
    element.issueQty = element.issueQty + 1
    this.getTotalIndentCost(null,element)
  }

  clear() {
    this.category.setValue('')
    this.Subcategory.setValue('')
    this.ItemType.setValue('')
    this.searchText = ''
    // .filter(item => Object.keys(item.workArea).includes(this.indentArea))
    this.dataSource.data = this.inventoryItems
    this.doFilter(this.searchText)
  }

  preview() {
    if (this.indentPreview == true) {
      this.curDataSource = this.dataSource.data
      //  && Object.keys(item.workArea).includes(this.indentArea)
      this.dataSource.data = this.inventoryItems.filter(item => item.issueQty > 0)
    }
    else {
      this.dataSource.data = this.curDataSource
    }
  }

  clearSearchText() {
    this.searchText = ''
    this.doFilter(this.searchText)
  }

  getTotalIndentCost(event , element) {
    let sum = 0;
    this.dataSource.data.forEach(element => {
      if (element.issueQty > 0 && element.price > 0) {
        sum = sum + element.price * element.issueQty
      }
    });
    this.totalIndentCost = sum
  }

  getUsers() {
  	let inputData: any = {
  		tenantId: this.auth.getCurrentUser().tenantId,
  		restaurantId: this.restaurantId,
  		type: "indentApproval"
  	}
  	this.purchases.getSelectedUsers(inputData).subscribe(data => {
    this.stockSeparation = data.TenantDetails[0].permission.stockSeparation ?  data.TenantDetails[0].permission.stockSeparation : false   ;
  		let role = []
  		data.data.forEach(function(element) {
  			role.push(element.role)
  		});
  		this.totalRoles = data.data;
  		this.roles = this.totalRoles;
  		if (this.roles.length === 0){
  		  this.allowOrder = true;
  		}
  		if (this.roles.length > 0){
  		  this.allowOrder = false;
  		}
      this.allowOrder = true;
  	})
  }

  stockChange(event){
    this.dataSource = new MatTableDataSource() ;
    this.currentWorkArea = undefined ;
  }

  filterWorkArea() {
    this.IndentAreas = this.IndentAreas.filter(
      (wa) =>
        wa.toLowerCase().includes(this.searchWorkAreaText.toLowerCase())
    );
  }

  numberOnly(event): boolean {
    let value = event.target.value;
    if (this.specialKeys.indexOf(event.key) !== -1) {
      return;
    }
     let current = value;
      const position = event.target.selectionStart;
      const next = [current.slice(0, position), event.key == 'Decimal' ? '.' : event.key, current.slice(position)].join('');
      if (next && !(next).match(this.regex)) {
       event.preventDefault();
      }
  }

  protected workAreafilterBanks() {
    if (!this.workAreaBank) {
      return;
    }
    let search = this.workAreaFilterCtrl.value;
    if (!search) {
      this.workAreasBanks.next(this.workAreaBank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.workAreasBanks.next(
      this.workAreaBank.filter(workAreaBank => workAreaBank.toLowerCase().indexOf(search) > -1)
    );
  }

  focusFunctionWithOutForm(element , value){
    if(Number(element[value]) === 0){
      element[value] = null;
    }
  }
  
  focusOutFunctionWithOutForm(element , value){
    if(element[value] === null){
      element[value] = 0
    }
  }
}

