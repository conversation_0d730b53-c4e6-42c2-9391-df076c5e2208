<div class="row">
  <div class="col-7">
    <mat-card appearance="outlined">
      <mat-card-header>
        <mat-card-subtitle>
             <mat-card-title > Master ID Setup </mat-card-title>
        </mat-card-subtitle>
       
      </mat-card-header>
      <mat-card-content class="mat-card-content">
        <mat-tab-group>
          <mat-tab label="PO Access"> 
            <form (ngSubmit)="submitPermission()" [formGroup]="poAccessForm" class="my-4 mx-auto" style="max-width: 500px;">
              <div class="mt-5">
                <div>
                  <div class="d-flex align-item-center mb-2">
                    <div class="radioHeading">Edit Roles</div>
                    <mat-radio-group [(ngModel)]="selectedEditPOStatus" formControlName="poEditAccess" (change)="onPOEditChange($event)">
                      <mat-radio-button [value]=true>Yes</mat-radio-button>
                      <mat-radio-button [value]=false>No</mat-radio-button>
                    </mat-radio-group>
                  </div>
                  <mat-form-field appearance="outline" style="width: 100% !important; margin-bottom: 25px;">
                    <mat-label>Edit Roles</mat-label>
                    <mat-select formControlName="editPORoles" multiple (ngModelChange)="validateSales()" [disabled]="!selectedEditPOStatus">
                      <mat-option>
                        <ngx-mat-select-search placeholderLabel="search roles" noEntriesFoundLabel="'no roles found'"
                          [formControl]="editPORoleCtrl">
                        </ngx-mat-select-search>
                      </mat-option>
                      <mat-option *ngFor="let option of editPORoleBanks | async" [value]="option" >
                        <!-- [disabled]="option === 'superadmin'" -->
                        {{option | titlecase}}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>

                <div>
                  <div class="d-flex align-item-center mb-2">
                    <div class="radioHeading">Delete Roles</div>
                    <mat-radio-group  [(ngModel)]="selectedDeletePOAccess" formControlName="poDeleteAccess" (change)="onPODeleteChange($event)">
                      <mat-radio-button  [value]=true>Yes</mat-radio-button>
                      <mat-radio-button [value]=false>No</mat-radio-button>
                    </mat-radio-group>
                  </div>
                  <mat-form-field appearance="outline" style="width: 100% !important;">
                    <mat-label>Delete Roles</mat-label>
                    <mat-select formControlName="deletePORoles" multiple (ngModelChange)="validateSales()" [disabled]="!selectedDeletePOAccess">
                      <mat-option>
                        <ngx-mat-select-search placeholderLabel="search roles" noEntriesFoundLabel="'no roles found'"
                          [formControl]="deletePORoleCtrl">
                        </ngx-mat-select-search>
                      </mat-option>
                      <mat-option *ngFor="let option of deletePORoleBanks | async" [value]="option" >
                        <!-- [disabled]="option === 'superadmin'" -->
                        {{option | titlecase}}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>
              </div>
              <div class="d-flex justify-content-end mt-2">
                <button mat-button type="submit" class="button3 mb-3">Submit</button>
              </div>
          </form>
          </mat-tab>

          <mat-tab label="GRN Access"> 
            <form (ngSubmit)="submitPermission()" [formGroup]="grnAccessForm" class="my-4 mx-auto" style="max-width: 500px;">
              <div class="mt-5">
                <div>
                  <div class="d-flex align-item-center mb-2">
                    <div class="radioHeading">Edit Roles</div>
                    <mat-radio-group  [(ngModel)]="selectedEditGRNStatus" formControlName="grnEditAccess" (change)="onGRNEditChange($event)">
                      <mat-radio-button  [value]=true>Yes</mat-radio-button>
                      <mat-radio-button [value]=false>No</mat-radio-button>
                    </mat-radio-group>
                  </div>
                  <mat-form-field appearance="outline" style="width: 100% !important; margin-bottom: 25px;">
                    <mat-label>Edit Roles</mat-label>
                    <mat-select formControlName="editGRNRoles" multiple (ngModelChange)="validateSales()" [disabled]="!selectedEditGRNStatus">
                      <mat-option>
                        <ngx-mat-select-search placeholderLabel="search roles" noEntriesFoundLabel="'no roles found'"
                          [formControl]="editGRNRoleCtrl">
                        </ngx-mat-select-search>
                      </mat-option>
                      <mat-option *ngFor="let option of editGRNRoleBanks | async" [value]="option" >
                        <!-- [disabled]="option === 'superadmin'" -->
                        {{option | titlecase}}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>

                <div>
                  <div class="d-flex align-item-center mb-2">
                    <div class="radioHeading">Delete Roles</div>
                    <mat-radio-group  [(ngModel)]="selectedDeleteGrnAccess" formControlName="grnDeleteAccess" (change)="onGRNDeleteChange($event)">
                      <mat-radio-button  [value]=true>Yes</mat-radio-button>
                      <mat-radio-button [value]=false>No</mat-radio-button>
                    </mat-radio-group>
                  </div>
                  <mat-form-field appearance="outline" style="width: 100% !important;">
                    <mat-label>Delete Roles</mat-label>
                    <mat-select formControlName="deleteGRNRoles" multiple (ngModelChange)="validateSales()" [disabled]="!selectedDeleteGrnAccess">
                      <mat-option>
                        <ngx-mat-select-search placeholderLabel="search roles" noEntriesFoundLabel="'no roles found'"
                          [formControl]="deleteGRNRoleCtrl">
                        </ngx-mat-select-search>
                      </mat-option>
                      <mat-option *ngFor="let option of deleteGRNRoleBanks | async" [value]="option" >
                        <!-- [disabled]="option === 'superadmin'" -->
                        {{option | titlecase}}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </div>
              </div>
              <div class="d-flex justify-content-end mt-2">
                <button mat-button type="submit" class="button3 mb-3">Submit</button>
              </div>
          </form>
          </mat-tab>
        </mat-tab-group>

      </mat-card-content>
    </mat-card>
  </div>

  <div class="col-5">
    <mat-card>
      <mat-card-header>
        <mat-card-subtitle>
             <mat-card-title > Profile </mat-card-title>
        </mat-card-subtitle>
       
      </mat-card-header>
      <div class="content">
        <mat-list>
          <mat-list-item>
            <div class="list-item-content">
              <mat-icon>person</mat-icon>
              <div class="content-class">
                <div class="bold">Name : </div>
                <div>{{user.name}}</div>
              </div>
            </div>
          </mat-list-item>
          <mat-list-item>
            <div class="list-item-content">
              <mat-icon>bookmark</mat-icon>
              <div class="content-class">
                <div class="bold">Role : </div>
                <div>{{user.role}}</div>
              </div>
            </div>
          </mat-list-item>
          <mat-list-item>
            <div class="list-item-content">
              <mat-icon>alternate_email</mat-icon>
              <div class="content-class">
                <div class="bold">Email : </div>
                <div>{{user.email}}</div>
              </div>
            </div>
          </mat-list-item>
        </mat-list>
      </div>
    </mat-card>
  </div>
</div>

