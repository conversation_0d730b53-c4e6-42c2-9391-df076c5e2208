import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { MasterdataupdateService } from '../_services/masterdataupdate.service';
import { AuthService } from '../_services';
import { FormControl, FormGroup, FormBuilder, Validators  } from '@angular/forms';
import { Router } from '@angular/router';
import { UtilsService } from '../_utils/utils.service';
import { GlobalsService } from "../_services/globals.service";
import { ReplaySubject, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-create-user-control',
  templateUrl: './create-user-control.component.html',
  styleUrls: ['./create-user-control.component.scss']
})
export class CreateUserControlComponent implements OnInit {
  user: any;
  roles: any;
  userAccess = new FormControl();
  selectedSettingRoles: any[] = [];
  accessForm: FormGroup;
  poAccessForm: FormGroup;
  grnAccessForm: FormGroup;
  access: any;

  selectedEditPOStatus : boolean = true;
  selectedDeletePOAccess : boolean = true;
  selectedEditGRNStatus : boolean = true;
  selectedDeleteGrnAccess : boolean = true;


  public editPORoleBanks: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public editPORole: any[] = [];
  public editPORoleCtrl: FormControl = new FormControl();
  
  public deletePORoleBanks: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public deletePORole: any[] = [];
  public deletePORoleCtrl: FormControl = new FormControl();

  public editGRNRoleBanks: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public editGRNRole: any[] = [];
  public editGRNRoleCtrl: FormControl = new FormControl();
  
  public deleteGRNRoleBanks: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public deleteGRNRole: any[] = [];
  public deleteGRNRoleCtrl: FormControl = new FormControl();

  protected _onDestroy = new Subject<void>();

  constructor(
    private masterDataService: MasterdataupdateService,
    private auth: AuthService,
    private cd: ChangeDetectorRef,
    private router: Router,
    private fb: FormBuilder,
    private utils: UtilsService,
  ) {
    this.user = this.auth.getCurrentUser();
    this.access = sessionStorage.getItem('access');
    // this.access = JSON.parse(sessionStorage.getItem('access'));
    this.poAccessForm = this.fb.group({
      editPORoles: [''],
      deletePORoles: [''],
      poEditAccess: true,
      poDeleteAccess: true,
    });

    this.grnAccessForm = this.fb.group({
      editGRNRoles: [''],
      deleteGRNRoles: [''],
      grnEditAccess: true,
      grnDeleteAccess: true,
    });

    this.getRoles();
    if(this.access && this.access.settings){
      this.selectedSettingRoles.push(...this.access.settings)
      this.accessForm.get('selectedSettingRoles').setValue(this.selectedSettingRoles)
    }

   }

  ngOnInit() {

  }

  getRoles(){
    let inputObj = {}
    inputObj['tenantId'] = this.user.tenantId
    this.masterDataService.getRoles(inputObj).subscribe({
      next: (res) => {
        if (res['success'] == true) {
          this.roles = res['roles']
          this.editPORole = this.roles 
          this.editPORoleBanks.next(this.editPORole.slice());
          this.editPORoleCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
              this.searchRoles(this.editPORole , this.editPORoleCtrl,this.editPORoleBanks);
          });

          this.deletePORole = this.roles 
          this.deletePORoleBanks.next(this.deletePORole.slice());
          this.deletePORoleCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
              this.searchRoles(this.deletePORole , this.deletePORoleCtrl,this.deletePORoleBanks);
          });

          this.editGRNRole = this.roles 
          this.editGRNRoleBanks.next(this.editGRNRole.slice());
          this.editGRNRoleCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
              this.searchRoles(this.editGRNRole , this.editGRNRoleCtrl,this.editGRNRoleBanks);
          });

          this.deleteGRNRole = this.roles 
          this.deleteGRNRoleBanks.next(this.deleteGRNRole.slice());
          this.deleteGRNRoleCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
              this.searchRoles(this.deleteGRNRole , this.deleteGRNRoleCtrl,this.deleteGRNRoleBanks);
          });

          // let superAdminIndex = this.roles.indexOf("superadmin");
          // if (superAdminIndex !== -1) {
          //   this.roles.unshift(this.roles.splice(superAdminIndex, 1)[0]);
          // }
          // this.selectedSettingRoles.push(this.roles[0])
          // this.accessForm.get('selectedSettingRoles').setValue(this.selectedSettingRoles)
          this.getAccess()
        }
      },
      error: (err) => {
        console.log(err);
      },
    });
}

protected searchRoles(value ,  ctrl , banks) {
  if (!value) {
    return;
  }
  let search = ctrl;
  if (!search) {
    banks.next(value.slice());
    return;
  } else {
    search = search;
  }
  banks.next(
    value.filter(VendorBank => VendorBank.indexOf(search) > -1)
  );
}

submitPermission(){
  console.log();
  console.log();
  
  // let settingRole = []
  // settingRole = this.accessForm.value.selectedSettingRoles;
  // settingRole.push('superAdmin')
  let obj = {}
  // let access = {
  //   settings :  Array.from(new Set(settingRole)) ,
  // }

  let poAccess = {
    'edit' : this.poAccessForm.value.poEditAccess,
    'delete' : this.poAccessForm.value.poDeleteAccess,
    'deleteAccess' : this.poAccessForm.value.deletePORoles,
    'editAccess' : this.poAccessForm.value.editPORoles,
  }

  let grnAccess = {
    'edit' : this.grnAccessForm.value.grnEditAccess,
    'delete' : this.grnAccessForm.value.grnDeleteAccess,
    'deleteAccess' : this.grnAccessForm.value.deleteGRNRoles,
    'editAccess' : this.grnAccessForm.value.editGRNRoles,
  }

  obj['tenantId'] = this.user.tenantId;
  obj['POAccess'] = poAccess;
  obj['grnAccess'] = grnAccess;
  // obj['access'] = access
  this.masterDataService.masterDataIdAccess(obj).subscribe({
    next: (res) => {
      if (res['success']) {
        this.utils.snackBarShowSuccess('Updated successfully')
        this.getRoles();
        this.getAccess();
      } else {
        this.utils.snackBarShowError('Something went wrong!');
      }
    },
    error: (err) => {
      console.log(err);
    },
  });
}

getAccess(){
  this.auth.getRolesList({ tenantId: this.user.tenantId }).subscribe((data) => {
    if ('access' in data){
      sessionStorage.setItem(GlobalsService.accessData, JSON.stringify(data['access']));
      sessionStorage.setItem('access', data['access'].settings);
      this.access = data['access'];
      if(this.access.settings){
        this.selectedSettingRoles.push(...this.access.settings)
        const uniqueSettings = new Set(this.selectedSettingRoles);
        this.selectedSettingRoles = Array.from(uniqueSettings);
        // this.accessForm.get('selectedSettingRoles').setValue(this.selectedSettingRoles)
      }
    }
  })
}

validateSales() {
  // this.profitTarget < 1 ? this.profitTarget = 1 : undefined;
  // this.cd.detectChanges();
}

onPOEditChange(e){
  this.poAccessForm.get('editPORoles').setValue([])
}

onPODeleteChange(e){
  this.poAccessForm.get('deletePORoles').setValue([])
}

onGRNEditChange(e){
  this.grnAccessForm.get('editGRNRoles').setValue([])
}

onGRNDeleteChange(e){
  this.grnAccessForm.get('deleteGRNRoles').setValue([])
}

}