
<!-- <div class="topItems" *ngIf="!this.showCreateQr"> -->
<div class="title">
  <!-- <mat-form-field appearance="none" class="qrBranchSelection" *ngIf="multiBranchUser">
    <label>Select Branch</label>
    <mat-select placeholder="Select Branch" class="outline" (selectionChange)="filterByBranch($event.value)">
      <mat-option *ngFor="let rest of this.user.restaurantAccess" [value]="rest.restaurantIdOld">
        {{ rest.branchName }}
      </mat-option>
    </mat-select>
  </mat-form-field> -->
  
  <button mat-button *ngIf="!this.showCreateQr" class="QrButton button3" (click)="showCreateQrGenerator()">
    Create QR
  </button>
</div>

<mat-card *ngIf="!this.showCreateQr">

  <mat-form-field appearance="none">
    <!-- <label>Search</label> -->
    <input matInput type="text" class="outline"  placeholder="Search" [(ngModel)]='searchText' (keyup)="applyFilter($event)"/>
    <mat-icon matSuffix (click)="clear()" class="closebtn">close</mat-icon>
  </mat-form-field>

  <button mat-button class="refButton buttonForRefresh" (click)="fetchQrList()">Refresh</button>

  <table  #table mat-table matSort [dataSource]="dataSource">
    <ng-container matColumnDef="id">
      <th mat-header-cell *matHeaderCellDef> ID </th>
      <td mat-cell *matCellDef="let element">{{element.qrGeneratorId }} </td>
    </ng-container>

    <ng-container matColumnDef="createTs">
      <th mat-header-cell *matHeaderCellDef>Created Date</th>
      <td mat-cell *matCellDef="let element"> {{element.createdAt | date:'MMM d, h:mm:ss a'}} </td>
    </ng-container>

    <ng-container matColumnDef="status">
      <th mat-header-cell *matHeaderCellDef>Created Date</th>
      <td mat-cell *matCellDef="let element"> {{element.status}} </td>
    </ng-container>

    <ng-container matColumnDef="download">
      <th mat-header-cell *matHeaderCellDef>Download</th>
      <td mat-cell *matCellDef="let element"> 
        <span (click)="downloadQr(element)" matTooltip="click to download">
          <mat-icon>file_download</mat-icon>
        </span>
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
  </table>
  <div class="dataMessage" *ngIf="dataSource.data.length == 0"> No Data Available </div>
  <mat-paginator [showTotalPages]="5" [pageSize]="10" [pageSizeOptions]="pageSizes"></mat-paginator>
</mat-card>

<mat-card *ngIf="showCreateQr">

  <button mat-icon-button class="plCloseBtn" matTooltip="go back"
    style=" float: right; margin-right: -10px; margin-top: 5px;" (click)="goBack()">
    <mat-icon>close</mat-icon>
  </button>
  <br><br>

  <div class="container">
  <form [formGroup]="crmForm" style="display: grid;">

        <mat-form-field appearance="outline" style="width: 100% !important;">
        <mat-label>Floor ID</mat-label>
          <input matInput type="number" style="color: white;" class="outlineSearch" formControlName="floorid"
            placeholder="Enter Floor ID" />
        </mat-form-field>

        <mat-form-field appearance="outline" style="width: 100% !important;">
        <mat-label>Label</mat-label>
          <input matInput type="text" style="color: white;" class="outlineSearch" formControlName="label"
            placeholder="Enter Label" />
        </mat-form-field>

        <mat-form-field appearance="outline" style="width: 100% !important;">
        <mat-label>Table</mat-label>
          <input matInput type="text" style="color: white;" class="outlineSearch" formControlName="table"
            placeholder="Enter Table" />
        </mat-form-field>

        <mat-form-field appearance="outline" style="width: 100% !important;">
        <mat-label class="mr-1">Base url</mat-label>
          <input matInput type="text" style="color: white;" class="outlineSearch" formControlName="baseurl"
            placeholder="Enter Url" />
        </mat-form-field>

        <mat-form-field appearance="outline" style="width: 100% !important;">
        <mat-label>QR Dimension </mat-label>
          <input matInput type="number" style="color: white;" class="outlineSearch" formControlName="qrDimension"
            placeholder="QR Dimension" />
            <div class="cm">cm</div>
        </mat-form-field>

        <mat-form-field appearance="outline" style="width: 100% !important;">
        <mat-label>Overall Dimension </mat-label>
          <mat-select placeholder="Select Dimension"
          [(ngModel)]="selectedOverallDim" [ngModelOptions]="{standalone: true}" formControlName="selectedOverallDim">
            <mat-option *ngFor="let size of this.overallDimension" [value]="size">
              {{ size }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline" style="width: 100% !important;">
        <mat-label class="slug">Slug</mat-label>
          <input matInput type="text" style="color: white;" class="outlineSearch" formControlName="slug"
            placeholder="Enter Slug" />
        </mat-form-field>

    <div class="qr-button">
      <button mat-button class="button3" [disabled]="!crmForm.valid" (click)="generateQr()">Generate QR</button>
    </div>
  </form>
</div>

</mat-card> 