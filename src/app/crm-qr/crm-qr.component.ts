import { Component, OnInit ,ViewChild } from '@angular/core';
import { FormBuilder, FormGroup,Validators } from '@angular/forms';
import { MatPaginator, MatTableDataSource } from '@angular/material';
import { MasterdataupdateService } from '../_services/masterdataupdate.service';
import { NotificationService } from '../_services/notification.service';
import { AuthService } from '../_services/auth.service';
import { UtilsService } from '../_utils/utils.service';

@Component({
  selector: 'app-crm-qr',
  templateUrl: './crm-qr.component.html',
  styleUrls: ['./crm-qr.component.scss', './../../common-dark.scss']
})
export class CrmQrComponent implements OnInit {
  showCreateQr : boolean = false;
  displayedColumns: string[];
  selectedOverallDim:any;
  dataSource = new MatTableDataSource();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  user: any;
  public crmForm: FormGroup;
  multiBranchUser: boolean;
  restaurantId: any;
  overallDimension: any;
  searchText: any;
  pageSizes: any;

  constructor(
    public fb: FormBuilder,
    private auth: AuthService,
    private masterDataService: MasterdataupdateService,
    private notifyService: NotificationService,
    private utils: UtilsService
  ) { 
    this.user = this.auth.getCurrentUser(); 
    this.multiBranchUser = this.user.multiBranchUser;
    this.crmForm = fb.group({
      floorid: ['', Validators.required],
      label: ['', Validators.required],
      table: ['', Validators.required],
      baseurl: ['', Validators.required],
      qrDimension: ['', Validators.required],
      selectedOverallDim:['', Validators.required],
      slug: ['', Validators.required],
    });
    this.overallDimension = [
      "A0","A1","A2","A3","A4","A5","A6","A7","A8"
    ]
  }

  ngOnInit() {
    this.displayedColumns = ['id','createTs','status','download'];
    if (!this.user.multiBranchUser) {
      this.restaurantId = this.user.restaurantAccess[0].restaurantIdOld;
      this.fetchQrList()
    }
    this.fetchQrList();
  }


  generateQr(){
    let inputObj = {}
     inputObj['tenantId'] = this.user.tenantId;
     inputObj['restaurantId'] = this.user.restaurantId;
     inputObj['floorID'] = this.crmForm.value.floorid;
     inputObj["label"] = this.crmForm.value.label;
     inputObj['tables'] = (this.crmForm.value.table).split(',');
     inputObj['baseUrl'] = this.crmForm.value.baseurl;
     inputObj['qrCodeDimension'] = this.utils.truncateNew(this.crmForm.value.qrDimension * 37.7952755906 );
     inputObj['overallDimension'] = this.selectedOverallDim;
     inputObj['slug'] = this.crmForm.value.slug
     this.masterDataService.generateQr(inputObj).subscribe((response: any) => {
      if (response.success) {
        this.utils.snackBarShowSuccess(response.message);
        this.fetchQrList();
      }else{
        this.utils.snackBarShowError(response.message);
      }
    }); 
    this.crmForm.reset();
  }

  fetchQrList() {
    let obj = this.user;
    this.masterDataService.fetchQrList(obj).subscribe((response: any) => {
      if (response.success) {
        this.dataSource.data= response.data
        this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
        this.dataSource.paginator = this.paginator;
      }
    });
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  downloadQr(eleObj) {
    let obj = {};
    obj['tenantId'] = this.user.tenantId;
    obj['restaurantId'] = this.user.restaurantId;
    obj["qrGeneratorId"]= eleObj["qrGeneratorId"]
    this.masterDataService.downloadQr(obj).subscribe((response: any) => {
      if (response.success) {
        var downloadLink = document.createElement("a");
        downloadLink.href = 'data:application/txt;base64,' + response.eFile;
        downloadLink.download = eleObj["qrGeneratorId"]+".pdf";
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
      }else{
        this.utils.snackBarShowError(response.message);
      }
    });
  }  


// filterByBranch(val){

// }

clear(){
this.searchText = '';
}

showCreateQrGenerator(){
 
 if(this.showCreateQr == false){
  this.showCreateQr = true;
 }else{
  this.showCreateQr = false;
 }
}

goBack(){
  this.showCreateQr = false;
  this.crmForm.reset();
}

}


