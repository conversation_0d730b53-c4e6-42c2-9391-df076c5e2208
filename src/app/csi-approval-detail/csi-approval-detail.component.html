<div class="title">
  <button mat-raised-button class="button" style="float: left; margin-left: 0px;" (click)="goBack()">
    <mat-icon>keyboard_backspace</mat-icon> Back To List
  </button>

  <div>
    <button mat-button mat-raised-button id="save-btn" class="button3" style="float: right;"
      [matTooltip]="reason" matTooltipPosition="left" (click)="rejectIndent()" [disabled] = "!showButton || isDone">
      Reject
    </button>
    <button mat-button mat-raised-button id="save-btn" class="button3" style="float: right;"
      [matTooltip]="reason" matTooltipPosition="left" (click)="checkApproval()" [disabled] = "!showButton || isDone">
      Approve
    </button>
    <!-- [matTooltip]="reason" matTooltipPosition="left" (click)="approveIndent()" [disabled] = "!showButton || isDone"> -->

    <!-- <button mat-button mat-raised-button id="save-btn" class="button" style="float: right;" (click)="printpdf()">
      Print
    </button>
    <button mat-button mat-raised-button id="save-btn" class="button" style="float: right;" (click)="exportToExcel()">
      Export
    </button> -->
  </div>
</div>

<div class="search-table-input fieldcontainer">
    <div class="row">
      <div class="col">
        <table class="table">
          <tbody>
            <tr>
              <th class="topItemkey" scope="row">Indent Id</th>
              <td>{{ indentData.ibtId }}</td>
            </tr>
            <tr>
              <th class="topItemkey" scope="row">Created By</th>
              <td>{{ indentData.creator }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="col">
        <table class="table">
          <tbody>
            <tr>
              <th class="topItemkey" scope="row">From Branch</th>
              <td>{{ indentData.fromBranch.location }}</td>
            </tr>
            <tr>
              <th class="topItemkey" scope="row">To Branch</th>
              <td>{{ indentData.toBranch.location }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="col">
        <table class="table">
          <tbody>
            <tr>
              <th class="topItemkey" scope="row">WorkArea</th>
              <td>{{ indentData.workArea }}</td>
            </tr>
            <tr>
              <th class="topItemkey" scope="row">Created Date</th>
              <td>{{ this.utils.formatDateToUTC(indentData.createTs) }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="col">
        <table class="table">
          <tbody>
            <tr>
              <th class="topItemkey" scope="row">Status</th>
              <td>{{ indentData.status.orderStatus }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
</div>
<mat-card class="matcontent">
  <mat-card-content>
    <table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort>
      <ng-container matColumnDef="index">
        <th mat-header-cell *matHeaderCellDef class="tableId"><b>#</b></th>
        <td mat-cell *matCellDef="let element; let i = index" class="name-cell tableId">
          {{ i + 1 }}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="actionBtns" >
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Actions</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <button mat-icon-button (click)="r(element)">
            <mat-icon>done</mat-icon>
          </button>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="itemName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Inventory Item</b>
        </th>
        <td mat-cell *matCellDef="let element">
          {{ element.itemName | titlecase }}
        </td>
        <td mat-footer-cell *matFooterCellDef>Total</td>
      </ng-container>

      <ng-container matColumnDef="pkgName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Pkg Name</b></th>
        <td mat-cell *matCellDef="let element"> {{element.packageName | titlecase}} </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="orderQty">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Order Quantity</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <!-- {{ element.quantity }} -->
          <span *ngIf='!showButton'>
            {{element.quantity}}
          </span>
          <div *ngIf='showButton'>
            <input class="input1" type="number" step="0.01" min="0" (keyup)="checkOrderQty($event , element)"
              [(ngModel)]="element.quantity" (focus)="focusFunctionWithOutForm(element,'quantity')" (focusout)="focusOutFunctionWithOutForm(element,'quantity')"/>
          </div>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="pendingQty">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Pending Quantity</b>
        </th>
        <td mat-cell *matCellDef="let element">
          {{ element.pendingQty }}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="vendor">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Brand</b></th>
        <td mat-cell *matCellDef="let element">{{ element.brand.name }}</td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="unitPrice">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Unit Cost</b>
        </th>
        <td mat-cell *matCellDef="let element">{{ this.utils.truncateNew(element.unitPrice)}}</td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="totalValue">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Total (incl.tax,etc)</b></th>
        <td mat-cell *matCellDef="let element">
          {{this.utils.truncateNew(
          ((element.unitPrice ) *
          element.quantity) +element.cessAmt + element.extraAmt - element.discAmt)
          }}
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getTotal())}}</td>
      </ng-container>

      <ng-container matColumnDef="subTotal">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Total (excl.tax)</b></th>
        <td mat-cell *matCellDef="let element">
          {{this.utils.truncateNew(
          ((element.quantity ) *
          element.unitPrice))
          }}
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getTotal())}}</td>
      </ng-container>

      <ng-container matColumnDef="taxAmt">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Tax Amt</b></th>
        <td mat-cell *matCellDef="let element">
          {{this.utils.truncateNew(
          ((element.taxAmount/element.quantity ) *
          element.quantity))
          }}
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getTaxTotal())}}</td>
      </ng-container>

      <ng-container matColumnDef="Action">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b> Action</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <button *ngIf="
              element.itemStatus != 'complete' && user.uType === 'restaurant'
            " class="mat-icon-button" matTooltip="Add or Edit Packaging Sizes" (click)="displayPackages(element)">
            <mat-icon>edit</mat-icon>
          </button>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      <tr mat-footer-row *matFooterRowDef="displayedColumns"></tr>
    </table>
    <div class="dataMessage" *ngIf="dataSource?.data.length == 0"> No Data Available </div>
  </mat-card-content>
</mat-card>