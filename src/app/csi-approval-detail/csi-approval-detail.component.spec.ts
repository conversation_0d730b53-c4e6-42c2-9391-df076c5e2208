import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { CsiApprovalDetailComponent } from './csi-approval-detail.component';

describe('CsiApprovalDetailComponent', () => {
  let component: CsiApprovalDetailComponent;
  let fixture: ComponentFixture<CsiApprovalDetailComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ CsiApprovalDetailComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CsiApprovalDetailComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
