import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { CsiApprovalComponent } from './csi-approval.component';

describe('CsiApprovalComponent', () => {
  let component: CsiApprovalComponent;
  let fixture: ComponentFixture<CsiApprovalComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ CsiApprovalComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CsiApprovalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
