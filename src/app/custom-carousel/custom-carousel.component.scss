.wrapper {
  position: relative;
  display:flex;
  overflow: hidden;
  margin-left: auto;
  margin-right: auto;
  align-items: center;
}
.inner{
  width:100%;
  position:relative;
}
.inner img
{
  width:100%;

}
.inner img.fixed-height{
  position: absolute;
    top:-100%; left:0; right: 0; bottom:-100%;
    margin: auto;
}
.wrapper::after {
  display: block;
  clear: both;
  content: '';
}
.wrapper div {
  float: left;
  margin-right: -100%;
}
.btn-prev:hover,.btn-next:hover,
.btn-prev:focus,.btn-next:focus {
  opacity:.9
}

.btn-prev,
.btn-next {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 15%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color:transparent;
  border:0;
  color:white;
  opacity: .5;
transition: opacity .15s ease;

}
.btn-next{
  right: 0;
}
.next-icon {
	background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' viewBox='0 0 16 16'><path d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/></svg>");
}
.prev-icon {
  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' class='bi bi-chevron-left' viewBox='0 0 16 16'><path  d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/></svg>");
}
.next-icon, .prev-icon {
	display: inline-block;
  background-color: black;
	width: 2rem;
	height: 2rem;
	background-repeat: no-repeat;
	background-position: 50%;
	background-size: 100% 100%;
}
.wrapper-thumbail {
  width: 100%;
  overflow: hidden;
}
.thumbail {
  display: flex;
}
.thumbail img {
  cursor: pointer;
}
.thumbail img:hover {
  opacity: 1 !important;
}
