<div class="title">
  <button mat-button class="button" (click)="back()">
    <mat-icon>keyboard_backspace</mat-icon>  Back
  </button>
  <div style="float: right;" [matTooltip]="(!delDate)  ? 'Please select delivery date' : '' ">
    <div style="float: right;" [matTooltip] = "this.data.hasOwnProperty('closingStatus') && this.data.closingStatus.indentStatus === 'closed' ? 'PO closed' : '' ">
    <button mat-button class="button3 createPrBtn" (click)="createPrs()" [disabled]="disablePOCreation ||this.data.isPoCreated || !delDate || this.data.hasOwnProperty('closingStatus') && this.data.closingStatus.indentStatus === 'closed' " >
      {{ approvalRequired ? 'Create PO Approval' : 'Create PO' }}
    </button>
  </div>
</div>
</div>
<div style="display: flex; margin-left: 2%; margin-right: 2%;">
</div>

<!-- <mat-card>
  <ng-container *ngIf="prArray.length > 0">
    <div class="row" *ngFor="let item of prArray; let i = index">
      <div class="col" *ngFor="let key of ['S.No.', 'PR No.', 'Work Area', 'Creator', 'Create Timestamp']; let j = index">
        <table class="table">
          <tbody>
            <tr *ngIf="j === 0">
              <th class="topItemkey" scope="row">{{ key }}</th>
              <td>{{ i + 1 }}</td>
            </tr>
            <tr *ngIf="j === 1">
              <th class="topItemkey" scope="row">{{ key }}</th>
              <td>{{ item['PRId'] }}</td>
            </tr>
            <tr *ngIf="j === 2">
              <th class="topItemkey" scope="row">{{ key }}</th>
              <td>{{ item['WorkArea'] }}</td>
            </tr>
            <tr *ngIf="j === 3">
              <th class="topItemkey" scope="row">{{ key }}</th>
              <td>{{ item['creator'] }}</td>
            </tr>
            <tr *ngIf="j === 4">
              <th class="topItemkey" scope="row">{{ key }}</th>
              <td>
                {{ this.utils.timesStampReduction(item['createTs']) | date: "EEEE, MMMM d, y h:mm a" }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </ng-container>
</mat-card>
 -->

  <mat-expansion-panel expanded>
    <mat-expansion-panel-header>
      <mat-panel-title class="topItemkey">
        Additional Details
      </mat-panel-title>
      <mat-panel-description>
        <i>Click Here  </i> &nbsp;
        &nbsp; <mat-icon>add_shopping_cart</mat-icon>
      </mat-panel-description>
    </mat-expansion-panel-header>
    
    <div class="row expansionInnerContent">
      <div class="col">
        <mat-form-field appearance="outline" style="width: 210px !important">
          <mat-label style="font-size: larger; font-weight: bolder;"> <b>Remark</b> </mat-label>
          <textarea matInput [(ngModel)]="remarks"></textarea>
        </mat-form-field>
      </div>
      <div class="col">
        <mat-form-field appearance="outline" style="width: 210px !important">
          <mat-label style="font-size: larger; font-weight: bolder;"><b>Payment Terms</b></mat-label>
          <textarea matInput [(ngModel)]="paymentTerms"></textarea>
        </mat-form-field>
      </div>
      <div class="col">
        <mat-form-field appearance="outline" style="width: 210px !important">
          <mat-label style="font-size: larger; font-weight: bolder;"><b>Po Terms</b></mat-label>
          <textarea matInput [(ngModel)]="poTerms"></textarea>
        </mat-form-field>
      </div>
      <div class="col">
        <mat-form-field appearance="outline" style="width: 210px !important">
          <mat-label> <b>Payment Method</b> </mat-label>
          <mat-select [(value)]="paymentMethod" [(ngModel)]="paymentMethod">
            <mat-option *ngFor="let method of paymentMethods" [value]="method">
              {{method}}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div class="col">
        <mat-form-field appearance="outline" style="width: 210px !important">
          <mat-label> <b>Delivery Date</b> </mat-label>
            <input matInput [matDatepicker]="picker1" tabindex="-1" placeholder="Delivery Date"
            [(ngModel)]="delDate"/>
          <mat-datepicker-toggle matSuffix [for]="picker1" tabindex="-1">
            <mat-icon matDatepickerToggleIcon><img src="./../../assets/calender.png" /></mat-icon>
          </mat-datepicker-toggle>
          <mat-datepicker #picker1 disabled="false"></mat-datepicker>
          <mat-error>
            Select Delivery Date
          </mat-error>
        </mat-form-field>
      </div>
    </div>

  </mat-expansion-panel>

  <!-- <div>
    <div class="row">
      <div class="col">
        <mat-form-field appearance="outline" style="width: 210px !important">
          <mat-label style="font-size: larger; font-weight: bolder;"> <b>Remark</b> </mat-label>
          <textarea matInput [(ngModel)]="remarks"></textarea>
        </mat-form-field>
      </div>
      <div class="col">
        <mat-form-field appearance="outline" style="width: 210px !important">
          <mat-label style="font-size: larger; font-weight: bolder;"><b>Payment Terms</b></mat-label>
          <textarea matInput [(ngModel)]="paymentTerms"></textarea>
        </mat-form-field>
      </div>
      <div class="col">
        <mat-form-field appearance="outline" style="width: 210px !important">
          <mat-label style="font-size: larger; font-weight: bolder;"><b>Po Terms</b></mat-label>
          <textarea matInput [(ngModel)]="poTerms"></textarea>
        </mat-form-field>
      </div>
      <div class="col">
        <mat-form-field appearance="outline" style="width: 210px !important">
          <mat-label> <b>Payment Method</b> </mat-label>
          <mat-select [(value)]="paymentMethod" [(ngModel)]="paymentMethod">
            <mat-option *ngFor="let method of paymentMethods" [value]="method">
              {{method}}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>


      <div class="col">
        <mat-form-field appearance="outline" style="width: 210px !important">
          <mat-label> <b>Delivery Date</b> </mat-label>
            <input matInput [matDatepicker]="picker1" tabindex="-1" placeholder="Validity Date"
            [(ngModel)]="delDate"/>
          <mat-datepicker-toggle matSuffix [for]="picker1" tabindex="-1">
            <mat-icon matDatepickerToggleIcon><img src="./../../assets/calender.png" /></mat-icon>
          </mat-datepicker-toggle>
          <mat-datepicker #picker1 disabled="false"></mat-datepicker>
          <mat-error>
            Select Delivery Date
          </mat-error>
        </mat-form-field>
      </div>

    </div>
  </div> -->
<!-- </mat-card> -->

<mat-card>
  <mat-form-field appearance="fill" class="mr-3 my-3">
    <!-- <label>Search</label> -->
    <input matInput type="text" autocomplete="false" [(ngModel)]='searchValue'
      (keyup)="doFilter($event.target.value)" placeholder="Search" />
    <!-- <mat-icon matSuffix (click)="clearFilter()" class="closebtn">close</mat-icon> -->
  </mat-form-field>

  <mat-form-field appearance="fill" class="my-3 filterInputs">
    <!-- <label>Sub Category</label> -->
    <mat-select placeholder="Subcategory" [formControl]="Subcategory">
      <mat-option *ngFor="let subCat of subCategoryList" [value]="subCat" (click)="selectSubCat(subCat)">
        {{ subCat | titlecase }}
      </mat-option>
    </mat-select>
  </mat-form-field>

  <mat-form-field appearance="fill" class="mr-3 my-3 filterInputs">
    <!-- <label>Category</label> -->
    <mat-select placeholder="Category" [formControl]="Category">
      <mat-option *ngFor="let cat of categoryList" [value]="cat" (click)="selectCategory(cat)">
        {{ cat | titlecase }}
      </mat-option>
    </mat-select>
  </mat-form-field>

  <section class="example-container-1 mat-elevation-z8">
  <table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort
  class="mat-elevation-z8">

  <ng-container matColumnDef="select">
    <th mat-header-cell *matHeaderCellDef>
      <mat-checkbox #selectAll (change)="$event ? masterToggle() : null"
        [checked]="selection.hasValue() && isAllSelected()"
        [indeterminate]="selection.hasValue() && !isAllSelected()" [aria-label]="checkboxLabel()"
        [(ngModel)]="checkSelection">
      </mat-checkbox>
    </th>
    <td mat-cell *matCellDef="let row">
      <mat-checkbox (change)="$event ? selection.toggle(row) : null; getTotal($event)"
        [checked]="selection.isSelected(row)" [aria-label]="checkboxLabel(row)"
        (keydown.Tab)="getVendors(row)"
        >
      </mat-checkbox>
    </td>
    <td mat-footer-cell *matFooterCellDef></td>
  </ng-container>

  <ng-container matColumnDef="index">
    <th mat-header-cell *matHeaderCellDef class="tableId"><b>#</b></th>
    <td mat-cell *matCellDef="let element; let i = index" class="name-cell tableId">
      {{ i + 1 }}
    </td>
    <td mat-footer-cell *matFooterCellDef></td>
  </ng-container>

  <ng-container matColumnDef="category" sticky>
    <th mat-header-cell *matHeaderCellDef mat-sort-header style="width: 0px !important;"><b>Category</b></th>
    <td mat-cell *matCellDef="let element">{{ element.category }}</td>
    <td mat-footer-cell *matFooterCellDef></td>
  </ng-container>

  <ng-container matColumnDef="subCategory" sticky>
    <th mat-header-cell *matHeaderCellDef mat-sort-header style="width: 140px !important;"><b>Sub Category</b></th>
    <td mat-cell *matCellDef="let element">{{ element.subCategory }}</td>
    <td mat-footer-cell *matFooterCellDef></td>
  </ng-container>
<!--   style="width: 180px !important;" -->

  <ng-container matColumnDef="itemName" sticky>
    <th mat-header-cell *matHeaderCellDef mat-sort-header>
      <b> Item Name</b>
    </th>
    <td mat-cell *matCellDef="let element">
      {{ element.itemName | titlecase }}
    </td>
    <td mat-footer-cell *matFooterCellDef></td>
  </ng-container>

<!--  style="width: 0px !important;" -->
  <ng-container matColumnDef="pkgName" sticky>
    <th mat-header-cell *matHeaderCellDef mat-sort-header>
      <b> Pkg Name</b>
    </th>
    <td mat-cell *matCellDef="let element">
      {{ element.packageName | titlecase }}
    </td>
    <td mat-footer-cell *matFooterCellDef></td>
  </ng-container>

  <ng-container matColumnDef="vendorList" sticky>
    <th mat-header-cell *matHeaderCellDef mat-sort-header class="ven-cell">
      <b>Vendor List</b>
    </th>
    <td mat-cell *matCellDef="let element" class="ven-cell">
      <div style="display: flex;">
        <div style="margin: auto; width: 14rem;">{{element.defVendor}}</div>
        <mat-form-field appearance="outline" class="m-1" 
        (click)="getVendors(element)"
        (keydown.Tab)="onTabPressed($event, element)"
        (keydown.Enter)="onEnterPressed($event, element)"
        style="width: 36px!important; margin: auto; ">
          <mat-select (selectionChange)="selectVendor($event.value,element)">
            <mat-option>
              <ngx-mat-select-search placeholderLabel="select vendor.." noEntriesFoundLabel="NO DATA"
                [formControl]="vendorFilterCtrl"></ngx-mat-select-search>
            </mat-option>
            <mat-option *ngFor="let vendor of vendorsBanks | async" [value]="vendor">
              {{ vendor.vendorName }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </td>
    <td mat-footer-cell *matFooterCellDef></td>
  </ng-container>

  <ng-container matColumnDef="onHand">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>
      <b> On Hand </b>
    </th>
    <td mat-cell *matCellDef="let element">
      {{ this.utils.truncateNew(element.onHand) }}
    </td>
    <td mat-footer-cell *matFooterCellDef></td>
  </ng-container>

  <ng-container matColumnDef="optStock">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>
      <b>Optimum Stock </b>
    </th>
    <td mat-cell *matCellDef="let element">{{ element.optimumStock }}</td>
    <td mat-footer-cell *matFooterCellDef></td>
  </ng-container>

  <ng-container matColumnDef="openOrders">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>
      <b>Open Orders</b>
    </th>
    <td mat-cell *matCellDef="let element" class="estimated-cell">
      {{ element.onOrder }}
    </td>
    <td mat-footer-cell *matFooterCellDef></td>
  </ng-container>
<!-- style="width: 0px !important;" -->
  <ng-container matColumnDef="orderQty">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>
      <b>Order Quantity</b>
    </th>
    <td mat-cell *matCellDef="let element">
      <div *ngIf="!isVendor">
        <input style="text-align: center;" class="input1" type="number" step="0.01" min="0"
        [(ngModel)]="element.orderQty" *ngIf="!isVendor" (keyup)="makeZero($event,element)"
        (focus)="focusFunctionWithOutForm(element , 'orderQty')" (focusout)="focusOutFunctionWithOutForm(element , 'orderQty')" />
        <!-- [disabled]="element?.vendorList[0]?.isContract == true" -->
        <!-- <mat-icon *ngIf="element?.vendorList[0]?.isContract == true" class="mt-1" style="position: absolute;" 
        matTooltip="you won't be able to edit contract price">info</mat-icon> -->
      </div>
      <div *ngIf="isVendor">
        {{ element.orderQty }}
      </div>
    </td>
    <td mat-footer-cell *matFooterCellDef></td>
  </ng-container>

  <ng-container matColumnDef="unit">
    <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Unit</b></th>
    <td mat-cell *matCellDef="let element">{{ element.uom }}</td>
    <td mat-footer-cell *matFooterCellDef></td>
  </ng-container>

  <ng-container matColumnDef="rate">
    <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Tax Rate(%)</b></th>
    <td mat-cell *matCellDef="let element">
      <input class="input1" type="number" step="0.01" min="0" [(ngModel)]=" element.taxRate"
      (keyup)="getSubTotal(element)" (focus)="focusFunctionWithOutForm(element , 'taxRate')" (focusout)="focusOutFunctionWithOutForm(element , 'taxRate')"/>
    </td>
    <td mat-footer-cell *matFooterCellDef></td>
  </ng-container>
<!--  style="width: 0px !important;" -->
  <ng-container matColumnDef="taxAmount">
    <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Tax Amount</b></th>
    <td mat-cell *matCellDef="let element">{{ this.utils.truncateNew((element.taxRate/100) * element.orderQty * element.unitPrice)}}</td>
    <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getTaxAmountFoot())}}</td>
  </ng-container>

  <ng-container matColumnDef="unitPrice">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>
      <b> Unit Cost</b>
    </th>
    <td mat-cell *matCellDef="let element">
      <div *ngIf="!(inputObj.tableType == 1)" [matTooltip] = "(element.isContract == true)  ? 'contract price' : '' ">
      <input *ngIf="!(inputObj.tableType == 1)" step="0.1" type="number" class="input1" (input)="getTotal(element.unitPrice , element)"
        style="text-align: center;" type="number" [(ngModel)]="element.unitPrice" [disabled]="element.isContract == true" (keyup)="getSubTotal(element)"
        [ngClass]="{ 'highlight-disabled': element.isContract == true }"  (focus)="focusFunctionWithOutForm(element , 'unitPrice')" (focusout)="focusOutFunctionWithOutForm(element , 'unitPrice')"/>
      </div>
      <div *ngIf="inputObj.tableType == 1">
        {{ this.utils.truncateNew(element.unitPrice) }}
      </div>
    </td>
    <td mat-footer-cell *matFooterCellDef></td>
  </ng-container>

  <ng-container matColumnDef="subTotal">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>
      <b> Sub Total</b>
    </th>
    <td mat-cell *matCellDef="let element" style="text-align: center;">
      <div>
        {{ this.utils.truncateNew(element.unitPrice * element.orderQty) }}
      </div>
    </td>
    <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getSubTotalFoot())}}</td>
  </ng-container>
  <!-- style="width: 0px !important;" -->
  <ng-container matColumnDef="totalValue">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>
      <b> Total Value</b>
    </th>
    <td mat-cell *matCellDef="let element" style="text-align: center;">
      <div>
        {{getItemTotal(element)}}
      </div>
    </td>
    <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getTotalFoot())}}</td>
  </ng-container>

  <ng-container matColumnDef="quotedUnitPrice">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>
      <b> Unit Cost</b>
    </th>
    <td mat-cell *matCellDef="let element">
      <input class="input1" *ngIf="!(inputObj.tableType == 1)" (input)="getTotal()" style="text-align: center;"
        type="number" [(ngModel)]="element.unitPrice" />
      <div *ngIf="inputObj.tableType == 1" [style.color]="isGreater(element)">
        {{ this.utils.truncateNew(element.quotedUnitPrice) }}
      </div>
    </td>
    <td mat-footer-cell *matFooterCellDef></td>
  </ng-container>

  <ng-container matColumnDef="deliverableQty">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>
      <b> Deliverable Quantity</b>
    </th>
    <td mat-cell *matCellDef="let element">
      <input *ngIf="!(inputObj.tableType == 1)" (input)="getTotal()" style="text-align: center;" type="number"
        [(ngModel)]="element.deliverableQty" />
      <div *ngIf="inputObj.tableType == 1">
        {{ element.deliverableQty }}
      </div>
    </td>
    <td mat-footer-cell *matFooterCellDef></td>
  </ng-container>

  <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky:true;"></tr>
  <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
  <tr mat-footer-row *matFooterRowDef="displayedColumns"></tr>

</table>
</section>

</mat-card>