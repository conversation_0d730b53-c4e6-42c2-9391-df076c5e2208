.createPrBtn{
    float: right !important;
}

::ng-deep textarea.mat-input-element {
    padding: 0px 0 !important;
    margin: -8px 0 !important;
  }

  .filterInputs{
    float: right !important;
  }

  ::ng-deep mat-panel-description.mat-expansion-panel-header-description {
    justify-content: flex-end !important;
  }

  .expansionInnerContent{
    margin-bottom: 10px;
    margin-left: 5px;
  }

  mat-expansion-panel{
    margin-left: 2%;
    margin-right: 2%;
  }

  ::ng-deep .mat-select-search-clear{
    display: none !important;
  }


  .tableMatSelect{
    width: 260px !important;
  }

  ::ng-deep .mat-select-search-clear {
    display: none !important;
  }

  .highlight-disabled {
    background-color: #909192;
  }

  .input1[disabled] {
    background-color: #909192;
  }

  .example-container-1{
    overflow : auto;
    width : 100%;
  }