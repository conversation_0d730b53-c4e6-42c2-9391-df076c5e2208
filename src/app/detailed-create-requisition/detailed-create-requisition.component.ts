import { Component, OnInit, ViewChild } from '@angular/core';
import { AuthService, PurchasesService, ShareDataService } from '../_services';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog } from '@angular/material';
import { UtilsService } from '../_utils/utils.service';
import { FormControl, FormGroup, FormBuilder, Validators } from '@angular/forms';
import { SelectionModel } from '@angular/cdk/collections';
import { ReplaySubject, Subject } from 'rxjs';
import { Location } from '@angular/common';
import { Router } from '@angular/router';
import { NotificationService } from '../_services/notification.service';
import { environment } from 'src/environments/environment';
import { takeUntil } from 'rxjs/operators';
import { first } from 'rxjs/operators';
import { SimpleDialogComponent } from '../_dialogs/simple-dialog/simple-dialog.component';

@Component({
  selector: 'app-detailed-create-requisition',
  templateUrl: './detailed-create-requisition.component.html',
  styleUrls: ['./detailed-create-requisition.component.scss', './../../common-dark.scss']
})
export class DetailedCreateRequisitionComponent implements OnInit {
  obj = [
    {
      'PRId': 'OO1',
      'WorkArea': 'Store'
    },
    {
      'PRId': 'OO2',
      'WorkArea': 'Warehouse'
    }
  ];
  prArray: any[] = [];
  data: any;
  displayedColumns: string[];
  dataSource: MatTableDataSource<any>;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  // @ViewChild(MatSort) sort: MatSort;
  pageSizes = [];
  user: any;
  restaurantId: any;
  inputObj: any;
  dataObj: any = {};
  inventoryItems: any[];
  initCategoryList = [];
  initSubCategoryList = [];
  disablePOCreation: boolean = false;
  // contract: boolean ;
  getBranchData: any[]
  branches: any[]
  categoryList = ['All'];
  subCategoryList = ['All'];
  filterKeys = { category: 'All', subCategory: 'All', vendorId: 'All' }
  Category = new FormControl();
  Subcategory = new FormControl();
  vendorForm = new FormControl();
  selection = new SelectionModel<any>(true, []);
  totalOrderValue = 0;
  vendors: any;


  vendor: any;
  totalVendorOrderValue: number;
  totalSubCatOrderValue: number;
  totalCatOrderValue: number;
  multiBranchUser; branchSelected: boolean;
  searchValue: any;
  checkSelection: boolean;
  approvalRequired: boolean = false;
  paymentMethods = [
    "Cash",
    "NEFT",
    "Other"
  ];
  remarks: any;
  paymentTerms: any;
  poTerms: any;
  paymentMethod: any;
  delDate: any;
  roles: any = [];

  public vendorsBanks: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public VendorBank: any[] = [];
  public vendorFilterCtrl: FormControl = new FormControl();
  protected _onDestroy = new Subject<void>();
  prIds: any[];
  workArea: any[];
  restId: any;
  ssidata: any;

  constructor(
    private sharedData: ShareDataService,
    public utils: UtilsService,
    private purchases: PurchasesService,
    private auth: AuthService,
    private fb: FormBuilder,
    private location: Location,
    private router: Router,
    private dialog: MatDialog,
    private purchasService: PurchasesService,
    private notifyService: NotificationService,
  ) {
    this.user = this.auth.getCurrentUser();
  }

  ngOnInit() {
    this.sharedData.crData.pipe(first()).subscribe(crData => {
      this.data = crData
    });
    if (!this.data || Object.keys(this.data).length === 0) {
      this.router.navigate(['/home/<USER>']);
    } else {
      this.restId = this.data.restaurantId;
      this.remarks = this.data.remarks
      this.paymentTerms = this.data.paymentTerms
      this.poTerms = this.data.poTerms
      this.paymentMethod = this.data.paymentMethod
      this.delDate = this.data.deliveryDate
      this.restaurantId = this.data.restaurantId
      this.inputObj = {}
      this.getSsi();
    }
  }

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }

  clearFilter() {
    this.vendor = ''
    this.searchValue = ''
    this.doFilter('')
    this.dataSource.data = this.inventoryItems
    this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
    this.dataSource.paginator = this.paginator;
  }

  getSsi() {
    if (this.data.approvalDetail) {
      if (this.data.approvalDetail) {
        let approvedObjects = this.data.approvalDetail.filter((el) => {
          return el.status == 'approved'
        })
        this.disablePOCreation = (approvedObjects.length === this.data.approvalDetail.length) ? false : true;
      }
    }

    if (Array.isArray(this.data) && this.data.every(item => typeof item === "object")) {
      this.prIds = [];
      this.workArea = [];
      this.data.forEach((obj: any) => {
        let object = {};
        object['PRId'] = obj.prId;
        object['creator'] = obj.creator;
        object['createTs'] = obj.createTs;
        if (Array.isArray(obj.selectedWorkArea)) {
          object['WorkArea'] = obj.selectedWorkArea[0]
        } else {
          object['WorkArea'] = 'Store'
        }
        this.prIds.push(obj.prId);
        this.workArea.push(obj.selectedWorkArea);
        this.prArray.push(object);
      });
      this.restId = this.data[0].restaurantId
    } else {
      this.prIds = [this.data.prId]
      this.workArea = [this.data.selectedWorkArea];

      let object = {};
      object['PRId'] = this.data.prId;
      object['creator'] = this.data.creator;
      object['createTs'] = this.data.createTs;
      if (Array.isArray(this.data.selectedWorkArea)) {
        object['WorkArea'] = this.data.selectedWorkArea[0]
      } else {
        object['WorkArea'] = 'Store'
      }
      this.prArray.push(object);
    }
    this.displayedColumns = ['select', 'category', 'subCategory', 'itemName', 'pkgName', 'vendorList', 'orderQty', 'unitPrice', 'rate', 'subTotal', 'taxAmount', 'totalValue'];
    this.data
    let obj = {
      tenantId: this.user.tenantId,
      restaurantId: this.restId,
      prId: this.prIds,
      specialFlag: true
    }
    this.purchases.getSsi(obj).subscribe(data => {
      this.ssidata = data
      // let filter = data.filter((el)=> el.isContract)
      // this.contract = (filter.length == data.length) ? true : false ;
      this.inputObj.data = data;
      this.getBranchDetails();
      this.getUsers();
      this.intializeInputObj();
    }, err => { console.error(err) })
  }

  private intializeInputObj() {
    this.dataSource = new MatTableDataSource<any>();
    this.inventoryItems = this.inputObj.data;
    this.inventoryItems.forEach((element) => {
      if (element.taxRate > 0) {
        let taxAmount = (element.otb * element.unitPrice) * (element.taxRate / 100);
        element.totalPrice = (element.otb * element.unitPrice) + taxAmount;
      }
    })
    this.inventoryItems.map((item: any) => {
      if (!item.vendorType)
        item.vendorType = 'Fixed'
      item.orderQty = item.otb;
      item.unitPrice = item.vendorList[0].unitPrice;
      item.totalValue = item.unitPrice * item.otb;
      item.defVendor = item.vendorList[0].vendorName;
      item.vendorId = item.vendorList[0].vendorId;
      return item
    }
    );
    this.dataSource.data = this.inventoryItems;
    this.pageSizes = this.utils.getPageSizes(this.dataSource.data);
    this.dataSource.paginator = this.paginator;


    this.inventoryItems.forEach(item => {
      if (item.category == null) {
        item.category = 'N/A'
      }
      if (item.subCategory == null) {
        item.subCategory = 'N/A'
      }
      this.categoryList.push(item.category)
      this.subCategoryList.push(item.subCategory)
      if (!item.uom)
        item.uom = "units"
    })
    this.categoryList = this.categoryList.filter((k, i, ar) => ar.indexOf(k) === i);
    this.subCategoryList = this.subCategoryList.filter((j, l, arr) => arr.indexOf(j) === l);
    this.initCategoryList = this.categoryList;
    this.initSubCategoryList = this.subCategoryList;
    this.displayedColumns = ['select', 'category', 'subCategory', 'itemName', 'pkgName', 'vendorList', 'orderQty', 'unitPrice', 'rate', 'subTotal', 'taxAmount', 'totalValue'];
    this.checkSelection = true;
    this.masterToggle();
  }

  filterByBranch(restId) {
    this.restaurantId = restId.restaurantIdOld;
    this.branchSelected = true
    this.inputObj = {}
    this.getSsi();
    this.getUsers();
  }

  selectCategory(cat) {
    let filteredSubCategoryList = []
    let filteredItem = []
    if (cat != 'All') {
      filteredItem = this.inventoryItems.filter(item => item['category'].toUpperCase() === cat.toUpperCase());
      filteredItem.forEach(element => {
        filteredSubCategoryList.push(element.subCategory)
      });
      this.subCategoryList = filteredSubCategoryList.filter((k, i, ar) => ar.indexOf(k) === i);
      this.subCategoryList.splice(0, 0, 'All')
    }
    else {
      this.subCategoryList = this.initSubCategoryList
    }
    this.filterKeys.category = cat;
    this.filterKeys.subCategory = 'All'
    this.allFilter().then(() => {
      this.getCatTotal();
    });
  }

  getCatTotal(e?: Event) {
    this.dataSource.data.forEach(item => {
      item.totalValue = item.otb * item.unitPrice;
    });
    this.totalCatOrderValue = this.utils.getTotal(this.dataSource.data, 'totalValue') / 100000;
  }

  selectSubCat(subCat) {
    this.filterKeys.subCategory = subCat;
    this.allFilter().then(() => {
      this.getSubCatTotal();
    });
  }

  getSubCatTotal(e?: Event) {
    this.dataSource.data.forEach(item => {
      item.totalValue = item.otb * item.unitPrice;
    });
    this.totalSubCatOrderValue = this.utils.getTotal(this.dataSource.data, 'totalValue') / 100000;
  }

  selectedVendor(vendor) {
    this.filterKeys.vendorId = vendor.tenantId;
    this.vendor = vendor.name;
    this.allFilter().then(() => {
      this.getVendorTotal();
    });
  }

  async allFilter() {
    let tmp = this.inventoryItems
    let prev = this.inventoryItems
    Object.keys(this.filterKeys).forEach(element => {
      if (this.filterKeys[element] != 'All') {
        tmp = prev.filter(item => item[element].toLowerCase() === this.filterKeys[element].toLowerCase())
        prev = tmp
      }
    });
    this.dataSource.data = tmp
    this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
    this.dataSource.paginator = this.paginator;
  }

  getVendorTotal(e?: Event) {
    this.dataSource.data.forEach(item => {
      item.totalValue = item.otb * item.unitPrice;
    });
    this.totalVendorOrderValue = this.utils.getTotal(this.dataSource.data, 'totalValue') / 100000;
  }

  selectVendor(vendor: any, element: any) {
    element.defVendor = vendor.vendorName;
    element.unitPrice = vendor.unitPrice
    element.taxRate = vendor.taxRate
    element.vendorId = vendor.vendorId
    element.isContract = vendor.isContract
  }

  isAllSelected() {
    if (this.dataSource) {
      const numSelected = this.selection.selected.length;
      const numRows = this.dataSource.data.length;
      return numSelected === numRows;
    }
  }

  masterToggle() {
    this.isAllSelected() ?
      this.selection.clear() :
      this.dataSource.data.forEach(row => this.selection.select(row));
  }

  checkboxLabel(row?: any): string {
    if (!row) {
      return `${this.isAllSelected() ? 'select' : 'deselect'} all`;
    }
    return `${this.selection.isSelected(row) ? 'deselect' : 'select'} row ${row.itemCode + 1}`;
  }

  getTotal(e, n) {
    this.selection.selected.forEach(item => {
      item.totalValue = item.otb * item.unitPrice;
    });
    this.totalOrderValue = this.utils.getTotal(this.selection.selected, 'totalValue') / 100000;
  }

  getSubTotalFoot() {
    let subTotalSum = 0
    this.selection.selected.forEach(element => {
      subTotalSum += element.orderQty * element.unitPrice
    });
    return subTotalSum
  }

  getTotalFoot() {
      const total = this.selection.selected.reduce((acc, element) => {
        const { taxRate, orderQty, unitPrice } = element;
        const subTotal = ((taxRate / 100) * orderQty * unitPrice) + (orderQty * unitPrice);
        return acc + subTotal;
      }, 0);
      return total
  }

  getItemTotal(element) {
    let data = element.vendorList.find(item => item.vendorName === element.defVendor)
    
      if (data.priceType == 'fixedContractPrice' && data.contractTax == true) {
        return this.utils.truncateNew(element.orderQty * data.unitPrice)
  
      }else{
        return this.utils.truncateNew(((element.taxRate / 100) * element.orderQty * element.unitPrice) +
        ((element.unitPrice * element.orderQty)))
      }

  }

  getTaxAmountFoot() {
    let totalTaxAmount = 0
    this.selection.selected.forEach(element => {
      totalTaxAmount += ((element.taxRate / 100) * element.orderQty * element.unitPrice);
    });
    return totalTaxAmount
  }

  makeZero(event, element) {
    this.getSubTotal(element);
  }

  getUsers() {
    let inputData: any = {
      tenantId: this.auth.getCurrentUser().tenantId,
      restaurantId: this.restId,
      type: "purchaseOrder"
    }
    this.purchases.getSelectedUsers(inputData).subscribe(data => {
      this.roles = data.data;
    })
  }

  getAppCatRoles(rolesArr, appCat) {
    const filteredObj = rolesArr.find(obj => obj.appCat === appCat);
    return filteredObj ? filteredObj.roles.map(role => role.value) : [];
  }


  createPrs() {
    let tempWorkArea = []
    tempWorkArea = this.workArea
    const mergedArray = [].concat(...this.workArea);
    let workArea = [...new Set(mergedArray)];
    this.workArea = workArea.filter(item => item !== null);
    let items = this.selection.selected.filter(item => item.orderQty > 0);
    const updatedData = items.map(obj => {
      if ("quantity" in obj) {
        const { quantity, ...rest } = obj;
        return { orderQty: quantity, ...rest };
      }
      return obj;
    });
    let selectedCategory = ("approvalCategory" in this.data) ? this.data.approvalCategory : "DEFAULT";
    let selectedRole = this.getAppCatRoles(this.roles, selectedCategory)
    let obj = {
      items: updatedData,
      restaurantId: this.restId,
      // restaurantId:  this.user.restaurantId,
      tenantId: this.auth.getCurrentUser().tenantId,
      uId: this.auth.getCurrentUser().mId,
      senderEmail: [this.user.email],
      prId: this.prIds,
      selectedWorkArea: this.workArea,
      baseurl: environment.baseUrl,
      userEmail: this.auth.getCurrentUser().email,
      deliveryDate: this.delDate,
      remarks: this.remarks,
      paymentTerms: this.paymentTerms,
      // isContract : this.contract,
      poTerms: this.poTerms,
      paymentMethod: this.paymentMethod,
      poMadeBy: ("userName" in this.auth.getCurrentUser()) ? this.auth.getCurrentUser().userName : "NA",
      category: selectedCategory,
      role: (selectedRole && selectedRole.length > 0) ? selectedRole : []
    }
    if (this.prIds.length > 1) {
      obj['isConvert'] = true;
    } else {
      obj['isConvert'] = false;
    }
    obj['createdUser'] = this.user.email;
    this.purchases.createPrs(obj).subscribe(data => {
      if (data['result'] === 'success') {
        let partialItems = updatedData.filter((el)=> el['orderQty'] < el['otb'])
        const itemCodeSet2 = new Set(updatedData.map(item => item.itemCode));
        const missingInArray2 = this.ssidata.filter(item => !itemCodeSet2.has(item.itemCode));
        let pendingItemArray = missingInArray2.concat(partialItems)
        if (pendingItemArray.length > 0 ){
          let obj = {
            "restaurantId": this.restId,
            "items": pendingItemArray,
            'uId': this.auth.getCurrentUser().mId,
            "prId": this.prIds[0],
          }
          this.dialog.open(SimpleDialogComponent, {
            data: {
              from: "partialPr",
              title: "Create Purchase Request",
              msg: `Do you want to create a PR for the pending items?`,
              create: function () {
                this.purchasService.createPartialPr(obj).subscribe((data: { result: string; message: any; }) => {
                  if (data['success'] === true) {
                    this.utils.snackBarShowSuccess(`${data.message}`);
                  } else {
                    this.utils.snackBarShowError("Something went wrong, please contact support!");
                  }
                });
              }.bind(this),
              cancel: function () {
                this.back()
              }.bind(this),
            },
          });
        }
      } else {
        this.utils.snackBarShowError(data['message']);
      }
      this.disablePOCreation = true;
    }, err => console.error(err))
  }

  back() {
    this.location.back();
  }

  getBranchDetails() {
    let obj = {}
    obj['tenantId'] = this.user.tenantId;
    obj['restaurantId'] = this.restId,
      obj['type'] = 'purchaseOrder',
      this.purchases.getBranchDetails(obj).subscribe(data => {
        this.approvalRequired = data.approvalRequired;
      })
  }



  getSubTotal(element) {
    let data = element.vendorList.find(item => item.vendorName === element.defVendor)
    var total
    //  (element.taxRate < 0 || element.taxRate == null) ? element.taxRate = 0 : null ;
    //  (element.orderQty < 0 || element.orderQty == null) ? element.orderQty = 0 : null ;
    //  (element.unitPrice < 0 || element.unitPrice == null) ? element.unitPrice = 0 : null ;
    (element.taxRate == 0) ? element.totalPrice = (element.orderQty * element.unitPrice) : null;
    if (element.taxRate > 0 && (data.priceType != 'fixedContractPrice' && data.contractTax != true)) {
      let taxAmount = (element.orderQty * element.unitPrice) * (element.taxRate / 100);
      element.totalPrice = (element.orderQty * element.unitPrice) + taxAmount;
    } else if (data.priceType == 'fixedContractPrice' && data.contractTax == true) {
      let requiredUnitPrice = this.utils.truncateNew((data.unitPrice / (Number(element.taxRate) + 100)) * 100)
      element.unitPrice = requiredUnitPrice
      total = element.orderQty *
        (Number(data.unitPrice) +
          (Number(data.unitPrice) *
            Number(element.taxRate)) /
          100);
      // total = element.orderQty * data.unitPrice;

    }
  }

  // \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\


  getVendors(element: any) {
    this.VendorBank = element.vendorList;
    this.vendorsBanks.next(this.VendorBank.slice());
    this.vendorFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
      if (!this.VendorBank) {
        return;
      }
      let search = this.vendorFilterCtrl.value;
      if (!search) {
        this.vendorsBanks.next(this.VendorBank.slice());
        return;
      } else {
        search = search.toLowerCase();
      }
      this.vendorsBanks.next(
        this.VendorBank.filter(VendorBank => VendorBank.vendorName.toLowerCase().indexOf(search) > -1)
      );
    });

    return this.vendorsBanks
  }

  onTabPressed(event: KeyboardEvent, element: any) {
    if (event.key === 'Tab') {
      this.getVendors(element);
    }
  }

  onEnterPressed(event: KeyboardEvent, element: any) {
    if (event.key === 'Enter') {
      this.getVendors(element);
    }
  }

  focusFunctionWithOutForm(element, value) {
    if (Number(element[value]) === 0) {
      element[value] = null;
    }
  }

  focusOutFunctionWithOutForm(element, value) {
    if (element[value] === null) {
      element[value] = 0
    }
  }

}
