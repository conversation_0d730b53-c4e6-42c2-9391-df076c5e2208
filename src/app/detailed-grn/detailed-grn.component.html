<div class="title">
  <button mat-raised-button class="button" style="float: left;" (click)=goBack()>
    <mat-icon>keyboard_backspace</mat-icon> Back
  </button>
  <button mat-button mat-raised-button class="button3" style="float: right;" (click)="save()"
  *ngIf="editAccess && (this.grn.grnType == 'po' || this.grn.grnType == 'ibt') && !purchaseStatus">
    Save
  </button>
  <button mat-raised-button class="button" style="float: right;" (click)="printpdf()" *ngIf="!purchaseStatus">
    Print
  </button>
  <button mat-raised-button class="button" style="float: right;" (click)="exportToExcel()" *ngIf="!purchaseStatus">
    Export
  </button>

  <button mat-raised-button class="button" style="float: right;" 
    (click)="fileInput.value=''; checkUpload()" *ngIf="!purchaseStatus">Upload Invoice
  </button>
  <input hidden type="file" #fileInput (change)="fileChange($event)" 
  accept="application/pdf, image/*"
  id="getFile" *ngIf="!purchaseStatus"/>
</div>

<div class="search-table-input fieldcontainer">
  <div class="row">
  <div class="col">
    <table class="table">
      <tbody>
        <tr>
          <th class="topItemkey" scope="row">GRN Id</th>
          <td>{{ grn.grnId }}</td>
        </tr>
        <tr>
          <th class="topItemkey" scope="row">Invoice Id</th>
          <td>
            <span *ngIf="!editAccess && (this.grn.grnType == 'po' || this.grn.grnType == 'ibt')">{{ grn.invoiceId }}</span>
            <mat-form-field appearance="outline" *ngIf="editAccess && this.grn.grnType == 'po'" >
              <mat-label>Invoice Number</mat-label>
              <input matInput placeholder="Invoice Number" [formControl]="invoiceNum" [readonly] = "purchaseStatus"/>
            </mat-form-field>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <div class="col">
    <table class="table">
      <tbody>
        <!-- <tr>
          <th class="topItemkey" scope="row">Request Type</th>
          <td>{{ grn.grnType }}</td>
        </tr> -->
        <tr *ngIf="grn.details.ibtId">
          <th class="topItemkey" scope="row">Request Id</th>
          <td>{{ grn.details.ibtId }}</td>
        </tr>
        <tr *ngIf="grn.details.poId">
          <th class="topItemkey" scope="row">Order Number</th>
          <td>{{ grn.details.poId }}</td>
        </tr>
        <tr>
          <th class="topItemkey" scope="row">Goods Received Date</th>
          <td>{{ (grn.grnDocumentDate | date: "EEEE, MMMM d, y") || '-'}}</td>
        </tr>
      </tbody>
    </table>
  </div>

  <div class="col">
    <table class="table">
      <tbody>
        <tr>
          <th class="topItemkey" scope="row">Receipient Name</th>
          <td>{{ grn.tenantName }} {{ this.receiveBranch | titlecase }}</td>
        </tr>
        <tr>
          <th class="topItemkey" scope="row">Sender Name</th>
          <td>{{ grn.vendorName }} {{ this.vendorBranch | titlecase }}</td>
        </tr>
      </tbody>
    </table>
  </div>

  <div class="col">
    <table class="table">
      <tbody>
        <tr>
          <th class="topItemkey" scope="row">GRN Date(System Entry Date)</th>
          <td>{{ this.utils.formatDateToUTC(grn.createTs) }}</td>
        </tr>
        <tr>
          <th class="topItemkey" scope="row">Vendor Invoice Date</th>
          <td>
            <span *ngIf="!editAccess && (this.grn.grnType == 'po' || this.grn.grnType == 'ibt')">{{ grn.invoiceDate | date: "EEEE, MMMM d, y" }}</span>
            <mat-form-field appearance="outline" *ngIf="editAccess && this.grn.grnType == 'po'" >
              <input matInput [matDatepicker]="picker" placeholder="Invoice Date" [disabled] = "purchaseStatus"
                [max]="today"  [formControl]="invoiceDate" [readonly] = "purchaseStatus"/>
              <mat-datepicker-toggle matSuffix [for]="picker">
                <mat-icon matDatepickerToggleIcon><img class="datepickIcon" src="./../../assets/calender.png" /></mat-icon>
              </mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
            </mat-form-field>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
</div>

<div class="matNavList mb-2">
  <mat-nav-list>
    <mat-expansion-panel (opened)="panelOpenState = true"
    (closed)="panelOpenState = false"   [disabled]="purchaseStatus">
      <mat-expansion-panel-header class="matExpansionTitle">
        <!-- <p (click)="previewInvoiceOpen()">Preview Invoice</p> -->
        <mat-panel-title class="topItemkey"  (click)="previewInvoiceOpen()">
          Preview Invoice
          <div class="spinner-border ml-2" role="status" *ngIf="previewInvoiceLoader">
            <span class="sr-only">Loading...</span>
          </div>
        </mat-panel-title>
        <mat-panel-description>
          <mat-icon *ngIf="panelOpenState == true">visibility</mat-icon>
          <mat-icon *ngIf="panelOpenState == false">visibility_off</mat-icon>
        </mat-panel-description>
      </mat-expansion-panel-header>
      <div class="d-flex justify-content-end m-2">
         <mat-icon *ngIf="previewInvoice" (click)="deleteInvoice(images)" matTooltip="delete invoice">delete</mat-icon>
      </div>
      <!-- <div *ngIf="previewInvoice">
        <button (click)="zoomIn()">Zoom In</button>
        <button (click)="zoomOut()">Zoom Out</button>
        <button (click)="download()">Download</button>
        <button (click)="rotate()">Rotate</button>
      </div> -->
      <app-image-viewer [images]="images" [idContainer]="'idOnHTML'" [loadOnInit]="true" *ngIf="previewInvoice">
      </app-image-viewer>
      <!-- [showOptions]="false"   #imageViewer  [allowDownload]="false" -->
          </mat-expansion-panel>
  </mat-nav-list>
</div>

<mat-card class="matcontent">
  <!-- <div class="searchInput">
    <mat-form-field appearance="none">
      <input matInput type="text" class="outline"  placeholder="Search" [(ngModel)]='searchText' (keyup)="doFilter($event.target.value)"/>
      <mat-icon matSuffix (click)="resetForm()" class="closebtn">close</mat-icon>
    </mat-form-field>
  </div> -->
  <!-- <div *ngIf="this.grn.grnType == 'po'" class="topSlideToggle mb-2">

    <mat-slide-toggle 
      (change)="addExtraFields()" [(ngModel)]="allExtraFieldFlag"  [disabled]="grn?.purchaseStatus">
      <span >Show Cess/Disct</span>
    </mat-slide-toggle>
    
    <mat-slide-toggle class="ml-3"  [(ngModel)]="otherChargesFieldFlag" (change)="addOtherCharges()"  [disabled]="grn?.purchaseStatus">
    <span>Show Other Charges</span>
  </mat-slide-toggle>
  </div> -->

  <div class="fieldboxToggle">

    <mat-form-field appearance="none">
      <!-- <label>Search</label> -->
      <input matInput type="text" class="outline" style="margin-top: -20px;" placeholder="Search" [(ngModel)]='searchText' (keyup)="doFilter($event.target.value)"/>
      <mat-icon matSuffix (click)="resetForm()" class="closebtn">close</mat-icon>
    </mat-form-field>

    <mat-slide-toggle class="ml-2" *ngIf="this.grn.grnType == 'po'" style="float: right !important;margin-top: 0px;" 
      (change)="addExtraFields()" [(ngModel)]="allExtraFieldFlag"  [disabled]="purchaseStatus">
      <span >Show Cess/Disct</span>
    </mat-slide-toggle>
    
    <mat-slide-toggle class="ml-2" *ngIf="this.grn.grnType == 'po'" style="float: right !important;margin-top: 0px;" 
    [(ngModel)]="otherChargesFieldFlag" (change)="addOtherCharges()"  [disabled]="purchaseStatus">
    <span>Show Other Charges</span>
  </mat-slide-toggle>

  </div>

  <section class="example-container-1 mat-elevation-z8">
  <table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort>
    <!-- Index Column -->
    <ng-container matColumnDef="index">
      <th mat-header-cell *matHeaderCellDef class="tableId"><b>#</b></th>
      <td mat-cell *matCellDef="let element; let i = index" class="name-cell tableId">
        {{ i + 1 }}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <!-- Name Column -->
    <ng-container matColumnDef="itemName" sticky>
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell">
        <b> Item Name</b>
      </th>
      <td mat-cell *matCellDef="let element" class="name-cell">
        <span *ngIf="this.grn.grnType == 'po'">
          {{ element.itemName | titlecase }}
        </span>
        <span *ngIf="this.grn.grnType == 'ibt'">
          {{ element.itemName.split("|")[2] | titlecase }}
        </span>
        <div style="font-size: 10px;" *ngIf="element.priceType">
          ( {{element.priceType}} - {{element.priceTypeValue}} )
        </div>
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
      <mat-divider></mat-divider>
    </ng-container>

    <ng-container matColumnDef="pkgName" sticky>
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell"><b> Pkg Name</b> </th>
      <td mat-cell *matCellDef="let element" class="name-cell">
        <span *ngIf="this.grn.grnType == 'po'">
          {{element.packages[0].packageName | titlecase}}
        </span>
        <span *ngIf="this.grn.grnType == 'ibt'">
          {{element.packageName | titlecase}}
        </span>

      </td>
      <mat-divider></mat-divider>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="entryType" sticky>
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell"><b> Entry Type</b> </th>
      <td mat-cell *matCellDef="let element" class="name-cell">
        {{element.entryType | titlecase}}
      </td>
      <mat-divider></mat-divider>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="unitPerPkg">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> <b>Units/Pkg</b> </th>
      <td mat-cell *matCellDef="let element">
        {{element.packages[0].unitPerPkg}}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <!-- difference Percent Column -->
    <ng-container matColumnDef="quantity">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Order Quantity</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ element.quantity }}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
      <!-- <td mat-footer-cell *matFooterCellDef> {{getTotal('quantity')}} </td> -->
    </ng-container>

    <ng-container matColumnDef="receivedQty">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Received Quantity</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ element.receivedQty }}
      </td>
      <td mat-footer-cell *matFooterCellDef> {{ this.utils.truncateNew(getTotal('receivedQty'))}}</td>

      <!-- <td mat-footer-cell *matFooterCellDef> {{getTotal('quantity')}} </td> -->
    </ng-container>

    <ng-container matColumnDef="pendingQty">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Pending Quantity</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ element.pendingQty }}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
      <!-- <td mat-footer-cell *matFooterCellDef> {{getTotal('quantity')}} </td> -->
    </ng-container>

    <ng-container matColumnDef="totalValue">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b> Total (incl.tax,etc)</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ this.utils.truncateNew(element.totalPrice) }}
      </td>
      <td mat-footer-cell *matFooterCellDef> {{ this.utils.truncateNew(getTotal('totalPrice'))}} </td>
      <!-- <td mat-footer-cell *matFooterCellDef> {{getTotal('unitPrice')*getTotal('quantity')}} </td> -->
    </ng-container>

    <ng-container matColumnDef="unitPrice">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b> Unit Cost</b>
      </th>
      <td mat-cell *matCellDef="let element">
        <span *ngIf="!editAccess && this.grn.grnType == 'po'">
          {{ this.utils.truncateNew(element.packages[0].packagePrice)}}
        </span>
        <!-- <input *ngIf="editAccess && this.grn.grnType == 'po'" class="input1" type="number" step="0.01" min="0"
            [(ngModel)]="element.packages[0].packagePrice"   (keyup)="changeAmounts($event.target.value , element)"
            (focus)="focusFunctionWithOutForm(element,'packages')" (focusout)="focusOutFunctionWithOutForm(element,'packages')"
            onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)"/> -->
        <span>
          {{ this.utils.truncateNew(element.unitPrice)}}
        </span>
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="subTotal">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b> Total (excl.tax)</b>
      </th>
      <td mat-cell *matCellDef="let element">
        <span *ngIf="this.grn.grnType == 'po'">
          {{ this.utils.truncateNew(element.subTotal)}}
        </span>
        <span *ngIf="this.grn.grnType == 'ibt'">
          N/A
        </span>
      </td>
      <!-- <td mat-footer-cell *matFooterCellDef></td> -->
      <td mat-footer-cell *matFooterCellDef> {{ this.utils.truncateNew(getTotal('subTotal'))}} </td>
    </ng-container>

    <ng-container matColumnDef="taxAmt">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b> Tax Amt</b>
      </th>
      <td mat-cell *matCellDef="let element">
        <span *ngIf="this.grn.grnType == 'po'">
          {{ this.utils.truncateNew(element.taxAmount )}}
        </span>
        <span *ngIf="this.grn.grnType == 'ibt'">
          N/A
        </span>
      </td>
      <!-- <td mat-footer-cell *matFooterCellDef></td> -->
      <td mat-footer-cell *matFooterCellDef> {{ this.utils.truncateNew(getTotal('taxAmount'))}} </td>
    </ng-container>

    <ng-container matColumnDef="cessAmt">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b> Cess Amt</b>
      </th>
      <td mat-cell *matCellDef="let element">
        <span *ngIf="this.grn.grnType == 'po'">
          {{ this.utils.truncateNew(element.cessAmt)}}
        </span>
        <span *ngIf="this.grn.grnType == 'ibt'">
          N/A
        </span>
      </td>
      <!-- <td mat-footer-cell *matFooterCellDef></td> -->
      <td mat-footer-cell *matFooterCellDef> {{this.utils.truncateNew(getTotal('cessAmt'))}} </td>
    </ng-container>

    <ng-container matColumnDef="discnt">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b> Discount</b>
      </th>
      <td mat-cell *matCellDef="let element">
        <span *ngIf="this.grn.grnType == 'po'">
          {{ this.utils.truncateNew(element.discAmt)}}
        </span>
        <span *ngIf="this.grn.grnType == 'ibt'">
          N/A
        </span>
      </td>
      <!-- <td mat-footer-cell *matFooterCellDef></td> -->
      <td mat-footer-cell *matFooterCellDef> {{ this.utils.truncateNew(getTotal('discAmt'))}} </td>
    </ng-container>

    <ng-container matColumnDef="itemCode">
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell"><b> Item Code</b> </th>
      <td mat-cell *matCellDef="let element" class="name-cell"> {{element.itemCode}} </td>
      <td mat-footer-cell *matFooterCellDef>Total</td>
    </ng-container>

    <ng-container matColumnDef="hsnCode">
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell"><b>Hsn Code</b> </th>
      <td mat-cell *matCellDef="let element" class="name-cell"> {{element.hsnCode}} </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="extraAmt">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b> Extra Amt</b>
      </th>
      <td mat-cell *matCellDef="let element">
        <span *ngIf="this.grn.grnType == 'po'">
          {{this.utils.truncateNew(element.extraAmt)}}
        </span>
        <span *ngIf="this.grn.grnType == 'ibt'">
          N/A
        </span>
      </td>
      <!-- <td mat-footer-cell *matFooterCellDef></td> -->
      <td mat-footer-cell *matFooterCellDef> {{ this.utils.truncateNew(getTotal('extraAmt'))}} </td>
    </ng-container>

  <div  *ngIf='this.grn.directIndentAreas'>
    <ng-container  *ngFor="let area of this.grn.directIndentAreas" matColumnDef="{{ area }}">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>  {{ area }}</b>
      </th>
      <td mat-cell *matCellDef="let element">
        <span>
          {{element.splitUp[area]}}
        </span>
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>
  </div>

    <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky:true;"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
    <tr mat-footer-row *matFooterRowDef="displayedColumns"></tr>
    <!-- <tr mat-footer-row *matFooterRowDef="displayedColumns"></tr> -->
  </table>
</section>
  <div class="dataMessage" *ngIf="dataSource?.data.length == 0"> No Data Available </div>

  <div *ngIf="otherChargesFieldFlag" class="mt-3 mb-3">
    <div>
      <span class="otherchargeHeading topItemkey">Freight/Transportation charges ₹</span>
      <input matInput type="number" class="outline othercharge" [(ngModel)]="othercharges" placeholder="charges ₹" disabled/>
    </div><br>
    <div *ngIf="labourCharges != 0">
      <span class="otherchargeHeading topItemkey">Labour charges ₹</span>
      <input matInput type="number" class="outline othercharge" [(ngModel)]="labourCharges" placeholder="charges ₹" disabled/>
    </div><br>
    <div *ngFor="let tax of grn.otherTax; let i = index" style="padding-bottom: 22px !important;">
      <span class="otherchargeHeading topItemkey">{{ tax.taxName }}</span>
      <input matInput class="outline otherTax" [(ngModel)]="tax.value" disabled/>
    </div>
    <div>
      <span class="otherchargeHeading topItemkey">Grand total ₹</span>
      <input matInput class="outline othercharge" [(ngModel)]="grandTotal" placeholder="Total ₹" disabled/>
    </div>
  </div>


  <mat-card-actions>
    <!-- <button <mat-butto></mat-butto>n color="primary" (click)="approvePr()" [disabled]="disableApprBtn">{{user.uType == 'vendor'? 'Approve Pr' : 'Create PO'}}</button> -->
  </mat-card-actions>
</mat-card>