.otherchargeHeading{
    margin-left: 600px;
}

.othercharge{
    width: 120px;
    float: right;
  }
  

.matNavList{
margin-left: 2%;
margin-right: 2%;
}
  
.topSlideToggle{
  float: right;
}

example-headers-align .mat-expansion-panel-header-title, 
.example-headers-align .mat-expansion-panel-header-description {
  flex-basis: 0;
}

.example-headers-align .mat-expansion-panel-header-description {
  justify-content: flex-end !important;
}

::ng-deep span.mat-expansion-indicator.ng-trigger.ng-trigger-indicatorRotate.ng-star-inserted {
  display: none !important;
}

::ng-deep mat-panel-description.mat-expansion-panel-header-description {
  justify-content: flex-end !important;
}

  .example-container-1{
    width: 100%;
    overflow-y: auto;
    max-height: 300px;
  }

  .searchInput{
    margin-top: -20px !important;
  }

  .otherTax{
    width: 120px;
    float: right;
  }

// /* styles.css or your component-specific CSS file */
// ::ng-deep .app-image-viewer .ng2-image-viewer-buttons .ng2-image-viewer-button:nth-child(9) {
//   display: none !important; /* Hide the download button */
// }
