import { Component, ElementRef, OnInit ,Renderer2,ViewChild} from '@angular/core';
import { PurchasesService, ShareDataService, AuthService, BranchTransferService } from '../_services';
import { GlobalsService } from '../_services/globals.service';
import { MatSort, Sort, MatTableDataSource } from '@angular/material';
import { SelectionModel } from '@angular/cdk/collections';
import { Location } from '@angular/common';
import { UtilsService } from '../_utils/utils.service';
import { NotificationService } from '../_services/notification.service';
import { ActivatedRoute, Router, Params } from '@angular/router';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { FormControl } from '@angular/forms';

@Component({
  selector: 'app-detailed-grn',
  templateUrl: './detailed-grn.component.html',
  styleUrls: ['./detailed-grn.component.scss', "./../../common-dark.scss"]
})
export class DetailedGrnComponent implements OnInit {
  @ViewChild('imageViewer') imageViewer: ElementRef;
  panelOpenState: boolean = false;
  @ViewChild('fileInput') fileInput: any;
  grn: any;
  user: any;
  fileObj: any ;
  dataSource: MatTableDataSource<any> ;
  displayedColumns: string[] ;
  receiveBranch: string ;
  fileContent: any = '' ;
  vendorBranch: string ;
  selection = new SelectionModel<any>(true, []);
  allExtraFieldFlag: boolean  = true ;
  otherChargesFieldFlag : boolean = true;
  public isValidFormat: boolean = true;
  othercharges: any;
  labourCharges: any;
  grandTotal: any;
  totalAmount: any;
  images : any;
  previewInvoiceLoader: boolean = false;
  previewInvoice: boolean = false;
  windowCurrentUrl: string;
  purchaseStatus: any;
  searchText: string;
  config = {
    btnClass: "hide",
    containerBackgroundColor: '#fff',
    allowKeyboardNavigation: true, 
    allowFullscreen: false, 
    wheelZoom: true,
    btnShow: {    
      next: false,
      prev: false,
      zoomIn: false,
      zoomOut: false,
      rotateClockwise: false,
      rotateCounterClockwise: false 
    }
  };
  enableDownload: boolean = false;

  currentImageIndex: number = 0;
  zoomLevel: number = 100; // Initial zoom level
  angle: number = 0; 
  access: string;
  checkAccess: boolean;
  invoiceDate = new FormControl();
  invoiceNum = new FormControl();
  deleteAccess: boolean;
  editAccess: boolean;
  constructor(
    private router : Router,
    private notifyService: NotificationService,
    private sharedData: ShareDataService, 
    private auth: AuthService,
    private loc: Location, 
    public utils: UtilsService, 
    private purchases: PurchasesService,
    private activateRoute : ActivatedRoute,
    private renderer: Renderer2,
    private sanitizer: DomSanitizer,
    private branchTransfer: BranchTransferService,

    ) {
  		this.user = this.auth.getCurrentUser();
      this.access = sessionStorage.getItem('access');
      let dataArray = this.access.split(',');
      this.checkAccess = dataArray.includes(this.user.role)

      this.activateRoute.params.subscribe((params: Params)=>{
        if(params.purchaseStatus){
          var windowLocation = window.location.href;
          this.windowCurrentUrl = windowLocation.split('/')[4].split(';')[0]
          this.purchaseStatus = params.purchaseStatus
        }
      });
  }

  
  checkUpload() {
    document.getElementById("getFile").click()
  }

  downloadImage() {
    // Implement download logic here
    // For example:
    const imageURL = this.images[0]; // Assuming you're downloading the first image
    const link = document.createElement('a');
    link.href = imageURL;
    link.download = 'image.jpg'; // Set desired filename here
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  fileChange(event:any) {
    let files = event.target.files;
    let file = files[0];
    this.fileObj = file;
    if ( (this.fileObj.type == "application/pdf" || this.fileObj.type == "image/jpeg" || this.fileObj.type == "image/png")) {
      this.isValidFormat = true;
      var reader = new FileReader();
      reader.onloadend = (e) => {
        this.fileContent = reader.result;
        this.uploadFile(this.fileContent, this.fileObj.name, this.fileObj.type);
      };
      reader.readAsDataURL(file);
    } else {
      this.isValidFormat = false;
      this.utils.snackBarShowWarning("Please select PDF and IMAGE format only" );
    } 
  }


  uploadFile(file:any, name:any, type:any) {
    this.purchases.uploadInvoice({
      fileObj: file,
      name: name,
      type: type,
      tenantId: this.user.tenantId,
      restaurantId: this.grn.restaurantId,
      grnId: this.grn.grnId
    }).subscribe((response) => {
      if (response.success) {
        this.utils.snackBarShowSuccess(response["message"]);
      } else {
        this.utils.snackBarShowError(response["message"]);
      }
    });
  }


  ngOnInit() {
    this.tenantDetail();
  	this.sharedData.currGrn.subscribe(grn => {
  		if (!grn.grnId)
  			this.loc.back();
  		this.grn = grn;
  		this.receiveBranch = grn['restaurantId'].split('@')[1];
  		if (grn['vendorBranch'] != null) {
  			this.vendorBranch = grn['vendorBranch'].split('@')[1];
  		} else {
  			this.vendorBranch = '';
  		}
  		this.grn.deliveryDate = new Date(grn.deliveryDate);
  		this.grn.createTs = new Date(grn.createTs);
  		this.dataSource = new MatTableDataSource < any > ();
  		this.dataSource.data = this.grn.grnItems;
  		this.displayedColumns = GlobalsService.detailedIbtGrnColumns;
      this.selection = new SelectionModel<any>(true, this.dataSource.data);
  		if (this.grn.grnType == 'po') {
  			this.displayedColumns = ['index','itemCode','hsnCode', 'itemName', 'pkgName', 'unitPerPkg', 'quantity', 'receivedQty', 'unitPrice', 'subTotal', 'taxAmt', 'totalValue']
  			if (this.grn.directIndent) {
  				if (this.grn.directIndentAreas && this.grn.directIndentAreas.length > 0) {
  					this.displayedColumns = ['index','itemCode','hsnCode', 'itemName', 'pkgName', 'unitPerPkg', 'quantity', 'receivedQty', 'unitPrice', 'subTotal', 'taxAmt', 'totalValue'].concat(this.grn.directIndentAreas);
  				} else {
  					this.displayedColumns = ['index','itemCode','hsnCode', 'itemName', 'pkgName', 'unitPerPkg', 'quantity', 'receivedQty', 'unitPrice', 'subTotal', 'taxAmt', 'totalValue']
  				}
  			}
  		}
  	}, err => {
  		console.error(err)
  	});

    this.totalAmount = this.grn.grnItems.reduce(function(accumulator, currentValue) {
  		return accumulator + currentValue.totalPrice;
  	}, 0);
    let total = this.getTotal('totalPrice') ;
  	this.othercharges = this.grn.otherCharges ? this.grn.otherCharges : 0;
    this.labourCharges = this.grn.labourCharges ? this.grn.labourCharges : 0;
    let taxTotal = this.grn.otherTax ? this.grn.otherTax.reduce((acc, item) => acc + item.value, 0): 0 ;  
  	this.grandTotal =  total + this.othercharges + this.labourCharges + taxTotal;
    this.addExtraFields() ;
    this.invoiceDate.setValue(this.grn.invoiceDate)
    this.invoiceNum.setValue(this.grn.invoiceId)

  }

  printpdf() {
    this.purchases.printpdfs(this.grn, 'Grn').subscribe(data => {
    //  window.open('data:application/pdf;base64,' + data.eFile);

      // var pdfData = atob(data.eFile);
      // var blob = new Blob([pdfData], { type: 'application/pdf' });
      // var pdfUrl = URL.createObjectURL(blob);
      // window.open(pdfUrl, '_blank');

      // var pdfData = atob(data.eFile);
      // var arrayBuffer = new ArrayBuffer(pdfData.length);
      // var uint8Array = new Uint8Array(arrayBuffer);
      // for (var i = 0; i < pdfData.length; i++) {
      //   uint8Array[i] = pdfData.charCodeAt(i);
      // }
      // var blob = new Blob([arrayBuffer], { type: 'application/pdf' });
      // var url = URL.createObjectURL(blob);
      // window.open(url, '_blank');

      this.purchases.globalPrintPdf(data.eFile);
        
    });
  }
  
  exportToExcel() {
    this.purchases.exportToExcel(this.grn, 'Grn').subscribe(data => {
      window.open('data:text/csv;base64,' + data.eFile);
    });
  }

  getTotal(key: string) {
    return this.utils.getTotal(this.dataSource.data, key);
  }

  goBack() {
    if(this.purchaseStatus){
      this.loc.back()
    }else{
    this.router.navigate(['/home/<USER>'])
  }
  }

  addExtraFields() {
  	if (this.allExtraFieldFlag) {
  		this.displayedColumns.splice(10, 0, 'extraAmt', 'discnt', 'cessAmt')
  	} else {
  		if (this.grn.directIndent) {
  			if (this.grn.directIndentAreas && this.grn.directIndentAreas.length > 0) {
  				this.displayedColumns = ['index','itemCode','hsnCode', 'itemName', 'pkgName', 'unitPerPkg', 'quantity', 'receivedQty', 'unitPrice', 'subTotal', 'taxAmt', 'totalValue'].concat(this.grn.directIndentAreas);
  			} else {
  				this.displayedColumns = ['index','itemCode','hsnCode', 'itemName', 'pkgName', 'unitPerPkg', 'quantity', 'receivedQty', 'unitPrice', 'subTotal', 'taxAmt', 'totalValue']
  			}
  		} else {
  			this.displayedColumns = ['index','itemCode', 'hsnCode','itemName', 'pkgName', 'unitPerPkg', 'quantity', 'receivedQty', 'unitPrice', 'subTotal', 'taxAmt', 'totalValue']
  		}
  	}
  }

  // addOtherCharges() {
    // if(this.grn.labourCharges && this.grn.otherCharges){
    //   this.otherChargesFieldFlag = true;
    // }
    // if(this.otherChargesFieldFlag == true){
    //   this.otherChargesFieldFlag = false
    // }else{
    //   this.otherChargesFieldFlag = true
    // }

  // }

  addOtherCharges(){
    this.otherChargesFieldFlag = this.otherChargesFieldFlag
  }

  previewInvoiceOpen() {
    this.previewInvoiceLoader = true 
    var reqObj={
      grnId : this.grn.grnId,
      tenantId : this.grn.tenantId,
      restaurantId : this.grn.restaurantId
    }
    this.purchases.retrieveInvoice(reqObj).subscribe(res => {
      if (res['success']){
        this.images = res['data'];
        // this.images = this.sanitizer.bypassSecurityTrustResourceUrl(res['data']);
        // this.images = this.saveAsFile(imageUrls);
        // this.images = this.images.changingThisBreaksApplicationSecurity;
        this.previewInvoiceLoader = false;
        if(this.images.length > 0){
          this.previewInvoice = true;
          this.previewInvoiceLoader = false;
        }else {
          this.previewInvoice = false;
          this.utils.snackBarShowWarning("No invoices to display");
        }  
      }
    }, err => {})
  }

  saveAsFile(data) {
    const blob = new Blob([data], { type: 'application/octet-stream' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    // a.download = 'invoice.pdf'; // Change the filename as needed
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
    return a
}

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }
  
  resetForm() {
    this.searchText = ''
    this.dataSource = new MatTableDataSource<any>();
    this.dataSource.data = this.grn.grnItems;
  }

  // uploadFile(file:any, name:any, type:any) {
    // this.purchases.uploadInvoice({
    //   fileObj: file,
    //   name: name,
    //   type: type,
    //   tenantId: this.user.tenantId,
    //   restaurantId: this.grn.restaurantId,
    //   grnId: this.grn.grnId
    // }).subscribe((response) => {
    //   if (response.success) {
    //     this.utils.snackBarShowSuccess(response["message"]);
    //   } else {
    //     this.utils.snackBarShowError(response["message"]);
    //   }
    // });
  // }

  deleteInvoice(images){
    this.purchases.deleteInvoice({
      fileObj: images,
      tenantId: this.user.tenantId,
      restaurantId: this.grn.restaurantId,
      grnId: this.grn.grnId
    }).subscribe((response) => {
      if (response.success) {
        this.utils.snackBarShowSuccess('Invoice deleted successfully');
        this.previewInvoiceOpen()
      } else {
        this.utils.snackBarShowError('something went wrong');
      }
    });
  }

  zoomIn() {
    this.zoomLevel += 10; // Increase zoom level by 10%
  }

  zoomOut() {
    this.zoomLevel -= 10; // Decrease zoom level by 10%
  }

  download() {
    // Get the current image URL
    const imageUrl = this.images[this.currentImageIndex];
    // Simulate downloading, replace it with actual download logic
    console.log('Downloading image:', imageUrl);
  }

  rotate() {
    // Simulate rotating by increasing the angle by 90 degrees
    this.angle = (this.angle + 90) % 360;
    console.log('Rotating image. New angle:', this.angle);
  }

  focusFunctionWithOutForm(element , value){
    if(value === 'packages'){
      if(element['packages'][0].packagePrice === 0){
        element['packages'][0].packagePrice  = null;
      }
    }
    else {
      if (Number(element[value]) === 0){
        element[value] = null;
      }
    }
   
  }
  
  focusOutFunctionWithOutForm(element , value){
    if(value === 'packages'){
      if(element['packages'][0].packagePrice === null){
        element['packages'][0].packagePrice  = 0;
      }
    }
    else {
      if(element[value] === null){
        element[value] = 0
      }
    }
  }
  changeAmounts(value , element){
    this.grn.grnItems.forEach(element => {
      let afterDiscount = 0;
      let tax = 0;
      let totalPrice = 0
      afterDiscount = (element.receivedQty * element.packages[0].packagePrice) - element.discAmt
      tax = afterDiscount * (element.taxRate / 100)
      element.subTotal = afterDiscount;
      element.taxAmount = tax;
      totalPrice += afterDiscount + element.cessAmt + element.extraAmt + tax;
      element.totalPrice = totalPrice
      element.unitPrice = element.packages[0].packagePrice
    });
    let total = this.getTotal('totalPrice') ;
  	this.othercharges = this.grn.otherCharges ? this.grn.otherCharges : 0;
    this.labourCharges = this.grn.labourCharges ? this.grn.labourCharges : 0;
    let taxTotal = this.grn.otherTax ? this.grn.otherTax.reduce((acc, item) => acc + item.value, 0): 0 ;  
  	this.grandTotal =  total + this.othercharges + this.labourCharges + taxTotal;
  }

  save() {
    let obj = {
      tenantId : this.grn.tenantId,
      restaurantId : this.grn.restaurantId,
      items: this.grn.grnItems,
      grnId: this.grn.grnId,
      InvNo: this.invoiceNum.value,
      invoiceDate:this.invoiceDate.value ?  this.utils.dateCorrection(this.invoiceDate.value) : undefined,
    }
    this.purchases.saveGrn(obj).subscribe(data => {
      data.result ? this.utils.snackBarShowSuccess(data.message) : this.utils.snackBarShowError(data.message)
    })
  }


  tenantDetail() {
    let obj = {
      tenantId: this.user.tenantId
    }
    this.branchTransfer.tenantDetails(obj).subscribe(res => {
      if (res.result == 'success') {
        let tenantDetails = res.data[0].permission 
        let deleteAccess = res.data[0] && res.data[0].permission && res.data[0].permission.POAccess ? res.data[0].permission.POAccess.delete : false;
        let editAccess = res.data[0] && res.data[0].permission && res.data[0].permission.POAccess ? res.data[0].permission.POAccess.edit : false;
        if (deleteAccess) {
          let rolesList = (res.data[0] && res.data[0].permission && res.data[0].permission.POAccess) ? res.data[0].permission.POAccess.deleteAccess : [];
          this.deleteAccess = (rolesList.some(role => role.toLowerCase() === this.user.role.toLowerCase())) ? true : false ;
        } else {
          this.deleteAccess = false ;
        }
        if (editAccess) {
          let rolesList = (res.data[0] && res.data[0].permission && res.data[0].permission.POAccess) ? res.data[0].permission.POAccess.editAccess : [];
          this.editAccess = (rolesList.some(role => role.toLowerCase() === this.user.role.toLowerCase())) ? true : false ;
        } else {
          this.editAccess = false ;
        }
      } else {
        this.deleteAccess = false ;
        this.editAccess = false ;
      }
    });
  }

}
