<div class="title">
  <button
    mat-raised-button
    class="button"
    style="float: left; margin-left: 0px;"
    (click) = goBack()
  >
    <mat-icon>keyboard_backspace</mat-icon> Back to IBTS
  </button>
  <button
    mat-raised-button
    class="button3"
    *ngIf="isIncoming"
    (click)="generateGrnFromIbt()"
    [disabled]="disableReceive || approvalRequired || isDone "
    style="float: right;"
  >
  {{ this.ibt.ibtType =="DIRECT" ? 'Receive' : 'Receive IBT' }}
  </button>
  <div style="float: right;" [matTooltip]=" approvalRequired  ? 'Approval required' : '' ">
      <button
      mat-raised-button
      class="button3"
      *ngIf="!isIncoming"
      (click) = "dispatchIbt()"
      [disabled]="disableDispatch || approvalRequired || isDone"
      style="float: right;"
    >
      {{ this.ibt.ibtType === 'DIRECT' ? 'Dispatch' : 'Dispatch IBT' }}
    </button>
  </div>

  <button
    mat-raised-button
    class="button"
    style="float: right;"
    (click)="printPdf()"
  >
    Print
  </button>
  <button
    mat-raised-button
    class="button"
    style="float: right;"
    (click)="exportToExcel()"
  >
    Export
  </button>

  <button
    mat-raised-button
    *ngIf="!isIncoming"
    class="button"
    style="float: right;"
    (click)="convertPo()"
  >
    Convert to PR
  </button>

</div>
<div class="search-table-input fieldcontainer">


  <div class="row">
    <div class="col">
      <table class="table">
        <tbody>
          <tr>
            <th class="topItemkey" scope="row">IBT ID</th>
            <td>{{ ibt.ibtId }}</td>
          </tr>
          <tr >
            <th class="topItemkey" scope="row">Requested Date</th>
            <td>{{ (this.utils.formatDateToUTC(this.ibt.createTs) || 'N/A') }} </td>
          </tr>
          <tr *ngIf="!isIncoming">
            <th class="topItemkey" scope="row">PR ID</th>
            <td>{{ ibt.prId?.length ? ibt.prId.join(', ') : '-' }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="col">
      <table class="table">
        <tbody>
          <tr>
            <th class="topItemkey" scope="row">Source</th>
            <td>{{ ibt.fromBranch.location }}</td>
          </tr>
          <tr >
            <th class="topItemkey" scope="row">Dispatched Date</th>
            <td>{{ this.ibt.dispatchedDate ? (this.utils.formatDateToUTC(this.ibt.dispatchedDate)) : 'N/A' }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  
    <div class="col">
      <table class="table">
        <tbody>
          <tr>
            <th class="topItemkey" scope="row">Destination</th>
            <td>{{ ibt.toBranch.location }}</td>
          </tr>
          <tr>
            <th class="topItemkey" scope="row">Received Date</th>
            <td>{{ this.ibt.receivedDate ? (this.utils.formatDateToUTC(this.ibt.receivedDate)) : 'N/A' }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="col">
      <table class="table">
        <tbody>
          <tr >
            <th class="topItemkey" scope="row">Expected Date</th>
            <td>{{ ibt.demandTs ? (ibt.demandTs | date: "EE, MMMM d, y") : 'N/A' }}</td>
          </tr>
          <tr >
            <th class="topItemkey" scope="row">Workarea</th>
            <td> {{ ibt.workArea ? ibt.workArea : 'store' }}</td>
          </tr>
        </tbody>
      </table>
    </div>
</div>

<div class="searchInput">
  <mat-form-field appearance="none">
    <!-- <label>Search</label> -->
    <input matInput type="text" class="outline"  placeholder="Search" [(ngModel)]='searchText' (keyup)="doFilter($event.target.value)"/>
    <mat-icon matSuffix (click)="resetForm()" class="closebtn">close</mat-icon>
  </mat-form-field>
</div>

<section class="example-container-1 mat-elevation-z8">
    <table
      #table
      mat-table
      [dataSource]="dataSource"
      matSortActive="itemName"
      matSortDirection="asc"
      matSort
    >
      <!-- Index Column -->
      <ng-container matColumnDef="index">
        <th mat-header-cell *matHeaderCellDef class="tableId"><b>#</b></th>
        <td mat-cell *matCellDef="let element; let i = index" class="name-cell tableId">
          {{ i + 1 }}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <!-- Name Column -->
      <ng-container matColumnDef="itemName" sticky>
        <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell">
          <b> Item Name</b>
        </th>
        <td mat-cell *matCellDef="let element" class="name-cell">
          {{ extractLastPart(element.itemName) | titlecase }}
        </td>
        <td mat-footer-cell *matFooterCellDef> Total </td>
      </ng-container>

      <!-- difference Percent Column -->
      <ng-container matColumnDef="quantity">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Ordered Quantity</b>
        </th>
        <td mat-cell *matCellDef="let element">
          {{ this.utils.truncateNew(element.quantity) }} 
          <span *ngIf="element.quantity > 0 && portionValueQty(element) !== ''">
            ({{ this.utils.truncateNew(portionValueQty(element)) }} portions)
          </span>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>            

      <ng-container matColumnDef="dispatchHistory">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Dispatched Qty</b>
        </th>
        <td mat-cell *matCellDef="let element">
          {{ getItemCalculation(element) }}
          <span *ngIf="portionValueDispatchQty(element) !== ''"> 
            ({{ this.utils.truncateNew(portionValueDispatchQty(element)) }} portions)</span>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="receivedHistory">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Received Qty</b>
        </th>
        <td mat-cell *matCellDef="let element">
          {{ this.utils.truncateNew(getReceivedCount(element))}}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="shortageHistory">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Shortage History</b>
        </th>
        <td mat-cell *matCellDef="let element">
          {{getShortageCount(element)}}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="receivedQty">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b *ngIf="isIncoming">To Be Received Quantity (Uom)</b>
          <b *ngIf="!isIncoming">To Be Dispatched Quantity (Uom)</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <input
          *ngIf="element.recPendingQty >= 0 && isIncoming"
          style="text-align: center; width: 110px;"
          type="number" step="0.01" min="0"
          [(ngModel)]="element.receivedQty"
          (ngModelChange)="receivedQtyChange(element)"
          (keyup)="validateDispatch($event, element)"
          [ngClass]="{'disabled-input': element.selectedOption == 'portion'}"
          (focus)="focusFunctionWithOutForm(element,'receivedQty')" (focusout)="focusOutFunctionWithOutForm(element,'receivedQty')"/>
          <input
            *ngIf="element.pendingQty >= 0 && !isIncoming"
            (keyup)="validateDispatch($event , element)"
            style="text-align: center; width: 110px;"
            type="number" step="0.01" min="0"
            [(ngModel)]="element.dispatchQty"
            (ngModelChange)="dispatchedQtyChange(element)"
            [class.invalid-border]="element.inStock < element.dispatchQty"
            [ngClass]="{'disabled-input': element.selectedOption == 'portion'}"
            (focus)="focusFunctionWithOutForm(element,'dispatchQty')" (focusout)="focusOutFunctionWithOutForm(element,'dispatchQty')"/>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="receivedQtyPortion">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b *ngIf="isIncoming">To Be Received Quantity (Portion)</b>
          <b *ngIf="!isIncoming">To Be Dispatched Quantity (Portion)</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <input
          *ngIf="isIncoming"
          style="text-align: center; width: 110px;"
          type="number" step="0.01" min="0"
          [(ngModel)]="element.receivedQtyPortion"
          (ngModelChange)="receivedQtyPortionChange(element)"
          (keyup)="validateDispatchPortion($event, element)"
          [ngClass]="{'disabled-input': element.selectedOption == 'uom'}"
          (focus)="focusFunctionWithOutForm(element,'receivedQtyPortion')" (focusout)="focusOutFunctionWithOutForm(element,'receivedQtyPortion')"/>
          <input
            *ngIf="!isIncoming"
            (keyup)="validateDispatchPortion($event , element)"
            style="text-align: center; width: 110px;"
            type="number" step="0.01" min="0"
            [(ngModel)]="element.dispatchQtyPortion"
            (ngModelChange)="dispatchedQtyPortionChange(element)"
            [class.invalid-border]="element.inStockPortion < element.dispatchQtyPortion"
            [ngClass]="{'disabled-input': element.selectedOption == 'uom'}"
            (focus)="focusFunctionWithOutForm(element,'dispatchQtyPortion')" (focusout)="focusOutFunctionWithOutForm(element,'dispatchQtyPortion')"/>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="pendingQty">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b *ngIf="isIncoming">Dispatched Quantity</b>
          <b *ngIf="!isIncoming">Pending Quantity</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <span *ngIf="isIncoming">{{ this.utils.truncateNew(element.recPendingQty - element.pendingQty ) }}</span>
          <span *ngIf="!isIncoming">{{ this.utils.truncateNew(element.pendingQty) }}</span>
          <span *ngIf="!isIncoming && element.pendingQty > 0 && portionValuePendingQty(element) !== ''"> 
            ({{ this.utils.truncateNew(portionValuePendingQty(element)) }} portions)</span>
          <span *ngIf="isIncoming && element.pendingQty > 0 && portionValuePendingQty(element) !== ''"> 
            ({{ this.utils.truncateNew(portionValueRecPendingQty(element)) }} portions)</span>  
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="shortageQty">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b> Shortage Quantity </b>
        </th>
        <td mat-cell *matCellDef="let element">
          <input matInput appearance="outline"
          style="text-align: center; width: 110px;"
          type="number" step="0.01" min="0"
          placeholder="Shortage Quantity"
          [(ngModel)]='element.shortageQty'disabled/>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="reason">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b> Reason </b>
        </th>
        <td mat-cell *matCellDef="let element">
            <textarea matInput rows="3" maxlength="200" wrap="soft" cols="50" style="height: 40px;font-size: 14px;padding-top: 15px;"
            [(ngModel)]=element.reason
            (keyup)="permissionCall($event.target.value,element)"
            [disabled] = "element.shortageQty == 0"
            ></textarea>
            <!-- <div *ngIf = "((element.quantity - element.pendingQty ) - element.receivedQty) != 0 && !element.reason && !ibt.status.delivered" class = "requiredField"  >Reason is required!</div> -->
            <div *ngIf = "element.shortageQty > 0 && !element.reason" class = "requiredField"  >Reason is required!</div>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="shortageType">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b> Shortage Type </b>
        </th>
        <td mat-cell *matCellDef="let element">
            <mat-form-field appearance="none" >
              <mat-select [(ngModel)]="element.shortageType" [disabled] = "element.shortageQty == 0">
                <mat-option value="transit">Transit</mat-option>
                <mat-option value="others">Others</mat-option>
              </mat-select>
            </mat-form-field>
          </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="forcastQnty">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Predicted Qty</b>
        </th>
        <td mat-cell *matCellDef="let element">
            {{this.utils.truncateNew(element?.forecastedQty)}}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="totalOutletStock">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Outlet Stock ({{ibt.toBranch.location}})</b>
        </th>
        <td mat-cell *matCellDef="let element">
            {{this.utils.truncateNew(element?.totalInHandStock)}}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>


      <ng-container matColumnDef="expectedConsumption">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Today Expected Consumption</b>
        </th>
        <td mat-cell *matCellDef="let element">
            {{element?.expectedConsumption ? this.utils.truncateNew(element.expectedConsumption) : '-' }}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>
      

      <ng-container matColumnDef="inStock">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>In Store ({{ibt.fromBranch.location}})</b>
        </th>
        <td mat-cell *matCellDef="let element">
            {{this.utils.truncateNew(element.inStock)}}
            <span *ngIf="element.inStock > 0 && portionValueInstore(element) !== ''">
              ({{ this.utils.truncateNew(portionValueInstore(element)) }} portions)
            </span>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="totalValue">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b> Total Value</b>
        </th>
        <td mat-cell *matCellDef="let element">
          {{ this.utils.truncateNew(getItemTotal(element),2) }}
        </td>
        <td mat-footer-cell *matFooterCellDef>{{ this.utils.truncateNew(getTotal(),2) }}</td>
      </ng-container>

      <ng-container matColumnDef="unitPrice">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b> WAC(incl.tax,etc) </b>
        </th>
        <td mat-cell *matCellDef="let element">
          {{ this.utils.truncateNew(element.unitPrice,2)}}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="uom">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b> UOM</b></th>
        <td mat-cell *matCellDef="let element">
          <mat-form-field appearance="outline" style="width: 100px !important;" class="uomAdj">
            <mat-select [(ngModel)]="element.selectedOption" [disabled]="disableUom(element)">
              <mat-option value="uom">{{ (element.uom || '-') | titlecase }}</mat-option>
              <mat-option value="portion">Portion</mat-option>
            </mat-select>
          </mat-form-field>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>      

      <ng-container matColumnDef="pkgName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b> Package Name</b></th>
        <td mat-cell *matCellDef="let element">
          <ng-container *ngIf = 'element.packageName == "N/A"'>
            {{ element.uom || '-' }}
          </ng-container>
          <ng-container *ngIf = 'element.packageName != "N/A"'>
            {{ element.packageName || '-' }}
          </ng-container>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="entryType">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b> Entry Type</b></th>
        <td mat-cell *matCellDef="let element">
          {{ element.entryType || '-' }}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      <tr mat-footer-row *matFooterRowDef="displayedColumns"></tr>
    </table>
  </section>
    <div class="dataMessage" *ngIf="dataSource?.data.length == 0"> No Data Available </div>
