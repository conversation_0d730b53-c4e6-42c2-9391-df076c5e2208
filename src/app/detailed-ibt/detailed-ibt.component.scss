input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
input[type=number] {
  -moz-appearance: textfield;
}

.example-container-1{
  max-height: 550px;
  overflow-y: auto;
}

.CloseBtn{
  float: right;
  margin-bottom: -1px;
}

.searchInput{
  display: flex;
  margin-top: -25px !important;
}

.requiredField {
  color: #ff0000;
}

.invalid-border {
  border-color: red; /* Change this to your desired color */
}

.disabled-input {
  opacity: 0.6;
  pointer-events: none;
}

::ng-deep .uomAdj{
  margin-left: 10px !important;
  margin-top: 10px !important;
  margin-bottom: 10px !important;
  font-size: 12px !important;
}