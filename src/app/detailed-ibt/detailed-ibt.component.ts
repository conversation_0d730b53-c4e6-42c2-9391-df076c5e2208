import { Component, OnInit } from '@angular/core';
import { ShareDataService, AuthService, BranchTransferService, PurchasesService } from '../_services';
import { GlobalsService } from '../_services/globals.service';
import { MatTableDataSource } from '@angular/material';
import { SelectionModel } from '@angular/cdk/collections';
import { Location } from '@angular/common';
import { MatDialog } from '@angular/material';
import { SimpleDialogComponent } from '../_dialogs/simple-dialog/simple-dialog.component'
import { Router } from '@angular/router';
import { UtilsService } from '../_utils/utils.service';
import { first } from 'rxjs/operators';

@Component({
  selector: 'app-detailed-ibt',
  templateUrl: './detailed-ibt.component.html',
  styleUrls: ['./detailed-ibt.component.scss', "./../../common-dark.scss"]
})
export class DetailedIbtComponent implements OnInit {
  ibt: any;
  user: any;
  isIncoming: boolean;
  approvalRequired: boolean;
  isDone: boolean = false;
  dataSource: MatTableDataSource<any>;
  displayedColumns: string[];
  selection = new SelectionModel<any>(true, []);
  restaurantId: any;
  pendingQty: any;
  estimated: any[];
  forcastQnty: any;
  private specialKeys: Array<string> = ['Backspace', 'Tab', 'End', 'Home', 'ArrowLeft', 'ArrowRight', 'Del', 'Delete'];
  private regex: RegExp = new RegExp(/^\d*\.?\d{0,2}$/g);
  searchText: string;
  dispatchStatus: any;
  deliveryStatus: any;
  status: string;
  dispatchedDate: string;
  deliveredDate: string;
  requestedDate: string;
  disableDispatch: boolean;
  disableReceive: boolean;
  constructor(private sharedData: ShareDataService, private auth: AuthService,
    private loc: Location, private branchTransfer: BranchTransferService,private router : Router,
    public utils: UtilsService,
    private branchTransferService: BranchTransferService,
    private dialog: MatDialog,private purchases: PurchasesService,) { }

  ngOnInit() {
    this.sharedData.currIbt.pipe(first()).subscribe(ibt => {
      if (!ibt.ibtId)
        this.loc.back();
      this.ibt = ibt;
      this.user = this.auth.getCurrentUser();

      if(this.ibt.indentApprovalDetail){
        let matchingObjects = this.ibt.indentApprovalDetail.filter(el => el.status === 'approved');
        this.approvalRequired = (this.ibt.indentApprovalDetail.length === matchingObjects.length) ? false : true;
      }
      this.isIncoming = this.ibt.selectedRestaurant === this.ibt.toBranch.restaurantId
      this.ibt.createTs = new Date(ibt.createTs);
      this.dataSource = new MatTableDataSource<any>();
      this.ibt.items.forEach(element => {
        if (this.isIncoming) {
          element.receivedQty = this.utils.truncateNew(element.recPendingQty - element.pendingQty) ;
          if (element.hasOwnProperty('portionWeight'))  {
            const conversionCoefficient =  element.uom == 'NOS' ? 1: 1000 ;
            element.receivedQtyPortion = this.utils.truncateNew ((element.receivedQty * conversionCoefficient) / this.utils.truncateNew(element.portionWeight,0));
            element.dispatchQtyPortion = this.utils.truncateNew ((element.dispatchQty * conversionCoefficient) / this.utils.truncateNew(element.portionWeight,0));
            element.inStockPortion = this.utils.truncateNew ((element.inStock * conversionCoefficient) / this.utils.truncateNew(element.portionWeight,0));
            element.pendingQtyPortion = this.utils.truncateNew ((element.pendingQty * conversionCoefficient) / this.utils.truncateNew(element.portionWeight,0));
            element.recPendingQtyPortion = this.utils.truncateNew ((element.recPendingQty * conversionCoefficient) / this.utils.truncateNew(element.portionWeight,0));
          }          
          element.shortageQty = 0 ;
        } else {
          element.dispatchQty = element.inStock > this.utils.truncateNew(element.pendingQty) ? this.utils.truncateNew(element.pendingQty) :  this.utils.truncateNew(element.inStock) ;
          if (element.hasOwnProperty('portionWeight'))  {
            const conversionCoefficient =  element.uom == 'NOS' ? 1: 1000 ;
            element.receivedQtyPortion = this.utils.truncateNew ((element.receivedQty * conversionCoefficient) / this.utils.truncateNew(element.portionWeight,0));
            element.dispatchQtyPortion = this.utils.truncateNew ((element.dispatchQty * conversionCoefficient) / this.utils.truncateNew(element.portionWeight,0));
            element.inStockPortion = this.utils.truncateNew ((element.inStock * conversionCoefficient) / this.utils.truncateNew(element.portionWeight,0));
            element.pendingQtyPortion = this.utils.truncateNew ((element.pendingQty * conversionCoefficient) / this.utils.truncateNew(element.portionWeight,0));
            element.recPendingQtyPortion = this.utils.truncateNew ((element.recPendingQty * conversionCoefficient) / this.utils.truncateNew(element.portionWeight,0));
          }
          element.forcastQnty  = element.inStock + element.forcastQnty
        }
      });
      if(this.ibt.ibtType =="MANUAL" || !("demand" in this.ibt)){
        this.displayedColumns = ['index', 'itemName', 'entryType', 'pkgName', "uom", 'quantity', 'dispatchHistory', 'pendingQty', 'inStock', 'receivedQty', 'receivedQtyPortion', 'unitPrice', 'totalValue'];
      }else{
        this.displayedColumns = [...GlobalsService.detailedIbtColumns];
      }

      if (this.isIncoming) {
        this.displayedColumns = [
          "index",
          "itemName",
          "entryType",
          "pkgName",
          "uom",
          "quantity",
          "receivedHistory",
          'shortageHistory',
          "pendingQty",
          "receivedQty",
          "receivedQtyPortion",
          "shortageQty",
          "shortageType",
          "reason",
          "unitPrice",
          "totalValue"
      ]
      } 

      this.dispatchStatus = this.ibt.status.dispatched    
      this.deliveryStatus = this.ibt.status.delivered
      this.status = this.dispatchStatus ? (this.deliveryStatus ? 'delivered' : 'dispatched') : 'yet to dispatch';
      this.ibt.items.forEach(element => {
        if(element.pendingQty != 0){
          if(! this.isIncoming){
            element.receivedQty = element.pendingQty >= this.utils.truncateNew(element.inStock) ? this.utils.truncateNew(element.inStock) : this.utils.truncateNew(element.pendingQty)
          }
        }
        if(element.pendingQtyPortion != 0){
          if(! this.isIncoming){
            element.receivedQtyPortion = element.pendingQtyPortion >= this.utils.truncateNew(element.inStockPortion) ? this.utils.truncateNew(element.inStockPortion) : this.utils.truncateNew(element.pendingQtyPortion)
          }
        }
      });
      this.dataSource.data = this.ibt.items
      let dispatchStatus = this.ibt.status.dispatched ? 'Completed' : 'Pending';
      let deliveredStatus = this.ibt.status.delivered ? 'Completed' : 'Pending';
      this.checkItemStatus() ;
      this.checkReceivedStatus() ;

      if (deliveredStatus === 'Completed') {
          this.deliveredDate = new Date(this.ibt.modTs).toDateString();
      } else {
          this.deliveredDate = 'N/A';
      }
      if ('demandTs' in ibt) {
        this.requestedDate = new Date(ibt.demandTs).toDateString();
    } else {
        this.requestedDate = 'N/A';
    }
    }, err => {
      console.error(err)
    });
    this.dataSource.data.forEach(element => {
      if (element.hasOwnProperty('selectedOption') && element.selectedOption === 'portion') {
        element.selectedOption = 'portion';
        element.defaultUOM = 'portion';
      } else {
        element.selectedOption = 'uom';
        element.defaultUOM = 'uom';
      }      
    });
  }

  generateGrnFromIbt() {
    this.isDone = true ;
    this.ibt.voidIbt = true;
    this.branchTransfer.generateGrnFromIbt({
      ibtMid: this.ibt._id.$oid,
      ibtId: this.ibt.ibtId,
      tenantId: this.user.tenantId,
      restaurantId:  this.ibt.toBranch['restaurantId'],
      items: this.ibt.items,
      uId: this.user.mId,
      ibtType: this.ibt.ibtType,
      workArea: this.ibt.workArea,
    }).subscribe(_data => {
      if(_data['status'] == 'success'){
          this.branchTransfer.voidIbt({
            tenantId: this.user.tenantId,
            ibtId: this.ibt._id.$oid,
            uId: this.user.mId,
          }).subscribe(_data => {
            this.dialog.open(SimpleDialogComponent, {
              data: {
                title: 'Goods received from Ibt',
                msg: 'The goods are received and added to the inventory.',
                ok: function () {
                  this.loc.back()
                }.bind(this)
              }
            })
          }, err => { })
      } else {
        this.utils.snackBarShowError(_data['message']) ;
      }
    }, err => console.error(err))
  }

  validateDispatch(event, element) {
    if (this.ibt.ibtType === "MANUAL" || !("demand" in this.ibt)) {
      if (!this.isIncoming) {
        element.dispatchQty = element.dispatchQty === null ? 0 : Math.max(0, element.dispatchQty);
        if (element.dispatchQty > element.pendingQty) {
          if (element.pendingQty <= element.inStock) {
            element.dispatchQty = element.pendingQty;
          } else {
            element.dispatchQty = element.inStock;
          }
        } else {
          if (element.dispatchQty > element.inStock) {
            element.dispatchQty = element.inStock;
          }
        }
      } else {
        element.receivedQty = element.receivedQty === null ? 0 : Math.max(0, element.receivedQty);
        if (element.receivedQty > (element.recPendingQty - element.pendingQty)) {
          element.receivedQty = (element.recPendingQty - element.pendingQty);
          element.shortageQty = this.utils.truncateNew((element.recPendingQty - element.pendingQty) - element.receivedQty);
          if (element.shortageQty > 0) {
            element.shortageType = 'transit';
          } else {
            element.shortageType = undefined;
          }
        } else {
          if (element.receivedQty < (element.recPendingQty - element.pendingQty)) {
            element.shortageQty = this.utils.truncateNew((element.recPendingQty - element.pendingQty) - element.receivedQty);
            element.shortageType = 'transit';
          } else {
            element.shortageType = undefined;
            element.shortageQty = this.utils.truncateNew((element.recPendingQty - element.pendingQty) - element.receivedQty);
          }
        }
        this.checkShortage();
      }
    } else {
      if (!this.isIncoming) {
        element.dispatchQty = Math.max(0, element.dispatchQty);
        if (element.dispatchQty > element.inStock) {
          element.dispatchQty = element.inStock;
        }
      } else {
        element.receivedQty = Math.max(0, element.receivedQty);
        if (element.receivedQty > element.recPendingQty) {
          element.receivedQty = element.recPendingQty;
        }
      }
    }
  }

  validateDispatchPortion(event, element) {
    if (this.ibt.ibtType === "MANUAL" || !("demand" in this.ibt)) {
      if (element.hasOwnProperty('portionWeight'))  {

        const conversionCoefficient = element['uom'] === 'NOS' ? 1 : 1000;
        let pendingQtyPortion = this.utils.truncateNew(element['pendingQty'] * conversionCoefficient) / element['portionWeight'];
        let inStockPortion = this.utils.truncateNew(element['inStock'] * conversionCoefficient) / element['portionWeight'];
        let recPendingQtyPortion = this.utils.truncateNew(element['recPendingQty'] * conversionCoefficient) / element['portionWeight'];
        let shortageQtyPortion = this.utils.truncateNew(element['shortageQty'] * conversionCoefficient) / element['portionWeight'];

        if (!this.isIncoming) {
          element.dispatchQtyPortion = element.dispatchQtyPortion === null ? 0 : Math.max(0, element.dispatchQtyPortion);
          if (element.dispatchQtyPortion > pendingQtyPortion) {
            if (pendingQtyPortion <= inStockPortion) {
              element.dispatchQtyPortion = pendingQtyPortion;
            } else {
              element.dispatchQtyPortion = inStockPortion;
            }
          } else {
            if (element.dispatchQtyPortion > inStockPortion) {
              element.dispatchQtyPortion = inStockPortion;
            }
          }
        } else {
          element.receivedQtyPortion = element.receivedQtyPortion === null ? 0 : Math.max(0, element.receivedQtyPortion);
          if (element.receivedQtyPortion > (recPendingQtyPortion - pendingQtyPortion)) {
            element.receivedQtyPortion = (recPendingQtyPortion - pendingQtyPortion);
            shortageQtyPortion = (recPendingQtyPortion - pendingQtyPortion) - element.receivedQtyPortion;
            if (shortageQtyPortion > 0) {
              element.shortageType = 'transit';
            } else {
              element.shortageType = undefined;
            }
          } else {
            if (element.receivedQtyPortion < (recPendingQtyPortion - pendingQtyPortion)) {
              shortageQtyPortion = (recPendingQtyPortion - pendingQtyPortion) - element.receivedQtyPortion;
              element.shortageType = 'transit';
            } else {
              element.shortageType = undefined;
              shortageQtyPortion = (recPendingQtyPortion - pendingQtyPortion) - element.receivedQtyPortion;
            }
          }
          this.checkShortage();
        }
      }
    } else {
      if (element.hasOwnProperty('portionWeight'))  {
        const conversionCoefficient = element['uom'] === 'NOS' ? 1 : 1000;
        let inStockPortion = this.utils.truncateNew(element['inStock'] * conversionCoefficient) / element['portionWeight'];
        let recPendingQtyPortion = this.utils.truncateNew(element['recPendingQty'] * conversionCoefficient) / element['portionWeight'];

        if (!this.isIncoming) {
          element.dispatchQtyPortion = Math.max(0, element.dispatchQtyPortion);
          if (element.dispatchQtyPortion > inStockPortion) {
            element.dispatchQtyPortion = inStockPortion;
          }
        } else {
          element.receivedQtyPortion = Math.max(0, element.receivedQtyPortion);
          if (element.receivedQtyPortion > recPendingQtyPortion) {
            element.receivedQtyPortion = recPendingQtyPortion;
          }
        }
      }
    }
  }

  receivedQtyChange(element) {
    if (element.hasOwnProperty('portionWeight')) {
      let conversionCoefficient = element['uom'] === 'NOS' ? 1 : 1000;
      if (element.receivedQty != null) {
        element.receivedQtyPortion = this.utils.truncateNew((element.receivedQty * conversionCoefficient) / element.portionWeight);
      }
    }
  }

  dispatchedQtyChange(element) {
    if (element.hasOwnProperty('portionWeight')) {
      let conversionCoefficient = element['uom'] === 'NOS' ? 1 : 1000;
      if (element.dispatchQty != null) {
        element.dispatchQtyPortion = this.utils.truncateNew((this.utils.truncateNew(element.dispatchQty) * conversionCoefficient) / element.portionWeight);
      }
    }
  }

  dispatchedQtyPortionChange(element) {
    if (element.hasOwnProperty('portionWeight')) {
      let conversionCoefficient = element['uom'] === 'NOS' ? 1 : 1000;
      if (element.dispatchQtyPortion != null) {
        element.dispatchQty = this.utils.truncateNew((this.utils.truncateNew(element.dispatchQtyPortion) * element.portionWeight) / conversionCoefficient);
      }
    }
  }
  
  receivedQtyPortionChange(element) {
    if (element.hasOwnProperty('portionWeight')) {
      let conversionCoefficient = element['uom'] === 'NOS' ? 1 : 1000;
      if (element.receivedQtyPortion != null) {
        element.receivedQty = this.utils.truncateNew((element.receivedQtyPortion * element.portionWeight) / conversionCoefficient);
        element.shortageQty = this.utils.truncateNew((element.recPendingQty - element.pendingQty) - element.receivedQty);
      }
    }
  }
  
  dispatchIbt() {
    this.isDone = true ; 
    for (let i = 0; i < this.ibt.items.length; i++) {
      this.ibt.items[i].receivedQty = this.utils.truncateNew(this.ibt.items[i].receivedQty)
      this.ibt.items[i].receivedQtyPortion = this.utils.truncateNew(this.ibt.items[i].receivedQtyPortion)
    }
    let zeroDispatchItems = this.ibt.items.filter((item)=>  item['dispatchQty'] == 0 || item['dispatchQtyPortion'] == 0) ;
    (zeroDispatchItems.length == this.ibt.items.length) ? (this.utils.snackBarShowWarning('You cannot dispatch zero quantity for all items'), this.isDone = false) : this.validateInStoreStock(this.ibt) ;
  }
  
  printPdf() {

    const inputDateStr = this.ibt.eta ;
    const inputDate = new Date(inputDateStr);
    // Extract the date components
    const year = inputDate.getFullYear();
    const month = String(inputDate.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed
    const day = String(inputDate.getDate()).padStart(2, '0');
    // Create the formatted date string
    const formattedDate = `${day}-${month}-${year}`;
    this.ibt.deliveryDate = formattedDate
    if (this.isIncoming) {
      this.ibt['type'] = 'Receive'
    } else {
      this.ibt['type'] = 'Dispatch'
    }
    this.branchTransfer.printpdfs(this.ibt, 'Ibt').subscribe(data => {
      this.purchases.globalPrintPdf(data.eFile);
    });

  }

  exportToExcel() {
    this.branchTransfer.exportToExcel(this.ibt, 'Ibt').subscribe(data => {
      window.open('data:text/csv;base64,' + data.eFile);
    });
  }

  goBack() {
    this.router.navigate(['/home/<USER>']);
  }

  numberOnly(event): boolean {
    let value = event.target.value;
    if (this.specialKeys.indexOf(event.key) !== -1) {
      return;
    }
     let current = value;
      const position = event.target.selectionStart;
      const next = [current.slice(0, position), event.key == 'Decimal' ? '.' : event.key, current.slice(position)].join('');
      if (next && !(next).match(this.regex)) {
       event.preventDefault();
      }
  }

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }
  
  resetForm() {
    this.searchText = ''
    this.dataSource = new MatTableDataSource<any>();
    this.dataSource.data = this.ibt.items;
  }

  extractLastPart(itemName: string): string {
    if (!itemName) return '';
    const parts = itemName.split('|');
    const lastPart = parts[parts.length - 1].trim();
    return lastPart;
  }

  getTotal() {
    let total = this.dataSource.data.reduce((accumulator, item) => {
      let recQty = this.isIncoming ? (item.receivedQty || item.receivedQtyPortion) : (item.dispatchQty || item.dispatchQtyPortion);
      return accumulator + (recQty * item.unitPrice); 
    }, 0);
    return this.utils.truncateNew(total);
  }

  getItemTotal(element) {
    let recQty = this.isIncoming ? (element.receivedQty || element.receivedQtyPortion) : (element.dispatchQty || element.dispatchQtyPortion);
    return this.utils.truncateNew(element.unitPrice * recQty);
  }    

  getItemCalculation(element: any): number {
    const quantity = (element.quantity * 1000);
    const pendingQty = (element.pendingQty * 1000);
    const qty = (quantity - pendingQty) / 1000    
    return this.utils.truncateNew(qty);
  }  

  permissionCall(event,element){
    let Data = this.dataSource.data.filter(item => item.shortageQty > 0) ;
    Data = Data.filter(obj => (!('reason' in obj) || obj.reason.length === 0));
    if (event.length > 0 && element.shortageQty > 0 && Data.length == 0){
      this.isDone = false;
    } else {
      this.isDone = true;
    }
  }

  validateInStoreStock(obj) {
    let element = {}
    element['fromBranch'] = this.ibt.fromBranch
    element['items'] = this.ibt.items
    this.branchTransferService.getInStockForIbt(obj).subscribe(data => {
    if( data['result']){
      let missingItems = [] ;
      this.ibt.items.forEach((item)=> {
      let requiredItem= data['items'].find((el)=> el.itemCode == item.itemCode && el.packageName == item.packageName)
          if (requiredItem){
              item['inStock'] = requiredItem['inStock'] ;
              item['duplication'] = (item['pendingQty'] == requiredItem['pendingQty']) ? false : true ;
          } else {
              missingItems.push(item.itemCode) ;
          }
      })
      let stockLessItems = this.ibt.items.filter((item)=> item['inStock'] <  item['dispatchQty'] || item['inStockPortion'] <  item['dispatchQtyPortion']) ;
      let screenDuplication = this.ibt.items.find((item)=> item['duplication'] == true) ;
      if (!screenDuplication) {
        if (stockLessItems.length > 0) {
          this.utils.snackBarShowWarning('Requested stock not available for the highlighted items');
          this.approvalRequired = false ; 
        } else {
          if (missingItems.length > 0) {
            let itemCodes = missingItems.join(',')
              this.utils.snackBarShowWarning(`${itemCodes} missing from inventory.`) ;
            this.isDone = false ; 
          } else {
            this.branchTransfer.updateIbt({
              uId: this.user.mId,
              ibtId: this.ibt._id.$oid,
              items: this.ibt.items,
              restaurantId:  this.ibt.fromBranch['restaurantId'],
              tenantId: this.user.tenantId
            }).subscribe((data: any) => {
              if( data['result'] == 'success'){
              this.isDone = true ; 
              this.dialog.open(SimpleDialogComponent, {
                data: {
                  title: 'Goods Dispatched',
                  msg: 'The goods are dispatched and deducted from the inventory.',
                  ok: function () {
                    this.loc.back() ;
                  }.bind(this)
                }
              })}
              else {
                this.utils.snackBarShowError(data['message']) ;
                this.isDone = true ; 
              }
            }, err => console.log(err))
          }
        }
      } else {
        this.utils.snackBarShowError("Restricted action") ;
        this.isDone = true ; 
      }
    } else {
      this.utils.snackBarShowWarning("Unable to check item stock, Please try again later!")
    }
    })
  }

  focusFunctionWithOutForm(element , value){
        if(Number(element[value]) === 0){
      element[value] = null;
    }
  }
  
  focusOutFunctionWithOutForm(element , value){
        if(element[value] === null){
      element[value] = 0
    }
  }

  checkItemStatus() {
    if (this.ibt.status.orderStatus != 'Closed' && this.ibt.status.dispatched != 'Completed'){
      let pendingItems = this.ibt.items.filter((item)=> item.itemStatus === "complete")
      this.disableDispatch = (pendingItems.length === this.ibt.items.length) ? true : false ;
    } else {
      this.disableDispatch = true ;
    }
  }

  checkReceivedStatus() {
    if (this.ibt.status.orderStatus != 'Closed' && this.ibt.status.delivered != 'Completed'){
        let items = this.ibt.items.filter((item)=> item.recPendingQty === 0)
        let pendingItems = this.ibt.items.filter((element) => ((element.recPendingQty - element.pendingQty) > 0))
        this.disableReceive  =  (this.ibt.items.length === items.length) || pendingItems.length == 0 ? true : false ;
      } else {
        this.disableReceive = true ;
    }
  }

  getShortageCount(data) {
    let totalSum = 0
    if (data.hasOwnProperty('shortageHistory')){
       totalSum = data['shortageHistory'].reduce((sum, num) => sum + num, 0);
    }
   return totalSum
  }

  getReceivedCount(element) {    
    let totalSum = 0
    if (element.hasOwnProperty('shortageHistory')){
       totalSum = element['shortageHistory'].reduce((sum, num) => sum + num, 0);
    }
   return (element.quantity - element.recPendingQty - totalSum)
  }

  checkShortage() {
    let item = this.dataSource.data.filter((el) => {
      return (el.shortageQty > 0 && !el.hasOwnProperty('reason')) || (el.shortageQty > 0 && el.hasOwnProperty('reason') && el.reason == null) || (el.shortageQty > 0 && el.hasOwnProperty('reason') && el.reason == undefined) ;
    });
    this.isDone =  item.length > 0 ? true : false ;
  }

  portionValueQty(element){
    if (element.hasOwnProperty('selectedOption') && element['selectedOption'] === 'portion' && element.hasOwnProperty('portionWeight'))  {
      let conversionCoefficient =  element['uom'] == 'NOS' ? 1: 1000 ;
      const portionWeight = (element.portionWeight * 1000) / 1000;
      let calculatedQuantity = (element.quantity * conversionCoefficient) / portionWeight;
      return calculatedQuantity
    }
    return ''
  } 

  portionValueDispatchQty(element){
    if (element.hasOwnProperty('selectedOption') && element['selectedOption'] === 'portion' && element.hasOwnProperty('portionWeight'))  {
      let conversionCoefficient =  element['uom'] == 'NOS' ? 1: 1000 ;
      const dispatchValue = element.quantity - element.pendingQty
      const portionWeight = (element.portionWeight * 1000) / 1000;
      let calculatedQuantity = ( dispatchValue * conversionCoefficient ) / portionWeight;     
      return calculatedQuantity
    }
    return ''
  } 

  portionValueRecPendingQty(element){
    if (element.hasOwnProperty('selectedOption') && element['selectedOption'] === 'portion' && element.hasOwnProperty('portionWeight'))  {
      let conversionCoefficient =  element['uom'] == 'NOS' ? 1: 1000 ;
      const pendingValue = element.recPendingQty - element.pendingQty
      const portionWeight = (element.portionWeight * 1000) / 1000;
      let calculatedQuantity = ( pendingValue * conversionCoefficient ) / portionWeight;    
      return calculatedQuantity
    }
    return ''
  } 

  portionValuePendingQty(element){
    if (element.hasOwnProperty('selectedOption') && element['selectedOption'] === 'portion' && element.hasOwnProperty('portionWeight'))  {
      let conversionCoefficient =  element['uom'] == 'NOS' ? 1: 1000 ;
      const portionWeight = (element.portionWeight * 1000) / 1000;
      let calculatedQuantity = ( element.pendingQty * conversionCoefficient ) / portionWeight;
      return calculatedQuantity
    }
    return ''
  }  

  // checkNumericInput(event: any , element) {   
  //   if (element.packageName === 'NOS') {
  //     const input = event.target.value;
  //     event.target.value = input.replace(/[^0-9]/g, ''); 
  //     element.quantity = event.target.value;
  //   }
  // }
  
  portionValueInstore(element){    
    if (element.hasOwnProperty('selectedOption') && element['selectedOption'] === 'portion' && element.hasOwnProperty('portionWeight'))  {
      let conversionCoefficient =  element['uom'] == 'NOS' ? 1: 1000 ;
      const portionWeight = (element.portionWeight * 1000) / 1000;
      let calculatedQuantity = (element['inStock'] * conversionCoefficient) / portionWeight;
      return this.utils.truncateNew(calculatedQuantity)
    }
    return ''
  } 

  disableUom(element){
    return element.defaultUOM == "uom" ? true : false
  }

  convertPo(){
    const filteredData = this.dataSource.data.filter((item)=> item['entryType'] == 'package');

    if (filteredData.length === 0) {
      this.utils.snackBarShowWarning('No package items available for conversion to Purchase Order.');
      return;
    }

    // Set data in shared service for the standalone component
    this.sharedData.changeConvertPoData({
      items: filteredData,
      ibt: this.ibt,
      title: 'Convert to Purchase Order'
    });

    // Navigate to the convert-po route
    this.router.navigate(['/home/<USER>']);
  }

}
