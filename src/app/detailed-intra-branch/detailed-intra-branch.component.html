<div class="title">
  <button mat-raised-button class="button" style="float: left;" (click)=goBack()>
    <mat-icon>keyboard_backspace</mat-icon> Back To Intra-Branch List 
  </button>
  <button mat-raised-button class="button" style="float: right;" (click)="print()">
    Print
  </button>
</div>

<div class="search-table-input fieldcontainer">
  <div class="row">
  <div class="col">
    <table class="table">
      <tbody>
        <tr>
          <th class="topItemkey" scope="row">IntraBranch Id</th>
          <td>{{ data.intraBranchId }}</td>
        </tr>
        
        <tr>
          <th class="topItemkey" scope="row">Transfer Document Date</th>
          <td> {{ data.transferDocumentDate | date: "EEEE, MMMM d, y" }} </td>
        </tr>
      </tbody>
    </table>
  </div>

  <div class="col">
    <table class="table">
      <tbody>
        <tr>
          <th class="topItemkey" scope="row">Source</th>
          <td>{{ data.sourceWorkArea }}</td>
        </tr>
      
        <tr>
          <th class="topItemkey" scope="row">Destination</th>
          <td> {{ data.destinationWorkArea }} </td>
        </tr>
      </tbody>
    </table>
  </div>

  <div class="col">
    <table class="table">
      <tbody>
        <tr>
          <th class="topItemkey" scope="row">Created By</th>
          <td>{{ data.creator }}</td>
        </tr>
        <tr>
          <th class="topItemkey" scope="row">Created Date</th>
          <td> 
            {{ this.utils.formatDateToUTC(data.date) }}
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
</div>

<div class="datacontainer">
  <mat-card>
    <mat-card-content>
      <div class="search-table-input">
        <mat-form-field appearance="none">
          <label>Search</label>
          <div class="search-container">
            <input matInput type="text" class="outline" (keyup)="doFilter($event.target.value)" 
            [(ngModel)]='searchText' placeholder="Search" />
            <mat-icon matSuffix class="closebtn" (click)="resetForm()">close</mat-icon>
          </div>
        </mat-form-field>
      </div>
      <section class="example-container-1 mat-elevation-z8">    
        <table #table mat-table [dataSource]="dataSource" matSortDirection="desc" matSort>

          <ng-container matColumnDef="index">
            <th mat-header-cell *matHeaderCellDef><b>S.No</b></th>
            <td mat-cell *matCellDef="let element; let i = index" style="padding-right: 30px;">
              {{ i + 1 }}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>

          <ng-container matColumnDef="itemName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Item Name </b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.itemName }}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>

          <ng-container matColumnDef="itemCode">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Item Code </b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.itemCode }}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>

          <ng-container matColumnDef="pkgName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Pkg Name</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.pkgName }}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>

          <ng-container matColumnDef="entryType">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Entry Type</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.entryType }}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>

          <ng-container matColumnDef="transferQty">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Transfer Qty </b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.qty }}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
          <tr mat-footer-row *matFooterRowDef="displayedColumns"></tr>
        </table>
      </section>
    </mat-card-content>
  </mat-card>
</div>
