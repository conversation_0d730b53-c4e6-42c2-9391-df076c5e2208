import { Component, OnInit } from '@angular/core';
import { PurchasesService } from '../_services/purchases.service';
import { ShareDataService } from '../_services/share-data.service';
import { Router } from '@angular/router';
import { UtilsService } from '../_utils/utils.service';
import { MatDialog, MatTableDataSource } from '@angular/material';
import { AuthService } from '../_services/auth.service';
import { GlobalsService } from '../_services/globals.service';
import { Location } from "@angular/common";

@Component({
  selector: 'app-detailed-intra-branch',
  templateUrl: './detailed-intra-branch.component.html',
  styleUrls: ['./detailed-intra-branch.component.scss', "./../../common-dark.scss"]
})
export class DetailedIntraBranchComponent implements OnInit {
  user: any;
  data: any;
  dataSource: MatTableDataSource<any> ;
  displayedColumns: string[];
  searchText: string;
  public searchValue: any = ''
  // transferData: { itemCode: string; pkgName: string; entryType: string; qty: unknown; }[];
  transferData: any;

  constructor(private purchases: PurchasesService, 
    private sharedData: ShareDataService,
    private router: Router, 
    public utils: UtilsService,
    public dialog: MatDialog,
    private auth: AuthService,
    private location: Location  
  ) {
    this.user = this.auth.getCurrentUser()
   }

  ngOnInit() {
    this.getData();
  }

  getData(){
    this.sharedData.currIbts.subscribe(data => {
      this.data = data;       
      this.transferData = Object.entries(data.items).map(([key, qty]) => {
        const [itemName, itemCode, pkgName, entryType] = key.split('|');
        return { itemName, itemCode, pkgName, entryType, qty };
      });
      this.dataSource = new MatTableDataSource < any > (this.transferData);
      this.displayedColumns = ['index','itemName','itemCode','pkgName','entryType','transferQty'];
    }, err => {
      console.error(err)
    });
  }

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }

  resetForm() {
    this.searchText = ''
    this.searchValue = ''
    this.doFilter(this.searchValue)
    this.getData();
  }

  // getTotal(key: string) {
  //   return this.utils.getTotal(this.dataSource.data, key);
  // }
  
  goBack(){
    this.location.back();
  }

  print() {
    this.purchases.print(this.data, 'IntraBranch').subscribe(data => {
      this.purchases.globalPrintPdf(data.eFile);       
    });
  }

}

