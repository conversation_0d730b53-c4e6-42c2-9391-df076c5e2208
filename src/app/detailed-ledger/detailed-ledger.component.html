<div class="d-flex justify-content-end" style="margin: 22px !important;">
  <button mat-stroked-button id="save-btn"
  class="button3" (click)="close()">Close Ledger</button>
</div>


<div class="search-table-input fieldcontainer">

  <div class="row">
    <div class="col">
      <table class="table">
        <tbody>
          <tr>
            <th class="topItemkey" scope="row">Ledger ID </th>
            <td>{{ ledgerData.id }}</td>
          </tr>
          <tr>
            <th class="topItemkey" scope="row">Restaurant Name </th>
            <td>{{ ledgerData.restaurantId }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="col">
      <table class="table">
        <tbody>
          <tr>
            <th class="topItemkey" scope="row">IndentArea</th>
            <td>{{ ledgerData.indentArea }}</td>
          </tr>
          <tr>
            <th class="topItemkey" scope="row">Created Date</th>
            <td >{{ (ledgerData.createTs | date: "EEEE, MMMM d, y") || '-' }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="col">
      <table class="table">
        <tbody>
          <tr>
            <th class="topItemkey" scope="row">Email</th>
            <td >{{ ledgerData.email }}</td>
          </tr>
          <tr>
            <th class="topItemkey" scope="row">status </th>
            <td >{{ ledgerData.status }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>

  <mat-card>
    <mat-card-content>
      <div class="search-table-input">
        <mat-form-field appearance="none">
          <label>Search</label>
          <input matInput type="text" class="outline" placeholder="Search" [(ngModel)]='searchText'
            (keyup)="doFilter($event.target.value)" />
        </mat-form-field>
      </div>

      <table #table mat-table [dataSource]="dataSource">
        <ng-container matColumnDef="itemName">
          <th mat-header-cell *matHeaderCellDef>
            <b> Item Name </b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{element.itemName}}
          </td>
          <td mat-footer-cell *matFooterCellDef class="name-cell">Total</td>
        </ng-container>

        <ng-container matColumnDef="packageName" >
          <th mat-header-cell *matHeaderCellDef>
            <b>PackageName</b>
          </th>
          <td mat-cell *matCellDef="let element">
           {{ element.packageName }} 
          </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="category">
          <th mat-header-cell *matHeaderCellDef>
            <b> Category </b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.category }}
          </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="subCategory">
          <th mat-header-cell *matHeaderCellDef>
            <b>Sub Category</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.subCategory }} 
          </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="Qty">
          <th mat-header-cell *matHeaderCellDef>
            <b>Spoilage Qty</b>
          </th>
          <td mat-cell *matCellDef="let element"> 
            {{ element.adjustQty }} 
          <!-- <input class="input1" type="number" step="0.01" min="0" (keyup)="getTotalPrCost($event , element)"
            [(ngModel)]="element.adjustQty" [disabled]="pr?.purchaseStatus || element.priceType"
            (focus)="focusFunctionWithOutForm(element)" (focusout)="focusOutFunctionWithOutForm(element)" /> -->
          </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="stockQty">
          <th mat-header-cell *matHeaderCellDef>
            <b>stock Qty</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.instockData }} 
          </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef>
            <b>status</b>
          </th>
          <td mat-cell *matCellDef="let element">
            <div class="d-flex" *ngIf="element.instockData > element.adjustQty || element.instockData === element.adjustQty">
              <mat-icon class="check_circle">check_circle</mat-icon> Adequate
            </div>
            <div class="d-flex" *ngIf="element.adjustQty > element.instockData">
              <mat-icon class="check_circle_error">error</mat-icon>InAdequate
            </div>
          </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="inclTax">
          <th mat-header-cell *matHeaderCellDef>
            <b>WAC(incl.tax,etc)</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ this.utils.truncateNew(element.withTaxPrice ,2)}} 
          </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="total">
          <th mat-header-cell *matHeaderCellDef>
            <b>Total</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ this.calculateAmount(element) }} 
          </td>
          <td mat-footer-cell *matFooterCellDef>{{ this.utils.truncateNew(getTotal('totalPrice'),2) }}</td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        <tr mat-footer-row *matFooterRowDef="displayedColumns"></tr>
      </table>
      <!-- <div class="dataMessage" *ngIf="dataSource?.data?.length == 0"> No Data Available </div> -->
      <!-- <mat-paginator [showTotalPages]="5" [pageSize]="10" [pageSizeOptions]="pageSizes"></mat-paginator> -->
    </mat-card-content>
  </mat-card>

<!-- </div> -->

<!-- ========================================================== -->

<ng-template #openD>
	<button mat-icon-button matDialogClose="yes" matTooltip="Close" class="CloseBtn">
		<mat-icon>close</mat-icon>
	</button>
	<h2 mat-dialog-title>
		<b>Close Ledger Document</b>
	</h2>

	  <div>
		<mat-dialog-content class="mat-typography">
      <div>
        <!-- <p class="d-flex justify-content-center dataMessage mb-3">Click to request the processing of a spoilage ledger</p>  -->
        <p class="d-flex justify-content-center dataMessage mb-3"> You are about to close this ledger document would you like to Proceed with inventory reduction for the item listed? this action is irreversible </p>
      </div>
			<div class="row justify-content-center">
        <!-- <button mat-raised-button class="button3 mb-2 mx-2" matDialogClose="yes" (click)="openConformation()">
					Request Ledger
				</button>
      <button mat-raised-button class="danger mb-2 mx-2" matDialogClose="yes" (click)="goBack()" style="margin-top: -15px;">
          <mat-icon style="margin-right: 5px;">cancel</mat-icon>close
				</button>
      -->

        <button mat-raised-button class="button3 mb-2 mx-2" matDialogClose="yes" (click)="sendReqAdequate()">
          yes, proceed
         </button>
 
         <button mat-raised-button class="mb-2 mx-2" matDialogClose="yes" (click)="closeAdequateLedger()">
           Skip Reduction
         </button>

			</div>
		</mat-dialog-content>
	  </div>
</ng-template>

<ng-template #openConformationDialog>
	<button mat-icon-button matDialogClose="yes" matTooltip="Close" class="CloseBtn">
		<mat-icon>close</mat-icon>
	</button>
	<h2 mat-dialog-title>
		<!-- <b *ngIf="adequateItems.length > 0 && inAdequateItems.length == 0">Close Ledger Document</b> -->
		<b>Inadequate Stock Detected</b>
	</h2>

	  <div>
		<mat-dialog-content class="mat-typography">
			<div class="row justify-content-center">
        <!-- <div *ngIf="adequateItems.length > 0 && inAdequateItems.length == 0">
          <p class="dataMessage"> You are about to close this ledger document would you like to Proceed with inventory reduction for the item listed? this action is irreversible </p>
        </div> -->

        <div>
          <!-- <p class="dataMessage">Oops! There are {{inAdequateItems.length}} inadequate items. Are you sure you want to continue?</p>   -->
          <p class="dataMessage">some items have inadequate stock for reduction. would you like to skip these items and proceed with closing the ledger, or handle them later. </p>  
        </div>
      </div>
		</mat-dialog-content>
	  </div>

    <mat-dialog-actions align='center'>
			<!-- <div class="reqBtn" *ngIf="adequateItems.length > 0 && inAdequateItems.length == 0">

				<button mat-raised-button class="button3 mb-2 mx-2" matDialogClose="yes" (click)="sendReqAdequate()">
				 yes, proceed
				</button>

        <button mat-raised-button class="mb-2 mx-2" matDialogClose="yes" (click)="closeAdequateLedger()">
          Skip Reduction
        </button>

			</div> -->

      <div class="reqBtn">
				
				<button mat-raised-button class="button3 mb-2 mx-2" matDialogClose="yes" (click)="sendReqInAdequate()">
          Skip & Close Ledger
				</button>

        <button mat-raised-button class="mb-2 mx-2" matDialogClose="yes" (click)="closeInAdequateLedger()">
          Handle Later
        </button>

			</div>
		</mat-dialog-actions>
</ng-template>