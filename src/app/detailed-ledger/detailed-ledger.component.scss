.check_circle_error{
    color: #A52A2A;
    font-size: 19px;
    // position: absolute;
    // margin-top: -19px;
    // margin-left: 65px;
  }

  .check_circle{
    color:green;
    font-size: 18px;
    // position: absolute;
    // margin-top: -18px;
    // margin-left: 65px;
  }

  .CloseBtn{
    float: right;
    margin-bottom: -1px;
  }

  .mat-dialog-content {
    margin: 15px !important; 
    padding: 15px 24px;
    max-height: 65vh;
    // max-width: 50vw !important;
    overflow: auto;
}

.dataMessage{
  font-size: large !important;
}

input[type=number]::-webkit-inner-spin-button, 
input[type=number]::-webkit-outer-spin-button { 
  -webkit-appearance: none; 
}

input[type=number] {
  -moz-appearance: textfield;
}

::ng-deep .mat-paginator {
  display: unset !important;
}

.spoilage{
  text-align: center;
  padding: 10px !important;
}