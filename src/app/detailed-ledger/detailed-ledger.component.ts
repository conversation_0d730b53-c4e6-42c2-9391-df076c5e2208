import { ChangeDetectorRef, Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { AuthService, BranchTransferService, PurchasesService, ShareDataService } from '../_services';
import { first } from 'rxjs/operators';
import { UtilsService } from '../_utils/utils.service';
import { Router } from '@angular/router';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog } from '@angular/material';
import { Location } from '@angular/common';

@Component({
  selector: 'app-detailed-ledger',
  templateUrl: './detailed-ledger.component.html',
  styleUrls: ['./detailed-ledger.component.scss', "./../../common-dark.scss"]
})
export class DetailedLedgerComponent implements OnInit {
  dataSource = new MatTableDataSource();
  displayedColumns: string[];
  spoilageDataSource = new MatTableDataSource();
  spoilageDataColumns: string[];
  pageSizes = []
  searchText: any;
  user: any;
  dialogItems: any[];
  adequateItems: any[];
  inAdequateItems: any[];
  currLedgerItems: any;
  ledgerData: any;
  spoilageData: any;
  disableSpoilage: any = false;
  editAccess: boolean;

  @ViewChild(MatSort) sort: MatSort;
  @ViewChild('openD') openD: TemplateRef<any>;
  @ViewChild('openConformationDialog') openConformationDialog: TemplateRef<any>;
  @ViewChild('spoilageDialog') spoilageDialog!: TemplateRef<any>;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  
  constructor(private shareData: ShareDataService,
    public utils: UtilsService,
    private router: Router,
    private branchTransfer: BranchTransferService,
    private loc: Location,
    private auth: AuthService,
    public dialog: MatDialog,
    private purchases: PurchasesService, 
    private cdr: ChangeDetectorRef
    ) { 

    this.user = this.auth.getCurrentUser();
    this.shareData.currLedger.pipe(first()).subscribe(data => {
      if (Object.keys(data).length === 0) {
        this.utils.snackBarShowInfo("data not available")
        this.router.navigate(['/home/<USER>'])
      } else {
        this.spoilageData = data;
        this.currLedgerItems = data.invItems
        this.ledgerData = data
        this.getStockDatas(data.invItems)
      }
    })
  }

  ngOnInit() {
    this.spoilageDataColumns = ['index','creator','createTs','qty'];
    this.tenantDetail();
  }

  enableEdit() {
    if (this.spoilageData && this.spoilageData.status == 'Pending'){
      this.displayedColumns = ['itemName','packageName','entryType','category','subCategory','stockQty','Qty','inclTax','total','status']
    } else {
      this.displayedColumns = ['itemName','packageName','entryType','category','subCategory','Qty','inclTax','total','history']
    }
  }

  getStockDatas(item){
    let obj = {
      item : item,
      workArea : this.ledgerData.indentArea,
    }
    this.branchTransfer.getStockData(obj).subscribe(res => {
      if (this.spoilageData && this.spoilageData.status == 'Pending'){
        this.displayedColumns = ['itemName','packageName','entryType','category','subCategory','stockQty','requestedQty','inclTax','total','status']
      } else {
        this.displayedColumns = ['itemName','packageName','entryType','category','subCategory','Qty','inclTax','total','history']
      }
      const data = res.data.map(item => {
        if (item['instockData'] < 0) {
          item['instockData'] = 0;
        }
        item.adjustQtyOld = item.adjustQty;
        return item;
      });
      
      this.dataSource.data = data
      this.dialogItems = this.dataSource.data
      this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
    })
  }

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }

  clearSearchText() {
    this.searchText = ''
    this.doFilter(this.searchText)
  }

  focusFunctionWithOutForm(element){
    if(Number(element.adjustQty) === 0){
      element.adjustQty = null;
    }
  }
  
  focusOutFunctionWithOutForm(element){
    if(element.adjustQty === null){
      element.adjustQty = 0
    }
  }

  restrictDecimal(event: KeyboardEvent, element: any) {
    const uom = element && element.uom ? element.uom.toLowerCase() : '';
    const isNos = uom === 'nos';

    if (isNos && event.key === '.') {
      event.preventDefault();
    }
  }

  getTotalPrCost(event,element) {
    if (element.adjustQty <= 0) {
      this.disableSpoilage = true;
    } else {
      this.disableSpoilage = false;
    }
    if (element.instockData < 0){
      element.instockData = 0;
    }
    element.adjustQty = element.adjustQty ? element.adjustQty : 0 ;
    element.subTotal = element.adjustQty * element.unitPrice
    element.taxAmount = element.taxRate / 100 * element.subTotal
    element.totalPrice = element.taxAmount + element.subTotal  
  }

  close(){
    const dialogRefTemplate = this.dialog.open(this.openD, {
      minWidth: '25vw',
    });
  }

  openConformation(){

    const dialogRefTemplate = this.dialog.open(this.openConformationDialog, {
      minWidth: '25vw',
    });
  }

  sendReqAdequate(){
    // this.getStockDatas(this.currLedgerItems)
    this.adequateItems = this.dataSource.data.filter(item => item['instockData'] > item['adjustQty'] || item['instockData'] === item['adjustQty']);
    this.inAdequateItems = this.dataSource.data.filter(item => item['adjustQty'] > item['instockData']);
    
    if(this.adequateItems.length > 0 && this.inAdequateItems.length == 0){
      let obj = {
        tenantId: this.spoilageData.tenantId,
        restaurantId: this.spoilageData.restaurantId,
        spoilageId: this.spoilageData.id,
        items : this.adequateItems,
        user: this.user.mId,
        workArea : this.ledgerData.indentArea,
        reduction: true
      }
      this.branchTransfer.reduceSpoilage(obj).subscribe(res => {
        if(res['result'] == 'success'){
          this.utils.snackBarShowSuccess("ledger closed successfully.")
          this.goBack();
        }else{
          this.utils.snackBarShowWarning("something went wrong")
          this.goBack();
        }
      })
    }else if(this.inAdequateItems.length > 0){
      const dialogRefTemplate = this.dialog.open(this.openConformationDialog, {
        minWidth: '25vw',
      });
    }
  }

  sendReqInAdequate(){
    this.inAdequateItems = this.dataSource.data.filter(item => item['adjustQty'] > item['instockData']);
    
    if (this.inAdequateItems.length > 0){
      this.inAdequateItems.forEach(item => {
        item['actualReductionQty'] = item['instockData']; 
      });
      let obj = {
        tenantId: this.spoilageData.tenantId,
        restaurantId: this.spoilageData.restaurantId,
        spoilageId: this.spoilageData.id,
        items : this.dataSource.data,
        workArea : this.ledgerData.indentArea,
        user: this.user.mId,
        reduction: true
      }
      this.branchTransfer.reduceSpoilage(obj).subscribe(res => {
        if(res['result'] == 'success'){
          this.utils.snackBarShowSuccess("Ledger closed successfully.")
          this.goBack();
        }else{
          this.utils.snackBarShowWarning("something went wrong")
          this.goBack();
        }
      })
    }
  }

  closeLedger(){
    let obj = {
      tenantId: this.spoilageData.tenantId,
      restaurantId: this.spoilageData.restaurantId,
      spoilageId: this.spoilageData.id,
      items : this.dataSource.data,
      user: this.user.mId,
      workArea : this.ledgerData.indentArea,
      reduction: false
    }
    this.branchTransfer.reduceSpoilage(obj).subscribe(res => {
      if(res['result'] == 'success'){
        this.utils.snackBarShowSuccess("Ledger closed successfully, without Reduction")
      }else{
        this.utils.snackBarShowWarning("something went wrong")
      }
    })
  }

  closeAdequateLedger(){
    this.closeLedger();
    this.goBack();
  }

  closeInAdequateLedger(){
    this.closeLedger();
    this.goBack();
  }

  goBack(){
    this.router.navigate(['/home/<USER>'])
  }

  getTotal(key: string) {
    return this.utils.getTotal(this.dataSource.data, key); 
  }

  calculateAmount(element) {
    element.totalPrice = this.utils.truncateNew((element.withTaxPrice * element.adjustQty),2)
    return this.utils.truncateNew((element.withTaxPrice * element.adjustQty),2)
  }

  save(){
    let obj = {
      tenantId: this.spoilageData.tenantId,
      restaurantId: this.spoilageData.restaurantId,
      spoilageId: this.spoilageData.id,
      user: this.user.mId,
      items : this.dataSource.data
    }
    this.branchTransfer.saveSpoilage(obj).subscribe(res => {
      if(res['result'] == 'success'){
        this.utils.snackBarShowSuccess("Saved successfully")
        this.goBack();
      }else{
        this.utils.snackBarShowWarning("something went wrong")
        this.goBack();
      }
    })
  }

  spoilageHistory(element) {
    this.dialog.open(this.spoilageDialog, {
      width: '700px',
      disableClose: true,
    });
    this.spoilageDataSource.data = element.history.slice().reverse();

    const generatedSizes = this.utils.getPageSizes(this.spoilageDataSource.data);
    if (!generatedSizes.includes(5)) {
      generatedSizes.unshift(5);
    }
    this.pageSizes = generatedSizes;
    
    setTimeout(() => {
      this.spoilageDataSource.paginator = this.paginator;
      this.cdr.detectChanges();
    });
  }

  closeDialog() {    
    this.dialog.closeAll();
  }

  printPdf() {
    this.purchases.printPdf(this.spoilageData, 'Spoilage').subscribe(data => {
      this.purchases.globalPrintPdf(data.eFile);
    });
  }

  tenantDetail() {
    let obj = {
      tenantId: this.user.tenantId
    }
    this.branchTransfer.tenantDetails(obj).subscribe(res => {
      if (res.result == 'success') {
        let editAccess = res.data[0] && res.data[0].permission && res.data[0].permission.spoilageAccess ? res.data[0].permission.spoilageAccess.edit : false;
        if (editAccess) {
          let rolesList = (res.data[0] && res.data[0].permission && res.data[0].permission.spoilageAccess) ? res.data[0].permission.spoilageAccess.editAccess : [];
          this.editAccess = (rolesList.some(role => role.toLowerCase() === this.user.role.toLowerCase())) ? true : false ;
        } else {
          this.editAccess = false ;
        }
      } else {
        this.editAccess = false ;
      }
    })
  }
}
