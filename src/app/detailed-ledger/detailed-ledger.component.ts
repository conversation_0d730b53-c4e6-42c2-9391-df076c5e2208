import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { AuthService, BranchTransferService, PurchasesService, ShareDataService } from '../_services';
import { first } from 'rxjs/operators';
import { UtilsService } from '../_utils/utils.service';
import { Router } from '@angular/router';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog } from '@angular/material';

@Component({
  selector: 'app-detailed-ledger',
  templateUrl: './detailed-ledger.component.html',
  styleUrls: ['./detailed-ledger.component.scss', "./../../common-dark.scss"]
})
export class DetailedLedgerComponent implements OnInit {
  dataSource = new MatTableDataSource();
  @ViewChild(MatSort) sort: MatSort;
  displayedColumns: string[];
  pageSizes = []
  searchText: string;
  user: any;
  @ViewChild('openD') openD: TemplateRef<any>;
  @ViewChild('openConformationDialog') openConformationDialog: TemplateRef<any>;
  dialogItems: any[];
  adequateItems: any[];
  inAdequateItems: any[];
  currLedgerItems: any;
  ledgerData: any;
  
    constructor(private shareData: ShareDataService,
    public utils: UtilsService,
    private router: Router,
    private branchTransfer: BranchTransferService,
    private auth: AuthService,
    public dialog: MatDialog,
    private purchases: PurchasesService, 
    ) { 
    this.user = this.auth.getCurrentUser();

    this.shareData.currLedger.pipe(first()).subscribe(data => {
      
      if (Object.keys(data).length === 0) {
        this.utils.snackBarShowInfo("data not available")
        this.router.navigate(['/home/<USER>'])
      } else {
        // itemName
        this.currLedgerItems = data.invItems
        this.ledgerData = data
        this.getStockDatas(data.invItems)
      }
    })
  }

  ngOnInit() {
  }

  getStockDatas(item){
    let obj = {
      item : item,
    }
    this.branchTransfer.getStockData(obj).subscribe(res => {
      this.displayedColumns = ['itemName', 'packageName' , 'category' , 'subCategory','Qty','inclTax','total']
      this.dataSource.data = res.data
      this.dialogItems = this.dataSource.data
      this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
    })
  }

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }

  focusFunctionWithOutForm(element){
    if(Number(element.adjustQty) === 0){
      element.adjustQty = null;
    }
  }
  
  focusOutFunctionWithOutForm(element){
    if(element.adjustQty === null){
      element.adjustQty = 0
    }
  }

  getTotalPrCost(event,element) {
    element.adjustQty = element.adjustQty ? element.adjustQty : 0 ;
    // element.subTotal = element.adjustQty * element.unitPrice
    // element.taxAmount = element.taxRate / 100 * element.subTotal
    // element.totalPrice = element.taxAmount + element.subTotal  
  }

  close(){
    const dialogRefTemplate = this.dialog.open(this.openD, {
      minWidth: '25vw',
    });
  }

  openConformation(){

    const dialogRefTemplate = this.dialog.open(this.openConformationDialog, {
      minWidth: '25vw',
    });
  }

  sendReqAdequate(){
    this.getStockDatas(this.currLedgerItems)
    this.adequateItems = this.dataSource.data.filter(item => item['instockData'] > item['adjustQty'] || item['instockData'] === item['adjustQty']);
    this.inAdequateItems = this.dataSource.data.filter(item => item['adjustQty'] > item['instockData']);
    
    if(this.adequateItems.length > 0 && this.inAdequateItems.length == 0){
      let obj = {
        items : this.adequateItems
      }
      this.branchTransfer.reduceSpoilage(obj).subscribe(res => {
        if(res['result'] == 'success'){
          this.utils.snackBarShowSuccess("ledger closed successfully. All inventory reduction have been completed")
        }else{
          this.utils.snackBarShowWarning("something went wrong")
        }
      })
    }else if(this.inAdequateItems.length > 0){
      const dialogRefTemplate = this.dialog.open(this.openConformationDialog, {
        minWidth: '25vw',
      });
    }
  }

  sendReqInAdequate(){
      let obj = {
        items : this.adequateItems
      }
      this.branchTransfer.reduceSpoilage(obj).subscribe(res => {
        if(res['result'] == 'success'){
          this.utils.snackBarShowSuccess("ledger closed successfully. Reduction for item with inadequate stock have been skipped")
        }else{
          this.utils.snackBarShowWarning("something went wrong")
        }
      })
  }

  closeAdequateLedger(){
    this.utils.snackBarShowSuccess("ledger closed successfully, without Reduction")
    this.router.navigate(['/home/<USER>'])
  }

  closeInAdequateLedger(){
    this.utils.snackBarShowWarning("ledger closure is pending")
  }

  goBack(){
    this.router.navigate(['/home/<USER>'])
  }

  getTotal(key: string) {
    return this.utils.getTotal(this.dataSource.data, key); 
  }

  calculateAmount(element) {
    element.totalPrice = this.utils.truncateNew((element.withTaxPrice * element.adjustQty),2)
    return this.utils.truncateNew((element.withTaxPrice * element.adjustQty),2)
  }

}
