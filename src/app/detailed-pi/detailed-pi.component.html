<div class="title">
  <button mat-raised-button class="button" style="float: left;" (click)=goBack() >
    <mat-icon>keyboard_backspace</mat-icon> Back to PI
  </button>
  <button mat-raised-button class="button" *ngIf="grn.piId !== undefined && !piApprovalScreen && showData " style="float: right;" (click)="printpdf()">
    Print
  </button>
  <button mat-raised-button class="button" *ngIf="grn.piId !== undefined && !piApprovalScreen && showData" style="float: right;" (click)="downloadPdf()">
    Download PDF
  </button>
  <button mat-raised-button class="button" *ngIf="grn.piId !== undefined && !piApprovalScreen && showData" style="float: right;" (click)="exportToExcel()">
    Export
  </button>
  <div  style="float: right;" [matTooltip]="(showData)  ? 'PI already created' : '' ">
    <!-- <button mat-raised-button class="button" *ngIf = '!piApprovalScreen && !showData || showData' style="float: right; margin-right: 5px;" (click)="generatePi()" [disabled] = "showData">
      {{ approvalRequired ? 'Generate PI Approval' : 'Generate PI' }}
    </button> -->

    <button mat-button mat-raised-button id="save-btn" class="button3" style="float: right;"
    *ngIf = '!piApprovalScreen && !showData || showData' style="float: right; margin-right: 5px;" (click)="generatePi()" [disabled] = "showData || requestPending ">
    {{ approvalRequired ? 'Generate PI Approval' : 'Generate PI' }}
    </button>


  </div>
  <button mat-raised-button class="button" *ngIf = 'piApprovalScreen' [disabled] = "isDone" style="float: right; margin-right: 5px;" (click)="rejectIndent()">
    Reject
  </button>
  <button mat-raised-button class="button" *ngIf = 'piApprovalScreen' [disabled] = "isDone" style="float: right; margin-right: 5px;" (click)="approveIndent()">
    Approve
  </button>
</div>

<mat-card >
  <div class="row">
    <div class="col">
      <table class="table">
        <tbody>
          <tr>
            <th class="topItemkey" scope="row">Recipient Name</th>
            <td>{{ grn.tenantName }} {{ this.receiveBranch | titlecase }}</td>
          </tr>
          <tr *ngIf="grn.details.poId">
            <th class="topItemkey" scope="row">Order Number</th>
            <td>{{ grn.details.poId }}</td>
          </tr>
          <tr>
            <th class="topItemkey" scope="row">GRN Made By</th>
            <td>{{ grn.grnMadeBy }}</td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="col">
      <table class="table">
        <tbody>
          <tr>
            <th class="topItemkey" scope="row">Sender Name</th>
            <td>{{ grn.vendorName }} {{ this.vendorBranch | titlecase }}</td>
          </tr>
          <tr>
            <th class="topItemkey" scope="row">GRN Id </th>
            <td>{{ grn.grnId }}</td>
          </tr>
          <tr>
            <th class="topItemkey" scope="row">PO Made By</th>
            <td>{{ grn.poMadeBy }}</td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="col">
      <table class="table">
        <tbody>
          <tr>
            <th class="topItemkey" scope="row">Invoice Id</th>
            <td>{{ grn.invoiceId }}</td>
          </tr>
          <tr *ngIf="grn.grnDocumentDate">
            <th class="topItemkey" scope="row">Goods Received Date</th>
            <td>{{ grn.grnDocumentDate | date: "EE, MMMM d, y"}}</td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="col">
      <table class="table">
        <tbody>
          <!-- <tr>
            <th class="topItemkey" scope="row">GRN Created Date</th>
            <td>{{ grn.createTs | date: "EE, MMMM d, y" }}</td>
          </tr> -->
          <tr>
            <th class="topItemkey" scope="row">GRN Date(System Entry Date)</th>
            <td>{{ grn.createTs | date: "EE, MMMM d, y" }}</td>
          </tr>
          <tr *ngIf="grn.piId">
            <th class="topItemkey" scope="row">PI Id </th>
            <td>{{ grn.piId }}</td>
          </tr>
          <tr *ngIf="grn.details.ibtId">
            <th class="topItemkey" scope="row">Request Id</th>
            <td>{{ grn.details.ibtId }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

    <div class="row">
      <div class="col">
        <mat-form-field  appearance="outline" style="width: 210px !important">
          <mat-label style="font-size: larger; font-weight: bolder;"> <b>Remark</b> </mat-label>
          <textarea matInput [(ngModel)]="remark"  ></textarea>
        </mat-form-field>
      </div>
      <div class="col">
        <mat-form-field  appearance="outline" style="width: 210px !important">
          <mat-label style="font-size: larger; font-weight: bolder;"><b>Payment Terms</b></mat-label>
          <textarea matInput [(ngModel)]="terms"  ></textarea>
        </mat-form-field>
      </div>
      <div class="col">
        <mat-form-field  appearance="outline" style="width: 210px !important">
          <mat-label style="font-size: larger; font-weight: bolder;"><b>Po Terms</b></mat-label>
          <textarea matInput [(ngModel)]="poTerms"  ></textarea>
        </mat-form-field>
      </div>
      <div class="col">
        <mat-form-field appearance="outline" style="width: 210px !important"> 
          <mat-label> <b>Payment Method</b> </mat-label>
          <mat-select [(value)]="paymentMethod" placeholder="Payment Method">
            <mat-option *ngFor="let method of paymentMethods" [value]="method">
              {{method}}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </div>
    
</mat-card>

<div class="matNavList mb-2" *ngIf = '!piApprovalScreen'>
  <mat-nav-list>
    <mat-expansion-panel >
      <mat-expansion-panel-header class="matExpansionTitle">
        <p (click)="previewInvoiceOpen()">Preview Invoice</p>
        <div class="spinner-border ml-2" role="status" *ngIf="previewInvoiceLoader">
          <span class="sr-only">Loading...</span>
        </div>
      </mat-expansion-panel-header>
      <app-image-viewer [images]="images" [idContainer]="'idOnHTML'" [loadOnInit]="true"  *ngIf="previewInvoice">
      </app-image-viewer>
    </mat-expansion-panel>
  </mat-nav-list>
</div>


<!-- <div class="image-container" *ngIf="previewInvoice">
  <app-image-viewer [images]="images" [idContainer]="'idOnHTML'" [loadOnInit]="true" *ngIf="previewInvoice">
  </app-image-viewer>
    <mat-icon class="close-button" *ngIf="previewInvoice" (click)="previewInvoiceClose()">close</mat-icon>
</div>  *ngIf="!previewInvoice"-->
<br>
<mat-card class="card">

  <section class="example-container-1 mat-elevation-z8">
  <table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort>
      <ng-container matColumnDef="select">
        <th mat-header-cell *matHeaderCellDef>
          <mat-checkbox *ngIf="user.uType === 'restaurant'" #selectAll (change)="$event ? masterToggle() : null"
            [checked]="selection.hasValue() && isAllSelected()"
            [indeterminate]="selection.hasValue() && !isAllSelected()" [aria-label]="checkboxLabel()">
          </mat-checkbox>
        </th>
        <td mat-cell *matCellDef="let row">
          <mat-checkbox *ngIf="user.uType === 'restaurant'"
            (change)="$event ? selection.toggle(row) : null; getTotal($event)" [checked]="selection.isSelected(row)"
            [aria-label]="checkboxLabel(row)">
          </mat-checkbox>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>
  
      <ng-container matColumnDef="index">
        <th mat-header-cell *matHeaderCellDef class="tableId"><b>#</b></th>
        <td mat-cell *matCellDef="let element; let i = index" class="name-cell tableId">
          {{ i + 1 }}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>
  
      <ng-container matColumnDef="itemName" sticky>
        <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell">
          <b> Item Name</b>
        </th>
        <td mat-cell *matCellDef="let element" class="name-cell">
          <span *ngIf="this.grn.grnType == 'po'">
            {{ element.itemName | titlecase }}
          </span>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
        <mat-divider></mat-divider>
      </ng-container>
  
      <ng-container matColumnDef="itemCode">
        <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell"><b> Item Code</b> </th>
        <td mat-cell *matCellDef="let element" class="name-cell"> {{element.itemCode}} </td>
        <td mat-footer-cell *matFooterCellDef>Total</td>
      </ng-container>
  
      <ng-container matColumnDef="pkgName" sticky>
        <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell"><b> Pkg Name</b> </th>
        <td mat-cell *matCellDef="let element" class="name-cell">
          <span *ngIf="this.grn.grnType == 'po'">
            {{element.packages[0].packageName | titlecase}}
          </span>
        </td>
        <mat-divider></mat-divider>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>
  
      <ng-container matColumnDef="entryType" sticky>
        <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell"><b> Entry Type</b> </th>
        <td mat-cell *matCellDef="let element" class="name-cell">
          {{element.entryType | titlecase}}
        </td>
        <mat-divider></mat-divider>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>
  
      <ng-container matColumnDef="unitPerPkg">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> <b>Units/Pkg</b> </th>
        <td mat-cell *matCellDef="let element">
          {{element.packages[0].unitPerPkg}}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>
  
      <ng-container matColumnDef="quantity">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Order Quantity</b>
        </th>
        <td mat-cell *matCellDef="let element">
          {{ element.quantity }}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="taxRate">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Tax Rate</b>
        </th>
        <td mat-cell *matCellDef="let element">
            <input [disabled]="piApprovalScreen" class="input1" type="number" step="any" min="0"
            [(ngModel)]="element.taxRate" 
            (focus)="focusFunctionWithOutForm(element,'taxRate')" (focusout)="focusOutFunctionWithOutForm(element,'taxRate')"/>
            <!-- onkeypress=" (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)"  -->
            <!-- <span *ngIf="element.itemStatus === 'complete' || user.uType === 'vendor'">{{ element.taxRate }}</span> -->
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>
    
      <ng-container matColumnDef="receivedQty">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Received Quantity</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <input (keyup)="validateReceviedQty(element)" 
            class="input1" type="number" step="0.01" min="0" [disabled]="piApprovalScreen" [(ngModel)]="element.receivedQty"
            (focus)="focusFunctionWithOutForm(element,'receivedQty')" (focusout)="focusOutFunctionWithOutForm(element,'receivedQty')"/>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>
  
      <ng-container matColumnDef="pendingQty">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Pending Quantity</b>
        </th>
        <td mat-cell *matCellDef="let element">
          {{ element.pendingQty }}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>
      <ng-container matColumnDef="taxAmt">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Tax Amt</b></th>
        <td mat-cell *matCellDef="let element">
          {{this.utils.truncateNew(
          ((element.taxAmount/element.quantity ) *
          element.receivedQty))
          }}
          <input type="text" style="display: none;" [ngModel]=" ((element.taxAmount/element.quantity ) * element.receivedQty)">
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getTaxTotal())}}</td>
      </ng-container>
  
  
      <ng-container matColumnDef="totalValue">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Total (incl.tax,etc)</b></th>
        <td mat-cell *matCellDef="let element">
          {{this.utils.truncateNew(
          ((element.receivedQty * element.packages[0].packagePrice) -
          element.discAmt) + element.cessAmt +element.extraAmt +((element.receivedQty *
          element.packages[0].packagePrice) -
          element.discAmt)* (element.taxRate /100))
          }}
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getTotal())}}</td>
      </ng-container>
  
  
      <ng-container matColumnDef="unitPrice">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Unit Cost</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <input class="input1" type="number" step="0.01" min="0" [disabled]="piApprovalScreen" [(ngModel)]="element.packages[0].packagePrice" 
          (focus)="focusFunctionWithOutForm(element,'packages[0].packagePrice')" (focusout)="focusOutFunctionWithOutForm(element,'packages[0].packagePrice')"/>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>
  
      <ng-container matColumnDef="subTotal">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Total (excl.tax)</b></th>
        <td mat-cell *matCellDef="let element">
          {{this.utils.truncateNew(
          ((element.packages[0].packagePrice ) *
          element.receivedQty))
          }}
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getSubTotal())}}</td>
      </ng-container>
  
      <ng-container matColumnDef="taxbleValue">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Taxble Amount</b></th>
        <td mat-cell *matCellDef="let element">
          {{this.utils.truncateNew(
          ((element.receivedQty * element.packages[0].packagePrice) -
          element.discAmt))
          }}
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getTaxbleTotal())}}</td>
      </ng-container>
  
      <ng-container matColumnDef="cessAmt">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Cess Amt</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <input *ngIf="element.itemStatus != 'complete'"  class="input1" type="number" step="any" min="0"
            [(ngModel)]="element.cessAmt"
            onkeypress=" (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)" />
          <span *ngIf="element.itemStatus === 'complete' || user.uType === 'vendor'">{{ element.cessAmt }}</span>
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getFieldTotal('cessAmt'))}}</td>
      </ng-container>
  
      <ng-container matColumnDef="extraAmt">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Extra Charge</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <input *ngIf="element.itemStatus != 'complete'"  class="input1" type="number" step="any" min="0"
            [(ngModel)]="element.extraAmt"
            onkeypress=" (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)" />
          <span *ngIf="element.itemStatus === 'complete' || user.uType === 'vendor'">{{ element.extraAmt }}</span>
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getFieldTotal('extraAmt'))}}</td>
      </ng-container>
  
      <ng-container matColumnDef="discnt">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Discount</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <input *ngIf="element.itemStatus != 'complete'"  class="input1" type="number" step="any" min="0"
            [(ngModel)]="element.discAmt"
            onkeypress=" (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)" />
          <span *ngIf="element.itemStatus === 'complete' || user.uType === 'vendor'">{{ element.discAmt }}</span>
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getFieldTotal('discAmt'))}}</td>
      </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky:true;"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
    <tr mat-footer-row *matFooterRowDef="displayedColumns"></tr>
  </table>
</section>
</mat-card>


