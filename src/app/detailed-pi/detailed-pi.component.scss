.wrapper {
  position: relative;
  display:flex;
  overflow: hidden;
  margin-left: auto;
  margin-right: auto;
  align-items: center;
}

.wrapper::after {
  display: block;
  clear: both;
  content: '';
}
.wrapper div {
  float: left;
  margin-right: -100%;
}
.btn-prev:hover,.btn-next:hover,
.btn-prev:focus,.btn-next:focus {
  opacity:.9
}

.btn-prev,
.btn-next {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 15%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color:transparent;
  border:0;
  color:white;
  opacity: .5;
  transition: opacity .15s ease;
}

.btn-next{
  right: 0;
}

.card {
  margin-top: -27px;
  padding-top: 18px !important;
}

.previewInvoiceInputs{
  text-align: center;
  justify-content: center;
  align-items: end;
}

::ng-deep .row {
  display: flex;
  flex-wrap: wrap;
}

::ng-deep .image-gallery-2 .next{
  color: white;
  font-size: 33px;
  background-color: rgba(0, 0, 0, 0.603);
}

::ng-deep .image-gallery-2 .prev{
  color: white;
  font-size: 33px;
  background-color:  rgba(0, 0, 0, 0.603);
}

::ng-deep .image-gallery-2 .next:hover {
  right: 25px !important;
  transition: linear 0.3s;
  color: green !important;
}

::ng-deep .image-gallery-2 .prev:hover {
    left: 25px !important;
    transition: linear 0.3s;
    color: green !important;
}

.image-container {
  margin: -27px 38px -40px 38px;
}

.close-button {
  position: fixed;
  font-size: xx-large;
  right: 1%;
  top: 1px;
  margin-top: 51px;
  margin-right: 43px;
  transition-duration: 1s;
}

.close-button:hover{
  color: rgb(255, 0, 0);
}


.formFieldAlingn{
  display: block !important;
  float: unset !important;
}

.threeInputBox{
  width: 103% !important;
}

.fieldboxCol{
  display: inline-grid;
}

.poAndGrnInputs{
  display: flex;
}

.poAndGrnInputstwo{
  display: inline-grid;
}

.poMargin{
  margin-left: -15px !important;
}

.grnMargin{
  margin-right: -15px !important;
}

::ng-deep .options-image-viewer.ng-star-inserted {
  margin-right: 37px !important;
}

::ng-deep .mat-form-field-appearance-outline .mat-form-field-infix {
  padding: 0.5em 0 0.8em 0 !important;
}

.row label {
  clear: both;
  float:left;
  margin-right:15px;
}

::ng-deep textarea.mat-input-element {
  padding: 0px 0 !important;
  margin: -8px 0 !important;
}

.row table th {
  border-left: none !important;
}

.matNavList{
  margin-left: 2%;
  margin-right: 2%;
}

.example-container-1{
  max-height: 300px;
  overflow-y: auto;
}