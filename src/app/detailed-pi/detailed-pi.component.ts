import { Component, ElementRef, OnInit, <PERSON>ry<PERSON><PERSON>, ViewChildren,ViewChild } from '@angular/core';
import { PurchasesService, ShareDataService, AuthService } from '../_services';
import { GlobalsService } from '../_services/globals.service';
import { MatDialog, MatInput, MatTableDataSource } from '@angular/material';
import { SelectionModel } from '@angular/cdk/collections';
import { Location } from '@angular/common';
import { UtilsService } from '../_utils/utils.service';
import { ChangeDetectorRef } from '@angular/core';
import { NotificationService } from '../_services/notification.service';
import { DomSanitizer } from '@angular/platform-browser';
import { SimpleDialogComponent } from '../_dialogs/simple-dialog/simple-dialog.component';
import { element } from 'protractor';
import { environment } from 'src/environments/environment';
@Component({
  selector: 'app-detailed-pi',
  templateUrl: './detailed-pi.component.html',
  styleUrls: ['./detailed-pi.component.scss', "./../../common-dark.scss"]
})
export class DetailedPiComponent implements OnInit {
  editRowId:number=-1
  @ViewChildren(MatInput,{read:ElementRef}) inputs:QueryList<ElementRef>;
  grn: any;
  user: any;
  dataSource: MatTableDataSource<any>;
  displayedColumns: string[];
  receiveBranch: string;
  vendorBranch: string;
  remark:string;
  terms:string;
  poTerms:string;
  grnId :string;
  selection = new SelectionModel<any>(true, []);
  tenantId: any;
  restaurantId: any;
  poMadeBy:string;
  grnMadeBy :string;
  paymentMethod :string;
  paymentMethods =[
    "Cash",
    "NEFT",
    "Other"
  ];
  users = [];
  images= [];
  previewInvoice: boolean = false;
  height=0;
  previewInvoiceLoader: boolean = false;
  private specialKeys: Array<string> = ['Backspace', 'Tab', 'End', 'Home', 'ArrowLeft', 'ArrowRight', 'Del', 'Delete'];
  private regex: RegExp = new RegExp(/^\d*\.?\d{0,2}$/g);
  grandTotal: number;
  piApprovalScreen: boolean = false;
  isDone: boolean = true;
  dialogRef: any;
  showData: boolean = false;
  approvalRequired: boolean = false;
  requestPending: boolean;

  constructor(
    private sanitizer: DomSanitizer,
    private notifyService: NotificationService,
    private sharedData: ShareDataService, 
    private auth: AuthService,
    private cdref: ChangeDetectorRef,
    private loc: Location,
    private utils: UtilsService, 
    private dialog: MatDialog,
    private purchases: PurchasesService) {
  }
  
  previewInvoiceOpen() {
    this.previewInvoiceLoader = true
    var reqObj={
      grnId : this.grn.grnId,
      tenantId : this.grn.tenantId,
      restaurantId : this.grn.restaurantId
    }
    this.purchases.retrieveInvoice(reqObj).subscribe(res => {
      if (res['success']){
        this.images = res['data'];
        this.previewInvoiceLoader = false;
        if(this.images.length > 0){
          this.previewInvoice = true;
          this.previewInvoiceLoader = false;
        }else {
          this.previewInvoice = false;
          this.utils.snackBarShowWarning("No invoices to display")
        }  
      }
    }, err => {})
  }

  previewInvoiceClose(){
    this.previewInvoice = false;
  }

  getUsersList(){
    var reqObj ={
      tenantId : this.tenantId
    }
    this.purchases.getUsers(reqObj).subscribe(data => {
      if (data.success){
        this.users = data['usersList']
      }
    }, err => {})
  }
  ngOnInit() {
    try{
      this.sharedData.currPi.subscribe(grn => {
        if (!grn.grnId)
          this.loc.back();
        this.grn = grn;
        this.grnId = grn._id.$oid;
        this.tenantId = grn.tenantId;
        if ("POTerms" in grn['vendorDetails'][0]){
          this.terms = grn['vendorDetails'][0]['POTerms']
        }
        this.poTerms = ("poTerms" in this.grn) ? this.grn['poTerms'] : undefined ;
        if ("remarks" in grn){
          this.remark = grn.remarks;
          this.grn['remarks'] = this.remark;
        }else{
          this.remark = grn['details'].remarks
          this.grn['remarks'] = this.remark;
        }
        if ("poMadeBy" in grn){
          this.poMadeBy = grn.poMadeBy;
          this.grn['poMadeBy'] = this.poMadeBy;
        }
        if ("grnMadeBy" in grn){
          this.grnMadeBy = grn.grnMadeBy;
          this.grn['grnMadeBy'] = this.grnMadeBy;
        }
        if ("paymentMethod" in grn){
          this.paymentMethod = grn.paymentMethod;
          this.grn['paymentMethod'] = this.paymentMethod;
        }else{
          this.paymentMethod = grn['details'].paymentMethod;
          this.grn['paymentMethod'] = this.paymentMethod;
        }
        if ("vendorTermsAndCondition" in grn){
          this.terms = grn.vendorTermsAndCondition;
          this.grn['vendorTermsAndCondition'] = this.terms;
        }  else{
          this.terms = grn['details'].paymentTerms;
          this.grn['vendorTermsAndCondition'] = this.terms;
        }      
        this.restaurantId = grn.restaurantId;
        this.receiveBranch = grn['restaurantId'].split('@')[1];
        if (grn['vendorBranch'] != null) {
          this.vendorBranch = grn['vendorBranch'].split('@')[1];
        }
        else {
          this.vendorBranch = '';
        }
        this.user = this.auth.getCurrentUser();
        this.grn.deliveryDate = new Date(grn.deliveryDate);
        this.grn.createTs = new Date(grn.createTs);
        this.dataSource = new MatTableDataSource<any>();
        this.dataSource.data = this.grn.hasOwnProperty('grnItems') ? this.grn.grnItems : this.grn.items ;
        (this.grn.hasOwnProperty('approvalDetail') && this.grn.approvalDetail.length > 0) ? this.getApprovalStatus() : (this.grn.piId  ? this.showData = true : undefined) ;
        this.getBranchDetails() ;
        this.piApprovalScreen = this.grn.piApprovalScreen ;
        this.displayedColumns = GlobalsService.detailedIbtGrnColumns;
        if (this.grn.grnType == 'po') {
          this.displayedColumns = ['index','itemCode', 'itemName', 'pkgName', 'unitPerPkg', 'quantity', 'receivedQty', 'unitPrice', 'subTotal','taxRate', 'taxAmt', 'totalValue'];
          if (this.grn.extraFieldFlag) {
            this.displayedColumns.splice(9, 0, 'extraAmt', 'discnt', 'cessAmt',)
          }
        }
      }, err => {
        console.error(err)
      });
    }catch{
      console.log("error")
    }
  }

  
  ngAfterContentChecked() {
    this.cdref.detectChanges();
  }

  generatePi(){
    this.dataSource.data
    // if(this.grn.piId == undefined){
      var reqObj ={
        grnId : this.grnId,
        uId: this.user.mId,
        grnNo : this.grn.grnId,
        workArea : this.grn.directIndentAreas,
        grandTotal : this.grandTotal,
        tenantId : this.tenantId,
        restaurantId : this.restaurantId,
        piItems : this.dataSource.data,
        remarks : this.remark,
        poTerms : this.poTerms,
        poMadeBy : this.poMadeBy,
        userEmail : this.user.email,
        baseUrl : environment.baseUrl,
        grnMadeBy : this.grnMadeBy,
        paymentMethod : this.paymentMethod,
        vendorTermsAndCondition : this.terms
      }
      this.purchases.createPi(reqObj).subscribe(data => {
        if (data.success){
          this.utils.snackBarShowSuccess(data.message)
        }else{
          this.utils.snackBarShowError(data.message)
        }
        this.loc.back();
  
      }, err => {})
    // } else{
    //   this.utils.snackBarShowError("Invoice Generated Already")
    // }
  }

  printpdf() {
    this.purchases.printpdfs(this.grn, 'Grn').subscribe(data => {
      // window.open('data:application/pdf;base64,' + data.eFile);
      this.purchases.globalPrintPdf(data.eFile);
    });
  }

  downloadPdf(){
    this.purchases.printpdfs(this.grn, 'Grn').subscribe(data => {
      var downloadLink = document.createElement("a");
      downloadLink.href = 'data:application/vnd.ms-excel;base64,'+data.eFile;
      downloadLink.download = data.fileName;
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
    });
  }
  exportToExcel() {
    this.purchases.exportToExcel(this.grn, 'Grn').subscribe(data => {
      window.open('data:text/csv;base64,' + data.eFile);
    });
  }

  goBack() {
    this.loc.back()
  }

// ##################################################################################


  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  masterToggle() {
    this.isAllSelected() ?
      this.selection.clear() :
      this.dataSource.data.forEach(row => this.selection.select(row));
  }

  checkboxLabel(row?: any): string {
    if (!row) {
      return `${this.isAllSelected() ? 'select' : 'deselect'} all`;
    }
    return `${this.selection.isSelected(row) ? 'deselect' : 'select'} row ${row.itemCode + 1}`;
  }

  validateReceviedQty(element) {
    element.quantity < element.receivedQty ? element.receivedQty = element.quantity : element;
  }

  getSubTotal() {
    let subTotalSum = 0
    if (this.selection.selected.length ==0) {
      this.dataSource.data.forEach(element => {
        subTotalSum += element.receivedQty * element.packages[0].packagePrice
      });
    }
    else {
      this.selection.selected.forEach(element => {
        subTotalSum += element.receivedQty * element.packages[0].packagePrice
      });
    }
    return subTotalSum
  }

  getTaxTotal() {
    let taxTotal = 0
    if (this.selection.selected.length ==0) {
      this.dataSource.data.forEach(element => {
        let afterDiscount = 0;
        let tax = 0;
        let tempTotal =0
        let tempUnitPrice = 0;
        afterDiscount = (element.receivedQty * element.packages[0].packagePrice) - element.discAmt
        tempTotal = ((element.receivedQty * element.packages[0].packagePrice) - element.discAmt) + element.cessAmt +element.extraAmt +((element.receivedQty * element.packages[0].packagePrice) - element.discAmt)* (element.taxRate /100)
        tempUnitPrice = element.packages[0].packagePrice;
        tax = afterDiscount * (element.taxRate / 100)
        element.subTotal = afterDiscount;
        element.taxAmount = tax;
        element.totalPrice = tempTotal;
        element.unitPrice = tempUnitPrice;
        taxTotal += element.receivedQty * (element.taxAmount / element.quantity)
      });
    }
    else {
      this.selection.selected.forEach(element => {
        let afterDiscount = 0;
        let tax = 0;
        afterDiscount = (element.receivedQty * element.packages[0].packagePrice) - element.discAmt
        tax = afterDiscount * (element.taxRate / 100)
        element.subTotal = afterDiscount;
        element.taxAmount = tax;
        taxTotal += element.receivedQty * (element.taxAmount / element.quantity)
      });
    }
    return taxTotal
  }

  getTaxbleTotal() {
    let taxbleSum = 0
    if (this.selection.selected.length ==0) {
      this.dataSource.data.forEach(element => {
        taxbleSum += (element.receivedQty * element.packages[0].packagePrice) - element.discAmt
      });
    }else {
      this.selection.selected.forEach(element => {
        taxbleSum += (element.receivedQty * element.packages[0].packagePrice) - element.discAmt
      });
    }
    return taxbleSum
  }


  getFieldTotal(key: string) {
    if (this.selection.selected.length ==0)
      return this.utils.getTotal(this.dataSource.data, key);
    else
      return this.utils.getTotal(this.selection.selected, key);
  }

  getTotal() {
    let totalPrice = 0
    if (this.selection.selected.length ==0) {
      this.dataSource.data.forEach(element => {
        let afterDiscount = 0;
        let tax = 0;
        afterDiscount = (element.receivedQty * element.packages[0].packagePrice) - element.discAmt
        tax = afterDiscount * (element.taxRate / 100)
        element.subTotal = afterDiscount;
        element.taxAmount = tax;
        totalPrice += afterDiscount + element.cessAmt + element.extraAmt + tax;
        
      });
    }
    else {
      this.selection.selected.forEach(element => {
        let afterDiscount = 0;
        let tax = 0;
        afterDiscount = (element.receivedQty * element.packages[0].packagePrice) - element.discAmt
        tax = afterDiscount * (element.taxRate / 100)
        element.subTotal = afterDiscount;
        element.taxAmount = tax;
        totalPrice += afterDiscount + element.cessAmt + element.extraAmt + tax;
      });
    }
    this.grandTotal = totalPrice
    return totalPrice
  }

  focusFunctionWithOutForm(element , value){
    if(Number(element[value]) === 0){
      element[value] = null;
    }
  }
  
  focusOutFunctionWithOutForm(element , value){
    if(element[value] === null){
      element[value] = 0
    }
  }

  getApprovalStatus() {
    let role = this.user.role
    const status = this.grn.approvalDetail.filter(function(element) {
      return element.role === role;
    });
    this.isDone = status[0].status === 'pending' ? false : true ;
    let approvalDetails = this.grn.approvalDetail.filter((element) => element.status === 'approved') ;
    this.showData = (approvalDetails.length === this.grn.approvalDetail.length) ? true : false ;

    let piStatus = this.getStatus(this.grn.approvalDetail) ;

    if (piStatus == 'approved'){
      this.requestPending = false ;
    } else if (piStatus == 'rejected') {
      this.requestPending = false ;
    } else {
      this.requestPending = true ;
    }
  }

  getStatus(data) {
    if (Object.keys(data).length !== 0) {
      const levelOrder = data.map(item => item.level);
      let statusWithRole = "";
      for (const currentLevel of levelOrder) {
        const matchingData = data.find(item => item.level === currentLevel);
        
        if (matchingData) {
          const { level, status, role } = matchingData;
          
          if (status === "rejected") {
            statusWithRole = status;
            break;
          } else if (status === "pending" && !statusWithRole.includes("rejected")) {
            statusWithRole = status;
          } else if (status === "approved" && !statusWithRole.includes("rejected") && !statusWithRole.includes("pending")) {
            statusWithRole = status;
          }
        }
      }
      return statusWithRole;
    }
  }

  approveIndent() {
    let obj = {}
    obj['tenantId'] = this.user.tenantId
    obj['restaurantId'] = this.restaurantId
    obj['role'] = this.user.role
    obj['piId'] = this.grn.piId
    obj['appCat'] = this.grn.approvalCategory
    this.purchases.approvalPI(obj).subscribe(data => {
      if (data.success == true) {
        this.utils.snackBarShowSuccess(`Approved Successfully`) ;
        this.isDone = true ;
      }
      else {
        this.utils.snackBarShowError('Something went wrong.Please try again later')
      }
    })
  }

  rejectIndent() {
    let obj = {}
    obj['tenantId'] = this.user.tenantId
    obj['restaurantId'] = this.restaurantId
    obj['role'] = this.user.role 
    obj['piId'] = this.grn.piId
    obj['appCat'] = this.grn.approvalCategory
    this.dialogRef = this.dialog.open(SimpleDialogComponent, {
      data: {
        title: 'Reject Reason',
        msg: 'Enter Reason(min 10 and max 40 characters allowed)',
        inputFromUser: { 'Reason': '' },
        ok: function () {
          this.dialogRef.afterClosed().subscribe(res => {
            this.reason = res['Reason'];
            if(res['Reason'] == ''){
              this.utils.snackBarShowInfo('Please enter Reason')
            }else{
              obj['reason'] = this.reason;
              this.purchases.rejectPI(obj).subscribe(data => {
                if (data.success == true) {
                  this.utils.snackBarShowSuccess(`Rejected Successfully`)
                  this.isDone = true ;
                }
                else {
                  this.utils.snackBarShowError('Something went wrong.Please try again later')
                }
              }, err => console.error(err))
            }
          })
        }.bind(this)
      }
    });
  }

  getBranchDetails() {
    let obj = {}
    obj['tenantId'] = this.user.tenantId;
    obj['restaurantId'] =   this.restaurantId,
    obj['type'] = 'piApproval',
    this.purchases.getBranchDetails(obj).subscribe(data => {
      this.approvalRequired = data.approvalRequired;
      // this.approvalRequired = true;
    })
  }

}
