<div class="title">
  <button mat-raised-button class="button" style="float: left; margin-left: 0px;"
   (click)="goToPl()" matTooltip="back to Contract">
    <mat-icon>keyboard_backspace</mat-icon> Back to List
  </button>

  <button mat-raised-button class="button templateBtn  ml-3" matTooltip="Upload & Update Template" (click)="fileInput.value=''; checkUpload()" [disabled]="this.exportStatusData?.export === false">
    Import
      <!-- <mat-icon *ngIf="importStatusDataBoolean == true && this.importStatusData.import === true" class="check_circle" >check_circle</mat-icon>
      <div class="spinner-border" role="status" *ngIf="importStatusDataBoolean == true && this.importStatusData.import === false" >
        <span class="sr-only">Loading...</span>
      </div> -->
      <mat-icon *ngIf="importStatusCircle == true " class="check_circle" >check_circle</mat-icon>
      <div class="spinner-border" role="status" *ngIf="importStatusLoading == true" >
        <span class="sr-only">Loading...</span>
      </div>
  </button>
  <input #fileInput (change)="processvendorContract($event)"
    accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
    type='file' id="getFile" style="display:none">


    <button mat-raised-button class="button templateBtn ml-3" matTooltip="click to download" (click)="generateVendorContract();"> 
      Export
        <!-- <mat-icon *ngIf="exportStatusDataBoolean == true && this.exportStatusData.export === true" class="check_circle" >check_circle</mat-icon>
        <div class="spinner-border" role="status" *ngIf="exportStatusDataBoolean == true && this.exportStatusData.export === false" >
          <span class="sr-only">Loading...</span>
        </div> -->
        <mat-icon *ngIf="exportStatusCircle == true" class="check_circle" >check_circle</mat-icon>
        <div class="spinner-border" role="status" *ngIf="exportStatusLoading == true" >
          <span class="sr-only">Loading...</span>
        </div>
    </button>

    <button mat-button class="togglebutton button ml-3" *ngIf="!deletedPlItems" (click)="deleteItemsByBtn()" matTooltip="click to bulk delete ">Bulk Delete</button>
    <button mat-button class="togglebutton button ml-3" *ngIf="edit == true && !deletedPlItems" (click)="enabletableInput()" matTooltip="Edit Price">
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-pencil-square plSvg svgEditIcon" viewBox="0 0 16 16">
        <path d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z"/>
        <path fill-rule="evenodd" d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5v11z"/>
      </svg>
      Edit</button>
    <button mat-button class="togglebutton button ml-3" *ngIf="deletedPlItems" (click)="revertTable()" matTooltip="Restore items">Revert</button>

</div>

<mat-card>
<div class="row">
  <div class="col">
    <table class="table">
      <tbody>
        <tr>
          <th class="topItemkey" scope="row">Contract Name</th>
          <td>{{ data.contract}}</td>
        </tr>
        <tr>
          <th class="topItemkey" scope="row">Vendor Name</th>
          <td>{{ data.vendorName }}</td>
        </tr>
        <tr>
          <th class="topItemkey" scope="row">ContractRef</th>
          <td>{{ data.contractRef }}</td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="col">

    <table class="table">
      <tbody>
        <tr>
          <th class="topItemkey" scope="row">Location</th>
          <td *ngIf="data.restaurantId != undefined">
            <!-- <div *ngIf="data.restaurantId.length == 1">
              <div *ngFor="let array of data.restaurantId">
                {{array.split("@")[1]}}
              </div>
            </div> -->
            <!-- <div *ngIf="data.restaurantId.length > 1" > -->
              <mat-form-field appearance="none" style="margin-top: -20px;">
                <mat-select placeholder="selected Locations" class="outline" [(ngModel)]="data.restaurantId[0]" >
                  <mat-option>
                    <ngx-mat-select-search placeholderLabel="Location..." noEntriesFoundLabel="'no Location found'" (keyup)="locSearch($event.target.value ,data)"></ngx-mat-select-search>
                  </mat-option>
                  <mat-option *ngFor="let array of data.restaurantId" [value]="array">
                    {{ array.split("@")[1] }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            <!-- </div> -->
          </td>
        </tr>
        <tr>
          <th class="topItemkey" scope="row">Start Date</th>
          <td>{{ data.startvendordate | date: 'MMM d, y'}}</td>
        </tr>
        <tr>
          <th class="topItemkey" scope="row">End Date</th>
          <td>{{ data.expireDate | date: 'MMM d, y' }}</td>
        </tr>
      </tbody>
    </table>
    
  </div>
  <div class="col">

    <table class="table">
      <tbody>
        <tr>
          <th class="topItemkey" scope="row">Create Date</th>
          <td>{{ data.createTs | date: "MMM d, y"}}</td>
        </tr>
        <tr>
          <th class="topItemkey" scope="row">Active</th>
          <td>{{ data.active}}</td>
        </tr>
        <tr>
          <th class="topItemkey" scope="row">Type</th>
          <td>{{ data.type }}</td>
        </tr>
      </tbody>
    </table>
  </div>
</div>

</mat-card>
<br>
<mat-card class="matcontent">

      <mat-form-field appearance="fill" class="mt-2 mb-2">
        <input matInput (keyup)="applyFilter($event)" placeholder="search here.." #input>
      </mat-form-field>

      <!-- <button mat-button class="togglebutton plBtn mt-3 ml-4" *ngIf="edit == false && !deletedPlItems" (click)="enabletableInput()">Disable</button> -->
      <button mat-button class="togglebutton plBtn mt-2 ml-4" *ngIf="edit == false && !deletedPlItems" (click)="storePriceList()">Save </button>
      <mat-slide-toggle class="togglebutton mt-2 ml-4" [(ngModel)]="deletedPlItems" (change)="deletedPl()" matTooltip="Deleted items">Show Deleted Items</mat-slide-toggle>
      <mat-slide-toggle class="togglebutton mt-2" *ngIf="!deletedPlItems" [(ngModel)]="adjInvPreview" (change)="preview()" matTooltip="show priced items">Contracts Only</mat-slide-toggle>

 <div *ngIf="!deletedPlItems">

  <table #table mat-table [dataSource]="dataSource"  matSortDirection="asc" matSort >
    <ng-container matColumnDef="select">
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell">
        <mat-checkbox #selectAll (change)="$event ? masterToggle() : null"
          [checked]="selection.hasValue() && isAllSelected()" 
          [indeterminate]="selection.hasValue() && !isAllSelected()" [aria-label]="checkboxLabel()">
        </mat-checkbox>
      </th>
      <td mat-cell *matCellDef="let element" class="name-cell">
        <mat-checkbox (change)="$event ? selection.toggle(element) : null;"
          [checked]="selection.isSelected(element)" [aria-label]="checkboxLabel(element)">
        </mat-checkbox>
      </td> 
    </ng-container>

    <ng-container matColumnDef="tenantId">
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell">
        <b> Tenant ID</b>
      </th>
      <td mat-cell *matCellDef="let element" class="name-cell">
        {{ element.tenantId }}
      </td>
    </ng-container>
    <ng-container matColumnDef="category">
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell">
        <b> Category </b>
      </th>
      <td mat-cell *matCellDef="let element" class="name-cell">
        {{ element.category }}
      </td>
    </ng-container>

    <ng-container matColumnDef="subCategory">
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell">
        <b> Sub Category</b>
      </th>
      <td mat-cell *matCellDef="let element" class="name-cell">
        {{ element.subCategory }}
      </td>
    </ng-container>

    <ng-container matColumnDef="itemcode">
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell">
        <b> Item Code</b>
      </th>
      <td mat-cell *matCellDef="let element" class="name-cell">
        {{ element.itemCode }}
      </td>
    </ng-container>

    <ng-container matColumnDef="itemName">
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell">
        <b> Item Name</b>
      </th>
      <td mat-cell *matCellDef="let element" class="name-cell">
        {{ element.itemName }}
        <mat-icon *ngIf="element.isNew == true" class="tableNewIcon">fiber_new</mat-icon>
      </td>
    </ng-container>

    <ng-container matColumnDef="packageName">
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell">
        <b> Package Name</b>
      </th>
      <td mat-cell *matCellDef="let element" class="name-cell">
        {{ element.packageName }}
      </td>
    </ng-container>

    <ng-container matColumnDef="packageqnty">
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell">
        <b> Package Quantity</b>
      </th>
      <td mat-cell *matCellDef="let element" class="name-cell">
        {{ element.packageQty }}
      </td>
    </ng-container>

    <ng-container matColumnDef="uom">
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell">
        <b> UOM </b>
      </th>
      <td mat-cell *matCellDef="let element" class="name-cell">
        {{ element.uom }}
      </td>
    </ng-container>

    <ng-container matColumnDef="taxRate">
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell">
        <b> Tax Rate </b>
      </th>
      <td mat-cell *matCellDef="let element" class="name-cell">
        <input class="input1" type="number" min="0" 
              [(ngModel)]="element.taxRate"  [disabled]="enableInput == false"
              (focus)="focusFunctionWithOutForm(element,'taxRate')" (focusout)="focusOutFunctionWithOutForm(element,'taxRate')" />
              <!-- onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)" -->
      </td> 
    </ng-container>

    <ng-container matColumnDef="types">
      <th mat-header-cell *matHeaderCellDef mat-sort-header > Type </th>
      <td mat-cell *matCellDef="let element">
        <!-- [ngClass]="{'typesTd': element.priceType == 'discountOnMRP'}"  style="width: 120px !important;"-->
        <mat-form-field appearance="none">
          <mat-select placeholder="types" class="outline" [disabled]="enableInput == false"  
          [(ngModel)]="element.priceType" (selectionChange)="typesFunc(element)">
            <mat-option>
              <ngx-mat-select-search placeholderLabel="types..." noEntriesFoundLabel="'no types found'"></ngx-mat-select-search>
            </mat-option>
            <mat-option *ngFor="let array of priceTypesData" [value]="array.value">
              {{ array.displayName }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- <input class="input1 typesInput" type="number" step="0.01" min="0" *ngIf="element.priceType == 'discountOnMRP'" 
        [(ngModel)]="element.percentage" [disabled]="enableInput == false"  
        (keyup)="typesRateReduction($event.target.value ,element)" maxlength="100" 
        (focus)="focusFunctionWithOutForm(element,'percentage')" (focusout)="focusOutFunctionWithOutForm(element,'percentage')"/> -->

      </td>
    </ng-container>

    <ng-container matColumnDef="price">
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell" style="align-items: center;"> Value </th>
      <td mat-cell *matCellDef="let element">
        <div style="display: flex; align-items: center;">
          <!-- <mat-icon style="cursor: grab;" (click)="openDialog(element)">slideshow</mat-icon> -->
          <div>
            <input class="input1" type="number" step="0.01" min="0"
            [(ngModel)]="element.unit" [disabled]="enableInput == false || !element.priceType" (keyup)="checkRate($event.target.value , element)"
            (focus)="focusFunctionWithOutForm(element,'unit')" (focusout)="focusOutFunctionWithOutForm(element,'unit')"/>
            <!--  [(ngModel)]="element.price" onkeypress="return (event.charCode != 45 && (event.charCode>=48 % ₹ &&event.charCode<=57)|| event.charCode == 46)"  -->
            <mat-icon matSuffix class="inputIcon" *ngIf="element.priceType == 'discountOnMRP'"> percent</mat-icon>
            <mat-icon matSuffix class="inputIcon" *ngIf="element.priceType != 'discountOnMRP'"> currency_rupee</mat-icon>
          </div>

          <div class="m-2">
            <!-- *ngIf="element.priceType === 'fixedContractPrice'"  || (element.priceType === 'absolutePriceReduction' || element.priceType === 'discountOnMRP')-->
            <mat-checkbox [disabled]="enableInput == false || (element.priceType === 'absolutePriceReduction' || element.priceType === 'discountOnMRP')" [(ngModel)]="element.tax" (change)="onCheckboxChange($event, element)">Incl Tax</mat-checkbox>
          </div>
        
        </div>
      </td>
    </ng-container>

    <ng-container matColumnDef="action">
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell">
        <b> Action </b>
      </th>
      <td mat-cell *matCellDef="let element" class="name-cell">
        <button mat-icon-button>
          <mat-icon (click)="deleteItemsShowDialog(element)" matTooltip="Delete item">delete</mat-icon>
        </button>
      </td>
    </ng-container>

   
    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
  </table>
  <div class="dataMessage" *ngIf="dataSource?.data?.length == 0"> No Data Available </div>
  <mat-paginator [showTotalPages]="5" [pageSize]='10' [pageSizeOptions]="pageSizes"></mat-paginator>
</div>
  <!-- <br>
  <div class="saveBtn">
    <button *ngIf="!deletedPlItems" mat-button class="plBtn" (click)="storePriceList()" [disabled]="edit == true">
      Save
    </button>
  </div> -->

<div *ngIf="deletedPlItems">
  <table #table mat-table [dataSource]="dataSource1"  matSortDirection="asc" matSort >

    <ng-container matColumnDef="select">
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell">
        <mat-checkbox #selectAll (change)="$event ? masterToggleRevert() : null"
          [checked]="selection.hasValue() && isAllSelectedRevert()" 
          [indeterminate]="selection.hasValue() && !isAllSelectedRevert()" [aria-label]="checkboxLabelRevert()">
        </mat-checkbox>
      </th>

      <td mat-cell *matCellDef="let element" class="name-cell">
        <mat-checkbox (change)="$event ? selection.toggle(element) : null;"
          [checked]="selection.isSelected(element)" [aria-label]="checkboxLabelRevert(element)">
        </mat-checkbox>
      </td> 
      
    </ng-container>

    <ng-container matColumnDef="tenantId">
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell">
        <b> Tenant ID</b>
      </th>
      <td mat-cell *matCellDef="let element" class="name-cell">
        {{ element.tenantId }}
      </td>
    </ng-container>

    <ng-container matColumnDef="category">
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell">
        <b> Category </b>
      </th>
      <td mat-cell *matCellDef="let element" class="name-cell">
        {{ element.category }}
      </td>
    </ng-container>

    <ng-container matColumnDef="subCategory">
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell">
        <b> Sub Category</b>
      </th>
      <td mat-cell *matCellDef="let element" class="name-cell">
        {{ element.subCategory }}
      </td>
    </ng-container>

    <ng-container matColumnDef="itemcode">
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell">
        <b> Item Code</b>
      </th>
      <td mat-cell *matCellDef="let element" class="name-cell">
        {{ element.itemCode }}
      </td>
    </ng-container>

    <ng-container matColumnDef="itemName">
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell">
        <b> Item Name</b>
      </th>
      <td mat-cell *matCellDef="let element" class="name-cell">
        {{ element.itemName }}
      </td>
    </ng-container>

    <ng-container matColumnDef="packageName">
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell">
        <b> Package Name</b>
      </th>
      <td mat-cell *matCellDef="let element" class="name-cell">
        {{ element.packageName }}
      </td>
    </ng-container>

    <ng-container matColumnDef="packageqnty">
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell">
        <b> Package Quantity</b>
      </th>
      <td mat-cell *matCellDef="let element" class="name-cell">
        {{ element.packageQty }}
      </td>
    </ng-container>

    <ng-container matColumnDef="uom">
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell">
        <b> UOM </b>
      </th>
      <td mat-cell *matCellDef="let element" class="name-cell">
        {{ element.uom }}
      </td>
    </ng-container>

    <ng-container matColumnDef="taxRate">
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell">
        <b> Tax Rate </b>
      </th>
      <td mat-cell *matCellDef="let element" class="name-cell">

        <input class="input1" type="number" min="0"
              [(ngModel)]="element.taxRate" [disabled]="enableInput == false"
              (focus)="focusFunctionWithOutForm(element,'taxRate')" (focusout)="focusOutFunctionWithOutForm(element,'taxRate')"/>
              <!-- onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)"  -->
            </td> 
    </ng-container>
    
    <ng-container matColumnDef="price">
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell" style="align-items: center;"> Price  ₹ </th>
      <td mat-cell *matCellDef="let element" class="name-cell">
        <div>
          <input class="input1" type="number" min="0"
            [(ngModel)]="element.price"  [disabled]="enableInput == false"   
            (focus)="focusFunctionWithOutForm(element,'price')" (focusout)="focusOutFunctionWithOutForm(element,'price')"/>
            <!-- onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)"  -->
        </div>
      </td>
    </ng-container>

    <ng-container matColumnDef="action">
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell">
        <b> Action </b>
      </th>
      <td mat-cell *matCellDef="let element" class="name-cell">
        <button mat-icon-button>
          <mat-icon (click)="revertItems(element)" matTooltip="Restore item">restore</mat-icon>
        </button>
      </td>
    </ng-container>
    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
  </table>
  <div class="dataMessage" *ngIf="dataSource1?.data?.length == 0"> No Data Available </div>
  <mat-paginator #paginator [pageSize]='10' [pageSizeOptions]="pageSizes1"></mat-paginator>
</div>

</mat-card>


<ng-template #openPriceDetailsDialog>
  <h2 mat-dialog-title>
    <b>Price Details</b>
      <button mat-icon-button style="float: right;" (click)="closeDialog()"><mat-icon>close</mat-icon></button>
  </h2>
  
  <mat-dialog-content class="mat-typography">

    <div *ngIf="dialogData" class="parentClass" style="margin: 30px;">
      <div class="childClass">
        <div class="headerChild">
          Actual Price  
        </div>
        <div class="valueChild">
          <mat-icon  matSuffix class="pertIcons"  *ngIf="dialogData.actualPrice ">₹</mat-icon> {{dialogData.actualPrice || '-' }} 
        </div>
      </div>
      <div class="childClass">
        <div class="headerChild">
          Price Type 
        </div>
        <div class="valueChild" *ngIf="dialogData.priceType != 'absolutePriceReduction'">
          {{dialogData.priceType || '-' }}
        </div>
        <div class="valueChild" *ngIf="dialogData.priceType === 'absolutePriceReduction'">
          {{dialogData.priceType || '-' }} (₹ 20)
        </div>
      </div>
      <div class="childClass">
        <div class="headerChild">
          Percentage
        </div>
        <div class="valueChild">
          {{dialogData.percentage || '-' }} <mat-icon matSuffix class="pertIcons"  *ngIf="dialogData.percentage">%</mat-icon>
        </div>
      </div>
      <div class="childClass">
        <div class="headerChild">
          Price
        </div>
        <div class="valueChild">
          <mat-icon matSuffix class="pertIcons"  *ngIf="dialogData.price">₹</mat-icon> {{dialogData.price}} 
        </div>
      </div>
    </div>

  </mat-dialog-content>
</ng-template>