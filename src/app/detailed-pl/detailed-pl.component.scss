.input1 {
  text-align: center;
  width: 73px;
  height: 25px;
  background: #191919;
  border: #191919;
  margin-left: 5px;
  margin-right: 5px;
  color: white;
  font-size: 12px !important;
}

.row label {
  text-align: right;
  clear: both;
  float: left;
  margin-right: 15px;
}

.togglebutton {
  float: right;
}

.saveBtn {
  text-align: end;
}

.plBtn {
  border: 1px solid currentColor !important;
}

.templateBtn {
  float: right;
}

.tableNewIcon {
  float: right;
  margin-right: 5px;
  color: #fbc02d;
}

.plSvg{
  margin-top: 0px !important;
}

.typesTd{
  width: 200px !important;
}

.typesInput{
  width: 50px !important;
  margin-top: 20px !important;
}

.childClass{
  margin: 10px;
  font-size: medium;
  display: flex;
  padding-bottom: 10px;
}
.headerChild{
  font-weight: bold;
  width: 150px;
}

.valueChild{
  display: flex;
  align-items: center;
  gap: 10px;
}

.pertIcons{
  font-size: 18px;
}

.dropdownDate{
  font-size: 9px;
  margin-top: -28px;
}

.inputIcon{
  // position: relative;
  // /* right: 10px; */
  // left: 60px;
  // top: -20px;
  // color: black;
  // font-size: medium;
  position: absolute;
  /* left: 0px; */
  /* top: 2px; */
  color: black;
  font-size: small;
  margin-left: -22px;
  margin-top: 6px;
}
