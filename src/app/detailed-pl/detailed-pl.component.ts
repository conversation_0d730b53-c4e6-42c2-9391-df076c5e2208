import { SelectionModel } from '@angular/cdk/collections';
import { Component, ElementRef, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { MatDialog, MatPaginator, MatTableDataSource } from '@angular/material';
import { Router } from '@angular/router';
import { AuthService, ShareDataService } from '../_services';
import { MasterdataupdateService } from '../_services/masterdataupdate.service';
import { UtilsService } from '../_utils/utils.service';
import { SimpleDialogComponent } from '../_dialogs/simple-dialog/simple-dialog.component';
import { NotificationService } from '../_services/notification.service';
import { interval } from 'rxjs';
import { log } from 'console';
import { FormControl, FormGroup, FormBuilder, Validators  } from '@angular/forms';


@Component({
  selector: 'app-detailed-pl',
  templateUrl: './detailed-pl.component.html',
  styleUrls: ['./detailed-pl.component.scss','./../../common-dark.scss']
})
export class DetailedPlComponent implements OnInit {
  @ViewChild('fileInput') fileInput: any;
  dataSource: MatTableDataSource<any>;
  dataSource1: MatTableDataSource<any>;
  displayedColumns: string[];
  displayedColumns1: string[];
  @ViewChild(MatPaginator) paginator: MatPaginator;
  data: any;
  pageSizes: any[];
  pageSizes1: any[];
  n: number;
  PriceListAmount: number;
  adjInvPreview: boolean = false;
  enableInput: boolean = false;
  deletedPlItems: boolean = false;
  edit: boolean = true;
  user: any;
  plDataSource: any[];
  filteredData: any[];
  deletepl: any;
  toggleData = [];
  togglearray: any;
  toggleDataArray = [];
  toggleRevertData: any;
  toggleRevertDataArray: any;
  pushedData: any[];
  selection = new SelectionModel<DetailedPlComponent>(true, []);
  checkedSelectedItems: DetailedPlComponent;
  public fileObj: any;
  public isValidFormat: boolean = true;
  public fileContent: any = '';
  sub: any;
  importStatusDataBoolean: boolean = false;

  exportStatusCircle: boolean = false;
  exportStatusLoading: boolean = false;
  importStatusCircle: boolean = false;
  importStatusLoading: boolean = false;

  uploadStatus: boolean = false; 
  subImport: any;
  exportStatusData: any;
  importStatusData: any;
  curdate: Date;
  private specialKeys: Array<string> = ['Backspace', 'Tab', 'End', 'Home', 'ArrowLeft', 'ArrowRight', 'Del', 'Delete'];
  private regex: RegExp = new RegExp(/^\d*\.?\d{0,2}$/g);
  priceTypesData = [
    { 'displayName' : 'Fixed contract price', 'value' : 'fixedContractPrice'},
    { 'displayName' : '% discount on MRP', 'value' : 'discountOnMRP'},
    { 'displayName' : 'Absolute price reduction on MRP', 'value' : 'absolutePriceReduction'}  
  ]
  reducedPrice: number;
  @ViewChild('openPriceDetailsDialog') openPriceDetailsDialog: TemplateRef<any>;
  dialogData: any;
  constructor(
    private sharedData: ShareDataService,
    private router: Router,
    private utils: UtilsService,
    private notifyService: NotificationService,
    private auth: AuthService,
    private masterDataService: MasterdataupdateService,
    private dialog: MatDialog, 
  ) { 
    this.user = this.auth.getCurrentUser();
  }

  ngOnInit() {
    this.sharedData.priceListData.subscribe(priceListData => {
      this.data =  priceListData
      if (!Array.isArray(this.data.restaurantId)) {
        this.data.restaurantId = [this.data.restaurantId];
      }
    });
    this.updateVendorContract();
    this.checkVendorContractStatus();
  }

  // generatePriceList(){
  //   let obj = {}
  //   obj['restaurantId'] = this.user.restaurantId;
  //   this.masterDataService.generatePriceList(obj).subscribe(res => {
  //     if (res['success'] == true) {
  //       this.dataSource = new MatTableDataSource<any>();
  //       this.dataSource.data = res.result
  //       this.plDataSource = this.dataSource.data
  //       this.displayedColumns = ['tenantId', 'category', 'subCategory', 'itemcode', 'itemName', 'packageName', 'packageqnty', 'uom', 'price'];
  //       this.pageSizes = this.utils.getPageSizes(this.plDataSource)
  //       this.dataSource.paginator = this.paginator;
  //     }
  //   });
  // }

  // fetchDetailedPriceList(){
  //   let obj ={}
  //   obj['tenantId'] = this.data.tenantId;
  //   obj['restaurantId'] = this.data.restaurantId;
  //   obj['priceListId'] = this.data.priceListId;
  //   this.masterDataService.fetchDetailedPriceList(obj).subscribe(res => {
  //     if (res['success'] == true) {
  //       this.dataSource = new MatTableDataSource<any>();
  //       this.dataSource.data = res.data
  //       this.displayedColumns = ['tenantId', 'category', 'subCategory', 'itemcode', 'itemName', 'packageName', 'packageqnty', 'uom', 'price'];
  //       this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
  //       this.dataSource.paginator = this.paginator;
  //     }
  //   });
  // }

  getVendorByid(){
    let obj ={}
    obj['tenantId'] = this.data.tenantId;
    obj['restaurantId'] = this.data.restaurantId;
    obj['priceListId'] = this.data.priceListId;
    this.masterDataService.getVendorByid(obj).subscribe(res => {
      if (res['success'] == true) {
        if (res.result[0] != undefined){
          this.dataSource = new MatTableDataSource<any>();
          this.dataSource.data = res.result[0].items[0].plItems
          this.dataSource.data.forEach(item => {
            item['unit'] = item.price ? item.price : item.percentage ? item.percentage : item.absolutePrice ? item.absolutePrice : 0
          });
          this.plDataSource = this.dataSource.data;
          this.displayedColumns = ['select','category', 'subCategory', 'itemcode', 'itemName', 'packageName','types','price' ,'action'];
          this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
          this.dataSource.paginator = this.paginator;
          this.dataSource1 = new MatTableDataSource<any>();
          this.dataSource1.data = res.result[0].items[0].recentItems
          this.displayedColumns1 = ['select','category', 'subCategory', 'itemcode', 'itemName', 'packageName', 'price','action'];
          this.pageSizes1 = this.utils.getPageSizes(this.dataSource1.data)
          this.dataSource1.paginator = this.paginator;
        }else{
          this.goToPl();
        }
      }
    });
  }

  goToPl(){
    this.router.navigate(['/home/<USER>']);
  }

  makeZero(event , element){
    if(event.keyCode == 190){
      return
    }

    if(!element.unit){
      element.unit = 0;
    }

    if(element.taxRate == ''){
      element.taxRate = 0
    }

  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
    // this.dataSourceVendor.filter = filterValue.trim().toLowerCase();
    // if (this.dataSource.paginator) {
    //   this.dataSource.paginator.firstPage();
    // }
    // if (this.dataSourceVendor.paginator) {
    //   this.dataSourceVendor.paginator.firstPage();
    // }
  }

  preview() {
    this.adjInvPreview = this.adjInvPreview
    if (this.adjInvPreview == true) {
      this.dataSource.data = this.plDataSource
      this.dataSource = new MatTableDataSource<any>();
      this.dataSource.data = this.plDataSource .filter(item => item.unit > 0 && Object.keys(item.itemCode))
      this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
      this.dataSource.paginator = this.paginator;
    }
    else {
      this.dataSource = new MatTableDataSource<any>();
      this.dataSource.data  = this.plDataSource 
      this.pageSizes = this.utils.getPageSizes(this.dataSource.data )
      this.dataSource.paginator = this.paginator;
    }
  }

  storePriceList(){
    this.dataSource.data = this.plDataSource
    for (let i = 0; i < this.dataSource.data.length; i++) {
      this.dataSource.data[i].price = this.dataSource.data[i].price;
    }
    let obj ={}
    obj['tenantId'] = this.data.tenantId;
    obj['rId'] = this.data.restaurantId;
    obj['priceListId'] = this.data.priceListId;
    obj['plItems'] = this.dataSource.data;
    obj['vendorId'] =  this.data.vendorId;
    this.masterDataService.storePriceList(obj).subscribe(res => {
      if (res['result'] == true) {
        this.utils.snackBarShowSuccess("Items Updated successfully")
        // this.fetchDetailedPriceList();
      }else{
        this.utils.snackBarShowError("Something went Wrong")
      }
    });
    this.adjInvPreview == false
    this.enabletableInput();
  }

  toggle(element){
    this.toggleData.push(element);
    this.toggleDataArray = this.toggleData;
  }

  deleteItemsShowDialog(element){
    this.dialog.open(SimpleDialogComponent, {
      data: {
        title: 'Delete Contract Item',
        msg: 'Are you sure you want to delete?',
        ok: function () {
          this.deleteItems(element)
        }.bind(this)
      }
    });
}
  
  deleteItems(element){
    let obj ={}
    this.deletepl = this.dataSource.data;
    const indexOfObject = this.deletepl.findIndex((object) => {
      return object.itemCode  === element.itemCode;
    });
    if (indexOfObject !== -1) {
      this.deletepl.splice(indexOfObject, 1);
    }
    // const array = Object.values(element);
    obj['priceListId'] = this.data.priceListId;
    obj['recentItems'] = [element]
    // obj['plItems'] = this.dataSource.data;
    this.masterDataService.deleteItems(obj).subscribe(res => {
      if (res['success'] == true) {
        this.utils.snackBarShowSuccess("Item deleted successfully")
        this.getVendorByid();
      }else{
        this.utils.snackBarShowError("Something went Wrong")
      }
    });
    this.getVendorByid();
  }

  deleteItemsByBtn(){
    let obj ={}
    obj['priceListId'] = this.data.priceListId;
    obj['recentItems'] = this.selection.selected
    this.masterDataService.deleteItems(obj).subscribe(res => {
      if (res['success'] == true) {
        this.utils.snackBarShowSuccess("Items deleted successfully")
        this.getVendorByid();
      }else{
        this.utils.snackBarShowError("Something went Wrong")
      }
    });
    this.getVendorByid();
    this.selection.clear();
  }

  enabletableInput(){
    if(this.enableInput == false && this.edit == true){
      this.edit = false
      this.enableInput = true
    }else{
      this.edit = true
      this.enableInput = false
    }
  }

  deletedPl(){
    this.deletedPlItems = this.deletedPlItems
  }

  revertToggle(element){
    // this.toggleRevertData.push(element);
    this.toggleRevertDataArray = element;

  }

  // revertTable(){
  //   this.dataSource = new MatTableDataSource<any>();
  //   this.dataSource.data.push(this.toggleRevertDataArray)
  //   this.getVendorByid();
    // this.dataSource1 = new MatTableDataSource<any>();
    // this.dataSource1.data.remove(this.toggleRevertDataArray);

    // for( var i = 0; i < this.dataSource1.data.length; i++){ 
    
    //   if ( this.dataSource1.data[i] === this.toggleRevertDataArray) { 
  
    //       this.dataSource1.data.splice(i, 1); 
    //   }
  
  // }

    // this.dataSource.data = this.pushedData
  //   this.getVendorByid()
  // }

  revertTable(){
    let obj ={}
    this.deletepl = this.dataSource.data;
    obj['priceListId'] = this.data.priceListId;
    obj['restoreItems'] = this.selection.selected;
    this.masterDataService.restoreItems(obj).subscribe(res => {
      if (res['success'] == true) {
        this.utils.snackBarShowSuccess("Items Restored successfully")
        this.getVendorByid();
      }else{
        this.utils.snackBarShowSuccess("Something went Wrong")
      }
    });
    this.getVendorByid();
    this.selection.clear();
  }

  revertItems(element){
    let obj ={}
    this.deletepl = this.dataSource.data;
    const indexOfObject = this.deletepl.findIndex((object) => {
      return object.itemCode  === element.itemCode;
    });
    if (indexOfObject !== -1) {
      this.deletepl.splice(indexOfObject, 1);
    }
    obj['priceListId'] = this.data.priceListId;
    obj['restoreItems'] = [element]

    this.masterDataService.restoreItems(obj).subscribe(res => {
      if (res['success'] == true) {
        this.utils.snackBarShowSuccess("Item Restored successfully")
        this.getVendorByid();
      }else{
        this.utils.snackBarShowError("Something went Wrong")
      }
    });
    this.getVendorByid();
  }


  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  masterToggle() {
    if (this.dataSource.data)
      this.isAllSelected() ?
        this.selection.clear() :
        this.dataSource.data.forEach(row => this.selection.select(row));
  }

  checkboxLabel(element?: any): string {
    if (!element) {
      return `${this.isAllSelected() ? 'select' : 'deselect'} all`;
    }
    return `${this.selection.isSelected(element) ? 'deselect' : 'select'} element ${element.itemCode + 1}`;

  }

  isAllSelectedRevert() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource1.data.length;
    return numSelected === numRows;
  }

  masterToggleRevert() {
    if (this.dataSource1.data)
      this.isAllSelectedRevert() ?
        this.selection.clear() :
        this.dataSource1.data.forEach(row => this.selection.select(row));
  }

  checkboxLabelRevert(element?: any): string {
    if (!element) {
      return `${this.isAllSelectedRevert() ? 'select' : 'deselect'} all`;
    }
    return `${this.selection.isSelected(element) ? 'deselect' : 'select'} element ${element.itemCode + 1}`;

  }

  generateVendorContract() {
    this.exportStatusLoading = true;
    let obj = {}
    obj['tenantId'] = this.user.tenantId;
    obj['restaurantId'] = this.data.restaurantId;
    obj['priceListId'] = this.data.priceListId;
    obj['vendorId'] = this.data.vendorId
    obj['priceListName'] = this.data.contract
    // obj['fullName'] = this.data.priceListId + '.xlsx'

    this.masterDataService.generateVendorContract(obj).subscribe(res => {
      if (res['success'] == true) {
        this.utils.snackBarShowSuccess(res['message'])
        
        this.checkExportStatus();
        this.sub = interval(10000).subscribe((_val) => { 
            this.checkExportStatus();
        });

      } else {
        this.utils.snackBarShowError("Something went Wrong")
      }
    });

  }

  ngOnDestroy() {
    if (this.sub) {
      this.sub.unsubscribe();
    }
    if (this.subImport) {
      this.subImport.unsubscribe();
    }
  }


  checkUpload() {
    document.getElementById("getFile").click()
  }

  processvendorContract(event) {
    this.importStatusLoading = true;
    this.uploadStatus = true
    let files = event.target.files;
    let file = files[0];
    this.fileObj = file;
    let possibleFormat = ['XLSX', 'XLS']
    let fileExtension = this.fileObj.name.split(".")[1];
    if (!possibleFormat.includes(fileExtension.toUpperCase())) {
      this.isValidFormat = false;
      this.utils.snackBarShowWarning("Please upload excel format only")
    } else {
      this.isValidFormat = true;
      var reader = new FileReader();
      reader.onloadend = (e) => {
        this.fileContent = reader.result;
        this.uploadFile(this.fileContent, this.fileObj.name, this.fileObj.type,);
      };
      reader.readAsDataURL(file);
    }

  }

  uploadFile(file, name, type) {
    let obj = this.user;
    let inputObj = {
      fileObj: file,
      name: name,
      type: type,
      tenantId: this.user.tenantId,
      priceListId: this.data.priceListId,
      restaurantId: this.data.restaurantId,
      selectedLocations: this.data.selectedvendorBranch,
      templateNo: this.data.priceListId,
      vendorId : this.data.vendorId,
      date: this.data.createTs,
      token: obj["token"],
      email: obj["email"],
    }
    this.masterDataService.processVendorContract(inputObj).subscribe((response: any) => {
      if (response.success) {
        this.utils.snackBarShowSuccess(response['message'])
        this.uploadStatus = true
  
        this.checkImportStatus();
        this.subImport = interval(10000).subscribe((_val) => { 
            this.checkImportStatus();
        });
        this.importStatusLoading = false;
        this.importStatusCircle = true;
      }
    });
  }

  checkVendorContractStatus(){
    let obj = {}
    obj['tenantId'] = this.user.tenantId;
    obj['priceListId'] = this.data.priceListId;
    this.masterDataService.checkExportAndImportStatus(obj).subscribe((response: any) => {
      if (response.success === true) {
        if ("status" in response && response.status != null){
          if(!(response.status.export == null && (response.status == null && response.status.export == undefined))){
            this.exportStatusData  = response.status
          }
          if(!(response.status.import == null && (response.status == null && response.status.import == undefined))){
            this.importStatusDataBoolean = true
            this.importStatusData  = response.status
            this.getVendorByid();
          }
        }
      }
    });
  }

  checkImportStatus(){
    let obj = {}
    obj['tenantId'] = this.user.tenantId;
    obj['priceListId'] = this.data.priceListId;
    this.masterDataService.checkImportStatus(obj).subscribe((response: any) => {
      if (response.success === true) {
        this.importStatusData = response.status
        this.importStatusDataBoolean = true

        if(this.importStatusData.import == true){
          this.getVendorByid();
          if (this.subImport) {
            this.subImport.unsubscribe();
          }
        }else{

        }
      }
    });
  }


  checkExportStatus(){
    let obj = {}
    obj['tenantId'] = this.user.tenantId;
    obj['priceListId'] = this.data.priceListId;
    this.masterDataService.checkExportStatus(obj).subscribe((response: any) => {
      if (response.success === true) {
        this.exportStatusData = response.status
        if(this.exportStatusData.export == true){
          this.exportStatusCircle = true;
          this.exportStatusLoading = false;
          this.downloadPriceList();
          if (this.sub) {
            this.sub.unsubscribe();
          }
        }
      }
    });
  }

  downloadPriceList() {
    this.curdate = new Date(Date.now());
    let dates = new Date(this.curdate).toLocaleString('en-IN');
    const date = this.curdate;
    const dd = date.getDate();
    const mm = date.getMonth()+1;
    const yyyy = date.getFullYear();
    const hhhh = date.getHours();
    // const hhhh = (date.getHours() + 5.30) % 24;
    const minutes = date.getMinutes();
    // const ampm = hhhh >= 12 ? 'PM' : 'AM';
    const formattedDate = `${dd}-${mm}-${yyyy}-${hhhh}.${minutes}`;

    let obj = {}
    obj['tenantId'] = this.user.tenantId;
    obj['restaurantId'] = this.data.restaurantId;
    obj['priceListName'] = this.data.contract;
    obj['priceListId'] = this.data.priceListId;
    obj['fullName'] = this.data.priceListId + '.xlsx'
    // obj['fullName'] = "ContractPricing" + '_' + this.data.priceListId + '_' + formattedDate + '.xlsx'
    this.masterDataService.downloadPriceList(obj).subscribe(res => {
      if (res['success'] == true) {
        // this.notifyService.showSuccess("successfully", '')
        var downloadLink = document.createElement("a");
        downloadLink.href = 'data:application/vnd.ms-excel;base64,' + res.eFile;
        downloadLink.download = "ContractPricing" + '_' + this.data.priceListId + '_' + dates + '.xlsx';
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
      } else {
        this.utils.snackBarShowError("Something went Wrong")
      }
    });
  }

  updateVendorContract(){
    let obj = {}
    obj['tenantId'] = this.user.tenantId;
    obj['priceListId'] = this.data.priceListId;
    obj['vendorId'] =  this.data.vendorId;
    if ((obj['priceListId'] && obj['vendorId']) && obj['tenantId']){
    this.masterDataService.updateContract(obj).subscribe((response: any) => {
        this.getVendorByid()
    });
  } else {
    this.router.navigate(['/home/<USER>']);
  }
  }

  locSearch(text,data){
    let location = data.restaurantId;
    if(text != ''){
      data.restaurantId = location.filter(item => item.includes(text));
    }else{
      data.restaurantId = data.selectedvendorBranch;
    }
  }

  focusFunctionWithOutForm(element , value){
    if(Number(element[value]) === 0){
      element[value] = null;
    }
  }
  
  focusOutFunctionWithOutForm(element , value){
    if(element[value] === null){
      element[value] = 0
    }
  }

  checkRate(value , element){
    this.typesFunc(element)
  }

  typesFunc(element){
    if(element.priceType === "absolutePriceReduction"){
      element.absolutePrice = element.unit
      element.price = '';
      element.tax = true;
    }else if(element.priceType == "fixedContractPrice"){
      element.price = element.unit;
    }else{
      if (element.unit > 100 ){
        element.unit = 100 
      }
      element.percentage = element.unit; 
      element.price = '';
      element.tax = true;
    }
  }

  typesRateReduction(pert,element){
    if(pert){
      element.price = this.reducedPrice ? this.reducedPrice : element.price
      let price = element.price == element.actualPrice ? element.price : element.actualPrice;
      let percentage = pert;
      // Convert percentage to decimal
      let decimalPercentage = percentage / 100;
      // Calculate reduction amount
      let reductionAmount = price * decimalPercentage;
      // Calculate reduced price
      let reducedPrice = price - reductionAmount;
      element.price = this.utils.truncateNew(reducedPrice)
    }else{
      element.price = this.reducedPrice ? this.reducedPrice : element.actualPrice
    }
  }



  openDialog(element): void {
    this.dialogData = element
      const dialogRef = this.dialog.open(this.openPriceDetailsDialog, {
      });
  }

  closeDialog(): void {
    this.dialog.closeAll();
  }

  onCheckboxChange(event: any , element) {
    element.tax = event.checked ? event.checked : false    
  }

}