<div class="title" style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;">
  <div>
    <button mat-raised-button class="button" style="margin-left: 0px;" (click)="goBack()">
      <mat-icon>keyboard_backspace</mat-icon> Back
    </button>
  </div>

  <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;">
    <div style="display: flex; gap: 8px;">
      <button mat-button mat-raised-button id="save-btn" class="button" *ngIf="user.uType == 'restaurant' && !purchaseStatus" (click)="exportToExcel()">
        Export
      </button>
      <button mat-raised-button class="button" (click)="printpdf()">
        Print
      </button>
      <button mat-button mat-raised-button *ngIf="user.uType == 'restaurant' && !purchaseStatus"
        class="button3"
        [matTooltip]="'Create PO'" matTooltipPosition="below" id="save-btn"
        [disabled]="pr.isPoCreated || disablePOCreation"
        (click)="approvePrWithoutApproval()">
        Create PO
      </button>
      <button mat-button mat-raised-button *ngIf="user.uType == 'restaurant' && !purchaseStatus"
        class="button3"
        [matTooltip]="'Modify PR'" matTooltipPosition="below" id="save-btn"
        [disabled]="pr.isPoCreated || disablePOCreation || !pr.vendorDetails?.vendorId"
        (click)="redirectSpecialOrder()">
        Edit PR
      </button>
    </div>
  </div>
</div>

<div class="search-table-input fieldcontainer">
  <div class="row">
    <div class="col">
      <table class="table">
        <tbody>
          <tr>
            <th class="topItemkey" scope="row">PR# </th>
            <td>{{ pr.prId }}</td>
          </tr>
          <tr>
            <th class="topItemkey" scope="row">Restaurant Name </th>
            <td>{{ pr?.tenantDetails?.tenantName }}
              {{ restaurantBranch | titlecase }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="col">
      <table class="table">
        <tbody>
          <tr>
            <th class="topItemkey" scope="row">System Entry Date</th>
            <td>{{ pr.createTs | date: "EEEE, MMMM d, y" }}</td>
          </tr>
          <tr>
            <th class="topItemkey" scope="row">Delivery Date</th>
            <td *ngIf="pr.deliveryDate">{{ (pr.deliveryDate | date: "EEEE, MMMM d, y") || '-' }}</td>
            <td *ngIf="pr.eta && !pr.deliveryDate">{{ (pr.eta | date: "EEEE, MMMM d, y") || '-' }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="col">
      <table class="table">
        <tbody>
          <tr>
            <th class="topItemkey" scope="row" >Vendor Name</th>
            <td *ngIf="pr?.vendorDetails">{{ pr.vendorDetails.vendorName }}</td>
            <td *ngIf="!pr?.vendorDetails"> - </td>
          </tr>
          <tr>
            <th class="topItemkey" scope="row">Department</th>
            <td >{{ pr.selectedWorkArea && pr.selectedWorkArea.length > 0 ? pr.selectedWorkArea[0] : 'store' }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>

<mat-card class="matcontent">
  <section class="example-container-1 mat-elevation-z8">
    <mat-slide-toggle style="float: right !important;margin-top: 25px !important;" class="ml-2" [(ngModel)]="allExtraFieldFlag"
    (change)="addExtraFields()" >
    <span >Show Cess</span>
    </mat-slide-toggle>


    <table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort>
      <!-- Index Column -->
      <ng-container matColumnDef="index">
        <th mat-header-cell *matHeaderCellDef class="tableId"><b>#</b></th>
        <td mat-cell *matCellDef="let element; let i = index" class="name-cell tableId">
          {{ i + 1 }}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <!-- Name Column -->
      <ng-container matColumnDef="itemName" sticky>
        <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell">
          <b> Item Name</b>
        </th>
        <td mat-cell *matCellDef="let element" class="name-cell">
          {{ element.itemName | titlecase }}
          <div style="font-size: 10px;" *ngIf="element.priceType">
            ( {{element.priceType}} - {{element.priceTypeValue}} )
          </div>
        </td>
        <td mat-footer-cell *matFooterCellDef class="name-cell">Total</td>
        <!-- <td mat-footer-cell *matFooterCellDef> Total </td> -->

        <mat-divider></mat-divider>
      </ng-container>

      <ng-container matColumnDef="hsnCode">
        <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell"><b>Hsn Code</b> </th>
        <td mat-cell *matCellDef="let element" class="name-cell"> {{element.hsnCode}} </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="itemDescription">
        <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell"><b> Item Description</b> </th>
        <td mat-cell *matCellDef="let element" class="name-cell"> {{element.itemDescription | titlecase}} </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="itemCode">
        <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell"><b> Item Code</b> </th>
        <td mat-cell *matCellDef="let element" class="name-cell"> {{element.itemCode}} </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="cessAmt">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Cess Amt</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <span *ngIf="!editPrFlag">
            {{ element.cessAmt ? element.cessAmt : 0 }}
          </span>
          <div *ngIf='editPrFlag'> 
          <input (keyup)="validateTotalPrice($event , element)" class="input1" type="number" step="any" min="0"
            [(ngModel)]="element.cessAmt"
            onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)"/>
          </div>
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getTotal('cessAmt'))}}</td>
      </ng-container>

      <ng-container matColumnDef="extraAmt">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Extra Charge</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <span *ngIf="!editPrFlag">
            {{ element.extraAmt ? element.extraAmt : 0 }}
          </span>
          <div *ngIf='editPrFlag'> 
            <input (keyup)="validateTotalPrice($event , element)" class="input1" type="number" step="any" min="0"
            [(ngModel)]="element.extraAmt"
            onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)"/>
          </div>
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getTotal('extraAmt'))}}</td>
      </ng-container>

      <ng-container matColumnDef="discnt">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Discount</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <span *ngIf="!editPrFlag">
            {{ element.discAmt ? element.discAmt : 0 }}
          </span>
          <div *ngIf='editPrFlag'> 
            <input (keyup)="validateTotalPrice($event , element)" class="input1" type="number" step="any" min="0"
            [(ngModel)]="element.discAmt"
            onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)" />
          </div>
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getTotal('discAmt'))}}</td>
      </ng-container>

      <ng-container matColumnDef="taxableAmt">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Taxable Amt</b></th>
        <td mat-cell *matCellDef="let element"> {{ this.utils.truncateNew(element.orderQty * element.unitPrice) - (element.discAmt ? element.discAmt : 0) }} </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="packageName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell"><b> Package Name</b> </th>
        <td mat-cell *matCellDef="let element" class="name-cell"> {{element.packages[0].packageName | titlecase}} </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="unitPerPkg">
        <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell"><b> Units/Pkg</b> </th>
        <td mat-cell *matCellDef="let element" class="name-cell"> {{element.packages[0].unitPerPkg}} </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="orderQty">
        <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell"><b> Altered Qty</b> </th>
        <td mat-cell *matCellDef="let element" class="name-cell">
          <span *ngIf='!editPrFlag'>
            {{element.quantity}}
          </span>
          <div *ngIf='editPrFlag'>
            <input class="input1" type="number" step="0.01" min="0" (keyup)="getTotalPrCost($event , element)"
              [(ngModel)]="element.quantity" [disabled]="purchaseStatus" (input)="checkNumericInput($event,element)"
              (focus)="focusFunctionWithOutForm(element)" (focusout)="focusOutFunctionWithOutForm(element)" />
          </div>
        </td>

        <td mat-footer-cell *matFooterCellDef class="name-cell"></td>
      </ng-container>

      <ng-container matColumnDef="originalQty">
        <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell"><b> Order Qty</b> </th>
        <td mat-cell *matCellDef="let element" class="name-cell">
          <span >
            {{element.originalQty ? element.originalQty :  element.quantity}}
          </span>
        </td>

        <td mat-footer-cell *matFooterCellDef class="name-cell"></td>
      </ng-container>


      <ng-container matColumnDef="totalValue">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b> Total(incl.tax)</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <!-- {{ this.utils.truncateNew(element.totalPrice)}} -->
          {{this.utils.truncateNew(
            ((element.quantity * element.packages[0].packagePrice) -
            element.discAmt) + element.cessAmt +element.extraAmt +((element.quantity *
            element.packages[0].packagePrice) -
            element.discAmt)* (element.taxRate /100))
            }}
        </td>
        <td mat-footer-cell *matFooterCellDef>{{ this.utils.truncateNew(getTotalPrice())}}</td>
      </ng-container>

      <ng-container matColumnDef="subTotal">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b> Total(excl.tax)</b>
        </th>
        <td mat-cell *matCellDef="let element">
          {{ this.utils.truncateNew(element.subTotal)}}
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getTotal('subTotal'))}}</td>
      </ng-container>

      <ng-container matColumnDef="taxAmt">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b> Tax Amt</b>
        </th>
        <td mat-cell *matCellDef="let element">
            {{ this.utils.truncateNew(element.taxAmount)}}
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getTotal('taxAmount'))}}</td>
      </ng-container>

      <ng-container matColumnDef="unitPrice">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b> Unit Cost</b>
        </th>
        <td mat-cell *matCellDef="let element">   
          {{ this.utils.truncateNew((element.unitPriceExclTax)) || 0}}     
          <!-- <span *ngIf='this.pr.isPoCreated'>
            {{ this.utils.truncateNew((element.unitPrice)) || 0}}
          </span>
          <input *ngIf='!this.pr.isPoCreated && editAccess' class="input1" type="number" step="0.01" min="0" (keyup)="getTotalPrCost($event , element)"
            [(ngModel)]="element.unitPrice" [disabled]="pr?.purchaseStatus || element.priceType"
            (focus)="focusFunctionWithOutForm(element)" (focusout)="focusOutFunctionWithOutForm(element)" [ngStyle]="{'opacity': (element.priceType) ? '0.5' : '1'}" 
            onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)"/> -->
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky:true;"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      <tr mat-footer-row *matFooterRowDef="displayedColumns"></tr>
    </table>

    <div class="dataMessage" *ngIf="dataSource?.data.length == 0"> No Data Available </div>
  </section>

  <div class="mt-3 mb-3">
    <div>
      <mat-form-field appearance="none" style="float: right !important; margin-right: 0px !important;">
        <mat-select placeholder="Select Tax" [formControl]="taxCtrl"  
        class="outline" (selectionChange)="addTax($event.value)" multiple>
          <mat-option>      
            <ngx-mat-select-search [formControl]="taxFilterCtrl" placeholderLabel="Select Tax..."></ngx-mat-select-search>  
          </mat-option>    
          <mat-option class="hide-checkbox" (click)="toggleSelectAll()">
            Select All / Deselect All
          </mat-option>           
          <mat-option *ngFor="let tax of filteredTaxArray" [value]="tax">
            {{ tax }}
          </mat-option>
        </mat-select>
      </mat-form-field> 
    </div>
    <br>
      <span class="otherchargeHeading topItemkey">ADD TAX</span>
    <br><br><br>
    <div *ngFor="let tax of otherTaxes; let i = index" style="padding-bottom: 22px !important;">
      <span class="otherchargeHeading topItemkey">{{ tax.taxName }}</span>
      <input matInput class="outline otherTax" [(ngModel)]="tax.value" type="number" step="0.01" min="0"
      (focus)="focusFn(tax)" (focusout)="focusOutFn(tax)" (ngModelChange)="adjustGrandTotal(tax, i)" [disabled]="pr.isPoCreated || disablePOCreation"
      onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)"/>
    </div>      
    <div>
      <span class="otherchargeHeading topItemkey">Grand total ₹</span>
      <input matInput class="outline otherTax" [value]="getGrandTotal()" placeholder="Total ₹" disabled />
    </div>
  </div>

<mat-card-actions> </mat-card-actions>
</mat-card>

<ng-template #openStatusDialog>
	<button mat-icon-button matDialogClose="yes" matTooltip="Close" class="CloseBtn">
		<mat-icon>close</mat-icon>
	</button>
	<h2 mat-dialog-title>
		<b>Approve Status</b>
	</h2>
<mat-dialog-content class="mat-typography">
  <div class="col">
    <table class="table">
      <tbody>
        <tr *ngFor="let item of this.pr.rejectReason">
          <th class="topItemkey" style="width: 65px !important; background: none;" scope="row">Level</th>
          <td>{{ item.level }}</td>
          <th class="topItemkey" style="width: 65px !important; background: none;" scope="row">Reason</th>
          <td>{{ item.reason || '-'}}</td>
        </tr>
      </tbody>
    </table>
  </div>

</mat-dialog-content>

<mat-dialog-actions align='center'>
  <div class="reqBtn">
    <button mat-raised-button class="button3 mb-2 mx-2" matDialogClose="yes" (click)="okDialog()">
      Ok
    </button>
  </div>
</mat-dialog-actions>
</ng-template>