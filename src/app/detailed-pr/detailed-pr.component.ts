import { ChangeDetectorRef, Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ShareDataService } from '../_services/share-data.service';
import { PurchaseItem } from '../_models';
import { MatTableDataSource } from '@angular/material';
import { SelectionModel } from '@angular/cdk/collections';
import { GlobalsService } from '../_services/globals.service';
import { SimpleDialogComponent } from '../_dialogs/simple-dialog/simple-dialog.component';
import { MatDialog } from '@angular/material';
import { ActivatedRoute, Router, Params } from '@angular/router';
import { UtilsService } from '../_utils/utils.service';
import { PurchasesService, AuthService, VendorsService, BranchTransferService } from '../_services';
import { Location } from '@angular/common';
import { PrPreviewDialogComponent } from '../_dialogs/pr-preview-dialog/pr-preview-dialog.component';
import { NotificationService } from '../_services/notification.service';
import { environment } from 'src/environments/environment';
import { FormBuilder, FormGroup, Validators ,FormControl } from '@angular/forms';
import { Vendor } from '../_models';
import { ReplaySubject, Subject } from 'rxjs';
import {takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-detailed-pr',
  templateUrl: './detailed-pr.component.html',
  styleUrls: ['./detailed-pr.component.scss', "./../../common-dark.scss"]
})
export class DetailedPrComponent implements OnInit {
  pr: any;
  user: any;
  restaurantBranch: any;
  dataSource: MatTableDataSource<PurchaseItem>;
  displayedColumns: string[];
  selection = new SelectionModel<PurchaseItem>(true, []);
  disableApprBtn: boolean = false;
  disablePOCreation: boolean = false;
  poCreated: boolean = false;
  reason: string
  editPrFlag: boolean = false
  userAccess: boolean;
  detailedPrForm: FormGroup;

  @ViewChild('openStatusDialog') openStatusDialog: TemplateRef<any>;
  openStatus: boolean = false;
  totalQuantity: any;
  vendors: Vendor[];
  public VendorBank: any[] = [];
  public vendorsBanks: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  protected _onDestroy = new Subject<void>();
  public vendorFilterCtrl: FormControl = new FormControl();
  vName: any;
  routeFlag: boolean = false;
  windowCurrentUrl: string;
  purchaseStatus: any;
  allExtraFieldFlag: boolean;
  access: any;
  checkAccess: boolean;
  deleteAccess: boolean;
  editAccess: boolean;
  dialogRef: any;
  value: number;
  grandTotal: number;
  otherTax: any;
  otherTaxes: any;
  taxArray = ["AROED", "EXCISE DUTY", "MISCELLANEOUS", "TCS", "VAT"];
  taxCtrl = new FormControl();
  taxFilterCtrl = new FormControl();
  filteredTaxArray: any[] = [];
  allSelected: boolean = false;
  data: any;
  purReq: any;
  
  constructor(private shareData: ShareDataService, private dialog: MatDialog,
    private notifyService: NotificationService,
    private router: Router, private utils: UtilsService,
    private purchases: PurchasesService, private auth: AuthService,
    private loc: Location,
    private vendorService: VendorsService,
    private fb: FormBuilder,
    private activateRoute : ActivatedRoute,
    private branchTransfer: BranchTransferService,
    private cdRef: ChangeDetectorRef

  ) {
    this.user = this.auth.getCurrentUser();
    this.detailedPrForm = this.fb.group({
      vendor: ['', Validators.required]
    });

    this.access = sessionStorage.getItem('access');
    let dataArray = this.access.split(',');
    this.checkAccess = dataArray.includes(this.user.role)

    this.activateRoute.params.subscribe((params: Params)=>{
      if(params.purchaseStatus){
        var windowLocation = window.location.href;
        this.windowCurrentUrl = windowLocation.split('/')[4].split(';')[0]
        this.purchaseStatus = params.purchaseStatus
      } else {
        this.tenantDetail();
        this.getVendors();
      }
    });
   }

  ngOnInit() {
    this.shareData.currPR.subscribe(
      values => {
        if (!values[0].prId)
          this.loc.back()
        else {
          this.pr = values[0]            
          this.purReq = this.pr;
          this.otherTaxes = this.pr.hasOwnProperty('otherTax') && this.pr.otherTax ? this.pr.otherTax : [];
          this.dataSource = new MatTableDataSource<PurchaseItem>();
          this.data = this.pr.prDetails;          
          this.data.forEach((element) => {
            element.unitPrice = this.utils.truncateNew(element.unitPrice)
          });
          this.dataSource.data = this.data;
          this.displayedColumns = ['index','itemCode', 'hsnCode' ,'itemName', 'packageName', 'unitPerPkg','originalQty', 'orderQty', 'unitPrice', 'subTotal', 'taxAmt', 'totalValue'];
          if (this.pr.hasOwnProperty('approvalDetail')) {
            if(this.pr.approvalDetail){
              let approvedObjects = this.pr.approvalDetail.filter((el)=>{
                return el.status == 'approved'
              })
              if(!this.editAccess){
                this.disablePOCreation = (approvedObjects.length === this.pr.approvalDetail.length) ? false : true ;
              }else{
                this.disablePOCreation = false;
              }
            }
          } else {
            this.disablePOCreation = false ;
          }
        }
      });
    this.disableApprBtnFn()
    this.restaurantBranch = this.pr.restaurantId.split('@')[1]
    this.getGrandTotal();
    this.filteredTaxArray = this.taxArray.slice(); 
    this.taxCtrl.setValue(this.otherTaxes.map(tax => tax.taxName));
    this.taxFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
      this.filterTaxArray();
    });
  }

  getTotal(key: string) {
      return this.utils.getTotal(this.dataSource.data, key); 
  }


  getTotalPrice() {
    let totalPrice = 0    
    this.dataSource.data.forEach(element => {      
      let afterDiscount = 0;
      let tax = 0;
      afterDiscount = (element.quantity * element.packages[0].packagePrice) - element.discAmt
      tax = afterDiscount * (element.taxRate / 100)
      element.subTotal = afterDiscount;
      element.taxAmount = tax;
      totalPrice += afterDiscount + element.cessAmt + element.extraAmt + tax;
    });        
    return totalPrice
  }

  approvePrWithoutApproval() {
    let obj = {}
    if (this.user.uType === GlobalsService.restaurant)
      obj = {
        pr: this.pr,
        userMId: this.user.mId,
        uType: this.user.uType,
        baseUrl: environment.baseUrl
      }      
      if (this.pr.hasOwnProperty('grandTotal')){
          this.pr.grandTotal = this.grandTotal;        
      }
      if (this.otherTaxes != null && this.otherTaxes != undefined){   
        const otherTaxNames = [...this.otherTaxes];
        this.pr.otherTax =  otherTaxNames
      }
      this.purchases.createPo(obj).subscribe(data => {
        this.poCreated = true ;
        this.disablePOCreation = true ;
        this.disableApprBtnFn()
        this.disableApprBtnFn();
        this.previewPo(data)
        this.shareData.changeOrder(null);
      }, err => console.error(err));
  }

  approvePr() {
    if (this.user.uType === GlobalsService.vendor)
      this.pr.status.approved.vendor = true;
    else if (this.user.uType === GlobalsService.restaurant)
      this.pr.status.approved.restaurant = true;
    let obj = {
      pr: this.pr,
      userMId: this.user.mId,
      uType: this.user.uType
    }

    if (this.user.uType === GlobalsService.vendor)
      this.purchases.approvePr(obj).subscribe(data => {
        this.dialog.open(SimpleDialogComponent, {
          data: {
            title: 'Purchase Request.',
            msg: 'PR has been approved. Do you want to go back to the PRs page?',
            ok: function () {
              this.loc.back();
              this.disableApprBtnFn();
            }.bind(this)
          }
        })
        this.shareData.changePr(null);
      }, err => console.error(err));

    else if (this.user.uType === GlobalsService.restaurant)
      this.dialog.open(SimpleDialogComponent, {
        data: {
          title: 'Create Purchase Order.',
          msg: 'Do you want to create a Purchase Order?',
          ok: function () {
            this.createPo(obj);
          }.bind(this)
        }
      });
  }

  createPo(obj) {
    this.purchases.createPo(obj).subscribe(data => {
      this.disableApprBtnFn()
      this.disableApprBtnFn();
      this.previewPo(data)
      this.shareData.changeOrder(null);
    }, err => console.error(err));
  }

  disableApprBtnFn() {
    if (this.user.uType === GlobalsService.vendor)
      this.disableApprBtn = this.user.uType === GlobalsService.vendor && this.pr.status.approved.vendor
    else if (this.user.uType === GlobalsService.restaurant)
      this.disableApprBtn = !this.pr.status.approved.vendor || (this.user.uType === GlobalsService.restaurant &&
        this.pr.status.approved.restaurant)
  }

  printpdf() {
    this.purchases.printpdfs(this.pr, 'Pr').subscribe(data => {
      this.purchases.globalPrintPdf(data.eFile);
    });
  }

  previewPo(obj) {
    this.router.navigate(['/home'])
    this.dialog.open(PrPreviewDialogComponent, {
      //  width: '80%',
     data: obj });
  }

  goBack() {
    if(this.purchaseStatus){
      this.loc.back()
    }else{
      this.router.navigate(['/home/<USER>'])
    }
  }

  getTotalPrCost(event,element) {
    // element.quantity = element.quantity ? element.quantity : 0 ;
    element.subTotal = element.quantity * this.utils.truncateNew(element.unitPrice)
    element.taxAmount = this.utils.truncateNew(element.taxRate / 100 * element.subTotal)
    element.totalPrice = this.utils.truncateNew(element.taxAmount + element.subTotal)  
  }

  modifyPr() {
    let obj: any = {}
    obj['tenantId'] = this.user.tenantId
    obj['restaurantId'] = this.pr.restaurantId
    obj['prObj'] = this.pr
    obj['baseUrl'] = environment.baseUrl
    obj['userEmail'] = this.auth.getCurrentUser().email
    this.purchases.modifyPr(obj).subscribe(data => {
      if (data.result == 'success') {
        this.utils.snackBarShowSuccess('Changes are saved successfully')
        this.editPrFlag = false
      }
    })
  }

  openFilter() {
    if(this.openStatus ==  false){
      this.openStatus = true;
    }else{
      this.openStatus = false;
    }

  let dialogRefTemplate = this.dialog.open(this.openStatusDialog);
  dialogRefTemplate.afterClosed().subscribe(result => {
      // if (result === 'yes') {
      //   this.dialog.closeAll();
      // }
  })
}

okDialog(){
  this.openStatus = false;
}

selectVendor(e) {
  
}

getVendors() {
  this.vendorService.getVendors(this.auth.getCurrentUser()).subscribe((data: Vendor[]) => {
    this.vendors = data;
    this.VendorBank = this.vendors
    this.vendorsBanks.next(this.VendorBank.slice());
    this.vendorFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
        this.vendorfilterBanks();
    });
  })
}
  protected vendorfilterBanks() {
    if (!this.VendorBank) {
      return;
    }
    let search = this.vendorFilterCtrl.value;
    if (!search) {
      this.vendorsBanks.next(this.VendorBank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.vendorsBanks.next(
      this.VendorBank.filter(VendorBank => VendorBank.name.toLowerCase().indexOf(search) > -1)
    );
  }

  focusFunctionWithOutForm(element){
    if(Number(element.quantity) === 0){
      element.quantity = null;
    }
  }
  
  focusOutFunctionWithOutForm(element){
    if(element.quantity === null){
      element.quantity = 0
    }
  }

  addExtraFields() {
    if (this.allExtraFieldFlag === true) {
      this.displayedColumns = ['index','itemCode', 'hsnCode', 'itemName', 'packageName', 'unitPerPkg','originalQty', 'orderQty', 'unitPrice', 'subTotal','taxableAmt', 'taxAmt','extraAmt', 'discnt', 'cessAmt', 'totalValue']
    } else {
      this.displayedColumns = ['index','itemCode', 'hsnCode', 'itemName', 'packageName', 'unitPerPkg','originalQty', 'orderQty', 'unitPrice', 'subTotal', 'taxAmt', 'totalValue']
    }
  }

  validateTotalPrice(event , element) {
    if(event.keyCode == 190){
      return
    }
    element['extraAmt'] === null ? element['extraAmt'] = 0 : undefined ;
    element['cessAmt'] === null ? element['cessAmt'] = 0 : undefined ;
    element['discAmt'] === null ? element['discAmt'] = 0 : undefined ;
    let afterDiscount = 0;
    let tax = 0;
    afterDiscount = (element.orderQty * element.unitPrice) - element.discAmt
    tax = afterDiscount * (element.taxRate / 100)
    element.subTotal = afterDiscount;
    element.taxAmount = this.utils.truncateNew(tax);
    element.totalPrice = afterDiscount + element.cessAmt + element.extraAmt + tax;
  }

  tenantDetail() {
    let obj = {
      tenantId: this.user.tenantId
    }
    this.branchTransfer.tenantDetails(obj).subscribe(res => {
      if (res.result == 'success') {
        let deleteAccess = res.data[0] && res.data[0].permission && res.data[0].permission.POAccess ? res.data[0].permission.POAccess.delete : false;
        let editAccess = res.data[0] && res.data[0].permission && res.data[0].permission.POAccess ? res.data[0].permission.POAccess.edit : false;
        if (deleteAccess) {
          let rolesList = (res.data[0] && res.data[0].permission && res.data[0].permission.POAccess) ? res.data[0].permission.POAccess.deleteAccess : [];
          this.deleteAccess = (rolesList.some(role => role.toLowerCase() === this.user.role.toLowerCase())) ? true : false ;
        } else {
          this.deleteAccess = false ;
        }
        if (editAccess) {
          let rolesList = (res.data[0] && res.data[0].permission && res.data[0].permission.POAccess) ? res.data[0].permission.POAccess.editAccess : [];
          this.editAccess = (rolesList.some(role => role.toLowerCase() === this.user.role.toLowerCase())) ? true : false ;
        } else {
          this.editAccess = false ;
        }
      } else {
        this.deleteAccess = false ;
        this.editAccess = false ;
      }
    });
  }

  checkNumericInput(event: any , element) {   
    if (element.packages[0].packageName === 'NOS') {
      const input = event.target.value;
      event.target.value = input.replace(/[^0-9]/g, ''); 
      element.quantity = event.target.value;
    }
  }

  focusFn(tax: any){
    if(Number(tax.value) === 0){
      tax.value = null;
    }
  }
  
  focusOutFn(tax: any){
    if (tax.value < 0) {
      tax.value = 0;
    }
    if(tax.value === null){
      tax.value = 0
    }
  }

  getGrandTotal() {
    let itemTotal = this.getTotalPrice();
    let taxTotal =  this.otherTaxes.reduce((acc, item) => acc + item.value, 0);  
    this.grandTotal =  this.utils.truncateNew(itemTotal) + this.utils.truncateNew(taxTotal)     
    return this.utils.truncateNew(this.grandTotal)
  }

  adjustGrandTotal(tax: any, index: number) {    
    const previousTaxValue = parseFloat(this.otherTaxes[index].oldValue) || 0;
    const newTaxValue = parseFloat(tax.value) || 0;  
    this.grandTotal += (newTaxValue - previousTaxValue);  
    this.otherTaxes[index].oldValue = newTaxValue;
    this.getGrandTotal();
  }

  private filterTaxArray() {
    const search = this.taxFilterCtrl.value ? this.taxFilterCtrl.value.toLowerCase() : '';
    this.filteredTaxArray = this.taxArray.filter(tax => tax.toLowerCase().includes(search));
  }

  toggleSelectAll() {
    this.allSelected = !this.allSelected;
    if (this.allSelected) {
      this.taxCtrl.setValue(this.taxArray);
    } else {
      this.taxCtrl.setValue([]);
    }
    this.addTax(this.taxCtrl.value || []);  
    this.cdRef.detectChanges();
  }

  addTax(selectedTaxes: string[]) {
    const updatedTaxes = [];  
    selectedTaxes.forEach((tax) => {
      const existingTax = this.otherTaxes.find(item => item.taxName === tax);
      if (existingTax) {
        updatedTaxes.push(existingTax);  
      } else {
        updatedTaxes.push({ taxName: tax, value: 0 });
      }
    });  
    this.otherTaxes = updatedTaxes;
  }

  redirectSpecialOrder(){
    const data = this.pr.prDetails;   
    const totalPrice = this.getTotalPrice();           
    data.forEach((element) => {
      element.totalPrice = totalPrice;
    });
    let obj = {
      tenantId: this.user.tenantId,
      pr: this.pr,
      userMId: this.user.mId,
      rId: this.pr.restaurantId,
      vendorId: this.pr.vendorDetails.vendorId
    } 
    if (this.otherTaxes != null && this.otherTaxes != undefined){   
        const otherTaxNames = [...this.otherTaxes];
        this.pr.otherTax =  otherTaxNames
    }
    this.purchases.editDraft(obj).subscribe(res => {      
      if (res.result == true){
        this.shareData.editPr(this.purReq);
        this.router.navigate(['/home/<USER>']);
      } else {
        this.utils.snackBarShowInfo('Failed to edit PR');
      }
    })
  }



  exportToExcel() {
    this.purchases.exportToExcel(this.pr, 'Pr').subscribe(data => {
      window.open('data:text/csv;base64,' + data.eFile);
    });
  }

}
