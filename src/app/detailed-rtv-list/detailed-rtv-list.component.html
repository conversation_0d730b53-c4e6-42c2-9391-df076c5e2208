<div class="title">
  <button mat-raised-button class="button" style="float: left;" (click)=goBack()>
    <mat-icon>keyboard_backspace</mat-icon> Back To RTV
  </button>
</div>

<div class="datacontainer">
  <mat-card>
    <mat-card-content>
      <div class="search-table-input">
        <mat-form-field appearance="none">
          <label>Search</label>
          <div class="search-container">
            <input matInput type="text" class="outline" (keyup)="doFilter($event.target.value)" 
            [(ngModel)]='searchText' placeholder="Search" />
            <mat-icon matSuffix class="closebtn" (click)="resetForm()">close</mat-icon>
          </div>
        </mat-form-field>
      </div>
      <section class="example-container-1 mat-elevation-z8">    
        <table #table mat-table [dataSource]="dataSource" matSortDirection="desc" matSort>

          <ng-container matColumnDef="index">
            <th mat-header-cell *matHeaderCellDef><b>S.No</b></th>
            <td mat-cell *matCellDef="let element; let i = index" style="padding-right: 30px;">
              {{ i + 1 }}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>

          <ng-container matColumnDef="returnDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> RTV Date </b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.returnDate | date: "EEEE, MMMM d, y" }}
            </td>
          </ng-container>

          <ng-container matColumnDef="rtvId">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> RTV Id </b>
            </th>
            <td mat-cell *matCellDef="let element" class="links" (click)="detailedRtvs(element)">
              {{ element.rtvId }}
            </td>
          </ng-container>

          <ng-container matColumnDef="grnId">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> GRN Id </b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.grnId }}
            </td>
          </ng-container>

          <ng-container matColumnDef="invId">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Invoice Id </b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.invoiceId }}
            </td>
          </ng-container>

          <ng-container matColumnDef="creditNo">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Credit Note No </b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.creditNo }}
            </td>
          </ng-container>

          <ng-container matColumnDef="creditDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Credit Note Date </b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.creditDate | date: "EEEE, MMMM d, y" }}
            </td>
          </ng-container>

          <ng-container matColumnDef="createdAt">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Created At </b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ this.utils.formatDateToUTC(element.createdAt) || '-' }}
            </td>
          </ng-container>

          <ng-container matColumnDef="createdBy">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Created By </b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.createdBy || '-' }}
            </td>
          </ng-container>

          <ng-container matColumnDef="action">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Action</b>
            </th>
            <td mat-cell *matCellDef="let element">
                <button mat-icon-button class="delete-button" matTooltip="Delete RTV" (click)="deleteRtv(element)" matTooltipPosition="left">
                  <mat-icon>delete_outline</mat-icon>
                </button>
            </td>
          </ng-container>
          
          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        </table>
      </section>
    </mat-card-content>
  </mat-card>
</div>
