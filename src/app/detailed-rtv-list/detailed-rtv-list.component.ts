import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { PurchasesService } from '../_services/purchases.service';
import { Router } from '@angular/router';
import { UtilsService } from '../_utils/utils.service';
import { SimpleDialogComponent } from '../_dialogs/simple-dialog/simple-dialog.component';
import { MatDialog } from '@angular/material';
import { AuthService } from '../_services/auth.service';
import { ShareDataService } from '../_services/share-data.service';

@Component({
  selector: 'app-detailed-rtv-list',
  templateUrl: './detailed-rtv-list.component.html',
  styleUrls: ['./detailed-rtv-list.component.scss', "./../../common-dark.scss"]
})
export class DetailedRtvListComponent implements OnInit {
  public dataSource: MatTableDataSource<any>;
  grn: any;
  actualData: any[] = [];
  displayedColumns: string[];
  user: any;
  restaurantId: any;
  searchText: string;
  public searchValue: any = ''

  constructor(private purchases: PurchasesService, 
    private sharedData: ShareDataService,
    private router: Router, 
    private utils: UtilsService,
    public dialog: MatDialog,
    private auth: AuthService,
  ) {
    this.user = this.auth.getCurrentUser()
   }

  ngOnInit() {
    this.sharedData.currGrn.subscribe(grn => {
      this.grn = grn;   
    }, err => {
      console.error(err)
    });
    if (!this.dataSource)
      this.dataSource = new MatTableDataSource<any>();
    this.getRtv();
    this.displayedColumns = ['index','rtvId','grnId','invId', 'creditNo', 'creditDate', 'returnDate', 'createdAt', 'createdBy', 'action'];   
  }

 getRtv() {    
  let obj = {}
  obj['tenantId'] = this.grn.tenantId;
  obj['grnId'] = this.grn.grnId;
  this.purchases.getRtv(obj).subscribe(res => {   
  const rtvData = res.data; 
  const filteredData = rtvData.filter(item => item.grnId === this.grn.grnId);
  this.dataSource.data = filteredData; 
 }, err => { })
}

 goBack(){
  this.router.navigate(['/home/<USER>'])
}

 deleteRtv(obj) {  
  let reqObj = {}
    reqObj['grnId'] = obj['grnId']
    reqObj['rtvId'] = obj['rtvId']
    reqObj['tenantId'] = this.user.tenantId;
    reqObj['user'] = this.user.mId;

  this.dialog.open(SimpleDialogComponent, {
    data: {
      title: 'Delete RTV',
      msg: 'Are you sure you want to delete?',
      ok: function () {
        this.purchases.deleteRtv(reqObj).subscribe(res => {
          if (res['result'] === true) {
            this.utils.snackBarShowSuccess('RTV deleted successfully!');
          } else {
            this.utils.snackBarShowError(`${res['message']}`);
          }
          this.getRtv();
        })
      }.bind(this)
    }
  });
}

public doFilter = (value: string) => {
  this.dataSource.filter = value.trim().toLocaleLowerCase();
}

resetForm() {
  this.searchText = ''
  this.searchValue = ''
  this.doFilter(this.searchValue)
  this.getRtv();
}

detailedRtvs(obj){
  this.sharedData.changeRtvs(obj)
  this.router.navigate(['/home/<USER>'])
}

}
