<div class="title">
  <button mat-raised-button class="button" style="float: left;" (click) = goBack()>
    <mat-icon>keyboard_backspace</mat-icon> Back to RTV
  </button>
  <button
    mat-raised-button
    class="button"
    style="float: right;"
    (click)="printpdf()"
  >
    Print
  </button>
  <button
    mat-raised-button
    class="button"
    style="float: right;"
    (click)="exportToExcel()"
  >
    Export
  </button>
</div>
<div class="search-table-input fieldcontainer">
  <div class="fieldbox">
    <label>RTV Id:</label>
    <span>{{ rtv.rtvId }}</span>
  </div>
  <div class="fieldbox">
    <label>GRN Id:</label>
    <span>{{ rtv.grnId }}</span>
  </div>
  <div class="fieldbox">
    <label> Invoice Id</label>
    <span>{{ rtv.invoiceId }}</span>
  </div>
  <div class="fieldbox3">
    <label>Receipient Name</label>
    <span>{{ rtv.tenantName }} {{ this.receiveBranch | titlecase }}</span>
  </div>
  <div class="fieldbox4">
    <label>Sender Name </label>
    <span>{{ rtv.vendorName }} {{ this.vendorBranch | titlecase }}</span>
  </div>
  <div class="fieldbox3">
    <label> Date</label>
    <span>{{ rtv.createTs | date: "EEEE, MMMM d, y" }}</span>
  </div>
</div>
<mat-card class="matcontent">
  <table
    #table
    mat-table
    [dataSource]="dataSource"
    matSortActive="itemName"
    matSortDirection="asc"
    matSort
  >
    <!-- Index Column -->
    <ng-container matColumnDef="index">
      <th mat-header-cell *matHeaderCellDef class="tableId"><b>#</b></th>
      <td mat-cell *matCellDef="let element; let i = index" class="name-cell tableId">
        {{ i + 1 }}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <!-- Name Column -->
    <ng-container matColumnDef="itemName" sticky>
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell">
        <b> Item Name</b>
      </th>
      <td mat-cell *matCellDef="let element" class="name-cell">
          {{ element.itemName | titlecase }}
      </td>
      <td mat-footer-cell *matFooterCellDef> Total</td>
      <!-- <td mat-footer-cell *matFooterCellDef> Total </td> -->

      <mat-divider></mat-divider>
    </ng-container>

    <ng-container matColumnDef="pkgName" sticky>
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell"><b> Pkg Name</b> </th>
      <td mat-cell *matCellDef="let element" class="name-cell">
          {{element.packageName | titlecase}}
      </td>
      <mat-divider></mat-divider>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>


    <!-- <ng-container matColumnDef="pkgQty">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> <b>Pkg Qty</b> </th>
      <td mat-cell *matCellDef="let element">
          {{element.packages[0].packageQty}}
      </td>
    </ng-container> -->

    <!-- difference Percent Column -->
    <!-- <ng-container matColumnDef="quantity">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Order Quantity</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ element.quantity }}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
      
    </ng-container> -->

    <!-- <ng-container matColumnDef="receivedQty">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Received Quantity</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ element.receivedQty }}
      </td>
      <td mat-footer-cell *matFooterCellDef> {{getTotal('receivedQty')}}</td>
    </ng-container> -->

    <ng-container matColumnDef="returnQty">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Return Qty</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ element.returnQty }}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
      <!-- <td mat-footer-cell *matFooterCellDef> {{getTotal('quantity')}} </td> -->
    </ng-container>

    <ng-container matColumnDef="totalValue">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b> Total (incl.tax,etc)</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ this.utils.truncateNew(element.totalPrice) }}
      </td>
      <td mat-footer-cell *matFooterCellDef> {{ this.utils.truncateNew(getTotal('totalPrice'))}} </td>
      <!-- <td mat-footer-cell *matFooterCellDef> {{getTotal('unitPrice')*getTotal('quantity')}} </td> -->
    </ng-container>

    <ng-container matColumnDef="grnUnitPriceWithTax">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b> GRN Rate</b>
      </th>
      <td mat-cell *matCellDef="let element">
          {{ this.utils.truncateNew(element.grnUnitPriceWithTax)}}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
      <!-- <td mat-footer-cell *matFooterCellDef> {{getTotal('unitPrice')}} </td> -->
    </ng-container>

    <ng-container matColumnDef="returnRate">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b> Return Rate</b>
      </th>
      <td mat-cell *matCellDef="let element">
          {{this.utils.truncateNew(element.returnUnitPrice)}}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
      <!-- <td mat-footer-cell *matFooterCellDef> {{getTotal('unitPrice')}} </td> -->
    </ng-container>

    <ng-container matColumnDef="totalReturnAmt">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b> Total</b>
      </th>
      <td mat-cell *matCellDef="let element">
          {{this.utils.truncateNew(element.subTotal)}} 
      </td>
      <!-- <td mat-footer-cell *matFooterCellDef></td> -->
      <td mat-footer-cell *matFooterCellDef> {{this.utils.truncateNew(getTotal('subTotal'))}} </td>
    </ng-container>

    <!-- <ng-container matColumnDef="uom">
      <th mat-header-cell *matHeaderCellDef mat-sort-header><b> UOM</b></th>
      <td mat-cell *matCellDef="let element">
        {{ element.uom }}
      </td>
    </ng-container> -->

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
    <tr mat-footer-row *matFooterRowDef="displayedColumns"></tr>
    <!-- <tr mat-footer-row *matFooterRowDef="displayedColumns"></tr> -->
  </table>

  <mat-card-actions>
    <!-- <button <mat-butto></mat-butto>n color="primary" (click)="approvePr()" [disabled]="disableApprBtn">{{user.uType == 'vendor'? 'Approve Pr' : 'Create PO'}}</button> -->
  </mat-card-actions>
</mat-card>
