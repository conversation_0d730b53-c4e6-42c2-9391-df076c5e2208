import { Component, OnInit } from '@angular/core';
import { PurchasesService, ShareDataService, AuthService } from '../_services';
import { GlobalsService } from '../_services/globals.service';
import { MatSort, Sort, MatTableDataSource } from '@angular/material';
import { SelectionModel } from '@angular/cdk/collections';
import { Location } from '@angular/common';
import { UtilsService } from '../_utils/utils.service';
@Component({
  selector: 'app-detailed-rtv',
  templateUrl: './detailed-rtv.component.html',
  styleUrls: ['./detailed-rtv.component.scss', "./../../common-dark.scss"]
})
export class DetailedRtvComponent implements OnInit {
  rtv: any;
  user: any;
  dataSource: MatTableDataSource<any>;
  displayedColumns: string[];
  receiveBranch: string;
  vendorBranch: string;
  selection = new SelectionModel<any>(true, []);
  constructor(private sharedData: ShareDataService, private auth: AuthService,
    private loc: Location, private utils: UtilsService, private purchases: PurchasesService) {

  }

  ngOnInit() {
    this.sharedData.currRtv.subscribe(rtv => {
      if (!rtv.rtvId)
        this.loc.back();
      this.rtv = rtv;
      this.receiveBranch = rtv['restaurantId'].split('@')[1];
      if (rtv['vendorBranch'] != null) {
        this.vendorBranch = rtv['vendorBranch'].split('@')[1];
      }
      else {
        this.vendorBranch = '';
      }
      this.user = this.auth.getCurrentUser();
      this.rtv.createTs = new Date(rtv.createTs);
      this.dataSource = new MatTableDataSource<any>();
      this.dataSource.data = this.rtv.items;
      this.displayedColumns = GlobalsService.rtvInfoColumns;
    }, err => {
      console.error(err)
    });
  }

  printpdf() {
    this.purchases.printpdfs(this.rtv, 'Grn').subscribe(data => {
      // window.open('data:application/pdf;base64,' + data.eFile);
      this.purchases.globalPrintPdf(data.eFile);
    });

  }
  exportToExcel() {
    this.purchases.exportToExcel(this.rtv, 'Grn').subscribe(data => {
      window.open('data:text/csv;base64,' + data.eFile);
    });
  }

  getTotal(key: string) {
    return this.utils.getTotal(this.dataSource.data, key);
  }

  goBack() {
    this.loc.back()
  }

}
