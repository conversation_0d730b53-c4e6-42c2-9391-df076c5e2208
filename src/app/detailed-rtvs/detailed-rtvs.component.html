<div class="title">
  <button mat-raised-button class="button" style="float: left;" (click)=goBack()>
    <mat-icon>keyboard_backspace</mat-icon> Back To RTV-List
  </button>
  <button mat-raised-button class="button" style="float: right;" (click)="printpdf()">
    Print
  </button>
</div>

<div class="search-table-input fieldcontainer">
  <div class="row">
  <div class="col">
    <table class="table">
      <tbody>
        <tr>
          <th class="topItemkey" scope="row">GRN Id</th>
          <td>{{ rtvs.grnId }}</td>
        </tr>
        <tr>
          <th class="topItemkey" scope="row">RTV Id</th>
          <td>{{ rtvs.rtvId }}</td>
        </tr>
        <tr>
          <th class="topItemkey" scope="row">Return Date</th>
          <td> {{ rtvs.returnDate | date: "EEEE, MMMM d, y" }} </td>
        </tr>
      </tbody>
    </table>
  </div>

  <div class="col">
    <table class="table">
      <tbody>
        <tr>
          <th class="topItemkey" scope="row">Invoice No.</th>
          <td>{{ rtvs.invoiceId }}</td>
        </tr>
        <tr>
          <th class="topItemkey" scope="row">Credit Note No.</th>
          <td>{{ rtvs.creditNo }}</td>
        </tr>
        <tr>
          <th class="topItemkey" scope="row">Credit Date</th>
          <td> {{ rtvs.creditDate | date: "EEEE, MMMM d, y" }} </td>
        </tr>
      </tbody>
    </table>
  </div>

  <div class="col">
    <table class="table">
      <tbody>
        <tr>
          <th class="topItemkey" scope="row">Created By</th>
          <td>{{ rtvs.createdBy }}</td>
        </tr>
        <tr>
          <th class="topItemkey" scope="row">Created Date</th>
          <td> {{ this.utils.formatDateToUTC(rtvs.createdAt) }} </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
</div>

<div class="datacontainer">
  <mat-card>
    <mat-card-content>
      <div class="search-table-input">
        <mat-form-field appearance="none">
          <label>Search</label>
          <div class="search-container">
            <input matInput type="text" class="outline" (keyup)="doFilter($event.target.value)" 
            [(ngModel)]='searchText' placeholder="Search" />
            <mat-icon matSuffix class="closebtn" (click)="resetForm()">close</mat-icon>
          </div>
        </mat-form-field>
      </div>
      <section class="example-container-1 mat-elevation-z8">    
        <table #table mat-table [dataSource]="dataSource" matSortDirection="desc" matSort>

          <ng-container matColumnDef="index">
            <th mat-header-cell *matHeaderCellDef><b>S.No</b></th>
            <td mat-cell *matCellDef="let element; let i = index" style="padding-right: 30px;">
              {{ i + 1 }}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>

          <ng-container matColumnDef="itemCode">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Item Code </b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.itemCode }}
            </td>
            <td mat-footer-cell *matFooterCellDef>Total</td>
          </ng-container>

          <ng-container matColumnDef="itemName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Item Name </b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.itemName }}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>

          <ng-container matColumnDef="receivedQty">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Received Quantity </b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.quantity }}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>

          <ng-container matColumnDef="returnQty">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> RTV Quantity </b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.receivedQty }}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>

          <ng-container matColumnDef="unitPrice">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Unit Cost </b>
            </th>
            <td mat-cell *matCellDef="let element">
              <!-- {{ element.unitPrice }} -->
              {{element.packages[0].packagePrice}}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>

          <ng-container matColumnDef="pkgName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Pkg Name</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.packages[0].packageName }}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>

          <ng-container matColumnDef="taxAmount">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Tax Amt</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ this.utils.truncateNew(element.taxAmount) }}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>
      
          <ng-container matColumnDef="extra">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Extra Amt</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.extraAmt }}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>
      
          <ng-container matColumnDef="disc">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Discount</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ (element.discAmt) }}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>
      
          <ng-container matColumnDef="cess">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Cess Amt</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ (element.cessAmt) }}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>
      
          <ng-container matColumnDef="totalPrice">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Total</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ this.utils.truncateNew(element.totalPrice) }}
            </td>
            <td mat-footer-cell *matFooterCellDef> 
              {{ this.dataSource?.data ? this.utils.truncateNew(getTotal('totalPrice')) : 0 }} 
            </td>
          </ng-container>

          <ng-container matColumnDef="remarks">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Remarks</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ (element.reason) }}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>
          
          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
          <tr mat-footer-row *matFooterRowDef="displayedColumns"></tr>
        </table>
      </section>
    </mat-card-content>
  </mat-card>
</div>
