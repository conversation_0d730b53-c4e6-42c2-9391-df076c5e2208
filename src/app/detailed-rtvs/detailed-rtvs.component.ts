import { Component, OnInit } from '@angular/core';
import { PurchasesService } from '../_services/purchases.service';
import { ShareDataService } from '../_services/share-data.service';
import { Router } from '@angular/router';
import { UtilsService } from '../_utils/utils.service';
import { MatDialog, MatTableDataSource } from '@angular/material';
import { AuthService } from '../_services/auth.service';
import { GlobalsService } from '../_services/globals.service';
import { Location } from "@angular/common";

@Component({
  selector: 'app-detailed-rtvs',
  templateUrl: './detailed-rtvs.component.html',
  styleUrls: ['./detailed-rtvs.component.scss', "./../../common-dark.scss"]
})
export class DetailedRtvsComponent implements OnInit {
  user: any;
  rtvs: any;
  dataSource: MatTableDataSource<any> ;
  displayedColumns: string[] ;
  searchText: string;
  public searchValue: any = ''

  constructor(private purchases: PurchasesService, 
    private sharedData: ShareDataService,
    private router: Router, 
    public utils: UtilsService,
    public dialog: MatDialog,
    private auth: AuthService,
    private location: Location  
  ) {
    this.user = this.auth.getCurrentUser()
   }

  ngOnInit() {
    this.getRtvs();
  }

  getRtvs(){
    this.sharedData.currRtvs.subscribe(rtvs => {
      this.rtvs = rtvs;   
      this.dataSource = new MatTableDataSource < any > ();
  		this.dataSource.data = this.rtvs.grnItems;
      this.displayedColumns = Object.create(GlobalsService.detailedRtvsColumns);
    }, err => {
      console.error(err)
    });
  }

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }

  resetForm() {
    this.searchText = ''
    this.searchValue = ''
    this.doFilter(this.searchValue)
    this.getRtvs();
  }

  getTotal(key: string) {
    return this.utils.getTotal(this.dataSource.data, key);
  }
  
  goBack(){
    this.location.back();
  }

  printpdf() {
    this.purchases.printpdf(this.rtvs, 'Rtv').subscribe(data => {
      this.purchases.globalPrintPdf(data.eFile);       
    });
  }

}
