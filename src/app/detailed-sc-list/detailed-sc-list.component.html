<!-- <div>
  <button mat-button class="button ml-3 mt-2" (click)="back()">
   <mat-icon>keyboard_backspace</mat-icon> back
  </button>
</div> -->
<div class="topBtn">
  <button mat-button class="button my-1 printBtn" (click)="printpdf()">
    Print
  </button>
</div>

<mat-card>
    <mat-icon class="ml-3 mt-2 closeBtnSc" matTooltip="click to back" (click)="back()">close</mat-icon>
  <div class="row">
    <div class="col">
      <table class="table">
        <tbody>
          <tr>
            <th class="topItemkey" scope="row">Item Category</th>
            <td>{{ topItems.items[0].itemCategory }}</td>
          </tr>
          <tr>
            <th class="topItemkey" scope="row">Item Code</th>
            <td>{{ topItems.items[0].itemCode }}</td>
          </tr>
          <tr>
            <th class="topItemkey" scope="row">Item Name</th>
            <td>{{ topItems.items[0].itemName }}</td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="col">
  
      <table class="table">
        <tbody>
          <tr>
            <th class="topItemkey" scope="row">Package Name</th>
            <td >{{ topItems.items[0].packageName }}</td>
          </tr>
          <tr>
            <th class="topItemkey" scope="row">Start Date</th>
            <td>{{ topItems.stockConvertionDate | date: 'MMM d, y'}}</td>
          </tr>
          <tr>
            <th class="topItemkey" scope="row">Total Amount</th>
            <td>{{ topItems.totalAmount  || '-'}}</td>
          </tr>
        </tbody>
      </table>
      
    </div>
    <div class="col">
  
      <table class="table">
        <tbody>
          <tr>
            <th class="topItemkey" scope="row">Total Quantity</th>
            <td>{{ topItems.totalQuantity || '-'}}</td>
          </tr>
          <tr>
            <th class="topItemkey" scope="row">Wastage Amount</th>
            <td>{{ topItems.wastageAmount || '-'}}</td>
          </tr>
          <tr>
            <th class="topItemkey" scope="row">Wastage Quantity</th>
            <td>{{ topItems.wastageQuantity  || '-'}}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>


<table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort>

  <ng-container matColumnDef="itemCode">
    <th mat-header-cell *matHeaderCellDef mat-sort-header><b> Item Code</b></th>
    <td mat-cell *matCellDef="let element">{{ element.itemCode }}</td>
    <td mat-footer-cell *matFooterCellDef class="name-cell"></td>
  </ng-container>

  <ng-container matColumnDef="itemName">
    <th mat-header-cell *matHeaderCellDef mat-sort-header><b> Item Name</b></th>
    <td mat-cell *matCellDef="let element">{{ element.itemName | titlecase }}</td>
    <td mat-footer-cell *matFooterCellDef class="name-cell">Total</td>
  </ng-container>

  <ng-container matColumnDef="uom">
    <th mat-header-cell *matHeaderCellDef mat-sort-header><b> UOM </b></th>
    <td mat-cell *matCellDef="let element">{{ element.uom }}</td>
    <td mat-footer-cell *matFooterCellDef class="name-cell"></td>
  </ng-container>

  <ng-container matColumnDef="inStock">
    <th mat-header-cell *matHeaderCellDef class="tableRole"><b>Current Stock</b></th>
    <td mat-cell *matCellDef="let element;">{{ this.utils.truncateNew(element.inStock) }}</td>
    <td mat-footer-cell *matFooterCellDef class="name-cell"></td>
  </ng-container>

  <ng-container matColumnDef="weight">
    <th mat-header-cell *matHeaderCellDef class="tableAmount"><b>Weight</b></th>
    <td mat-cell *matCellDef="let element;">{{ this.utils.truncateNew(element.weight) }}</td>
    <td mat-footer-cell *matFooterCellDef class="name-cell">{{this.utils.truncateNew(getTotal('weight'))}}</td>
  </ng-container>

  <ng-container matColumnDef="price">
    <th mat-header-cell *matHeaderCellDef><b>Unit Price</b></th>
    <td mat-cell *matCellDef="let element;">{{ this.utils.truncateNew(element.price) }}</td>
    <td mat-footer-cell *matFooterCellDef class="name-cell"></td>
  </ng-container>

  <ng-container matColumnDef="totalAmount">
    <th mat-header-cell *matHeaderCellDef><b>Total Price</b></th>
    <td mat-cell *matCellDef="let element;">{{this.utils.truncateNew(element.totalPrice)}}</td>
    <td mat-footer-cell *matFooterCellDef class="name-cell">{{this.utils.truncateNew(getTotal('totalPrice'))}}</td>
  </ng-container>

  <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky : true"></tr>
  <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
  <tr mat-footer-row *matFooterRowDef="displayedColumns"></tr>
</table>
</mat-card>
