import { Component, OnInit } from '@angular/core';
import { ShareDataService } from '../_services/share-data.service';
import { PurchasesService } from '../_services/purchases.service';
import { AuthService } from '../_services/auth.service';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog, MatTable } from '@angular/material';
import { NotificationService } from '../_services/notification.service';
import { UtilsService } from '../_utils/utils.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-detailed-sc-list',
  templateUrl: './detailed-sc-list.component.html',
  styleUrls: ['./detailed-sc-list.component.scss', './../../common-dark.scss']
})
export class DetailedScListComponent implements OnInit {
  data: any;
  user: any;
  dataSource = new MatTableDataSource();
  displayedColumns: string[];topItems: any;
;

  constructor(
    private sharedData: ShareDataService,
    private purchases: PurchasesService,
    private auth: AuthService,
    private utils: UtilsService,
    public router: Router,
  ) { }

  ngOnInit() {
    this.sharedData.detailedStockConversionListData.subscribe(stockConversionData => {
      this.data = stockConversionData;
    });
    if(Object.keys(this.data).length === 0){
      this.router.navigate(['/home/<USER>'])
    }else{
      this.displayedColumns = ['itemCode','itemName','uom','inStock','weight','price','totalAmount'];
      this.topItems = this.data;
      this.dataSource.data =  this.data.items[0].scItems.filter(item => item.weight > 0);
    }
  }

  getTotal(key: string) {
    return this.utils.getTotal(this.dataSource.data, key); 
  }

  back(){
    this.router.navigate(['/home/<USER>'])
  }

  printpdf() {
    this.purchases.printpdfs(this.data, 'SC').subscribe(data => {
      this.purchases.globalPrintPdf(data.eFile);
    });
  }

}
