<mat-tab-group>
  <mat-tab>
    <ng-template mat-tab-label>Forcast</ng-template>

    <br>
    <div class="example-container mat-elevation-z8">
      <div class="example-header" fxLayoutAlign="space-between center">
        <mat-form-field fxFlex="30%">
          <input matInput type="text" [formControl]="searchText" (keyup)="doFilter($event.target.value)"
            placeholder="Search">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>
        <mat-form-field id="branch-select" fxFlex="30%">
          <mat-select placeholder="Category" [formControl]="category" (selectionChange)="selectCategory($event)">
            <mat-option [value]="all">
              ALL
            </mat-option>
            <mat-option *ngFor="let cat of categories" [value]="cat.name">
              {{cat.name}}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <table #table mat-table [dataSource]="sortedData" matSortActive="name" matSortDirection="asc" matSort
        class="mat-elevation-z8">
        <ng-container matColumnDef="index">
          <th mat-header-cell *matHeaderCellDef>#</th>
          <td mat-cell *matCellDef="let element; let i = index;" class="name-cell">
            {{ (i+1) + (paginator.pageIndex * paginator.pageSize) }}
          </td>
        </ng-container>
        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell"> Name </th>
          <td mat-cell *matCellDef="let element" class="name-cell"> {{element.itemName | titlecase}} </td>
        </ng-container>
        <ng-container matColumnDef="servingSize">
          <th mat-header-cell *matHeaderCellDef mat-sort-header class="servingSize-cell"> Uom </th>
          <td mat-cell *matCellDef="let element" class="servingSize-cell">
            <div *ngIf="element.menuGroup.name!='SUBRECIPE'">{{element.servingSize | titlecase}}</div>
            <div *ngIf="element.menuGroup.name=='SUBRECIPE'">Kg</div>
          </td>
        </ng-container>
        <ng-container matColumnDef="predicted">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Predicted </th>
          <td mat-cell *matCellDef="let element"> {{this.utils.truncateNew(element.predicted)}} </td>
        </ng-container>
        <ng-container matColumnDef="actual">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Actual </th>
          <td mat-cell *matCellDef="let element"> {{element.actual}} </td>
        </ng-container>
        <ng-container matColumnDef="estimated">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Estimated </th>
          <td mat-cell *matCellDef="let element" class="estimated-cell"> <input *ngIf="!isForecastReport "
              [disabled]="!allowEdit" style="text-align: center;" type="number" min="0" step="any"
              oninput="this.value = this.value.replace(/[^0-9.]/g, ''); this.value = this.value.replace(/(\..*)\./g, '$1');"
              (ngModelChange)="element.estimated=$event" [ngModel]="element.estimated|number : '1.0-2'">
            <div [ngStyle]="{'font-size':isForecastReport ? '104px' : '0' }">
              {{this.utils.truncateNew(element.estimated)}}
            </div>
          </td>
        </ng-container>
        <ng-container matColumnDef="difference">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Difference </th>
          <td mat-cell *matCellDef="let element"> {{element.diff}} </td>
        </ng-container>
        <ng-container matColumnDef="differencePercent">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Difference Percentage </th>
          <td mat-cell *matCellDef="let element"> {{ this.utils.truncateNew(element.diffP)}}% </td>
        </ng-container>
        <ng-container matColumnDef="apDiff">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Difference (A vs P) </th>
          <td mat-cell *matCellDef="let element"> {{element.apDiff }} </td>
        </ng-container>
        <ng-container matColumnDef="apDiffP">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Difference% (A vs P) </th>
          <td mat-cell *matCellDef="let element"> {{this.utils.truncateNew(element.apDiffP)}}% </td>
        </ng-container>
        <ng-container matColumnDef="aeDiff">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Difference (A vs E) </th>
          <td mat-cell *matCellDef="let element"> {{element.aeDiff}} </td>
        </ng-container>
        <ng-container matColumnDef="aeDiffP">
          <th mat-header-cell *matHeaderCellDef mat-sort-header> Difference% (A vs E) </th>
          <td mat-cell *matCellDef="let element"> {{this.utils.truncateNew(element.aeDiffP)}}% </td>
        </ng-container>
        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>
      <mat-paginator [showTotalPages]="5" [pageSize]="10" [pageSizeOptions]="pageSizes">
      </mat-paginator>
    </div>
  </mat-tab>

  <mat-tab>
    <ng-template mat-tab-label>Status</ng-template>
    <br>
    <button mat-button (click)="refreshtable()" class="refreshBtn buttonForRefresh mb-2">Refresh</button>
    <table #table mat-table [dataSource]="dataSource" matSortActive="name" matSortDirection="asc" matSort
      class="mat-elevation-z8">
      <ng-container matColumnDef="index">
        <th mat-header-cell *matHeaderCellDef>#</th>
        <td mat-cell *matCellDef="let element; let i = index;" class="name-cell">
          {{ (i+1) + (paginator.pageIndex * paginator.pageSize) }}
        </td>
      </ng-container>
      <ng-container matColumnDef="restaurantId">
        <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell"> RestaurantId </th>
        <td mat-cell *matCellDef="let element" class="name-cell"> {{element.restaurantId.split("@")[1] }} </td>
      </ng-container>

      <ng-container matColumnDef="event">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Event </th>
        <td mat-cell *matCellDef="let element"> {{element.event}} </td>
      </ng-container>

      <ng-container matColumnDef="startDateTime">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Start DateTime </th>
        <td mat-cell *matCellDef="let element"> {{element.createTs | date: 'MMM d, y, h:mm:ss a' }} </td>
      </ng-container>

      <ng-container matColumnDef="endDateTime">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> End DateTime </th>
        <td mat-cell *matCellDef="let element"> 
          <div *ngIf=" element.pssi === true">
            {{element.modTs | date: 'MMM d, y, h:mm:ss a' }}
          </div>
          <div *ngIf="element.pssi === false">
            -
          </div>
         </td>
      </ng-container>

      <ng-container matColumnDef="status">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Status </th>
        <td mat-cell *matCellDef="let element">
          <div *ngIf=" element.pssi === true">
            Completed
            <mat-icon class="check_circle" >check_circle</mat-icon>
          </div>
          <div *ngIf="element.pssi === false">
            On progress
            <div class="spinner-border" role="status">
              <span class="sr-only">Loading...</span>
            </div>
          </div>
        </td>
      </ng-container>
      <tr mat-header-row *matHeaderRowDef="displayedColumns_status"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns_status;"></tr>
    </table>
  </mat-tab>
</mat-tab-group>