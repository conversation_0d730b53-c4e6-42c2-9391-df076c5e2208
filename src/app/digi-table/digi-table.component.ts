
import { Component, OnInit, ViewChild, EventEmitter, Output,Input } from '@angular/core';
import { MatSort, Sort,MatDialog, MatTableDataSource } from '@angular/material';
import { MatPaginator } from '@angular/material/paginator';
import { FormControl, Validators } from '@angular/forms';
import { MenuItem } from '../_models';
import { UtilsService } from '../_utils/utils.service';
import * as xlsx from 'xlsx';
// import * as jspdf from 'jspdf';
import { jsPDF } from 'jspdf';

import html2canvas from 'html2canvas';
import { User } from '../_models/user';
import { AuthService, GlobalsService, PurchasesService, ShareDataService } from '../_services';
import { MasterdataupdateService } from '../_services/masterdataupdate.service';
import { Branch } from '../_models';

@Component({
  selector: 'app-digi-table',
  templateUrl: './digi-table.component.html',
  styleUrls: ['./digi-table.component.scss']
})
export class DigiTableComponent implements OnInit {
  @Output() getItems: EventEmitter<any> = new EventEmitter();


  private _tableInput;
  dataSource = new MatTableDataSource();
  @ViewChild('table') private _table: any;
  @Input() allowEdit: boolean;
  @Input() isForecastReport: boolean;
  @ViewChild(MatSort) sort: MatSort;
  displayedColumns: string[];
  displayedColumns_status : string[];
  sortedData: any;
  menuItems: any[];
  all = "ALL";
  private isExporting: boolean = false;
  categories: any[];
  @ViewChild(MatPaginator) paginator: MatPaginator;
  category = new FormControl('', [Validators.required]);
  searchText = new FormControl('', [Validators.required]);
  pageSizes: number[] = [];
  currentUser: User;
  branches: Branch[];
  branchValue: any;
  interval: NodeJS.Timer;
  branch: any;
  restaurant: void;

  constructor(private utils: UtilsService, private sharedData: ShareDataService,
    private purchases: PurchasesService,private auth: AuthService,private masterDataService: MasterdataupdateService,) {
    this.currentUser = this.auth.getCurrentUser();
    // this.branches = JSON.parse(localStorage.getItem(GlobalsService.branches));
    const storedBranches = localStorage.getItem(GlobalsService.branches);
    this.branches = storedBranches ? JSON.parse(storedBranches) : [];
  }

  ngOnInit() {
    this.forcastStatusTable();
  }

  ngAfterViewInit() {
    // this.dataSource.sort = this.sort;forcastStatusTable()
  }

  ngOnDestroy() {
  }
  
  @Input() set tableInput(obj: any) {
    this._tableInput = obj;
    this.menuItems = this._tableInput.data;
    this.displayedColumns = this._tableInput.columns;
    this.sortedData = new MatTableDataSource<MenuItem>(this.menuItems);
    this.sortedData.sort = this.sort;
    this.sortedData.paginator = this.paginator;
    this.categories = []
    this.menuItems.forEach(item => {
      this.categories.push(item.menuGroup)
    })
    this.categories = this.utils.getUniqueItems(this.categories).filter(category => category.name)
      .sort((a, b) => a.name > b.name)
    this.pageSizes = this.utils.getPageSizes(this.menuItems)
    this.sortedData.paginator = this.paginator;
  }


  get tableInput() {
    return this.sortedData;
  }

  get table() {
    return this._table;
  }

  public exportToExcel(obj) {
    this.isExporting = true;
    let ws: xlsx.WorkSheet = xlsx.utils.table_to_sheet(this._table._elementRef.nativeElement);
    xlsx.utils.sheet_add_json(ws, [obj], { origin: -1 });
    const wb: xlsx.WorkBook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(wb, ws, `sheet1`)
    xlsx.writeFile(wb, `${obj.name}_${obj.dateRange}.xlsx`);
    this.isExporting = false;
  }

  captureScreen() {
    let data = document.getElementsByTagName('table');
    html2canvas(data[0]).then(canvas => {
      let imgWidth = 208;
      let pageHeight = 295;
      let imgHeight = canvas.height * imgWidth / canvas.width;
      let heightLeft = imgHeight;
      const contentDataURL = canvas.toDataURL('image/png')
      // let pdf = new jspdf('l', 'mm', 'a4'); // A4 size page of PDF
      let pdf = new jsPDF('l', 'mm', 'a4');
      let position = 0;
      pdf.addImage(contentDataURL, 'PNG', 0, position, imgWidth, imgHeight)
      // Generated PDF
      pdf.save('MYPdf.pdf')
    }
    );
  }


  getTotal(key: string) {
    return this.utils.getTotal(this.sortedData.data, key);
  }

  getapDiffPercent() {
    if (this.getTotal('actual') > 0)
      return (this.getTotal('predicted') / this.getTotal('actual')) * 100;
    return this.getTotal('predicted') * 100

  }
  getaeDiffPercent() {
    if (this.getTotal('actual') > 0)
      return (this.getTotal('estimated') / this.getTotal('actual')) * 100;
    return this.getTotal('estimated') * 100
  }

  public doFilter = (value: string) => {
    this.sortedData.filter = value.trim().toLocaleLowerCase();
  }

  selectCategory(val) {
    let obj = {
      key1: 'menuGroup',
      key2: 'name',
      arr: this._tableInput.data,
      val: val.value
    }

    this.sortedData.data = this.utils.filterArrByPropVal(obj)
  }

  refreshtable(){
    this.forcastStatusTable();
  }


  forcastStatusTable(){
    this.sharedData.ForcastRes.subscribe(ForcastRes => { 
      this.restaurant  = ForcastRes.branchArray;
    });
    let obj = {}
    obj['tenantId'] = this.currentUser.tenantId;
    // obj['restaurantId'] = this.currentUser.restaurantId;
    obj['restaurantId'] = this.restaurant;
    obj['event'] = "estimation"
    this.masterDataService.forcastStatusTable(obj).subscribe(response => {
      if (response.success === true) {
        this.dataSource.data = response.data
        this.displayedColumns_status = ['index','restaurantId','event','startDateTime','endDateTime', 'status'];
        // this.pageSizes = this.utils.getPageSizes(response.data)
        // this.dataSource.paginator = this.paginator;
      }
  });
  }

}
