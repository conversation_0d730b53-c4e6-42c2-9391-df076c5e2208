import { Component, OnInit, ViewChild } from "@angular/core";
import {
  FormBuilder,
  FormGroup,
  Validators,
  FormControl,
  FormArray,
} from "@angular/forms";
import {
  AuthService,
  BranchTransferService,
  PurchasesService,
  ShareDataService,
} from "../_services";
import { UtilsService } from "../_utils/utils.service";
import { GlobalsService } from "../_services";
import { Observable } from "rxjs";
import { map, startWith } from "rxjs/operators";
import { PreviewIbtComponent } from "../_dialogs/preview-ibt/preview-ibt.component";
import { MatDialog, MatOption, MatTableDataSource } from "@angular/material";
import { NotificationService } from "../_services/notification.service";
import {
  DateAdapter,
  MAT_DATE_FORMATS,
  MAT_DATE_LOCALE,
} from "@angular/material";
import {
  MomentDateModule,
  MomentDateAdapter,
} from "@angular/material-moment-adapter";
import { Router } from "@angular/router";
import { MatPaginator, MatSort } from "@angular/material";
import { ReplaySubject, Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { environment } from "src/environments/environment";
@Component({
  selector: "app-direct-ibt-list",
  templateUrl: "./direct-ibt-list.component.html",
  styleUrls: ["./direct-ibt-list.component.scss", "./../../common-dark.scss"],
})
export class DirectIbtListComponent implements OnInit {
  indentAreas: string[] = [];
  ibtForm: FormGroup;
  maxDispatchLimit: any = 0;
  dataObj: any = {};
  tableType = 0;
  user: any;
  submitAttempted: boolean;
  branches: any[];
  branchInvItems: any[];
  fromBrachFilterOptions: Observable<any[]>;
  toBrachFilterOptions: Observable<any[]>;
  invItemFilterOptions: any[] = [];
  sourceBranch: any;
  sourceReceipient: any;
  dataSource: MatTableDataSource<any>;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  displayedColumns: any[];
  pageSizes: any;
  newData: { quantity: number };
  isShowButton: boolean = false;
  isShowtable: boolean = false;
  indentPreview: boolean = false;
  categoryList = ["All"];
  subCategoryList = ["All"];
  ItemTypeList = ["All"];

  category = new FormControl("", [Validators.required]);
  Subcategory = new FormControl();
  ItemType = new FormControl();
  filterKeys = { ItemType: "All", category: "All", subCategory: "All" };
  subCatList: any = [];
  initSubCategoryList: any = [];
  initCategoryList: any = [];
  items = [];
  filteredWorkAreasList: any;
  allBranches: any;
  filteredBranches: any;
  allStores: any;
  searchText: any;
  searchLetter: any;
  showTooltip: boolean = false;
  hoveredElement: any = null;
  selectedOption: boolean = false;
  searchValue: any = ''

  public Bank: any[] = [];
  public VendorBank: any[] = [];
  public workAreaFilterCtrl: FormControl = new FormControl();
  public workAreaBanks: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  protected _onDestroy = new Subject<void>();
  source: any;
  stockType: string;
  disableStock: boolean = false;
  selectedWorkArea: string;
  currentWorkArea: any;
  showFooter = false;
  categoryData: any;
  isTrue = true
  multiCategory: any;
  @ViewChild('categoryAllSelected') private categoryAllSelected: MatOption;
  today: Date;

  constructor(
    private notifyService: NotificationService,
    private auth: AuthService,
    private fb: FormBuilder,
    private branchTransfer: BranchTransferService,
    private dialog: MatDialog,
    private utils: UtilsService,
    private router: Router,
    private sharedData: ShareDataService,
    private purchases: PurchasesService
  ) {
    this.user = this.auth.getCurrentUser();
    let date = new Date();
    date.setHours(0, 0, 0, 0);
    this.today = date ; 
  }

  ngOnInit() {
    this.invItemFilterOptions = [];
      this.ibtForm = this.fb.group({
        fromBranch: ["", Validators.required],
        toBranch: ["", Validators.required],
        stockType: ["", Validators.required],
        workArea: ["", Validators.required],
        category: ["", Validators.required],
        eta: ["", Validators.required],
        items: this.fb.array([]),
      });
      this.branchInvItems = [];
    this.auth
      .getBranchesForDirectIndent({
        tenantId: this.user.tenantId,
      })
      .subscribe(
        (data: any) => {
          this.allBranches = data["branches"];
          if(data["stores"].length == 0){
            this.allStores = data["branches"];
          }else{
            this.allStores = data["stores"];
          }
          
          this.source = this.allStores.length == 1 ? this.allStores[0] : undefined;
          this.multiCategory = data.multiCategory[0].permission.multiCategory
          
          this.sharedData.sharedBranchData.subscribe((val) => {
            let available = val;
            this.filteredBranches = this.allBranches.filter((bItem) => {
              return available.some(
                (aItem) => aItem.restaurantIdOld === bItem.restaurantIdOld
              );
            });
          });
        },
        (err) => console.error(err)
      );
    this.displayedColumns = [
      "category",
      "subCategory",
      "itemName",
      "itemType",
      "entryType",
      "pkgName",
      "inStock",
      "uom",
      "quantity",
      "unitPrice",
      "totalPrice"
    ];

    
  }

  isFieldInvalid(field: any) {
    let isInvalid: boolean =
      (!this.ibtForm.get(field).valid && this.ibtForm.get(field).touched) ||
      (this.ibtForm.get(field).untouched && this.submitAttempted);
    return isInvalid;
  }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  truncateNew(number, precision = 3) {
    try {
        if (typeof number === 'string') {
            number = parseFloat(number);
        }
        if (isNaN(number)) {
            number = 0;
        }
        const factor = Math.pow(10, precision);
        const truncatedNumber = Math.trunc(number * factor) / factor;
        return Math.floor(truncatedNumber * Math.pow(10, precision)) / Math.pow(10, precision);
    } catch (error) {
        console.error('Error occurred in truncateNew:', error);
        return 0;
    }
  }

  generateIbt() {
    this.preview();
    const fromBranch = this.ibtForm.value.fromBranch;
    const toBranch = this.ibtForm.value.toBranch;
    const eta = this.ibtForm.value.eta;
    const items = this.dataSource.data.filter(
      (item) => item.name != "" && item.quantity > 0
    );
    items.forEach((item)=> {
      item['price'] = item['actualPrice']
      if ( item['uom'] === 'portion') {
        item['uom'] = item['defaultUOM']
        item['inStock'] = item['currentStock']
        let conversionCoefficient =  item['defaultUOM'] == 'NOS' ? 1: 1000 ;
        let portionWeight = (item.portionWeight * 1000);
        let convertedWeight = (portionWeight / conversionCoefficient) * item.quantity;
        item['quantity'] = convertedWeight / 1000 
      }
    })
    let obj = {};
    obj["eta"] = this.utils.dateCorrection(eta);
    obj["fromBranch"] = fromBranch;
    obj["toBranch"] = toBranch;
    obj["items"] = items;
    obj["workArea"] = this.ibtForm.value.workArea;

    if (fromBranch.branchName != toBranch.branchName) {
      if (items.length > 0) {
        this.isShowButton = true;
        this.branchTransfer
          .createIbt({
            tenantId: this.user.tenantId,
            restaurantId: toBranch.restaurantIdOld,
            uId: this.user.mId,
            ibt: obj,
            role: this.user.role,
            userEmail: this.user.email,
            baseUrl: environment.baseUrl,
            directIbt: true,
          })
          .subscribe((data: any) => {
            this.preview()
            this.ibtDialog(data.newIbt);
            this.ibtForm.reset();
          });
      } else {
        this.utils.snackBarShowWarning("Add Quantity to IBT.");
      }
    } else {
      this.utils.snackBarShowError("Both from and to branches are the same");
    }
  }

  displayBranchName(option?: any): string | undefined {
    return option ? option.branchName : undefined;
  }

  displayItemName(option?: any): string | undefined {
    return option ? option.itemName : undefined;
  }
  addItemToForm() {
    const items = this.ibtForm.controls.items as FormArray;
    items.push(
      this.fb.group({
        name: "",
        quantity: 0,
        unitPrice: 0,
        uom: "",
        itemCode: "",
        entryType: "",
        pkgName: "",
        pkgQty: "",
      })
    );
    const newFormGroup = items.at(items.length - 1);
    this.invItemFilterOptions.push(
      newFormGroup.get("name").valueChanges.pipe(
        startWith(""),
        map((value) => this._filter(value, this.branchInvItems, "itemName"))
      )
    );
  }

  removeItemFromForm(remItem) {
    const items = this.ibtForm.controls.items as FormArray;
    items.removeAt(this.ibtForm.value.items.indexOf(remItem));
  }

  selectFromBranch(e) {
    this.sourceReceipient = e.branchName;
    this.branchTransfer;
  }

  selectReceipient(e) {
    this.sourceBranch = e.branchName;
  }

  private intialiseFilters() {
    this.fromBrachFilterOptions = this.ibtForm
      .get("fromBranch")
      .valueChanges.pipe(
        startWith(""),
        map((value) => this._filter(value, this.branches, "branchName"))
      );

    this.toBrachFilterOptions = this.ibtForm.get("toBranch").valueChanges.pipe(
      startWith(""),
      map((value) => this._filter(value, this.branches, "branchName"))
    );
  }

  private _filter(value: string, arr: any[], key: string): any[] {
    const filterValue = typeof value === "string" ? value.toLowerCase() : "";
    return arr.filter((option) =>
      new RegExp(`${filterValue}`, "gi").test(option[key])
    );
  }

  private ibtDialog(data) {
    this.router.navigate(["/home"]);
    this.dialog.open(PreviewIbtComponent, {
      // height: "600px",
      width: "800px",
      data: {
        id: data.ibtId,
        role:this.user.role,
        directIbtTitle: "CSI",
        items: data.items,
        fromBranch: data.fromBranch,
        toBranch: data.toBranch,
        eta: data.eta,
        createTs: data.createTs,
        ok: function () {
          this.location.back();
        }.bind(this),
      },
    });
  }

  raiseKitchenIndent() {
    const items = this.ibtForm.controls.items as FormArray;
    if (items.length > 0) {
      this.ibtForm.value.items.map((item) => {
        item.name = item.name.itemName;
      });
      let ibt: any = this.ibtForm.value;
      ibt.items = this.utils.arrToObjUpdatingQuantites(
        this.ibtForm.value.items
      );
    } else {
      this.utils.snackBarShowWarning("Add items to IBT.");
    }
  }

  validateDispatchQty(i) {
    const items = this.ibtForm.controls.items as FormArray;
    const selectedItem = items.at(i);
    this.maxDispatchLimit = selectedItem.value.name.inStock;
    if (selectedItem.get("quantity").value > this.maxDispatchLimit) {
      selectedItem.get("quantity").setValue(this.maxDispatchLimit);
    }
  }

  getTotalIndentCost(event, element) {
    element.totalPrice = this.utils.truncateNew(element.quantity * element.withTaxPrice)
  }

  portionChange() {    
    if (this.selectedOption) {
        this.dataSource.data = this.dataSource.data.map(element => {          
            if (element.ItemType === 'SubRecipe' && element.hasOwnProperty('portionWeight')) {
                element['uom'] = 'portion';
                element.selectedOption = 'portion';
                let conversionCoefficient =  element['defaultUOM'] == 'NOS' ? 1: 1000 ;
                element['inStock'] = element['currentStock'] / (element.portionWeight / conversionCoefficient)
                element['withTaxPrice'] = this.utils.truncateNew((element['actualPrice'] * (element.portionWeight / conversionCoefficient)),2)
            }
            return element;
        });
    } else {
        this.dataSource.data = this.dataSource.data.map(element => {
            if (element.ItemType === 'SubRecipe' && element.hasOwnProperty('portionWeight')) {
                element['uom'] = 'uom'; 
                element.selectedOption = 'uom';
                element['uom'] = element['defaultUOM']
                element['inStock'] = element['currentStock']
                element['withTaxPrice'] = this.utils.truncateNew((element['actualPrice']),2)
            }
            return element;
        });
    }
  }

  getTotalIndentCostQuantity(event, element) {

    if ( element['uom'] == 'portion'){
      let conversionCoefficient =  element['defaultUOM'] == 'NOS' ? 1: 1000 ;
      let portionWeight = element.portionWeight * 1000;   
      let convertWeight = (portionWeight / conversionCoefficient) * element.quantity;  
      let convertedWeight =  convertWeight / 1000 ;
      element['convertedWeight'] = convertedWeight;
    }

    this.items = this.dataSource.data.filter(
      (item) => item.name != "" && item.quantity > 0
    );

    element.totalPrice = element.quantity * element.withTaxPrice
  }

  convertToQty(el){
    if (el['selectedOption'] === 'portion')  {
      let conversionCoefficient =  el['defaultUOM'] == 'NOS' ? 1: 1000 ;
      el['uom'] = 'portion'
      el['inStock'] = el['currentStock'] / (el.portionWeight / conversionCoefficient)
      el['withTaxPrice'] = this.utils.truncateNew((el['actualPrice'] * (el.portionWeight / conversionCoefficient)),2)
      el['quantity'] = 0
    } else {
      el['quantity'] = 0
      el['uom'] = el['defaultUOM']
      el['inStock'] = el['currentStock']
      el['withTaxPrice'] = (el['actualPrice'],2)
    }
  }

  findData() {
    this.isShowtable = true;
    this.sourceReceipient = this.ibtForm.value.fromBranch.branchName;
    let obj = {
      tenantId: this.user.tenantId,
      restaurantId: this.ibtForm.value.fromBranch.restaurantIdOld,
      recipientRestaurantId: this.ibtForm.value.toBranch.restaurantIdOld,
      stockType: this.stockType,
      workArea: this.ibtForm.value.workArea,
    }

    if(Array.isArray(this.ibtForm.value.category)){
      const elementToRemove = 1;
      const filteredArray = this.ibtForm.value.category.filter(item => item !== elementToRemove); 
      obj['category'] = filteredArray
       // multi select 
    }else{
      obj['category'] = [this.ibtForm.value.category]; // single select
    }

    this.branchTransfer.getIbtInvWithWorkArea(obj).subscribe((data) => {
          if (data) {
            data.invItems["quantity"] = 0;
            data.invItems["totalPrice"] = 0;
          }
          this.branchInvItems = data.invItems;
          this.dataSource = new MatTableDataSource();
          this.dataSource.data = this.branchInvItems;
          this.pageSizes = this.utils.getPageSizes(this.dataSource.data);
          this.dataSource.paginator = this.paginator;

          this.branchInvItems.forEach((item) => {
            item['defaultUOM'] = item['uom']
            item['currentStock'] = item['inStock']
            let withTaxPrice = this.utils.truncateNew(item['withTaxPrice'],2)
            item['withTaxPrice'] = withTaxPrice
            item['actualPrice'] = withTaxPrice

            if (item.category == null) {
              item.category = "N/A";
            }
            if (item.ItemType == null) {
              item.ItemType = "N/A";
            }
            if (item.subCategory == null) {
              item.subCategory = "N/A";
            }
            this.ItemTypeList.push(item.ItemType);
            this.categoryList.push(item.category);
            this.subCategoryList.push(item.subCategory);
          });
          this.categoryList = this.categoryList.filter(
            (k, i, ar) => ar.indexOf(k) === i
          );
          this.ItemTypeList = this.ItemTypeList.filter(
            (p, q, arrr) => arrr.indexOf(p) === q
          );
          this.subCategoryList = this.subCategoryList.filter(
            (j, l, arr) => arr.indexOf(j) === l
          );
          this.initCategoryList = this.categoryList;
          this.initSubCategoryList = this.subCategoryList;
        },
        (err) => console.error(err)
      );
  }

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  };

  clear(){
    this.searchLetter = ''
    this.searchValue = ''
    this.doFilter(this.searchValue)
  }

  preview() {
    // this.indentPreview = this.indentPreview
    this.showFooter = !this.showFooter;
    if (this.indentPreview == false) {
      this.indentPreview = true;
    } else {
      this.indentPreview = false;
    }

    if (this.indentPreview == true) {
      this.dataSource = new MatTableDataSource();
      this.dataSource.data = this.branchInvItems.filter(
        (item) => item.quantity > 0 && Object.keys(item.workArea)
      );
      this.pageSizes = this.utils.getPageSizes(this.dataSource.data);
      this.dataSource.paginator = this.paginator;
    } else {
      this.dataSource = new MatTableDataSource();
      this.dataSource.data = this.branchInvItems;
      this.pageSizes = this.utils.getPageSizes(this.dataSource.data);
      this.dataSource.paginator = this.paginator;
    }
  }

  selectItemType(itemType) {
    let filteredCategoryList = [];
    let filteredSubCategoryList = [];
    if (itemType != "All") {
      let filteredItem = this.branchInvItems.filter(
        (item) => item["ItemType"].toUpperCase() === itemType.toUpperCase()
      );
      filteredItem.forEach((element) => {
        filteredCategoryList.push(element.category);
        filteredSubCategoryList.push(element.subCategory);
      });
      this.categoryList = filteredCategoryList.filter(
        (k, i, ar) => ar.indexOf(k) === i
      );
      this.subCategoryList = filteredSubCategoryList.filter(
        (k, i, ar) => ar.indexOf(k) === i
      );
      this.categoryList.splice(0, 0, "All");
      this.subCategoryList.splice(0, 0, "All");
      this.subCatList = this.subCategoryList;
    } else {
      this.categoryList = this.initCategoryList;
      this.subCategoryList = this.initSubCategoryList;
    }
    this.filterKeys = {
      ItemType: itemType,
      category: "All",
      subCategory: "All",
    };
    this.allFilter();
  }

  toggleAllSelectionCategory(manual = false){
    if (this.categoryAllSelected && this.categoryAllSelected.selected ) {
      this.ibtForm.get('category').patchValue([...this.categoryData, 1]);
    } else if (manual){
      this.ibtForm.get('category').patchValue([...this.categoryData, 1]);
    }else {
      this.ibtForm.get('category').patchValue([]);
    }
  }

  selectCategory(cat) {
    this.dataSource = new MatTableDataSource();
    this.dataSource.data = []
  }

  //   let filteredSubCategoryList = [];
  //   let filteredItem = [];
  //   if (cat != "All") {
  //     if (this.filterKeys.ItemType != "All") {
  //       filteredItem = this.branchInvItems.filter(
  //         (item) =>
  //           item["category"].toUpperCase() === cat.toUpperCase() &&
  //           item["ItemType"].toUpperCase() ===
  //             this.filterKeys.ItemType.toUpperCase()
  //       );
  //     } else {
  //       filteredItem = this.branchInvItems.filter(
  //         (item) => item["category"].toUpperCase() === cat.toUpperCase()
  //       );
  //     }
  //     filteredItem.forEach((element) => {
  //       filteredSubCategoryList.push(element.subCategory);
  //     });
  //     this.subCategoryList = filteredSubCategoryList.filter(
  //       (k, i, ar) => ar.indexOf(k) === i
  //     );
  //     this.subCategoryList.splice(0, 0, "All");
  //   } else if (this.filterKeys.ItemType != "All") {
  //     this.subCategoryList = this.subCatList;
  //   } else {
  //     this.subCategoryList = this.initSubCategoryList;
  //   }
  //   this.filterKeys.category = cat;
  //   this.filterKeys.subCategory = "All";
  //   // this.allFilter();
  // }

  selectSubCat(subCat) {
    this.filterKeys.subCategory = subCat;
    this.allFilter();
  }

  allFilter() {
        let tmp = this.branchInvItems;
    let prev = this.branchInvItems;
        Object.keys(this.filterKeys).forEach((element) => {
      if (this.filterKeys[element] != "All") {
        tmp = prev.filter(
          (item) =>
            item[element].toLowerCase() ===
            this.filterKeys[element].toLowerCase()
        );
        prev = tmp;
      }
    });
    this.dataSource.data = tmp;
  }

  selectedIndentArea(val) {
    this.sourceBranch = val.branchName;
    if (val.branchType == "outlet"){
      this.stockType = 'Non-Stockable';
    } else {
      this.stockType = 'Stockable';
    }
    this.disableStock =  val.storeAvailable  ? false : true ;
    let workArea = val.workAreas
    this.user.restaurantAccess.forEach(element => {
      if (element.restaurantIdOld == val.restaurantIdOld) {
        workArea = element.workAreas;
      }
    });
    this.filteredWorkAreasList = this.indentAreas = this.currentWorkArea = workArea;
    this.VendorBank = this.filteredWorkAreasList;
    this.workAreaBanks.next(this.VendorBank.slice());
    this.workAreaFilterCtrl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this.vendorfilterBanks();
      });
      this.searchCategory()
  }

  stockChange(){
    this.stockType == 'Stockable' ? (this.ibtForm.get('workArea').setValue('Store',this.filteredWorkAreasList = this.indentAreas = ['Store'])) : (this.filteredWorkAreasList = this.indentAreas = this.currentWorkArea) ;
    this.VendorBank = this.filteredWorkAreasList;
    this.workAreaBanks.next(this.VendorBank.slice());
    this.workAreaFilterCtrl.valueChanges
      .pipe(takeUntil(this._onDestroy))
      .subscribe(() => {
        this.vendorfilterBanks();
      });
  }

  filterWorkArea() {
    this.filteredWorkAreasList = this.indentAreas.filter((wa) =>
      wa.toLowerCase().includes(this.searchText.toLowerCase())
    );
  }

  onWorkAreaSelectionChange(val) {
    this.ibtForm.value.workArea = val;
    this.searchText = val;
  }

  protected vendorfilterBanks() {
    if (!this.VendorBank) {
      return;
    }
    let search = this.workAreaFilterCtrl.value;
    if (!search) {
      this.workAreaBanks.next(this.VendorBank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.workAreaBanks.next(
      this.VendorBank.filter(
        (VendorBank) => VendorBank.toLowerCase().indexOf(search) > -1
      )
    );
  }

  getTotal(key: string) {
    if(this.showFooter){
      return this.utils.getTotal(this.dataSource.data, key);
    }
  }

  searchCategory() {
    let obj = {
      tenantId: this.auth.getCurrentUser().tenantId,
      restaurantId: this.ibtForm.value.toBranch.restaurantIdOld
    } 
    this.purchases.fetchCategoryWithoutVendor(obj).subscribe(res => {
      if (res.result){
        this.categoryData = res.data ;
        if(this.multiCategory){
          this.toggleAllSelectionCategory(true)
        }
      } else {
        this.utils.snackBarShowInfo('Category not found')
      }
    })
  }

  focusFunctionWithOutForm(element , value){
    if(Number(element[value]) === 0){
      element[value] = null;
    }
  }
  
  focusOutFunctionWithOutForm(element , value){
    if(element[value] === null){
      element[value] = 0
    }
  }

  onMouseOver(element: any){    
    this.showTooltip = true;  
    this.hoveredElement = element;     
  }

  onMouseOut(){
    this.showTooltip = false;
  }

}

// this.utils.snackBarShowSuccess("success...");
//  this.utils.snackBarShowError("error....");
//  this.utils.snackBarShowWarning("warning....");
//  this.utils.snackBarShowInfo("info....");