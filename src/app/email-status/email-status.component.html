<div class="emailInputs">
  <form [formGroup]="emailForm" style="display: flex !important;">
    <div>
      <mat-form-field *ngIf="multiBranchUser" appearance="none" class="topitem mr-2">
        <label>Select Branch</label>
        <mat-select placeholder="Select Branch" class="outline" formControlName="branchSelection" (selectionChange)="selectedbranch($event)" class="outline">
          <mat-option *ngFor="let rest of branchesData" [value]="rest">
            {{ rest.branchName }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>

  <div>
    <mat-form-field appearance="none" class="topitem">
      <label class="title-palce">Select Template </label>
      <mat-select class="outline" placeholder="Select Template" formControlName="templateSelection" (selectionChange)="filterbybranch()">
        <mat-option value="purchaseApproval">Purchase Request</mat-option>
        <mat-option value="purchaseOrder">Purchase Order</mat-option>
        <mat-option value="postGrnStatus">Grn Approval</mat-option>
        <mat-option value="purchaseRequest">Vendor</mat-option>
        <!-- <mat-option value="reportFailed">reportFailed</mat-option>[(ngModel)]='selectedTemplate' -->
      </mat-select>
    </mat-form-field>
  </div>

  <div>
    <button *ngIf="showItems" mat-raised-button matTooltip="click to refresh" class="refreshBtn buttonForRefresh ml-2" (click)="refreshdata()">
      Refresh
    </button>
  </div>
</form>
</div>


<mat-card *ngIf="showItems">
<br>
  <div class="mat-elevation-z8" >
    <section class="example-container-1 mat-elevation-z8">
    <table mat-table [dataSource]="dataSource">

      <ng-container matColumnDef="poId">
        <th mat-header-cell *matHeaderCellDef>  Po Id </th>
        <td mat-cell *matCellDef="let element"> {{element.details.poId}} </td>
      </ng-container>

      <ng-container matColumnDef="grnId">
        <th mat-header-cell *matHeaderCellDef>  Grn Id </th>
        <td mat-cell *matCellDef="let element"> {{element.details.grnId}} </td>
      </ng-container>

      <ng-container matColumnDef="prId">
        <th mat-header-cell *matHeaderCellDef>  Pr Id </th>
        <td mat-cell *matCellDef="let element"> {{element.details.prId}} </td>
      </ng-container>

      <ng-container matColumnDef="createsAt">
        <th mat-header-cell *matHeaderCellDef> Creates At </th>
        <td mat-cell *matCellDef="let element"> {{ element.createTs | date: "EEEE, MMMM d, y"}} </td>
      </ng-container>

      <ng-container matColumnDef="status">
        <th mat-header-cell *matHeaderCellDef> Status </th>
        <td mat-cell *matCellDef="let element">
          <div *ngIf="element.pssi== true">{{ element.message }}
              <mat-icon class="check_circle">check_circle</mat-icon>
          </div>
          <div *ngIf="element.pssi==false">{{ element.message }}
            <!-- <p class="sp sp-circle "></p> -->
            <div class="spinner-border" role="status">
              <span class="sr-only">Loading...</span>
            </div>
          </div>
        </td>
      </ng-container>

      <ng-container matColumnDef="message">
        <th mat-header-cell *matHeaderCellDef> Message </th>
        <td mat-cell *matCellDef="let element">{{ element.message }}</td>
      </ng-container>

      <ng-container matColumnDef="approvalStatus">
        <th mat-header-cell *matHeaderCellDef> approval status </th>
        <td mat-cell *matCellDef="let element" class="objectOfTbale"><!-- {{ element.approvalDetail |json}} -->
          <div *ngFor="let item of element.approvalDetail | keyvalue">
              <div *ngIf="(isoDateTimeRegex.test(item.value))">
                {{ item.key }}  :  {{ item.value | date:'MMM d, hh:mm:ss a'}} 
              </div>
              <div *ngIf="!(isoDateTimeRegex.test(item.value))">
                {{ item.key }}  :  {{ item.value }} 
              </div>
          </div>
      </td>
      </ng-container>

      <ng-container matColumnDef="error">
        <th mat-header-cell *matHeaderCellDef> Error </th>
        <td mat-cell *matCellDef="let element">
          <div>
            <div *ngIf="element.error">Error</div>
            <div *ngIf="!element.error">-</div>
          </div>
        </td>
      </ng-container>
      <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>
  </section>
		<div class="dataMessage" *ngIf="dataSource.data.length == 0"> No Data Available </div>
    <mat-paginator [showTotalPages]="5" [pageSize]="10" [pageSizeOptions]="pageSizes"></mat-paginator>
  </div>
</mat-card>