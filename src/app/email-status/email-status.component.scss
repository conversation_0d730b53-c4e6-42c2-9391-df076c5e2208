.outline-light {
    background-color: #2f2f2f !important;
    height: 40px;
    border: solid 1px #2f2f2f;
    padding-left: 10px !important;
    border-radius: 5px 0 0 5px;
    font-size: 12px;
    ::ng-deep .mat-select-value {
      padding: 12px 0 !important;
    }
  
    ::ng-deep .mat-select-arrow-wrapper {
      height: 38px !important;
      border-radius: 0px 3px 3px 0 !important;
      background-color: #464646 !important;
    }
  
    ::ng-deep .mat-select-arrow {
      width: 12px;
      height: 16px;
      border: none !important;
      margin: 0 4px;
      background-image: url(/assets/if_chevron-right.png);
      padding: 0 12px;
      background-repeat: no-repeat;
      background-position-x: center;
    }
  }

.check_circle{
  color:green;
  font-size: 15px;
}

.inputMargin{
  margin-left: 20px;
}

.refreshBtn{
  margin-top: 24px;
}

.emailInputs{
  // display: flex;
  margin-left: 2%;
  margin-right: 2%;
}

.objectOfTbale{
  padding: 5px;
}

.example-container-1{
  max-height: 525px;
  overflow-y: auto;
}