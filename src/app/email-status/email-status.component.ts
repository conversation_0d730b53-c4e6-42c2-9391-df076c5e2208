import { Component, OnInit, ViewChild } from '@angular/core';
import { MatPaginator, MatTableDataSource } from '@angular/material';
import { AuthService, PurchasesService, ShareDataService } from '../_services';
import { UtilsService } from '../_utils/utils.service';
import { GlobalsService } from '../_services';
import { Branch } from '../_models';
import { interval } from 'rxjs';
import { FormBuilder, FormControl, FormGroup, Validators } from "@angular/forms";

@Component({
  selector: 'app-email-status',
  templateUrl: './email-status.component.html',
  styleUrls: ['./email-status.component.scss', "./../../common-dark.scss"]
})
export class EmailStatusComponent implements OnInit {
  displayedColumns: string[];
  dataSource = new MatTableDataSource();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  user: any;
  inputObj: any;
  pageSizes = []
  branches: Branch[];
  selectedTemplate: any;
  templatetype: string[];
  showItems: boolean = false;
  multiBranchUser; branchSelected: boolean;
  sub: any;
  isoDateTimeRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d+)?(([+-]\d{2}:\d{2})|Z)?$/;
  branchesData: any[];
  getBranchData: any[]
  emailForm: FormGroup;
  restId: any;
  constructor(
    private purchases: PurchasesService,
    private auth: AuthService,
    private utils: UtilsService,
    private sharedData: ShareDataService,
    private fb: FormBuilder
  ) {
    this.user = this.auth.getCurrentUser();
    this.branches = JSON.parse(localStorage.getItem(GlobalsService.branches));
    this.emailForm = this.fb.group({
      branchSelection: [null, Validators.required],
      templateSelection: [null, Validators.required]
    });

    this.emailForm.get('templateSelection').setValue('purchaseApproval');
    this.filterbybranch()

    this.sharedData.sharedBranchData.subscribe((val) => {
      this.getBranchData = val;
      if(this.getBranchData.length == 0 ){
        this.branchesData = this.user.restaurantAccess;
      }else if(this.getBranchData.length == 1){        
        const toSelect = this.getBranchData.find(data => data.branchName == this.getBranchData[0].branchName);
        this.emailForm.get('branchSelection').setValue(toSelect);
        this.branchesData = this.getBranchData
        this.restId = this.emailForm.value.branchSelection
        this.filterbybranch();
      }else{
        this.branchesData = this.getBranchData
      }
  });

  }

  ngOnInit() {
    this.multiBranchUser = this.user.multiBranchUser
    this.user.restaurantAccess[0].restaurantIdOld
  }

  selectedbranch() {
    this.selectedTemplate = ''
    this.emailForm.get('templateSelection').setValue('');
    this.dataSource = new MatTableDataSource()
  }


  filterbybranch() {
    let obj = {
      tenantId: this.user.tenantId,
      templateType: this.emailForm.value.templateSelection,
      userEmail: this.auth.getCurrentUser().email,
      event: "email"
    }
    if(!this.emailForm.value.branchSelection){
      obj['restaurantId'] = this.user.restaurantAccess[0].restaurantIdOld
    }else{
      obj['restaurantId'] = this.emailForm.value.branchSelection.restaurantIdOld
    }
    this.purchases.emailStatus(obj).subscribe(data => {
    	if (obj.templateType == 'postGrnStatus') {
    		this.displayedColumns = ["grnId", "createsAt", "status"]
    	} else if (obj.templateType == 'purchaseApproval') {
    		this.displayedColumns = ["prId", "createsAt", "status", "approvalStatus"]
    	} else if (obj.templateType == 'purchaseRequest') {
    		this.displayedColumns = ["poId", "createsAt", "status"]
    	} else {
    		this.displayedColumns = ["poId", "createsAt", "status", "approvalStatus"]
    	}
      this.dataSource.data = data;
    	this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
    	this.dataSource.paginator = this.paginator;
    });
    this.showItems = true;  }

    refreshdata() {
    this.filterbybranch();
  }

}
