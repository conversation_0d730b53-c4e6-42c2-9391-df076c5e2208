<!-- <div>
    <div>
      <h3 *ngIf="data.status != 401">
        Error
      </h3>
      <h3 *ngIf="data.status == 401">
        Attention
      </h3>
        <p *ngIf="data.status != 401">
            Reason: {{data.msg}}
        </p>
        <p *ngIf="data.status == 401">
            {{data.msg}}
        </p> -->
        <!-- <p>
            Status: {{data.status}}
        </p> -->
    <!-- </div>
</div> -->

<h2 mat-dialog-title >
  <button mat-icon-button class="CloseBtn">
    <mat-icon (click)="closeDialog()" matTooltip="close" class="closeIcon">close</mat-icon>
  </button>
  <div *ngIf="data.status != 401">
    Error
  </div>
  <div *ngIf="data.status == 401">
    Attention
  </div>
</h2>

<mat-dialog-content class="mat-typography">
    <p *ngIf="data.status != 401">
      <b> Reason: </b>{{data.msg}}
  </p>
  <p *ngIf="data.status == 401">
      {{data.msg}}
  </p>
</mat-dialog-content>