import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA} from  '@angular/material';

@Component({
  selector: 'app-error-dialog',
  templateUrl: './error-dialog.component.html',
  styleUrls: ['./error-dialog.component.scss']
})
export class ErrorDialogComponent implements OnInit {
  showDupAlert : boolean = false
  title = 'Angular-Interceptor';
   constructor(@Inject(MAT_DIALOG_DATA) public data: any,
   private  dialogRef:  MatDialogRef<ErrorDialogComponent>,) {
     console.log(data);
   }

  ngOnInit() {
  }

  public close(){
    this.dialogRef.close(this.data.inputFromUser);
  }
  
  public ok(){
    if(!this.showDupAlert){
      this.close();
      this.data.ok();
    }
  }

  closeDialog(){
    this.close();
  }

}
