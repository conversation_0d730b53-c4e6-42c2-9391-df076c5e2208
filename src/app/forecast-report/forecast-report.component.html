<mat-card>
	<div class="infoMessage">
		<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
			class="bi bi-info-circle-fill infoSvgIcon" viewBox="0 0 16 16">
			<path
				d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm.93-9.412-1 4.705c-.07.34.029.533.304.533.194 0 .487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703 0-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381 2.29-.287zM8 5.5a1 1 0 1 1 0-2 1 1 0 0 1 0 2z" />
		</svg>
		<p class="ml-2"> Hit refresh button , to pull the updated report status !</p>
	</div>

	<div class="inputsForTop">
		<form [formGroup]="reportForm">
			<mat-form-field appearance="none" class="mr-3" style="width: 250px !important">
				<label>Select Report Type</label>
				<mat-select placeholder="Select Report Type" class="outline" [ngModelOptions]="{standalone: true}"
					[(ngModel)]="selectedValue" formControlName="reportselection"
					(selectionChange)="getBaseDateFilter($event.value);processSelectedReport($event.value)">
					<input matInput type="text" style="color: white;" class="outlineSearch" name="search"
						[ngModelOptions]="{standalone: true}" [(ngModel)]="searchReportText"
						(input)="filterReport(searchReportText)" placeholder="search" />
					<mat-option *ngFor="let report of this.filteredReportList" [value]="report">
						{{ report.displayName }}
					</mat-option>
				</mat-select>
			</mat-form-field>
		</form>

		<button mat-icon-button matTooltip="Filters" class="findButton mr-2" (click)="openFilter()"
			[disabled]="!this.selectedValue || reportForm.invalid">
			<mat-icon>filter_list</mat-icon>
		</button>
	</div>
</mat-card>

<mat-card>
	<div class="example-container mat-elevation-z8">
		<div style="display:inline">
			<mat-form-field appearance="none" class="mb-2">
				<input matInput class="outlineSearch" (keyup)="applyFilter($event)" placeholder="search" #input>
			</mat-form-field>

			<button matTooltip="click to refresh" class="mb-2 buttonForRefresh"
				style=" margin-top: 17px;margin-left: 10px; float: right;" mat-raised-button id="save-btn"
				(click)="refreshData();">Refresh
			</button>
		</div>

		<table mat-table matSort [dataSource]="dataSource1">
			<ng-container matColumnDef="reportName">
				<th mat-header-cell mat-sort-header *matHeaderCellDef> Report Name </th>
				<td mat-cell *matCellDef="let element" class="links">
					<button mat-icon-button class="linkBtn" (click)="download(element)" matTooltip="download"
						[disabled]="element.status == 'Completed' && element.hasOwnProperty('error')">
						{{element.reportId}}
					</button>
				</td>
			</ng-container>
			<ng-container matColumnDef="type">
				<th mat-header-cell mat-sort-header *matHeaderCellDef> Type </th>
				<td mat-cell *matCellDef="let element"> {{this.checkReportsName(element.reportName)}} </td>
			</ng-container>
			<ng-container matColumnDef="createdAt">
				<th mat-header-cell mat-sort-header *matHeaderCellDef> Created Date </th>
				<td mat-cell *matCellDef="let element">
					{{ this.utils.formatDateToUTC(element.createdAt) }}
				</td>
			</ng-container>
			<ng-container matColumnDef="info">
				<th mat-header-cell mat-sort-header *matHeaderCellDef> Report Info </th>
				<td mat-cell *matCellDef="let element">
					<span (click)="metaData(element)" matTooltip="click to view report info">
						<mat-icon>info</mat-icon>
					</span>
				</td>
			</ng-container>
			<ng-container matColumnDef="action">
				<th mat-header-cell mat-sort-header *matHeaderCellDef> Download </th>
				<td mat-cell *matCellDef="let element">
					<button id="downloadBtn" (click)="download(element)" matTooltip="download" class="mt-1 mb-1"
						[disabled]="element.status == 'Completed' && element.hasOwnProperty('error')">
						<mat-icon style="font-size:15px;">file_download</mat-icon>
					</button>
				</td>
			</ng-container>
			<ng-container class="spinner-class" matColumnDef="status">
				<th mat-header-cell mat-sort-header *matHeaderCellDef> Status </th>
				<td mat-cell *matCellDef="let element">
					{{element.status}}
					<div class="tableStatusIcons">
						<mat-icon class="check_circle"
							*ngIf="element.status == 'Completed' && !element.hasOwnProperty('error')">check_circle
						</mat-icon>
						<mat-icon class="check_circle_error"
							*ngIf="element.status == 'Completed' && element.hasOwnProperty('error')">error</mat-icon>
						<div class="spinner-border spinner_class" role="status" *ngIf="element.status =='On Progress'">
							<span class="sr-only">Loading...</span>
						</div>
					</div>
				</td>
			</ng-container>
			<tr mat-header-row *matHeaderRowDef="displayedColumns_1; sticky: true"></tr>
			<tr mat-row *matRowDef="let row; columns: displayedColumns_1;"></tr>
		</table>
	</div>
	<div class="dataMessage" *ngIf="dataSource1.data.length == 0"> No Data Available </div>
	<mat-paginator [showTotalPages]="5" [pageSize]='10' [pageSizeOptions]="pageSizes1"></mat-paginator>
</mat-card>


<ng-template #openFilterDialog>
	<button mat-icon-button matDialogClose="yes" matTooltip="Close" class="CloseBtn">
		<mat-icon>close</mat-icon>
	</button>
	<h2 mat-dialog-title>
		<b>Select Filters</b>
	</h2>

	<div *ngIf="dialogLoader" class="loader-container">
		<div class="childLoader_conatiner">
			<mat-spinner diameter="50"></mat-spinner>
		</div>
	</div>

	<div *ngIf="!dialogLoader">
		<mat-dialog-content class="mat-typography" *ngIf="!dialogLoader">
			<div class="row">
				<label for="brand" *ngIf="this.brands && !brand.disabled && singleSelectBranch"
					class="angular2-multiselectLabel">Brand</label>
				<angular2-multiselect id="brand" [data]="brandKeys" [formControl]="brand"
					[settings]="singleDropdownSettings" class="customSelect" (ngModelChange)="Brands($event)"
					*ngIf="this.brands && !brand.disabled && singleSelectBranch">
					<c-item>
						<ng-template let-item="item">
							<label>
								<div>
									{{item.itemName}}
								</div>
							</label>
						</ng-template>
					</c-item>
				</angular2-multiselect>

				<label for="brand" *ngIf="this.brands && !brand.disabled && !singleSelectBranch"
					class="angular2-multiselectLabel">Brand</label>
				<angular2-multiselect id="brand" [data]="brandKeys" [formControl]="brand"
					[settings]="multiBrandBranchDropdownSettings" class="customSelect" (ngModelChange)="Brands($event)"
					*ngIf="this.brands && !brand.disabled && !singleSelectBranch">
					<c-item>
						<ng-template let-item="item">
							<label>
								<div>
									{{item.itemName}}
								</div>
							</label>
						</ng-template>
					</c-item>
				</angular2-multiselect>

				<label for="branches" *ngIf="this.brands && !filterBranches.disabled"
					class="angular2-multiselectLabel">Branches</label>
				<angular2-multiselect id="branches" [data]="brandBranchNames" [formControl]="filterBranches"
					[settings]="multiBrandBranchDropdownSettings" class="customSelect"
					(ngModelChange)="brandBranch($event)" *ngIf="this.brands && !filterBranches.disabled">
					<c-item>
						<ng-template let-item="item">
							<label>
								<div>
									{{item.itemName}}
								</div>
							</label>
						</ng-template>
					</c-item>
				</angular2-multiselect>

				<label for="singleBranch" *ngIf="singleSelectBranch && !this.brands && !filterBranches.disabled"
					class="angular2-multiselectLabel">Branch</label>
				<angular2-multiselect id="singleBranch" [data]="branchFilter" [formControl]="filterBranches"
					[settings]="singleLocationDropdownSettings" class="customSelect"
					(ngModelChange)="singleBranch($event.value)"
					*ngIf="singleSelectBranch && !this.brands && !filterBranches.disabled">
					<c-item>
						<ng-template let-item="item">
							<label>
								<div>
									{{item.itemName}}
								</div>
							</label>
						</ng-template>
					</c-item>
				</angular2-multiselect>


				<label for="multiBranch" *ngIf="!singleSelectBranch && !this.brands && !filterBranches.disabled"
					class="angular2-multiselectLabel">Branch</label>
				<angular2-multiselect id="multiBranch" [data]="branchFilter" [formControl]="filterBranches"
					[settings]="multiLocationDropdownSettings" class="customSelect"
					(ngModelChange)="multipleBranch($event.value)"
					*ngIf="!singleSelectBranch && !this.brands && !filterBranches.disabled">
					<c-item>
						<ng-template let-item="item">
							<label>
								<div>
									{{item.itemName}}
								</div>
							</label>
						</ng-template>
					</c-item>
				</angular2-multiselect>

				<mat-form-field appearance="outline" class="m-2" style="width: 100% !important;"
					*ngIf="(this.reportForm.value.reportselection.backendName != 'consolidated_purchase_indent' && this.reportForm.value.reportselection.backendName !== 'inventoryConsumptionNew' && this.reportForm.value.reportselection.backendName !== 'consolidated_purchase_indent_wac') && items.requiredColumns == true">
					<mat-label>Required Columns</mat-label>
					<mat-select placeholder="Required Columns" [formControl]="requiredColumns" [multiple]="true"
						#multiSelect>
						<mat-option>
							<ngx-mat-select-search placeholderLabel="required columns Data..."
								noEntriesFoundLabel="'no Data found'"
								[formControl]="reqColumnsFilterCtrl"></ngx-mat-select-search>
						</mat-option>
						<mat-option #allSelected (click)="toggleReqAllSelection()" [value]="1">All</mat-option>
						<mat-option *ngFor="let data of reqColumns | async" [value]="data"
							[disabled]="data.isMandatory">
							{{ data.name | titlecase }}
						</mat-option>
					</mat-select>
				</mat-form-field>

				<mat-form-field appearance="outline" class="m-2" style="width: 100% !important;"
				*ngIf="(items.requiredColumns == true) && 
				(this.reportForm.value.reportselection.backendName === 'consolidated_purchase_indent' 
				 || this.reportForm.value.reportselection.backendName === 'consolidated_purchase_indent_wac')">
					<mat-label>Required Columns For GRN</mat-label>
					<mat-select placeholder="Required GRN Columns" [formControl]="requiredGrnColumns" [multiple]="true"
						#multiSelect>
						<mat-option>
							<ngx-mat-select-search placeholderLabel="required columns Data..."
								noEntriesFoundLabel="'no Data found'"
								[formControl]="reqColumnsGrnFilterCtrl"></ngx-mat-select-search>
						</mat-option>
						<mat-option #allSelectedGrn (click)="toggleReqGrnAllSelection()" [value]="1">All</mat-option>
						<mat-option *ngFor="let data of reqColumnsGrn | async" [value]="data"
							[disabled]="data.isMandatory">
							{{ data.name | titlecase }}
						</mat-option>
					</mat-select>
				</mat-form-field>

				<mat-form-field appearance="outline" class="m-2" style="width: 100% !important;"
				*ngIf="(items?.requiredColumns) && 
				(reportForm.value?.reportselection?.backendName === 'consolidated_purchase_indent' || 
				 reportForm.value?.reportselection?.backendName === 'consolidated_purchase_indent_wac')">
					<mat-label>Required Columns For Indent</mat-label>
					<mat-select placeholder="Required Indent Columns" [formControl]="requiredIndentColumns"
						[multiple]="true" #multiSelect>
						<mat-option>
							<ngx-mat-select-search placeholderLabel="required columns Data..."
								noEntriesFoundLabel="'no Data found'"
								[formControl]="reqColumnsIndentFilterCtrl"></ngx-mat-select-search>
						</mat-option>
						<mat-option #allSelectedIndent (click)="toggleReqIndentAllSelection()"
							[value]="1">All</mat-option>
						<mat-option *ngFor="let data of reqColumnsIndent | async" [value]="data"
							[disabled]="data.isMandatory">
							{{ data.name | titlecase }}
						</mat-option>
					</mat-select>
				</mat-form-field>

				<mat-form-field appearance="outline" class="m-2" style="width: 100% !important;"
					*ngIf="(this.reportForm.value.reportselection.backendName === 'inventoryConsumptionNew') && items.requiredColumns == true">
					<mat-label>Required Columns For INV</mat-label>
					<mat-select placeholder="Required INV Columns" [formControl]="requiredInvColumns" [multiple]="true"
						#multiSelect>
						<mat-option>
							<ngx-mat-select-search placeholderLabel="required columns Data..."
								noEntriesFoundLabel="'no Data found'"
								[formControl]="reqColumnsInvFilterCtrl"></ngx-mat-select-search>
						</mat-option>
						<mat-option #allSelectedInv (click)="toggleReqInvAllSelection()" [value]="1">All</mat-option>
						<mat-option *ngFor="let data of reqColumnsInv | async" [value]="data"
							[disabled]="data.isMandatory">
							{{ data.name | titlecase }}
						</mat-option>
					</mat-select>
				</mat-form-field>

				<mat-form-field appearance="outline" class="m-2" style="width: 100% !important;"
					*ngIf="(this.reportForm.value.reportselection.backendName === 'inventoryConsumptionNew') && items.requiredColumns == true">
					<mat-label>Required Columns For SubRecipe</mat-label>
					<mat-select placeholder="Required SubRecipe Columns" [formControl]="requiredSubRecipeColumns"
						[multiple]="true" #multiSelect>
						<mat-option>
							<ngx-mat-select-search placeholderLabel="required columns Data..."
								noEntriesFoundLabel="'no Data found'"
								[formControl]="reqColumnsSubRecipeFilterCtrl"></ngx-mat-select-search>
						</mat-option>
						<mat-option #allSelectedSubRecipe (click)="toggleReqSubRecipeAllSelection()"
							[value]="1">All</mat-option>
						<mat-option *ngFor="let data of reqColumnsSubRecipe | async" [value]="data"
							[disabled]="data.isMandatory">
							{{ data.name | titlecase }}
						</mat-option>
					</mat-select>
				</mat-form-field>

				<mat-form-field #priceTierField appearance="outline" class="m-2" style="width: 100% !important;"
					*ngIf="this.reportForm.value.reportselection.backendName === 'profitMarginReport'">
					<mat-label>PriceTier List</mat-label>
					<mat-select placeholder="PriceTier List" [formControl]="priceTier" >						
						<mat-option>
							<ngx-mat-select-search placeholderLabel="PriceTier Names..."
								noEntriesFoundLabel="'no Data found'"
								[formControl]="priceTierFilterCtrl"></ngx-mat-select-search>
						</mat-option>
						<mat-option *ngFor="let data of priceTierNames | async" [value]="data">
							{{ data.name | titlecase }}
						</mat-option>
					</mat-select>
				</mat-form-field>

				<div class="parentStockClass m-2"
					[ngClass]="{'stockClass': this.storeStockValue.value == true || this.workAreaStockValue.value == true}"
					*ngIf="items.storeStock == true">
					<div style="width: 140px;">
						<label class="mr-2">Store Stock</label>
						<mat-slide-toggle [checked]="toggleValue" [formControl]="storeStockValue"
							(change)="onStockToggleChange()"></mat-slide-toggle>
					</div>

					<div style="width: 300px;margin-top: -17px;" *ngIf="this.storeStockValue.value == true">
						<ngx-slider [(value)]="minStoreValue" [(highValue)]="maxStoreValue" [options]="options"
							(onUserChange)="onSliderChange($event)">
						</ngx-slider>
					</div>
				</div>

				<div class="parentStockClass m-2"
					[ngClass]="{'stockClass': this.storeStockValue.value == true || this.workAreaStockValue.value == true}"
					*ngIf="items.workAreaStock == true">
					<div style="width: 140px;">
						<label class="mr-2">WorkArea Stock</label>
						<mat-slide-toggle [checked]="toggleValue" [formControl]="workAreaStockValue"
							(change)="onNonStockToggleChange()"></mat-slide-toggle>
					</div>

					<div style="width: 300px;margin-top: -17px;" *ngIf="this.workAreaStockValue.value == true">
						<ngx-slider [(value)]="minWorkAreaValue" [(highValue)]="maxWorkAreaValue" [options]="options"
							(onUserChange)="onSliderWorkAreaChange($event)">
						</ngx-slider>
					</div>
				</div>

				<mat-form-field appearance="outline" *ngIf="!baseDate.disabled" class="m-2"
					style="width: 100% !important;">
					<mat-label *ngIf="this.baseDateValueForIndent .length > 0">Select Base Date For GRN</mat-label>
					<mat-label *ngIf="this.baseDateValueForIndent .length == 0">Select Base Date</mat-label>
					<mat-select placeholder="Select BaseDate" [formControl]="baseDate">
						<mat-option *ngFor="let data of baseDateValue" [value]="data.value">
							{{ data.displayName }}
						</mat-option>
					</mat-select>
				</mat-form-field>

				<mat-form-field appearance="outline" *ngIf="!baseDateForConsolidation.disabled" class="m-2"
					style="width: 100% !important;">
					<mat-label>Select Base Date For Indent</mat-label>
					<mat-select placeholder="Select Base Date For Indent" [formControl]="baseDateForConsolidation">
						<mat-option *ngFor="let data of baseDateValueForIndent" [value]="data.value">
							{{ data.displayName }}
						</mat-option>
					</mat-select>
				</mat-form-field>

				<mat-form-field appearance="outline" class="m-2" style="width: 100% !important;"
					*ngIf="!startDate.disabled">
					<mat-label
						*ngIf="selectedValue['backendName'] === 'systemClosingReport' || selectedValue['backendName'] === 'closingReport'">Closing
						Date</mat-label>
					<mat-label
						*ngIf="selectedValue['backendName'] !== 'systemClosingReport' && selectedValue['backendName'] !== 'closingReport'">Start
						Date</mat-label>
					<input matInput *ngIf="selectedValue['backendName'] === 'systemClosingReport'"
						[matDatepicker]="picker1" [max]="maxDate"
						[placeholder]="(selectedValue['backendName'] === 'systemClosingReport' || selectedValue['backendName'] === 'closingReport') ? 'Closing Date' : 'Start Date'"
						[formControl]="startDate" />
					<input matInput *ngIf="selectedValue['backendName'] != 'systemClosingReport'"
						[matDatepicker]="picker1" [max]="maxDate"
						[placeholder]="(selectedValue['backendName'] === 'systemClosingReport' || selectedValue['backendName'] === 'closingReport') ? 'Closing Date' : 'Start Date'"
						[formControl]="startDate" />
					<mat-datepicker-toggle matSuffix [for]="picker1">
						<mat-icon matDatepickerToggleIcon>
							<img src="./../../assets/calender.png" />
						</mat-icon>
					</mat-datepicker-toggle>
					<mat-datepicker #picker1></mat-datepicker>
				</mat-form-field>

				<mat-form-field appearance="outline" class="m-2" style="width: 100% !important;"
					*ngIf="!endDate.disabled">
					<mat-label>End Date</mat-label>
					<input matInput [matDatepicker]="picker2" [formControl]="endDate" placeholder="End Date"
						[min]="startDate.value" [max]="maxDate"/>
					<mat-datepicker-toggle matSuffix [for]="picker2">
						<mat-icon matDatepickerToggleIcon>
							<img src="./../../assets/calender.png" />
						</mat-icon>
					</mat-datepicker-toggle>
					<mat-datepicker #picker2></mat-datepicker>
				</mat-form-field>

				<div class="col startDateInput">
					<mat-form-field appearance="outline" class="m-2" style="width: 100% !important;"
						*ngIf="!yearForm.disabled">
						<mat-label>Select Base Year</mat-label>
						<mat-select placeholder="Select Year" [formControl]="yearForm"
							(selectionChange)="selectYear($event.value)">
							<mat-option *ngFor="let year of years" [value]="year">{{ year }}</mat-option>
						</mat-select>
					</mat-form-field>
				</div>

				<div class="col">
					<mat-form-field appearance="outline" class="m-2" style="width: 100% !important;"
						*ngIf="!monthForm.disabled">
						<mat-label>Select Base Month</mat-label>
						<mat-select placeholder="Select month" [formControl]="monthForm">
							<mat-option *ngFor="let month of months" [value]="month.number"
								[disabled]="month.number >=this.currentMonth">{{ month.monthName }}</mat-option>
						</mat-select>
					</mat-form-field>
				</div>

				<div class="col-2" style="margin-top: 20px;" *ngIf="!disabledCheck">
					<mat-checkbox class="example-margin custom-checkbox" [(ngModel)]="checked"
						[disabled]="disabledCheck">Tax Rate</mat-checkbox>
				</div>



				<div class="col-12 mb-2" *ngIf="!categoryForm.disabled">
					<p class="fw-bold fs-5 mb-2">Category</p>
					<div class="btn-group" role="group" aria-label="Category Selection">
						<input type="radio" style="margin-right: 10px;" name="categorySelect"
							[(ngModel)]="showCategoryDropdown" [value]="false" /> Select All
						<input type="radio" style="margin-left: 2rem; margin-right: 10px;" name="categorySelect"
							[(ngModel)]="showCategoryDropdown" [value]="true" /> Select Specific
					</div>
					<angular2-multiselect style="display: inline-table; margin-top:2px" *ngIf="showCategoryDropdown" id="cat" [data]="filteredCategoryList"
						[formControl]="categoryForm" [settings]="multiCatDropdownSettings" 
						(ngModelChange)="filterByCat($event.value)">
						<c-item>
							<ng-template let-item="item">
								<label>
									<div>{{ item.itemName }}</div>
								</label>
							</ng-template>
						</c-item>
					</angular2-multiselect>
				</div>

				<div class="col-12 mb-2" *ngIf="!SubcategoryForm.disabled && showCategoryDropdown" >
					<p class="fw-bold fs-5 mb-2">Sub Category</p>
					<div class="btn-group" role="group" aria-label="sub Category Selection">
						<input type="radio" style="margin-right: 10px;" 
							[(ngModel)]="showSubCategoryDropdown" [value]="false" /> Select All
						<input type="radio" style="margin-left: 2rem; margin-right: 10px;" 
							[(ngModel)]="showSubCategoryDropdown" [value]="true" /> Select Specific
					</div>
					<angular2-multiselect id="subCat" *ngIf="showSubCategoryDropdown" [data]="filteredSubCategoryList"
						[formControl]="SubcategoryForm" [settings]="multiSubCatDropdownSettings" style="display: inline-table; margin-top:2px">
						<c-item>
							<ng-template let-item="item">
								<label>
									<div>
										{{item.itemName}}
									</div>
								</label>
							</ng-template>
						</c-item>
					</angular2-multiselect>
				</div>


				<label for="indentWorkArea" *ngIf="!workAreaForm.disabled"
					class="angular2-multiselectLabel">WorkArea</label>
				<angular2-multiselect [data]="this.indentWorkArea" id="indentWorkArea" [formControl]="workAreaForm"
					[settings]="multiIndentWorkAreaDropdownSettings" class="customSelect"
					*ngIf="!workAreaForm.disabled">
					<c-item>
						<ng-template let-item="item">
							<label>
								<div>
									{{item.itemName}}
								</div>
							</label>
						</ng-template>
					</c-item>
				</angular2-multiselect>

				<label for="vendorDropdown" *ngIf="!vendorForm.disabled"
					class="angular2-multiselectLabel">Vendors</label>
				<angular2-multiselect [data]="this.vendorList" id="vendorDropdown" [formControl]="vendorForm"
					[settings]="multiVendorDropdownSettings" class="customSelect" *ngIf="!vendorForm.disabled">
					<c-item>
						<ng-template let-item="item">
							<label>
								<div>
									{{item.itemName}}
								</div>
							</label>
						</ng-template>
					</c-item>
				</angular2-multiselect>

				<div class="pt-3 pl-3" *ngIf="this.reportForm.value.reportselection.displayName === 'IBT Report'">
					<div>
						<mat-checkbox [(ngModel)]="isSourceChecked" (change)="onCheckboxChange('source')"
							[disabled]="isDestinationChecked"> Source </mat-checkbox>
					</div>
					<div>
						<mat-checkbox [(ngModel)]="isDestinationChecked" (change)="onCheckboxChange('destination')"
							[disabled]="isSourceChecked"> Destination </mat-checkbox>
					</div>
				</div>

				<div class="pt-3 pl-3" *ngIf="isReportSelected()">
					<div>
						<mat-checkbox [(ngModel)]="showStock"> Show Zero Stock </mat-checkbox>
					</div>
				</div>

			</div>
		</mat-dialog-content>

		<mat-dialog-actions align='center' *ngIf="!dialogLoader">
			<div class="reqBtn">
				<button mat-raised-button class="button3 mb-2 mx-2" matDialogClose="yes" (click)="reqReport()">
					Request Report
				</button>
			</div>
		</mat-dialog-actions>
	</div>
</ng-template>