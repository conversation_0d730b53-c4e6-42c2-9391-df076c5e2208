
#notificationbar p {
  font-weight: bold;
  padding: 5px;
}

.message{
  font-size: 12px;
}

.mat-select-value {
color: white;
}

.msgDiv{
  // text-align: center;
  max-width: 450px !important;
}

.ui-dialog-titlebar {
display: none;
}

.outlineSearch {
  background-color: #424242 !important;
  height: 40px;
  // background-color: #2f2f2f !important;
  // height: 30px;
  border: solid 1px #2f2f2f;
  padding-left: 10px !important;
  border-radius: 5px 0 0 5px;
  font-size: 12px;
  ::ng-deep .mat-select-value {
    padding: 12px 0 !important;
  }
}


.example-container {
  display: flex;
  flex-direction: column;
  min-width: 300px;
}

.example-header {
  min-height: 64px;
}
div#cdk-overlay-2 {
  border: 1px solid;
}
.check_circle{
  color:green;
  font-size: 18px;
  position: absolute;
  margin-top: -18px;
  margin-left: 65px;
}

.example-container-1{
  max-height: 200px;
  overflow-y: auto;
}

table {
  width: 100%;
}

.findButton{
  margin-top: 40px !important;
}

.reqBtn{
  text-align: center;
}

p {
  margin-top: 0;
  margin-bottom: 0;
}

.CloseBtn{
    float: right;
    margin-bottom: -1px;
  }

  #downloadBtn{
    width: 30px !important;
    border-radius: 4px !important;
    border: none !important;
    padding-top: 7px !important;
  }

.mat-dialog-content {
    margin: 15px !important; 
    padding: 15px 24px;
    max-height: 65vh;
    // max-width: 50vw !important;
    overflow: auto;
}

.dialogInput{
width: 80%; 
margin-top: 10px;
}

.dialogMsg{
	font-size: large;
}

.reportReqBtn{
  // margin-top: 40px !important;
  float: right !important;
}

.topInputsCard{
  width: 80% !important;
}

.matCardRowInput{
  justify-content: center !important;
}

.inputsForTop{
  display: flex;
}

.tableStatusIcons{
  position: relative;
}

.check_circle_error{
  color: #A52A2A;
  font-size: 19px;
  position: absolute;
  margin-top: -19px;
  margin-left: 65px;
}

.spinner_class{
  position: absolute;
  margin-top: -16px;
  margin-left: 65px;
}

.linkBtn{
  display: block;
  font-size: 12px;
}

::ng-deep .mat-form-field .mat-select.mat-select-disabled .mat-select-arrow {
  color: #3b3b3b !important;
}

.startDateInput{
  margin-left: -15px;
}

.dark-theme {
  // background-color: #333;
  color: #fff;
  width: 100% !important;
  border: 1px solid gray !important;
  border-radius: 5px !important;
  // margin: 5px !important;
  margin: 2px 9px 15px 9px !important;
  height: 45px;
  max-height: 42px;
  overflow: hidden;
}

::ng-deep .list-area {
  border: 1px solid #000 !important;
  border-radius: 3px;
  background-color: #000 !important;
  margin: 0;
}

::ng-deep .list-filter input {
  border: 0;
  width: 100%;
  height: 100%;
  padding: 0;
  background-color: black !important;
  color: white !important;
}

::ng-deep .pure-checkbox input[type=checkbox]+label:before {
  box-sizing: initial;
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  width: 15px;
  height: 15px;
  margin-top: -9px;
  text-align: center;
  transition: all .4s ease;
  border-radius: 3px;
  border: 1px solid !important;
}
.customSelect{
  display: inline-table !important;
}

.customSelect .arrow {
  color: red;
}

.disabled {
  pointer-events: none;
  opacity: 0.5;
}

.angular2-multiselectLabel{
  margin-bottom: 0px !important;
  margin-left: 5px !important;
}

span.countplaceholder.ng-star-inserted {
  margin-top: -12px;
  /* margin-left: 25px; */
  margin-right: -20px;
}


.childLoader_conatiner{
  margin: 10px;
  display: flex;
  justify-content: center;
}

.loader-container{
  min-width: 40vw;
}

::ng-deep .pure-checkbox input[type=checkbox]:disabled+label:before {
  // border-color: #ccc;
  display: none !important;
}

.label{
  .span{
    .my_class{
      color: red !important;
    }
  }
}


::ng-deep .ngx-slider .ngx-slider-pointer {
  // background-color: rgba(245, 124, 0, 0.54) !important;
  background-color: #f57c00 !important;
}


::ng-deep .ngx-slider .ngx-slider-selection {
  background-color: rgba(245, 124, 0, 0.54) !important;
}

::ng-deep .ngx-slider .ngx-slider-pointer:before {
  background-color: rgba(255, 172, 88, 0.54) !important;
}

::ng-deep .ngx-slider .ngx-slider-pointer.ngx-slider-active:after{
  background-color: rgba(255, 172, 88, 0.54) !important;
}

.stockClass{
  width: 100% !important;
  margin-top: 20px !important;
}

.parentStockClass{
  display: flex;
  gap: 40px;
  // align-items: end;
}


input[type="radio"] {
  -ms-transform: scale(1.5); /* IE 9 */
  -webkit-transform: scale(1.5); /* Chrome, Safari, Opera */
  transform: scale(1.5);
  cursor: pointer;
  padding-bottom: 2px;
}