import { ChangeDetectorRef, Component, NgZone, OnInit, ViewChild } from '@angular/core';
import { MenuItemService, ShareDataService } from '../_services';
import { AuthService } from '../_services';
import { User } from '../_models/user';
import { DigiTableComponent } from '../digi-table/digi-table.component'
import { FormControl, FormGroup, FormBuilder, Validators } from '@angular/forms';
import { MatPaginator } from '@angular/material';
import { MatTableDataSource } from '@angular/material/table';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { InfoComponent } from '../_dialogs/info/info.component';
import { MatSort } from '@angular/material/sort';
import { NotifiDialogComponent } from '../_dialogs/notifi-dialog/notifi-dialog.component';
import { NotificationService } from '../_services/notification.service';
import { VendorsService } from '../_services';
import { Vendor } from '../_models';
import { ReplaySubject, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { UtilsService } from '../_utils/utils.service';
import { TemplateRef } from '@angular/core';
import { MatOption } from '@angular/material';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material';
import { MomentDateAdapter } from '@angular/material-moment-adapter';
import { GlobalsService } from '../_services/globals.service';
import { E } from '@angular/cdk/keycodes';
import { MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import * as moment from 'moment';
import { Options } from '@angular-slider/ngx-slider';
export const MY_FORMATS = {
  parse: {
    dateInput: 'LL'
  },
  display: {
    dateInput: 'DD-MM-YYYY',
    monthYearLabel: 'YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'YYYY'
  }
};

@Component({
  selector: 'app-forecast-report',
  templateUrl: './forecast-report.component.html',
  styleUrls: ['./forecast-report.component.scss', "./../../common-dark.scss"],
  providers: [{ provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS] },
  { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }
  ],
})

export class ForecastReportComponent implements OnInit {
  showCategoryDropdown = false
  showSubCategoryDropdown = false


  sliderValue = 1;
  searchReportText: any;
  searchTextCat: any;
  searchTextSubCat: any;
  displayedColumns: any;
  displayedColumns_1: any;
  dataSource = new MatTableDataSource();
  dataSource1 = new MatTableDataSource();
  combinedDataSource = new MatTableDataSource();
  currentUser: User;
  categoryList: any[];
  subCategoryList: any[];
  filteredCategoryList: any;
  filteredSubCategoryList: any;
  requiredColumnsData: any;
  categoryForm = new FormControl();
  SubcategoryForm = new FormControl();
  filterBranches = new FormControl();
  startDate = new FormControl();
  endDate = new FormControl();
  workAreaForm = new FormControl();
  vendorForm = new FormControl();
  brand = new FormControl();
  baseDate = new FormControl();
  baseDateForConsolidation = new FormControl();
  yearForm = new FormControl()
  monthForm = new FormControl()
  newSelect = new FormControl()
  requiredColumns = new FormControl()
  requiredGrnColumns = new FormControl()
  requiredIndentColumns = new FormControl()
  requiredInvColumns = new FormControl()
  requiredSubRecipeColumns = new FormControl()
  priceTier = new FormControl()
  storeStockValue = new FormControl()
  workAreaStockValue = new FormControl()
  isDataAvailable: any;
  reportList: any
  selectedReportType: string = '';
  selectedSubCategories: any = [];
  selectedWorkAreas: any = [];
  selectedRest: any = [];
  filteredReportList: any;
  reportListArr: any;
  allowEdit: boolean;
  @ViewChild('digiTable') digiTable: DigiTableComponent;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  msgToDisplay: null;
  public element: any[] = [];
  selectedValue: any;
  sub: any;
  indentAreas: string[] = [];
  indentWorkArea: any[] = [];
  workArea: any;
  public reqColumns: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public reqColumnsData: any[] = [];
  public reqColumnsFilterCtrl: FormControl = new FormControl();

  public reqColumnsGrn: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public reqColumnsGrnData: any[] = [];
  public reqColumnsGrnFilterCtrl: FormControl = new FormControl();

  public reqColumnsIndent: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public reqColumnsIndentData: any[] = [];
  public reqColumnsIndentFilterCtrl: FormControl = new FormControl();

  public reqColumnsInv: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public reqColumnsInvData: any[] = [];
  public reqColumnsInvFilterCtrl: FormControl = new FormControl();

  public reqColumnsSubRecipe: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public reqColumnsSubRecipeData: any[] = [];
  public reqColumnsSubRecipeFilterCtrl: FormControl = new FormControl();

  public priceTierNames: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public priceTierList: any[] = [];
  public priceTierFilterCtrl: FormControl = new FormControl();

  public vendorsBanks: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public vendorFilterCtrl: FormControl = new FormControl();
  vendors: Vendor[];
  vendorId: string;
  public VendorBank: any[] = [];
  protected _onDestroy = new Subject<void>();
  interval: any;
  items: any;
  pageSizes: any[];
  pageSizes1: any[];
  openfilters: boolean = false;
  isDirectValueOfBranch: boolean = false;
  @ViewChild('openFilterDialog') openFilterDialog: TemplateRef<any>;
  reportNumber: any;
  getDatas: any[];
  branches: any[];
  reportForm: FormGroup;
  nonArrayCat: any;
  vendorList = [];
  vendorIdData = [];
  selectedVendorArr: any[];
  filterVendorList: any;
  getBranchData: any[];
  @ViewChild('allSelected') private allSelected: MatOption;
  @ViewChild('allSelectedGrn') private allSelectedGrn: MatOption;
  @ViewChild('allSelectedIndent') private allSelectedIndent: MatOption;
  @ViewChild('allSelectedSubRecipe') private allSelectedSubRecipe: MatOption;
  @ViewChild('allSelectedInv') private allSelectedInv: MatOption;
  @ViewChild('subCatallSelected') private subCatallSelected: MatOption;
  @ViewChild('workAreaallSelected') private workAreaallSelected: MatOption;
  @ViewChild('vendorallSelected') private vendorallSelected: MatOption;
  @ViewChild('branchAllSelected') private branchAllSelected: MatOption;
  @ViewChild('allSelectedBrandBranch') private allSelectedBrandBranch: MatOption;
  forEnableReqReport: boolean = true;
  private unsubscribe$ = new Subject<void>();
  singleSelectBranch: boolean = false;
  singleBranchData: boolean = false;
  defaultSelectionBranches: any;
  branchFilterSingle: any[];
  branchFilter: any[];
  tempBranch: any[];
  selectedBranchesRestaurantIds: any[];
  brands: any;
  brandKeys: any[];
  tempBrands: any;
  branchFilterBrands: any;
  brandBranchNames: any[];
  selectedBrandBranchesRestaurantIds: any[];
  disableButton: boolean;
  workAreaKeyForAllSelect: boolean = false;
  months = [
    { 'monthName': 'January', 'number': 1 },
    { 'monthName': 'February', 'number': 2 },
    { 'monthName': 'March', 'number': 3 },
    { 'monthName': 'April', 'number': 4 },
    { 'monthName': 'May', 'number': 5 },
    { 'monthName': 'June', 'number': 6 },
    { 'monthName': 'July', 'number': 7 },
    { 'monthName': 'August', 'number': 8 },
    { 'monthName': 'September', 'number': 9 },
    { 'monthName': 'October', 'number': 10 },
    { 'monthName': 'November', 'number': 11 },
    { 'monthName': 'December', 'number': 12 }
  ]
  years: number[] = [];
  baseDateFilter: object = {};
  selectedMonth: Date;
  checked: boolean;
  disabledCheck: boolean = false;
  currentMonth: number;

  disabled = false;
  ShowFilter = false;
  limitSelection = false;
  cities = [];
  BrandAllOption: any;
  dropdownList = [];
  selectedItems = [];
  singleDropdownSettings = {};
  multiBrandBranchDropdownSettings = {};
  singleLocationDropdownSettings = {};
  multiLocationDropdownSettings = {};
  multiCatDropdownSettings = {};
  multiSubCatDropdownSettings = {};
  multiIndentWorkAreaDropdownSettings = {};
  multiVendorDropdownSettings = {};
  multiRequiredDropdownSettings = {};
  baseDateValue: any[] = [];
  baseDateValueForIndent: any[] = [];
  isSourceChecked: boolean = false;
  isDestinationChecked: boolean = true;
  showStock: boolean = true;
  isChecked: boolean = false;
  dialogLoader: boolean = false;
  tempCat: any;
  tempvendor: { id: number; itemName: any; }[];
  tempBrandKeys: any;
  temSubCat: any;
  tempBranchNames: { id: number; itemName: any; }[];
  tempIndentWorkArea: { id: number; itemName: any; }[];
  tempReq: any;
  mandatoryData: any[];
  tempReqData: any;
  tempReqGrnData: any;
  tempReqIndentData: any;
  requiredColumnsGrnData: any;
  requiredColumnsIndentData: any;
  draftReqColumns: any;
  tempReqSubRecipeData: any;
  tempReqInvData: any;
  requiredColumnsInvData: any;

  minStoreValue = 0;
  maxStoreValue = 0;
  minWorkAreaValue = 0;
  maxWorkAreaValue = 0;

  rangeValues: number[] = [300, 400];
  value: number = 40;
  highValue: number = 600;
  options: Options = {
    floor: 0,
    ceil: 5000,
    step :1,
    // showTicks:true,
    // noSwitching:true
  //  translate: (value: number): string => {
  //   return '';
  // }
  };
  maxDate: Date = new Date();
  constructor(
    private auth: AuthService,
    private menuItems: MenuItemService,
    public dialog: MatDialog,
    private notifyService: NotificationService,
    private vendorService: VendorsService,
    private utils: UtilsService,
    private fb: FormBuilder,
    private sharedData: ShareDataService,
    private cd: ChangeDetectorRef,
    private ngZone: NgZone
  ) {
    this.currentUser = this.auth.getCurrentUser();
    this.allowEdit = false;
    this.displayedColumns = ['reportName', 'type', 'createdAt', 'info', 'action'];
    this.getReportDraft();
    this.reportForm = this.fb.group({
      reportselection: [null, Validators.required],
      branchSelection: [null, Validators.required]
    });

    // this.branchFilterSingle = this.currentUser.restaurantAccess;
    // this.branchFilterSingle = this.branchFilterSingle.map((item, index) => ({
    //   index: index + 1,
    //   restaurantIdOld: item.restaurantIdOld,
    //   branchLocation: item.branchLocation,
    //   branchName: item.branchName,
    //   itemName: item.branchName,
    //   workAreas: item.workAreas
    // }));
    this.branchFilter = this.currentUser.restaurantAccess.map(item => item.branchName);
    this.branchFilter = this.branchFilter.map((itemName, index) => ({ id: index + 1, itemName }));
    this.tempBranch = this.currentUser.restaurantAccess.map(item => item.branchName);
    this.sharedData.sharedBranchData.pipe(takeUntil(this.unsubscribe$)).subscribe((val) => {
      this.getBranchData = val;
      if (this.getBranchData.length == 0) {
        this.branches = this.currentUser.restaurantAccess;
      } else if (this.getBranchData.length == 1) {
        const toSelect = this.getBranchData.find(data => data.branchName == this.getBranchData[0].branchName);      
        this.defaultSelectionBranches = toSelect;
        this.reportForm.get('branchSelection').setValue(toSelect);
        this.branches = this.getBranchData
        this.isDirectValueOfBranch = true
        this.fetchReportList();
      } else {
        this.branches = this.getBranchData
        this.defaultSelectionBranches = this.branches;
      }
    });
    this.startDate = new FormControl('', [Validators.required]);
    this.endDate = new FormControl('', [Validators.required]);
    this.categoryForm = new FormControl('', [Validators.required]);
    this.SubcategoryForm = new FormControl('', [Validators.required]);
    this.workAreaForm = new FormControl('', [Validators.required]);
    this.vendorForm = new FormControl('', [Validators.required]);
    this.workAreaStockValue.setValue(true)
    
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource1.paginator = this.paginator;
  }

  metaData(reportData) {
    let obj = {}
    obj['tenantId'] = this.currentUser.tenantId;
    obj['reportName'] = reportData.reportName;
    this.menuItems.getFileFormat(obj).subscribe(res => {
      if (res['result'] == 'success') {
        let fileFormat = res['data']
        let obj = {
          "createdAt": reportData.createdAt,
          "name": reportData.reportId + "." + fileFormat,
          "type": reportData.reportName
        }
        const dialogRef = this.dialog.open(InfoComponent, {
          width: '400px',
          data: {
            reportData: obj
          }
        });
        dialogRef.afterClosed().subscribe(result => {
        });
      }
    });
  }

  ngOnInit() {
    this.getPOSPriceTires();
    const currentYear = new Date().getFullYear();
    const startYear = 2021;
    for (let year = startYear; year <= currentYear; year++) {
      this.years.push(year);
    }
    this.singleDropdownSettings = {
      // singleSelection: true, 
      // text:"Select Countries",
      // selectAllText:'Select All',
      // unSelectAllText:'UnSelect All',
      // enableSearchFilter: true,
      singleSelection: true,
      text: 'Select Brand',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      searchPlaceholderText: 'Search Brand',
      enableSearchFilter: true,
      badgeShowLimit: 5,
      classes: "myclass custom-class dark-theme"
    };

    this.multiBrandBranchDropdownSettings = {
      singleSelection: false,
      text: 'Select Location',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      searchPlaceholderText: 'Search Location',
      enableSearchFilter: true,
      badgeShowLimit: 5,
      classes: "myclass custom-class dark-theme"
    };

    this.multiLocationDropdownSettings = {
      singleSelection: false,
      text: 'Select Location',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      searchPlaceholderText: 'Search Location',
      enableSearchFilter: true,
      badgeShowLimit: 5,
      classes: "myclass custom-class dark-theme"
    };

    this.multiCatDropdownSettings = {
      singleSelection: false,
      text: 'Select Category',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      searchPlaceholderText: 'Search Category',
      enableSearchFilter: true,
      badgeShowLimit: 5,
      classes: "myclass custom-class dark-theme"
    };

    this.multiSubCatDropdownSettings = {
      singleSelection: false,
      text: 'Select subCategory',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      searchPlaceholderText: 'Search subCategory',
      enableSearchFilter: true,
      badgeShowLimit: 5,
      classes: "myclass custom-class dark-theme"
    };

    this.multiIndentWorkAreaDropdownSettings = {
      singleSelection: false,
      text: 'Select WorkArea',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      searchPlaceholderText: 'Search WorkArea',
      enableSearchFilter: true,
      badgeShowLimit: 5,
      classes: "myclass custom-class dark-theme"
    };

    this.multiVendorDropdownSettings = {
      singleSelection: false,
      text: 'Select vendor',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      searchPlaceholderText: 'Search vendor',
      enableSearchFilter: true,
      badgeShowLimit: 5,
      classes: "myclass custom-class dark-theme"
    };

    this.singleLocationDropdownSettings = {
      // singleSelection: true,
      // text: 'Select Location',
      // selectAllText: 'Select All',
      // unSelectAllText: 'UnSelect All',
      // searchPlaceholderText: 'Search Location',
      // enableSearchFilter: true,
      // badgeShowLimit: 5,
      // classes: "myclass custom-class dark-theme"
      singleSelection: true,
      text: 'Select Location',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      searchPlaceholderText: 'Search Location',
      enableSearchFilter: true,
      badgeShowLimit: 5,
      classes: "myclass custom-class dark-theme"
    };

    this.multiRequiredDropdownSettings = {
      singleSelection: false,
      text: 'Select Required Columns',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      searchPlaceholderText: 'Search Required Columns',
      enableSearchFilter: true,
      badgeShowLimit: 5,
      classes: "myclass custom-class dark-theme"
    };

  }

  getVendors() {
    this.vendorList = []
    let obj = {
      "tenantId" : this.currentUser.tenantId,
      "report" : true
    }
    this.vendorService.getVendors(obj).subscribe((data: Vendor[]) => {
      this.vendors = data;
      this.vendors.forEach(element => {
        this.vendorId = element.tenantId
      });
      this.VendorBank = this.vendors
      this.vendors.forEach(element => {
        this.vendorList.push(element.name);
      });
      this.tempvendor = this.vendorList.map((itemName, index) => ({ id: index + 1, itemName }));
      this.vendorList = this.vendorList.map((itemName, index) => ({ id: index + 1, itemName }));
      this.vendorForm.setValue(this.tempvendor)
      this.vendorsBanks.next(this.VendorBank.slice());
      this.vendorFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
        this.vendorfilterBanks();
      });
    })
  }

  protected vendorfilterBanks() {
    if (!this.VendorBank) {
      return;
    }
    let search = this.vendorFilterCtrl.value;
    if (!search) {
      this.vendorsBanks.next(this.VendorBank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.vendorsBanks.next(
      this.VendorBank.filter(VendorBank => VendorBank.name.toLowerCase().indexOf(search) > -1)
    );
  }

  protected filterData(bank:any, form:any, data:any) {
    if (!bank) {
      return;
    }
    let search = form.value;
    if (!search) {
      data.next(bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    data.next(
      bank.filter(data => data.name.toLowerCase().indexOf(search) > -1)
    );
  }
  

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource1.filter = filterValue.trim().toLowerCase();
    if (this.dataSource1.paginator) {
      this.dataSource1.paginator.firstPage();
    }
  }

  reportStatus() {
    let obj = {
      'tenantId': this.currentUser.tenantId,
      'email': this.currentUser.email,
      'type': this.selectedReportType
    }
    this.menuItems.reportStatus(obj).subscribe(res => {
      if (res.result == 'success') {
        this.dataSource1.data = res['data']
        this.getDatas = this.dataSource1.data
        this.pageSizes1 = this.utils.getPageSizes(this.dataSource1.data)
        let value = this.reportNumber
        let searchObject = this.getDatas.find((datas) => datas['reportId'] == value);
        this.combinedDataSource.data.push(this.dataSource1.data)
      }
    }, err => {
      console.log(err)
    });
  }

  fetchReportList() {
    let obj = {}
    obj['tenantId'] = this.currentUser.tenantId
    obj['role'] = this.currentUser.role
    this.menuItems.fetchReportList(obj).subscribe(res => {
      if (res.result) {
        this.reportList = res['reportList'].filter((el) => el['access'].includes(this.currentUser.role))
        this.baseDateFilter = res['dateFilter']
        if (res['brands']) {
          this.brands = res['brands']
          this.tempBrands = Object.keys(this.brands);
          this.filterBranches.disable();
        }
        this.reportListArr = this.reportList
        this.filteredReportList = this.reportListArr
        const toSelect = this.filteredReportList && this.filteredReportList.length > 0 ? this.filteredReportList[0] : undefined;
        this.reportForm.get('reportselection').setValue(toSelect);
        toSelect ? this.filterJobsData(toSelect) : null;
        toSelect ? this.processSelectedReport(toSelect,true) : null;
        this.filteredReportList.length == 0 ? this.utils.snackBarShowError('Reports not available for selected user') : null;
      }
      else {
        this.utils.snackBarShowWarning(res.desc);
      }
    },
      err => {
        console.log(err)
      });
  }

  refreshData() {
    this.filterJobsData(this.selectedValue)
  }

  filterJobsData(val) {
    let obj = {}
    obj['tenantId'] = this.currentUser.tenantId
    obj['selectedReport'] = val.backendName
    obj['userEmail'] = this.currentUser.email
    this.menuItems.filterJobsData(obj).subscribe(res => {
      if (res.result == 'success') {
        this.displayedColumns_1 = ["reportName", "type", "createdAt", "status", "info", "action"]
        this.dataSource1.data = res['data']
        this.getDatas = this.dataSource1.data
        this.pageSizes1 = this.utils.getPageSizes(this.dataSource1.data)
      }
    }, err => {
      console.log(err)
    });
  }

  processSelectedReport(val,defaultAction = false) {
    this.baseDate.disable();
    this.baseDateForConsolidation.disable();
    this.categoryForm.disable();
    this.categoryForm.disable();
    this.SubcategoryForm.disable();
    this.startDate.disable();
    this.endDate.disable();
    this.workAreaForm.disable();
    this.vendorForm.disable();
    this.yearForm.disable();
    this.monthForm.disable();
    this.searchTextCat = '';
    this.searchTextSubCat = '';
    this.combinedDataSource.data = []
    this.startDate.setValue(null)
    this.endDate.setValue(null)
    this.selectedReportType = val['backendName']
    this.msgToDisplay = val['description']
    this.items = val['filters']

    if (val.multiBranch == false || !val.multiBranch) {
      this.singleSelectBranch = true;
      this.filterBranches.setValue('')
    } else {
      this.singleSelectBranch = false;
    }
    
    if (this.items && this.items.workArea == true) {
      this.workAreaForm.enable();
    }

    if (this.items && this.items.category == true) {
      this.categoryForm.enable(); 
      // defaultAction ? this.loadCategorySubCategory() : this.categoryForm.setValue(this.tempCat);
      this.loadCategorySubCategory();
    } else {
      this.categoryForm.setValue([''])
    }

    if (this.items && this.items.subCategory == true) {
      this.SubcategoryForm.enable();
    }

    if (this.items && this.items.hasOwnProperty('baseDate') && this.items.baseDate == true) {
      this.baseDate.enable();
      if (val.backendName === 'consolidated_purchase_indent' || val.backendName === 'consolidated_purchase_indent_wac') {
        this.baseDateForConsolidation.enable();
      } else {
        this.baseDateForConsolidation.disable();
        this.baseDateValueForIndent = [];
      }
    } else {
      this.baseDate.disable();
      this.baseDateForConsolidation.disable();
    }
    if (this.items && this.items.vendor == true) {
      this.vendorForm.enable();
      // this.vendorList.length === 0 ?  this.getVendors(): this.vendorForm.setValue(this.tempvendor) ;
      this.getVendors();
    }

    if (this.items && this.items.startDate == true) {
      this.startDate.enable();
    }

    if (this.items && this.items.endDate == true) {
      this.endDate.enable();
    }

    if (this.items && this.items.year == true) {
      this.yearForm.enable();
    } else {
      this.yearForm.setValue('');
    }

    if (this.items && this.items.month == true) {
      this.monthForm.enable();
    } else {
      this.monthForm.setValue('');
    }

    if (this.items && this.items.tax == true) {
      this.disabledCheck = false;
      this.checked = true;
    } else {
      this.disabledCheck = true
      this.checked = false;
    }
    this.workArea = [];
    this.workAreaKeyForAllSelect = false;
    this.BrandAllOption = val.all
  }

  loadCategorySubCategory() {
    let obj = this.currentUser
    this.currentUser['categoryType'] =["inventory","menu", "subRecipe"]
    this.menuItems.getCategories(obj).subscribe(res => {
      if (res['result'] == 'success') {
        this.nonArrayCat = res['categories'];
        this.categoryList = res['categories'];
        this.filteredCategoryList = [...new Set(this.categoryList)];
        this.tempCat = this.filteredCategoryList.map((itemName, index) => ({ id: index + 1, itemName }));
        this.filteredCategoryList = this.filteredCategoryList.map((itemName, index) => ({ id: index + 1, itemName }));        
        this.categoryForm.setValue(this.tempCat)
        this.getSubCategory();
      };
    }, err => {
      console.log(
        err
      )
    });
  }

  filterReport(searchReportText) {
    this.filteredReportList = this.reportListArr.filter(
      (rep) =>
        rep['displayName'].toLowerCase().includes(searchReportText.toLowerCase())
    );
  }

  selectRestaurants(val) {
    this.fetchReportList();
  }

  searchFilterBranch(txt) {
    if (txt != '') {
      this.branchFilter = this.tempBranch.filter((brn) => brn.toLowerCase().includes(txt.toLowerCase()));
    } else {
      this.branchFilter = this.tempBranch;
    }
  }

  searchFilterBrand(txt) {
    if (txt != '') {
      this.brandKeys = this.tempBrands.filter((brn) => brn.toLowerCase().includes(txt.toLowerCase()));
    } else {
      this.brandKeys = this.tempBrands;
    }
    this.brandKeys = this.brandKeys.map((itemName, index) => ({ id: index + 1, itemName }));
  }

  searchFilterCat(txt) {
    this.filteredCategoryList = this.categoryList.filter(
      (cat) =>
        cat.toLowerCase().includes(txt.toLowerCase())
    );
  }

  searchFilterVendor(txt) {
    this.filterVendorList = this.vendorList.filter(
      (vendor) =>
        vendor.toLowerCase().includes(txt.toLowerCase())
    );
  }

  searchFilterSubCat(txt) {
    this.filteredSubCategoryList = this.subCategoryList.filter(
      (subCat) =>
        subCat.toLowerCase().includes(txt.toLowerCase())
    );
  }

  searchFilterWorkArea(txt) {
    this.indentWorkArea = this.indentAreas.filter(
      (area) =>
        area.toLowerCase().includes(txt.toLowerCase())
    );
  }

  selectSubCat(event) {
    this.selectedSubCategories = event.value
  }

  selectIndentArea(val) {
    this.workArea = val;
  }

  isReportSelected(): boolean {
    const displayName = this.reportForm.value.reportselection.displayName;
    return displayName === 'Live Stock Store& Workarea' || displayName === 'Manual Closing Report' || displayName === 'System Closing Report';
  }

  reqReport() {
    let obj = {}
    obj = this.currentUser;
    obj['type'] = this.selectedReportType;
    obj['requestDate'] = new Date().toLocaleString();
    if (this.brands) {
      obj['selectedRestaurants'] = this.selectedBrandBranchesRestaurantIds;
    } else {
      if (this.singleBranchData) {
        let data = this.filterBranches.value.map(item => item.itemName);
        let selectedRestaurantId = []
        for (const location of data) {
          const restaurant = this.currentUser.restaurantAccess.find(item => item.branchName === location);
          if (restaurant) {
            selectedRestaurantId.push(restaurant.restaurantIdOld);
          }
        }
        obj['selectedRestaurants'] = selectedRestaurantId;
      } else {
        obj['selectedRestaurants'] = this.selectedBranchesRestaurantIds;
      }
    }
    if (this.forEnableReqReport == true && this.items.startDate == true) {
      if (this.startDate.value != '') {
        obj['startDate'] = this.utils.dateCorrection(this.startDate.value);
      } else {
        this.forEnableReqReport = false
        this.utils.snackBarShowWarning("Please fill all required filters");
      }
    } else {
      obj['startDate'] = undefined;
    }

    if (this.forEnableReqReport == true && this.items.endDate == true) {
      if (this.endDate.value != '') {
        obj['endDate'] = this.utils.dateCorrection(this.endDate.value);
      } else {
        this.forEnableReqReport = false
        this.utils.snackBarShowWarning("Please fill all required filters");
      }
    } else {
      obj['endDate'] = undefined
    }

    if (this.forEnableReqReport == true && this.items.category == true) {
      if (this.categoryForm.value == '') {
        this.forEnableReqReport = false
        this.utils.snackBarShowWarning("Please fill all required filters");
      } else {
        let data = this.categoryForm.value.map(item => item.itemName);
        let array = data
        let elementToRemove = 1;
        let index = array.indexOf(elementToRemove);
        if (index > -1) {
          array.splice(index, 1);
        }
        obj['categories'] = data
        this.categoryForm.setValue([''])
      }
    } else {
      obj['categories'] = [];
    }

    if (this.forEnableReqReport == true && this.items.subCategory == true) {
      if (this.SubcategoryForm.value == '') {
        this.forEnableReqReport = false
        this.utils.snackBarShowWarning("Please fill all required filters");
      } else {
        let data = this.SubcategoryForm.value.map(item => item.itemName)
        let array = data
        let elementToRemove = 1;
        let index = array.indexOf(elementToRemove);
        if (index > -1) {
          array.splice(index, 1);
        }
        obj['subCategories'] = data;
        this.SubcategoryForm.setValue([''])
        this.selectedSubCategories = ''
      }
    } else {
      obj['subCategories'] = []
    }

    if (!this.showCategoryDropdown){
      obj['categories'] = ["ALL"];
      obj['subCategories'] = ["ALL"]
    }

    if (this.forEnableReqReport == true && this.items.workArea == true) {

      // if (this.workAreaForm.value == '') {
      //   this.forEnableReqReport = false
      //   this.utils.snackBarShowWarning("Please fill all required filters");
      // } else {
        if(this.workAreaForm.value){
          let array = this.workAreaForm.value
          let data = array.map(item => item.itemName)
          let elementToRemove = 1;
          let index = data.indexOf(elementToRemove);
          if (index > -1) {
            data.splice(index, 1);
          }
          obj['selectedWorkAreas'] = data;
          this.workAreaForm.setValue([''])
        }else{
          obj['selectedWorkAreas'] = []
        }
      // }
    } else {
      obj['selectedWorkAreas'] = []
    }

    if (this.forEnableReqReport == true) {
      if (this.items.year == true) {
        if (this.yearForm.value != '') {
          obj['startYear'] = this.yearForm.value
        } else {
          this.forEnableReqReport = false
          this.utils.snackBarShowWarning("Please fill all required filters");
        }
      } else {
        obj['startYear'] = null;
      }
    } else {
      obj['startYear'] = undefined;
    }

    if (this.forEnableReqReport == true) {
      if (this.items.year == true) {
        if (this.monthForm.value != '') {
          obj['startMonth'] = this.monthForm.value
        } else {
          this.forEnableReqReport = false
          this.utils.snackBarShowWarning("Please fill all required filters");
        }
      } else {
        obj['startMonth'] = null;
      }
    } else {
      obj['startMonth'] = undefined;
    }

    if (this.forEnableReqReport == true) {
      if (this.items.tax == true) {
        if (this.checked) {
          obj['tax'] = this.checked
        } else {
          obj['tax'] = false;
          this.forEnableReqReport = false
          this.utils.snackBarShowWarning("Please fill all required filters");
        }
      } else {
        obj['tax'] = null;
      }
    } else {
      obj['tax'] = undefined;
    }

    if (this.forEnableReqReport == true && this.items.vendor == true) {

      if (this.vendorForm.value == '') {
        this.forEnableReqReport = false
        this.utils.snackBarShowWarning("Please fill all required filters");;
      } else {
        let data = this.vendorForm.value.map(item => item.itemName)
        let evens = this.vendors.filter(num => data.includes(num.name));
        let vendorIdArr = []
        evens.forEach(function (element) {
          vendorIdArr.push(element.tenantId)
        });
        this.selectedVendorArr = vendorIdArr
        let array = this.selectedVendorArr
        let elementToRemove = 1;
        let index = array.indexOf(elementToRemove);
        if (index > -1) {
          array.splice(index, 1);
        }
        obj['selectedVendor'] = this.selectedVendorArr
        this.forEnableReqReport = true
        this.vendorIdData = [];
        this.selectedVendorArr = []
        this.vendorForm.setValue([''])
      }
    } else {
      obj['selectedVendor'] = []
    }
    if (this.reportForm.value.reportselection.backendName === 'ibtReport') {
      obj['isSource'] = this.isChecked
    }
    const reportName = this.reportForm.value.reportselection.backendName;
    if (reportName === 'inventoryStatus' || reportName === 'closingReport' || reportName === 'systemClosingReport') {
      obj['showZeroStock'] = this.showStock;
    }
    if(this.reportForm.value.reportselection.backendName === 'consolidated_purchase_indent' || this.reportForm.value.reportselection.backendName === 'consolidated_purchase_indent_wac'){
      let grnData = this.requiredGrnColumns.value ? this.requiredGrnColumns.value.map(item => item.name) : []
      let indentData = this.requiredIndentColumns.value ? this.requiredIndentColumns.value.map(item => item.name) : []
      obj['requiredGrnColumns'] = grnData.filter(item => item !== undefined && item !== null && item.trim() !== "");
      obj['requiredIndentColumns'] = indentData.filter(item => item !== undefined && item !== null && item.trim() !== "");
      obj['requiredColumns'] = []
      obj['requiredInvColumns'] = []
      obj['requiredSubRecipeColumns'] = []
    }else if(this.reportForm.value.reportselection.backendName === 'inventoryConsumptionNew'){
      let invData = this.requiredInvColumns.value ? this.requiredInvColumns.value.map(item => item.name) : []
      let subRecipeData = this.requiredSubRecipeColumns.value ? this.requiredSubRecipeColumns.value.map(item => item.name) : []
      obj['requiredInvColumns'] = invData.filter(item => item !== undefined && item !== null && item.trim() !== "");
      obj['requiredSubRecipeColumns'] = subRecipeData.filter(item => item !== undefined && item !== null && item.trim() !== "");
      obj['requiredColumns'] = []
      obj['requiredGrnColumns'] = []
      obj['requiredIndentColumns'] = []
    }else{
      let data = this.requiredColumns.value ? this.requiredColumns.value.map(item => item.name) : []
      obj['requiredColumns'] = data.filter(item => item !== undefined && item !== null && item.trim() !== "");
      obj['requiredGrnColumns'] = []
      obj['requiredIndentColumns'] = []
      obj['requiredInvColumns'] = []
      obj['requiredSubRecipeColumns'] = []
    }

    if (this.reportForm.value.reportselection.backendName === 'profitMarginReport') {
      obj['priceTier'] = {
        name: this.priceTier.value.name,
        id: this.priceTier.value.id,
      };
    } else {
      obj['priceTier'] = {};
    }

    let storeObj ={
      'minStoreValue' : this.minStoreValue,
      'maxStoreValue' : this.maxStoreValue
    }

    let workAreaObj ={
      'minWorkAreaValue' : this.minWorkAreaValue,
      'maxWorkAreaValue' : this.maxWorkAreaValue
    }

    obj['storeStockValue'] = this.storeStockValue.value ? storeObj : {};
    obj['workAreaStockValue'] = this.workAreaStockValue.value ? workAreaObj : {};

    obj['selectedRestaurants'].length === 0 ? (this.forEnableReqReport = false,this.utils.snackBarShowWarning("Please Select Required Branches")) : undefined ;
    if (this.forEnableReqReport == true) {
      !this.baseDate.disabled ? obj['selectedBaseDate'] = this.baseDate.value : undefined;
      !this.baseDateForConsolidation.disabled ? obj['selectedBaseDateForIndent'] = this.baseDateForConsolidation.value : undefined;
      this.menuItems.requestReport(obj).subscribe(res => {
        if (res['result'] == 'success') {
          this.reportNumber = res['reportNo']
          this.addReportDraftData();
          const dialogRef = this.dialog.open(NotifiDialogComponent, {
            width: '350px',
            height: '325px',
            data: {
              reportNo: res['reportNo'], message: res['message']
            }
          });
          dialogRef.afterClosed().subscribe(_result => {
          });
        }
        else {
          this.dialog.open(NotifiDialogComponent, {
            width: '350px',
            data: {
              message: 'An error must have occurred. Please try again.',
              reportNo: res['reportNo']
            }
          });
        };
      }, err => {
        console.log(err)
      });
    }
    this.forEnableReqReport = true;
    this.processSelectedReport(this.selectedValue)
    this.workAreaKeyForAllSelect = true;
  }

  retrieveReportList() {
    let obj = this.currentUser;
    obj['type'] = this.selectedReportType;
    this.displayedColumns = ['reportName', 'type', 'createdAt', 'status', 'info', 'action'];
    this.reportStatus();
    this.menuItems.retrieveReportList(obj).subscribe(res => {
      if (res['result'] == 'success') {
        this.isDataAvailable = res['reportList'];
        this.dataSource.data = res['reportList'];
        this.combinedDataSource.data.push(this.dataSource.data)
        this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
      }
      else {
        this.isDataAvailable = [];
        this.dataSource.data = [];
      }
    }, err => {
      console.log(err)
    });
    this.reportNumber = ''
  }

  retrieveReport(this, reportName, currentUser) {
    let obj = currentUser;
    obj['type'] = reportName;
    this.menuItems.retrieveReport(obj).subscribe(res => {
      if (res['result'] == 'success') {
        var downloadLink = document.createElement("a");
        downloadLink.href = 'data:application/vnd.ms-excel;base64,' + res.eFile;
        let parts = reportName.split('_');
        parts[1] = this.reportForm.value.reportselection.displayName;
        let newFilename = parts.join('_');
        downloadLink.download = newFilename;
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
      }
      else {
        this.utils.snackBarShowWarning('File not available for logged in user');
      }
    }, err => {
      console.log(
        err
      )
    });
  }

  download(inputs) {
    let obj = {}
    obj['tenantId'] = this.currentUser.tenantId;
    obj['reportName'] = inputs.reportName;
    this.menuItems.getFileFormat(obj).subscribe(res => {
      if (res['result'] == 'success') {
        let fileFormat = res['data']
        let currentUser = this.currentUser;
        const date = new Date(inputs.createdAt);
        const day = String(date.getUTCDate()).padStart(2, '0');
        const month = String(date.getUTCMonth() + 1).padStart(2, '0');
        const year = date.getUTCFullYear();
        let hours = date.getUTCHours();
        const minutes = String(date.getUTCMinutes()).padStart(2, '0');
        const ampm = hours >= 12 ? 'PM' : 'AM';
        hours = hours % 12 || 12;
        const formattedHours = String(hours).padStart(2, '0');
        const formattedCreatedAt = `${day}-${month}-${year}|${formattedHours}:${minutes}_${ampm}`;
        var fullName = `${inputs.reportId}_${inputs.reportName}_${formattedCreatedAt}.${fileFormat}`
        this.retrieveReport(fullName, currentUser);
      }
    });
  }

  retrieveNotification() {
    let obj = this.currentUser;
    this.menuItems.retrieveNotification(obj).subscribe(res => {
      if (res['result'] == 'success') {
        this.menuItems.notificationDta.next(res.data);
      }
    }, err => {
      console.log(
        err
      )
    });
  }

  ngOnDestroy() {
    if (this.sub) {
      this.sub.unsubscribe();
    }
    clearInterval(this.interval);
    this._onDestroy.next();
    this._onDestroy.complete();
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  delete(inputs) {
    let obj = {}
    obj['tenantId'] = this.currentUser.tenantId;
    obj['reportName'] = inputs.reportName;
    this.menuItems.getFileFormat(obj).subscribe(res => {
      if (res['result'] == 'success') {
        let fileFormat = res['data']
        let currentUser = this.currentUser;
        var fullName = `${inputs.reportId}_${inputs.reportName}.${fileFormat}`
        this.deleteReport(fullName, currentUser);
      }
    });
  }

  deleteReport(this, reportName, currentUser) {
    let obj = currentUser;
    obj['reportName'] = reportName;
    this.menuItems.deleteReport(obj).subscribe(res => {
      if (res['result'] == 'success') {
        this.retrieveReportList();
      }
      else {
        this.utils.snackBarShowError('File not available for logged in user');
      }
    }, err => {
      console.log(
        err
      )
    });
  }

  openFilter() {
    this.dialogLoader = true;
    this.showStock = true;
    const dialogRefTemplate = this.dialog.open(this.openFilterDialog, {
      maxWidth: '60vw',
    });
    dialogRefTemplate.afterClosed().subscribe(result => {
      this.workAreaKeyForAllSelect = true;
    })

    dialogRefTemplate.afterOpened().subscribe(() => {
      this.dialogLoader = false;
      this.loadData();
    });
  }

  loadData(){
    // this.requiredGrnColumns.setValue([])
    // this.requiredIndentColumns.setValue([])
    // this.requiredColumns.setValue([])
    if (this.baseDateValue.length > 0) {
      this.baseDate.setValue(this.baseDateValue[0].value);
    }
    if (this.baseDateValueForIndent.length > 0) {
      this.baseDateForConsolidation.setValue(this.baseDateValueForIndent[0].value);
    }
    this.startDate.setValue(new Date());
    this.endDate.setValue(new Date());
    this.currentUser['brands'] = [...new Set(this.currentUser['brands'])];
    this.tempBrandKeys = this.currentUser['brands'].map((itemName, index) => ({ id: index + 1, itemName }));
    this.brandKeys = this.currentUser['brands'].map((itemName, index) => ({ id: index + 1, itemName }));
    this.brand.setValue(this.tempBrandKeys[0])
    this.filterBranches.setValue('')

    if(this.reportForm.value.reportselection.backendName === 'consolidated_purchase_indent' || this.reportForm.value.reportselection.backendName === 'consolidated_purchase_indent_wac'){
      let grnData 
      grnData = this.reportList.find(name => name.backendName == this.reportForm.value.reportselection.backendName)
      let originalGrnColumns = grnData.requiredcolumnsForGRN ? grnData.requiredcolumnsForGRN : []
      originalGrnColumns.forEach(item => {
        if (item.isMandatory) {
            item.selected = true;
        }
      });
      let draftGrnColumns
      if(this.draftReqColumns){
        draftGrnColumns = this.draftReqColumns['consolidated GRN']
      }
      if(this.draftReqColumns && (draftGrnColumns && draftGrnColumns.length > 0)){
      originalGrnColumns.forEach(item => {
          if (draftGrnColumns.includes(item.name)) {
            item.selected = true;
          }else{
            item.selected = false;
          }
       });
      }else{
        var filteredGrnData = originalGrnColumns.filter(item => item.name !== "S.No");
      }
      this.requiredColumnsGrnData = originalGrnColumns
      this.tempReqGrnData = originalGrnColumns
      this.reqColumnsGrnData = this.requiredColumnsGrnData
      this.reqColumnsGrn.next(this.reqColumnsGrnData.slice());
      this.reqColumnsGrnFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
        this.filterData(this.reqColumnsGrnData, this.reqColumnsGrnFilterCtrl, this.reqColumnsGrn);
      });
      filteredGrnData = filteredGrnData ? filteredGrnData : this.requiredColumnsGrnData
      let mandatoryDataGrn = filteredGrnData.filter(item => item.selected === true);
      // let mandatoryDataGrn = this.requiredColumnsGrnData.filter(item => item.selected === true)
      if(mandatoryDataGrn.length > 0){
        this.requiredGrnColumns.setValue(mandatoryDataGrn)
      }else{
        this.requiredGrnColumns.setValue([...this.requiredColumnsGrnData , 1])
      }

      let IndentData 
      IndentData = this.reportList.find(name => name.backendName == this.reportForm.value.reportselection.backendName)
      let originalIndentColumns = IndentData.requiredcolumnsForIndent ? IndentData.requiredcolumnsForIndent : []
      originalIndentColumns.forEach(item => {
        if (item.isMandatory) {
            item.selected = true;
        }
      });
      let draftIndentColumns
      if(this.draftReqColumns){
        draftIndentColumns = this.draftReqColumns['consolidated Indent']
      }
      if(this.draftReqColumns && (draftIndentColumns && draftIndentColumns.length > 0)){
      originalIndentColumns.forEach(item => {
          if (draftIndentColumns.includes(item.name)) {
              item.selected = true;
          }else{
            item.selected = false;
          }
       });
      }else{
        var filteredIndentData = originalIndentColumns.filter(item => item.name !== "S.No");
      }

      this.requiredColumnsIndentData = originalIndentColumns
      this.tempReqIndentData = originalIndentColumns
      this.reqColumnsIndentData = this.requiredColumnsIndentData
      this.reqColumnsIndent.next(this.reqColumnsIndentData.slice());
      this.reqColumnsIndentFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
        this.filterData(this.reqColumnsIndentData, this.reqColumnsIndentFilterCtrl, this.reqColumnsIndent);
      });

      filteredIndentData = filteredIndentData ? filteredIndentData : this.requiredColumnsIndentData
      let mandatoryDataIndent = filteredIndentData.filter(item => item.selected === true);
      // let mandatoryDataIndent = this.requiredColumnsIndentData.filter(item => item.selected === true)
      if(mandatoryDataIndent.length > 0){
        this.requiredIndentColumns.setValue(mandatoryDataIndent)
      }else{
        this.requiredIndentColumns.setValue([...this.requiredColumnsIndentData , 1])
      }

    }else if(this.reportForm.value.reportselection.backendName === 'inventoryConsumptionNew'){

      let invData 
      invData = this.reportList.find(name => name.backendName == this.reportForm.value.reportselection.backendName)
      let originalInvColumns = invData.requiredColumnsForInv ? invData.requiredColumnsForInv : []
      originalInvColumns.forEach(item => {
        if (item.isMandatory) {
            item.selected = true;
        }
      });
      let draftInvColumns
      if(this.draftReqColumns){
        draftInvColumns = this.draftReqColumns['inventoryConsumption Inv'] ? this.draftReqColumns['inventoryConsumption Inv'] : []
      }
      if(this.draftReqColumns && (draftInvColumns && draftInvColumns.length > 0)){
      originalInvColumns.forEach(item => {
          if (draftInvColumns.includes(item.name)) {
            item.selected = true;
          }else{
            item.selected = false;
          }
       });
      }else{
        var filteredInvData = originalInvColumns.filter(item => item.name !== "S.No");
      }
      this.requiredColumnsInvData = originalInvColumns
      this.tempReqInvData = originalInvColumns
      this.reqColumnsInvData = this.requiredColumnsInvData
      this.reqColumnsInv.next(this.reqColumnsInvData.slice());
      this.reqColumnsInvFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
        this.filterData(this.reqColumnsInvData, this.reqColumnsInvFilterCtrl, this.reqColumnsInv);
      });

      filteredInvData = filteredInvData ? filteredInvData : this.requiredColumnsInvData
      let mandatoryDataGrn = filteredInvData.filter(item => item.selected === true);
      // let mandatoryDataGrn = this.requiredColumnsInvData.filter(item => item.selected === true)
      if(mandatoryDataGrn.length > 0){
        this.requiredInvColumns.setValue(mandatoryDataGrn)
      }else{
        this.requiredInvColumns.setValue([...this.requiredColumnsInvData , 1])
      }

      let subRecipeData 
      subRecipeData = this.reportList.find(name => name.backendName == this.reportForm.value.reportselection.backendName)
      let originalSubRecipeColumns = subRecipeData.requiredColumnsForSubRecipe ? subRecipeData.requiredColumnsForSubRecipe : []
      originalSubRecipeColumns.forEach(item => {
        if (item.isMandatory) {
            item.selected = true;
        }
      });
      let draftSubRecipeColumns
      if(this.draftReqColumns){
        draftSubRecipeColumns = this.draftReqColumns['inventoryConsumption SubRecipe']
      }
      if(this.draftReqColumns && (draftSubRecipeColumns && draftSubRecipeColumns.length > 0)){
      originalSubRecipeColumns.forEach(item => {
          if (draftSubRecipeColumns.includes(item.name)) {
            item.selected = true;
          }else{
            item.selected = false;
          }
       });
      }else{
        var filteredData = originalSubRecipeColumns.filter(item => item.name !== "S.No");
      }

      this.requiredColumnsIndentData = originalSubRecipeColumns
      this.tempReqSubRecipeData = originalSubRecipeColumns
      this.reqColumnsSubRecipeData = this.requiredColumnsIndentData
      this.reqColumnsSubRecipe.next(this.reqColumnsSubRecipeData.slice());
      this.reqColumnsSubRecipeFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
        this.filterData(this.reqColumnsSubRecipeData, this.reqColumnsSubRecipeFilterCtrl, this.reqColumnsSubRecipe);
      });

      filteredData = filteredData ? filteredData : this.requiredColumnsIndentData;
      let mandatoryDataIndent = filteredData.filter(item => item.selected === true);
      // let mandatoryDataIndent = this.requiredColumnsIndentData.filter(item => item.selected === true)
      if(mandatoryDataIndent.length > 0){
        this.requiredSubRecipeColumns.setValue(mandatoryDataIndent)
      }else{
        this.requiredSubRecipeColumns.setValue([...this.requiredColumnsIndentData , 1])
      }
    }else{
      let data 
      data = this.reportList.find(name => name.backendName == this.reportForm.value.reportselection.backendName)
      let originalColumns = data.requiredColumns ? data.requiredColumns : []
      originalColumns.forEach(item => {
        if (item.isMandatory) {
            item.selected = true;
        }
      });
      let draftColumns
      if(this.draftReqColumns){
        draftColumns = this.draftReqColumns[this.reportForm.value.reportselection.backendName]
      }
      if(this.draftReqColumns && (draftColumns && draftColumns.length > 0)){
        originalColumns.forEach(item => {
          if (draftColumns.includes(item.name)) {
            item.selected = true;
          }else{
            item.selected = false;
          }
        });
      }else{
        var filteredData = originalColumns.filter(item => item.name !== "S.No");
      }
      this.requiredColumnsData = originalColumns
      // this.requiredColumnsData = data.requiredColumns && data.requiredColumns.length > 0 ? data.requiredColumns : data
      this.tempReqData = this.requiredColumnsData
      this.reqColumnsData = this.requiredColumnsData
      this.reqColumns.next(this.reqColumnsData.slice());
      this.reqColumnsFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
        this.filterData(this.reqColumnsData, this.reqColumnsFilterCtrl, this.reqColumns);
      });

      filteredData = filteredData ? filteredData : this.requiredColumnsData
      let mandatoryData = filteredData.filter(item => item.selected === true);
      if(mandatoryData.length > 0){
        this.requiredColumns.setValue(mandatoryData)
      }else{
        this.requiredColumns.setValue([...this.requiredColumnsData , 1])
      }
    }

    if(Object.keys(this.brand.value)){
      this.brand.setValue([this.brand.value])
    }
    this.filterBranches.enable();
    if ((this.brand.value && this.brand.value.length) > 0 && this.brands) {
      this.Brands(this.brand.value)
    } else {
      var branch
      let data = []
      data.push(this.defaultSelectionBranches)
      if (data.length > 0) {
        branch = data.map(item => item.branchName);
        branch = branch.map((itemName, index) => ({ id: index + 1, itemName }));
        this.filterBranches.setValue(branch);
      }
    }
    if(this.singleSelectBranch === true){
      this.singleBranch(this.filterBranches.value);
    }else{
      this.multipleBranch();
    }
  }

  okDialog() {
    this.openfilters = false;
  }

    getSubCategory() {
    let data = this.categoryForm.value.map(item => item.itemName);
    let inputObj = this.currentUser
    inputObj['categories'] = data;
    this.currentUser['categoryType'] =["inventory","menu", "subRecipe"]
    this.menuItems.getSubCategories(inputObj).subscribe(res => {
      if (res['result'] == 'success') {
        this.subCategoryList = res['subCategories'];
        this.subCategoryList = this.subCategoryList.filter(item => item !== "");
        if (res['subCategories'].length > 0) {
          this.SubcategoryForm.enable();
          this.filteredSubCategoryList = [...new Set(this.subCategoryList)];
          this.temSubCat = this.filteredSubCategoryList.map((itemName, index) => ({ id: index + 1, itemName }));
          this.filteredSubCategoryList = this.filteredSubCategoryList.map((itemName, index) => ({ id: index + 1, itemName }));
          this.SubcategoryForm.setValue(this.temSubCat)
        } else {
          this.SubcategoryForm.disable();
          this.SubcategoryForm.setValue([''])
        }
      }
    })
  }

  filterByCat() {
    let data = this.categoryForm.value.map(item => item.itemName);
    let obj = {
      "tenantId": this.currentUser.tenantId,
      "categories": data
    }
    this.menuItems.getSubCategories(obj).subscribe(res => {
      if (res['result'] == 'success') {
        if (res['subCategories'].length > 0) {
          this.subCategoryList = res['subCategories'];
          this.SubcategoryForm.enable();
          this.filteredSubCategoryList = [...new Set(this.subCategoryList)];
          this.temSubCat = this.filteredSubCategoryList.map((itemName, index) => ({ id: index + 1, itemName }));
          this.filteredSubCategoryList = this.filteredSubCategoryList.map((itemName, index) => ({ id: index + 1, itemName }));
          this.SubcategoryForm.setValue(this.temSubCat)
        } else {
          this.SubcategoryForm.disable();
          this.SubcategoryForm.setValue([''])
        }
      } else {
        this.filteredSubCategoryList = [];
      }
    })
  }

  toggleAllSelectionSubcategory(manual = false) {
    if (this.subCatallSelected && this.subCatallSelected.selected) {
      this.SubcategoryForm.patchValue([...this.filteredSubCategoryList.map(item => item), 1]);
    } else if (manual) {
      this.SubcategoryForm.patchValue([...this.filteredSubCategoryList.map(item => item), 1]);
    } else {
      this.SubcategoryForm.patchValue([]);
    }
  }

  toggleAllSelectionWorkArea(manual = false) {
    if (this.workAreaallSelected && this.workAreaallSelected.selected) {
      this.workAreaForm.patchValue([...this.indentWorkArea.map(item => item), 1]);
    } else if (manual) {
      this.workAreaForm.patchValue([...this.indentWorkArea.map(item => item), 1]);
    } else {
      this.workAreaForm.patchValue([]);
    }
  }

  toggleAllSelectionVendor(manual = false) {
    if (this.vendorallSelected && this.vendorallSelected.selected) {
      this.vendorForm.patchValue([...this.vendorList.map(item => item), 1]);
    } else if (manual) {
      this.vendorForm.patchValue([...this.vendorList.map(item => item), 1]);
    } else {
      this.vendorForm.patchValue([]);
    }
  }

  singleBranch(val) {
    if((val && val.length > 0 ) || (this.filterBranches.value && this.filterBranches.value.length > 0)){
      let data = val ? val : this.filterBranches.value;
      // data = this.filterBranches.value.map(item => item.restaurantIdOld);
      data = this.currentUser.restaurantAccess.find(item => item.branchName === data[0].itemName);
      this.singleBranchData = true;
      this.getWorkAreas([data.restaurantIdOld]);
    }

  }

  multipleBranch() {    
    let data
    if (this.filterBranches.value.length > 0) {
      data = this.filterBranches.value.map(item => item.itemName)
      this.singleBranchData = false;
      this.selectedBranchesRestaurantIds = [];
      for (const branchName of data) {
        const matchingBranch = this.currentUser.restaurantAccess.find(item => item.branchName === branchName);
        if (matchingBranch) {
          this.selectedBranchesRestaurantIds.push(matchingBranch.restaurantIdOld);
        }
      }
      this.getWorkAreas(this.selectedBranchesRestaurantIds);
    }else{
      this.selectedBranchesRestaurantIds = []
      this.getWorkAreas(this.selectedBranchesRestaurantIds);
    }
  }

  Brands(val) {
    this.filterBranches.setValue('')
    let itemNames
    itemNames = val.map(item => item.itemName);
    if (!itemNames && this.brand.value) {
      itemNames = this.brand.value
    }
    let requiredBranches = []
    // val.forEach(element => {
    //   let requiredLocation = this.brands.hasOwnProperty(element.itemName) ? this.brands[element.itemName] : undefined ;
    //   requiredLocation ? requiredBranches.concat(requiredLocation) : undefined;
    // });

    val.forEach(element => {
      if (this.brands.hasOwnProperty(element.itemName)) {
          const requiredLocation = this.brands[element.itemName];
          requiredBranches = requiredBranches.concat(requiredLocation);
      }
  });

    this.branchFilterBrands = requiredBranches ;
    const brnName = [];
    if (this.branchFilterBrands.length > 0) {
      for (const branchId of this.branchFilterBrands) {
        const matchingBranch = this.currentUser.restaurantAccess.find(item => item.restaurantIdOld === branchId);
        if (matchingBranch) {
          brnName.push(matchingBranch.branchName)
          this.brandBranchNames = brnName
          this.tempBranchNames = this.brandBranchNames.map((itemName, index) => ({ id: index + 1, itemName }));
          this.brandBranchNames = this.brandBranchNames.map((itemName, index) => ({ id: index + 1, itemName }));
        }
      }
      this.filterBranches.enable()
      this.filterBranches.setValue(this.tempBranchNames)
      this.brandBranch(this.filterBranches.value);
    } else {
      this.filterBranches.setValue([]);
      this.filterBranches.disable()
      this.brandBranch(this.filterBranches.value);
    }
  }

  brandBranch(val) {
    let itemNames
    if (val || this.filterBranches.value) {
      if (val) {
        itemNames = val.map(item => item.itemName);
      } else if (this.filterBranches.value) {
        itemNames = this.filterBranches.value.map(item => item.itemName);
      }
      this.selectedBrandBranchesRestaurantIds = [];
      for (const branchName of itemNames) {
        const matchingBranch = this.currentUser.restaurantAccess.find(item => item.branchName === branchName);
        if (matchingBranch) {
          this.selectedBrandBranchesRestaurantIds.push(matchingBranch.restaurantIdOld);
        }
      }
      this.getWorkAreas(this.selectedBrandBranchesRestaurantIds);
    }else{
      this.selectedBrandBranchesRestaurantIds = []
      this.getWorkAreas(this.selectedBrandBranchesRestaurantIds);
    }
  }

  getWorkAreas(value) {
    if (this.items && this.items.workArea == true && value.length > 0) {
      const workAreasMap = [];
      for (const restaurantId of value) {
        const matchingBranch = this.currentUser.restaurantAccess.find(item => item.restaurantIdOld === restaurantId);
        if (matchingBranch) {
          workAreasMap.push(matchingBranch.workAreas);
          this.indentWorkArea = workAreasMap.reduce((result, workAreas) => result.concat(workAreas), []);
          this.tempIndentWorkArea = this.indentWorkArea.map((itemName, index) => ({ id: index + 1, itemName }));
          this.indentWorkArea = this.indentWorkArea.map((itemName, index) => ({ id: index + 1, itemName }));
          this.workAreaForm.setValue(this.tempIndentWorkArea)
        }
      }
    }else{
      this.workAreaForm.setValue([])
    }
  }

  selectYear(value) {
    const currentYear = new Date().getFullYear();
    if (value === currentYear) {
      const currentDate = new Date();
      this.currentMonth = currentDate.getMonth() + 1;
      if (this.monthForm.value >= this.currentMonth) {
        this.monthForm.setValue([''])
      }
    } else {
      this.currentMonth = undefined;
    }
  }

  checkReportsName(backendName: string): string {
    let data = null;
    for (let i = 0; i < this.filteredReportList.length; i++) {
      if (this.filteredReportList[i].backendName === backendName) {
        data = this.filteredReportList[i];
        break;
      }
    }

    if (!data) {
      return "Data not found";
    } else if (!data.displayName) {
      return "Display name not found";
    } else {
      return data.displayName;
    }
  }

  onCheckboxChange(checkbox: string) {
    if (checkbox === 'source') {
      this.isDestinationChecked = false;
      this.isChecked = true;
    } else if (checkbox === 'destination') {
      this.isSourceChecked = false;
      this.isChecked = false;
    }
  }

  getBaseDateFilter(event) {
    if (event.displayName === 'Consolidated Purchase & Indent' || event.displayName === 'Consolidated WAC Report') {
      if (event.displayName in this.baseDateFilter) {
        this.baseDateValue = event.displayName === 'Consolidated Purchase & Indent' ? this.baseDateFilter['Consolidated Purchase & Indent'][0]['GRN'] : this.baseDateFilter['Consolidated WAC Report'][0]['GRN'] ;
        for (let i = 0; i < this.baseDateValue.length; i++) {
          if (this.baseDateValue[i].displayName === "Grn Date") {
            this.baseDateValue[i].displayName = "GRN Date";
          }
        }
        this.baseDateValue = this.baseDateValue
        this.baseDateValueForIndent = event.displayName === 'Consolidated Purchase & Indent' ? this.baseDateFilter['Consolidated Purchase & Indent'][0]['Indent'] : this.baseDateFilter['Consolidated WAC Report'][0]['Indent'];
        let reqObject = this.baseDateValueForIndent.find((el) => el.value === 'createdDate');
        reqObject ? this.baseDateForConsolidation.setValue(reqObject) : undefined;
      } else {
        this.baseDateValue = [];
        this.baseDateValueForIndent = [];
      }
    } else {
      if (event.displayName in this.baseDateFilter) {
        this.baseDateValue = this.baseDateFilter[event.displayName];
        let reqObject = this.baseDateValue.find((el) => el.value === 'deliveryDate');
        for (let i = 0; i < this.baseDateValue.length; i++) {
          if (this.baseDateValue[i].displayName === "Grn Date") {
            this.baseDateValue[i].displayName = "GRN Date";
          }
        }
        this.baseDateValue = this.baseDateValue
        reqObject ? this.baseDate.setValue(reqObject) : undefined;
      } else {
        this.baseDateValue = [];
      }
    }
    this.refreshData() ;
  }

  toggleReqAllSelection(manual = false) {
    if (this.allSelected && this.allSelected.selected || manual) {
      this.requiredColumns.patchValue([]);
      this.requiredColumns.patchValue([...this.tempReqData, 1]);
    }else {
      let selectedValues = [];
      this.tempReqData.forEach(item => {
        if (item.isMandatory) {
          selectedValues.push(item);
        }
      });
      this.requiredColumns.patchValue(selectedValues);
    }    
  }

  toggleReqGrnAllSelection(manual = false){
    if (this.allSelectedGrn && this.allSelectedGrn.selected || manual) {
      this.requiredGrnColumns.patchValue([]);
      this.requiredGrnColumns.patchValue([...this.tempReqGrnData, 1]);
    }else {
      let selectedValues = [];
      this.tempReqGrnData.forEach(item => {
        if (item.isMandatory) {
          selectedValues.push(item);
        }
      });
      this.requiredGrnColumns.patchValue(selectedValues);
    }    
  }

  toggleReqIndentAllSelection(manual = false){
    if (this.allSelectedIndent && this.allSelectedIndent.selected || manual) {
      // this.requiredIndentColumns.patchValue([]);
      this.requiredIndentColumns.patchValue([...this.tempReqIndentData, 1]);
    }else {
      let selectedValues = [];
      this.tempReqIndentData.forEach(item => {
        if (item.isMandatory) {
          selectedValues.push(item);
        }
      });
      this.requiredIndentColumns.patchValue(selectedValues);
    }    
  }

  toggleReqInvAllSelection(manual = false){
    if (this.allSelectedInv && this.allSelectedInv.selected || manual) {
      // this.requiredIndentColumns.patchValue([]);
      this.requiredInvColumns.patchValue([...this.tempReqInvData, 1]);
    }else {
      let selectedValues = [];
      this.tempReqInvData.forEach(item => {
        if (item.isMandatory) {
          selectedValues.push(item);
        }
      });
      this.requiredInvColumns.patchValue(selectedValues);
    }    
  }

  toggleReqSubRecipeAllSelection(manual = false){
    if (this.allSelectedSubRecipe && this.allSelectedSubRecipe.selected || manual) {
      // this.requiredIndentColumns.patchValue([]);
      this.requiredSubRecipeColumns.patchValue([...this.tempReqSubRecipeData, 1]);
    }else {
      let selectedValues = [];
      this.tempReqSubRecipeData.forEach(item => {
        if (item.isMandatory) {
          selectedValues.push(item);
        }
      });
      this.requiredSubRecipeColumns.patchValue(selectedValues);
    }    
  }

  addReportDraftData() {
    var key = this.reportForm.value.reportselection.backendName
    let reportDetails
    if(this.reportForm.value.reportselection.backendName === 'consolidated_purchase_indent' || this.reportForm.value.reportselection.backendName === 'consolidated_purchase_indent_wac'){
      reportDetails = {
        'consolidated GRN'  : this.requiredGrnColumns.value.map(item => item.name),
        'consolidated Indent'  : this.requiredIndentColumns.value.map(item => item.name)
      }
    }else if(this.reportForm.value.reportselection.backendName === 'inventoryConsumptionNew'){
      reportDetails = {
        'inventoryConsumption Inv'  : this.requiredInvColumns.value.map(item => item.name),
        'inventoryConsumption SubRecipe'  : this.requiredSubRecipeColumns.value.map(item => item.name)
      }
    }else{
      reportDetails = {
        [key]  : this.requiredColumns.value.map(item => item.name)
      }
    }
    let obj = {
      "tenantId": this.currentUser.tenantId,
      "restaurantId": this.currentUser.restaurantId,
      "role": this.currentUser.role,
      "reportDetails" : reportDetails
    }
    this.menuItems.addReportDraftData(obj).subscribe(res => {
      if (res['result'] == true) {
        this.getReportDraft();
        // if(this.reportForm.value.reportselection.backendName === 'consolidated_purchase_indent'){
        //   if(this.draftReqColumns){
        //     this.draftReqColumns['consolidated GRN'] = []
        //     this.draftReqColumns['consolidated Indent'] = []
        //   }
        //   this.draftReqColumns['consolidated GRN'] = this.requiredGrnColumns.value.map(item => item.name)
        //   this.draftReqColumns['consolidated Indent'] = this.requiredIndentColumns.value.map(item => item.name)
        // }else if(this.reportForm.value.reportselection.backendName === 'inventoryConsumptionNew'){
        //   if(this.draftReqColumns){
        //     this.draftReqColumns['inventoryConsumption Inv'] = []
        //     this.draftReqColumns['inventoryConsumption SubRecipe'] = []
        //   }
        //   this.draftReqColumns['inventoryConsumption Inv'] = this.requiredInvColumns.value.map(item => item.name)
        //   this.draftReqColumns['inventoryConsumption SubRecipe'] = this.requiredSubRecipeColumns.value.map(item => item.name)
        // }else{
        //   if(this.draftReqColumns){
        //     this.draftReqColumns[this.reportForm.value.reportselection.backendName] = []
        //   }
        //   this.draftReqColumns[this.reportForm.value.reportselection.backendName] = this.requiredColumns.value.map(item => item.name)
        // }
      }
    })
  }

  getReportDraft(){
    let obj = {
      "tenantId": this.currentUser.tenantId,
      "restaurantId": this.currentUser.restaurantId,
      "role": this.currentUser.role,
    }
    this.menuItems.getReportDraft(obj).subscribe(res => {
      if (res['success'] == true) {
        this.draftReqColumns = res['draft']['reportList']
      }
    })
  }

  getPOSPriceTires(){
    let obj = {
      "tenantId": this.currentUser.tenantId,
    }
    this.menuItems.getPOSPriceTires(obj).subscribe(res => {
      if (res.length > 0) {     
        this.priceTierList = res.map(item => ({ id: item.id, name: item.name })); 
        this.priceTierNames.next(this.priceTierList.slice());
        this.priceTier.setValue(this.priceTierList[0]); 
        this.priceTierFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
        this.filterData(this.priceTierList, this.priceTierFilterCtrl, this.priceTierNames);
      });
      }
    })
  }

  onStockToggleChange() {
    const value = this.storeStockValue.value;
    console.log('Toggle value changed:', value);
  }

  onNonStockToggleChange() {
    const value = this.workAreaStockValue.value;
    console.log('Toggle value changed:', value);
  }

  // onSliderChange(event: any) {
  //   console.log('Slider values changed:', event);
  //   // You can perform any actions based on the event, such as updating other components or making API calls
  // }

  onSliderChange(event: any) {
    this.ngZone.run(() => {
      console.log('Slider values changed:', event);
      // Perform any actions based on the event
    });
  }

  onSliderStoreChange(event: any) {
    console.log('Slider store value:', event);
    console.log('minStoreValue:', this.minStoreValue);
  console.log('maxStoreValue:', this.maxStoreValue);
  }

  onSliderWorkAreaChange(event: any) {
    console.log('Slider workArea value:', event);
  }

}