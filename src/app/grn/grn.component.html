<div class="title row ">
  <div>
    <form [formGroup]="grnForm">
      <mat-form-field *ngIf="multiBranchUser" appearance="none" class="topitem">
        <label class="title-palce">Select Branch</label>
        <mat-select placeholder="Select Branch" class="outline" formControlName="branchSelection"
          (selectionChange)="filterByBranch($event.value)">
          <mat-option *ngFor="let rest of branches" [value]="rest">
            {{ rest.branchName }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </form>
  </div>

  <div>
    <mat-form-field *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" appearance="none"
      style="margin-left: 10px;" class="topitem">
      <label>Start Date</label>
      <input matInput class="outline" [matDatepicker]="picker1" placeholder="Start Date" [formControl]="startDate" />
      <mat-datepicker-toggle matSuffix [for]="picker1">
        <mat-icon matDatepickerToggleIcon>
          <img src="./../../assets/calender.png" />
        </mat-icon>
      </mat-datepicker-toggle>
      <mat-datepicker #picker1></mat-datepicker>
    </mat-form-field>
  </div>

  <div>
    <mat-form-field *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" appearance="none"
      style="margin-left: 10px;" class="topitem">
      <label>End Date</label>
      <input matInput class="outline" [matDatepicker]="picker2" [formControl]="endDate" placeholder="End Date"
        [readonly]="!startDate.value" [disabled]="!startDate.value" [min]="startDate.value" />
      <mat-datepicker-toggle matSuffix [for]="picker2">
        <mat-icon matDatepickerToggleIcon>
          <img src="./../../assets/calender.png" />
        </mat-icon>
      </mat-datepicker-toggle>
      <mat-datepicker #picker2></mat-datepicker>
    </mat-form-field>
  </div>

  <div class=" mr-2">
    <button *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" mat-stroked-button
      class="btn-block findButton button3" (click)="filterByDate()">
      Find
    </button>
  </div>

  <div>
    <button *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" mat-stroked-button
      class="btn-block findButton button3" (click)="clearDates()">
      Clear
    </button>
  </div>

</div>

<div *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" class="datacontainer">
  <mat-card>
    <mat-card-content>
      <!-- <div class="search-table-input row"> -->
      <div class="search-table-input">
        <mat-form-field appearance="none">
          <label>Invoice Search</label>
          <input matInput type="text" placeholder="Search" autocomplete="off" class="outline" [(ngModel)]='invoiceId'
            (keyup)="getInvoiceSearchData($event)" />
          <mat-icon matSuffix (click)="getGrnbyId()" class="closebtn">search</mat-icon>
        </mat-form-field>

        <mat-form-field appearance="none" style="padding-right: 10px;">
          <label>Filter By Date</label>
          <input matInput class="outline" [matDatepicker]="picker2" placeholder="Filter By Date"
            [formControl]="filterDate" (dateChange)="doDateFilter($event.target.value)" [(ngModel)]="clearDate"
            readonly />
          <mat-datepicker-toggle matSuffix [for]="picker2">
            <mat-icon matDatepickerToggleIcon style="margin-right:15px !important;">
              <img class="datepickIcon" src="./../../assets/calender.png" />
            </mat-icon>
          </mat-datepicker-toggle>
          <mat-icon matSuffix (click)="dateFilter()" matTooltip="Clear Date" class="closebtn">close</mat-icon>
          <mat-datepicker #picker2></mat-datepicker>
        </mat-form-field>

        <mat-form-field appearance="none">
          <label>Vendor Name</label>
          <mat-select placeholder="Vendor Name" [formControl]="vendorName"
            (selectionChange)="vendorChange($event.value)" class="outline" [multiple]="true" #multiSelect>
            <mat-option>
              <ngx-mat-select-search placeholderLabel="Vendor Items..." noEntriesFoundLabel="'no Vendor Item found'"
                [formControl]="vendorMultiFilterCtrl"></ngx-mat-select-search>
            </mat-option>
            <mat-option (click)="toggleAllVendors()" class="hide-checkbox">
              Deselect All / Select All
            </mat-option>
            <mat-option *ngFor="let vendor of grnvendorsMulti | async" [value]="vendor">
              {{ vendor }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="none">
          <label>Item Name</label>
          <mat-select placeholder="Item Name" [formControl]="itemName" (selectionChange)="itemNameChange($event.value)"
            class="outline" [multiple]="true" #multiSelect>
            <mat-option>
              <ngx-mat-select-search placeholderLabel="Items..." noEntriesFoundLabel="'no Item found'"
                [formControl]="itemFilterCtrl"></ngx-mat-select-search>
            </mat-option>
            <mat-option (click)="toggleAllItems()" class="hide-checkbox">
              Deselect All / Select All
            </mat-option>
            <mat-option *ngFor="let item of itemBanks | async" [value]="item">
              {{ item }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <button mat-button class="buttonForRefresh refreshBtn" (click)="refreshdata()">Refresh</button>
      </div>
      <table #table mat-table [dataSource]="dataSource" matSortDirection="desc" matSort>
        <ng-container matColumnDef="grnId">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> GRN Id</b>
          </th>
          <td mat-cell *matCellDef="let element" class="links" (click)="detailedGrn(element)">
            {{ element.grnId }}
          </td>
        </ng-container>

        <ng-container matColumnDef="poId">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Request Id </b>
          </th>
          <td mat-cell *matCellDef="let element">
            <span *ngIf="element.details.ibtId">{{
              element.details.ibtId
              }}</span>
            <span *ngIf="element.details.poId">{{ element.details.poId }}</span>
          </td>
        </ng-container>

        <ng-container matColumnDef="vendorName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Vendor Name </b>
          </th>
          <td mat-cell *matCellDef="let element">{{ (element.vendorName ) || "-"}}</td>
        </ng-container>
        <ng-container matColumnDef="type">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Type </b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.grnType | titlecase }}
          </td>
        </ng-container>

        <ng-container matColumnDef="date">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>GRN Date(System Entry Date)</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ this.utils.formatDateToUTC(element.createTs) }}
          </td>
        </ng-container>

        <ng-container matColumnDef="invoiceDate">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Vendor Invoice Date</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.invoiceDate ? (element.invoiceDate | date: "EEEE, MMMM d, y") : '-'}}
          </td>
        </ng-container>

        <ng-container matColumnDef="invId">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Invoice Id </b>
          </th>
          <td mat-cell *matCellDef="let element">
            <span *ngIf="element.details.ibtId">N/A</span>
            <span *ngIf="element.details.poId">{{ element.invoiceId }}</span>
          </td>
        </ng-container>

        <ng-container matColumnDef="totalAmount">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Total Amount</b>
          </th>
          <td mat-cell *matCellDef="let element">
            <!-- {{ this.utils.truncateNew(getTotal('totalPrice',element['grnItems']))}} -->
            {{ this.utils.truncateNew(element.grandTotal)}}
          </td>
        </ng-container>

        <!-- <ng-container matColumnDef="action">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>RTV</b>
          </th>
          <td mat-cell *matCellDef="let element">
            <button mat-icon-button (click)="rtvProcess(element)" class="action-print" matTooltip="Return Items"
              matTooltipPosition="right">
              <mat-icon class="action-print-icon">swap_vertical_circle</mat-icon>
            </button>
          </td>
        </ng-container> -->

        <ng-container matColumnDef="action">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Actions</b>
          </th>
          <td mat-cell *matCellDef="let element">
            <div [matTooltip]="(element.stockConversion)  ? 'Items already used for stockConversion' : '' ">
              <button mat-icon-button (click)="deleteGrn(element)" *ngIf="deleteAccess" class="delete-button"
              [disabled]="isButtonDisabled(element)" matTooltip="Delete Grn" matTooltipPosition="left">
              <mat-icon>delete_outline</mat-icon>
            </button>
              <button mat-icon-button *ngIf="rtvAccess" (click)="rtvProcess(element)" class="action-print"
                matTooltip="Return Items" matTooltipPosition="right">
                <mat-icon class="action-print-icon">swap_vertical_circle</mat-icon>
              </button>
              <button mat-icon-button *ngIf="rtvAccess && element['RTV'] === true" (click)="detailedRtvList(element)" class="action-print ml-2"
                matTooltip="RTV List" matTooltipPosition="right">
                <mat-icon class="action-print-icon">subject</mat-icon>
              </button>

            </div>
          </td>
        </ng-container>


        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>
      <div class="dataMessage" *ngIf="dataSource?.data.length == 0"> No Data Available </div>
      <mat-paginator [showTotalPages]="5" [pageSize]="10" [pageSizeOptions]="pageSizes"></mat-paginator>
    </mat-card-content>
  </mat-card>
</div>