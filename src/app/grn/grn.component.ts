import { Component, ViewChild, OnInit } from '@angular/core';
import { PurchasesService, AuthService, ShareDataService, BranchTransferService } from '../_services';
import { MatSort, Sort, MatTableDataSource, MatPaginator, MatOption } from '@angular/material';
import { GlobalsService } from '../_services/globals.service';
import { ActivatedRoute, NavigationStart, Params, Router } from '@angular/router';
import { UtilsService } from '../_utils/utils.service';
import { NotificationService } from '../_services/notification.service'
import { FormBuilder, FormControl, FormGroup, Validators } from "@angular/forms";
import { ReplaySubject, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material';
import { MomentDateModule, MomentDateAdapter } from '@angular/material-moment-adapter';
import { SharedFilterService } from '../_services/shared-filter.service';
import { SimpleDialogComponent } from '../_dialogs/simple-dialog/simple-dialog.component';
import { MatDatepickerInputEvent, MatDialog } from '@angular/material';
import { InvoiceDialogComponent } from '../_dialogs/invoice-dialog/invoice-dialog.component';

export const MY_FORMATS = {
  parse: {
    dateInput: 'LL'
  },
  display: {
    dateInput: 'DD-MM-YYYY',
    monthYearLabel: 'YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'YYYY'
  }
};
@Component({
  selector: 'app-grn',
  templateUrl: './grn.component.html',
  styleUrls: ['./grn.component.scss', "./../../common-dark.scss"],
  providers: [{ provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
  { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }
  ]
})
export class GrnComponent implements OnInit {
  grnNumber: any;
  searchText: string;
  searchValue: string;
  data: any[];
  actualData: any;
  windowCurrentUrl: string;
  selectedvendorId: any;
  vendorNameArray: any[];
  uniqueVendorNames: any[];
  vendorNameList: any;
  prevBranchId: any;
  rtvAccess: boolean;
  deleteAccess: boolean;
  @ViewChild(MatPaginator) set paginator(value: MatPaginator) {
    if (this.dataSource) {
      this.dataSource.paginator = value;
    }
  };
  pageSizes = [];
  selectedTypeName: any;
  public startDate = new FormControl();
  public endDate = new FormControl();
  filterKeys = { vendorName: 'All', grnType: 'All' }
  restaurantId: any;
  multiBranchUser; branchSelected: boolean;
  grnType = new FormControl();
  vendorName = new FormControl();
  filterDate = new FormControl();
  clearDate: any;
  grnList: any;
  vendorList = [{ vendorName: "All" }];
  grnTypeList = ['All', 'po', 'ibt'];
  private user: any;
  dataSource: MatTableDataSource<any> = new MatTableDataSource<any>();
  private displayedColumns: string[];
  @ViewChild(MatSort) sort: MatSort;
  branches: any[];
  getBranchData: any[]
  grnForm: FormGroup;
  invoiceId: string;
  public grnvendorsMulti: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public vendorBank: any[] = [];
  public vendorMultiFilterCtrl: FormControl = new FormControl();
  protected _onDestroy = new Subject<void>();
  grnsUrl = encodeURI(GlobalsService.grns)
  grnListUrl = encodeURI(GlobalsService.grnList)
  private unsubscribe$ = new Subject<void>();
  sharedFilterData: any = {};
  detailedGrnUrl = encodeURI(GlobalsService.detailedGrn)
  selectVendorAll: boolean = true;
  stopSecondApiCall: boolean = false;
  @ViewChild('allSelected') private allSelected: MatOption;
  clearedData: boolean = false;
  itemNameList: any;
  itemName = new FormControl();
  public itemBank: any[] = [];
  public itemBanks: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public itemFilterCtrl: FormControl = new FormControl();
  selectAll: boolean = true;
  selectAllVendors: boolean = true;
  dataRefresh: boolean = false;
  constructor(
    private notifyService: NotificationService,
    private activateRoute: ActivatedRoute,
    private purchases: PurchasesService, private auth: AuthService,
    private router: Router,
    private utils: UtilsService,
    private sharedData: ShareDataService,
    private branchTransfer: BranchTransferService,
    private fb: FormBuilder,
    private sharedFilterService: SharedFilterService,
    private dialog: MatDialog
  ) {
    this.user = this.auth.getCurrentUser()
    this.multiBranchUser = this.user.multiBranchUser;

    this.activateRoute.params.subscribe((params: Params) => {
      if (params.grnNumber) {
        this.grnNumber = params.grnNumber;
      }
    });

    this.grnForm = this.fb.group({
      branchSelection: [null, Validators.required]
    });

    var windowLocation = window.location.href;
    this.windowCurrentUrl = windowLocation.split('/')[4]
    if ((this.windowCurrentUrl != this.grnsUrl) || (this.windowCurrentUrl != this.grnListUrl)) {
      this.windowCurrentUrl = windowLocation.split('/')[4].split('/')[0]
    }

    this.sharedFilterService.getFilteredGrn.subscribe(obj =>
      this.sharedFilterData = obj
    );


    this.sharedData.sharedBranchData.pipe(takeUntil(this.unsubscribe$)).subscribe((val) => {
      this.getBranchData = val;      
      if (this.getBranchData.length == 0) {
        this.branches = this.user.restaurantAccess;
      } else if (this.getBranchData.length == 1) {
        const toSelect = this.getBranchData.find(data => data.branchName == this.getBranchData[0].branchName);
        if (toSelect != this.sharedFilterData.restaurantId) {
          this.sharedFilterData = '';
          this.clearDate = null;
          this.filterDate.setValue(null);
          this.startDate.setValue(null);
          this.endDate.setValue(null);
          this.selectedvendorId = null;
        }
        this.grnForm.get('branchSelection').setValue(toSelect);
        this.branches = this.getBranchData
        this.filterByBranch(this.grnForm.value.branchSelection);
        if (this.sharedFilterData) {
          this.sharedFilterData.branchFlag = false;
        }
      } else {
        if (this.sharedFilterData.branchFlag == true) {
          this.filterByBranch(this.sharedFilterData.restaurantId);
          this.sharedFilterData.branchFlag = false;
        }
        this.branches = this.getBranchData
      }
    });
    this.router.events.subscribe(event => {
      if (event instanceof NavigationStart) {
        if (this.router.url.includes(this.detailedGrnUrl)) {
          localStorage.setItem('savedItemNames', JSON.stringify(this.itemName.value));
          localStorage.setItem('savedVendorNames', JSON.stringify(this.vendorName.value));
        }
      }
    }); 
  }

  ngOnInit() {
    if (this.branchSelected) {
    } else {
      if (!this.user.multiBranchUser) {
        this.restaurantId = this.user.restaurantAccess[0].restaurantIdOld;
        this.branchSelected = true;
        let reqObj: any = {
          tenantId: this.user.tenantId,
          restaurantId: this.restaurantId
        }
        this.getGrns(reqObj)
      }
    }
    this.getTenantData();
  }

  ngOnDestroy() {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
    this._onDestroy.next();
    this._onDestroy.complete();
    if (!this.router.url.includes(this.detailedGrnUrl)) {
      this.sharedFilterService.getFilteredGrn['_value'] = ''
      localStorage.removeItem('savedItemNames');
      localStorage.removeItem('savedVendorNames');
    }
  }

  getTotal(key: string, element) {
    return this.utils.getTotal(element, key);
  }

  clearDates() {
    this.clearedData = true;
    this.grnList = undefined;
    if (this.sharedFilterData.vendor) {
      this.sharedFilterData.vendor = ''
    }
    this.clearDate = null;
    this.filterDate.setValue(null);
    this.startDate.setValue(null);
    this.endDate.setValue(null);
    this.vendorName.setValue(null);
    this.selectedvendorId = null;
    this.stopSecondApiCall = true;
    let reqObj: any = {
      tenantId: this.user.tenantId,
      restaurantId: this.restaurantId
    }
    this.getGrns(reqObj)
  }

  getTenantData() {
    let obj = {
      tenantId: this.user.tenantId
    }
    this.branchTransfer.tenantDetails(obj).subscribe(res => {          
      if (res.result == 'success') {
        let deleteAccess = res.data[0] && res.data[0].permission && res.data[0].permission.grnAccess ? res.data[0].permission.grnAccess.delete : false;        
        let rtvAccess = res.data[0] && res.data[0].permission && res.data[0].permission.grnAccess ? res.data[0].permission.grnAccess.rtv : false;

        if (deleteAccess) {
          let rolesList = (res.data[0] && res.data[0].permission && res.data[0].permission.grnAccess) ? res.data[0].permission.grnAccess.deleteAccess : [];
          this.deleteAccess = (rolesList.some(role => role.toLowerCase() === this.user.role.toLowerCase())) ? true : false ;          
        } else {
          this.deleteAccess = false;
        }
        if (rtvAccess) {
          let rolesList = (res.data[0] && res.data[0].permission && res.data[0].permission.grnAccess) ? res.data[0].permission.grnAccess.rtvAccess : [];
          this.rtvAccess = (rolesList.some(role => role.toLowerCase() === this.user.role.toLowerCase())) ? true : false ;
        } else {
          this.rtvAccess = false;
        }
      } else {
        this.deleteAccess = false;
        this.rtvAccess = false;
      }
      this.columnsDisplay();
    })
  }

  columnsDisplay(){
    if(this.deleteAccess || this.rtvAccess){
      this.displayedColumns = GlobalsService.grnColumns
    } else {
      this.displayedColumns = ['grnId', 'poId', 'invId', 'vendorName', 'totalAmount', 'date', 'invoiceDate'];
    }
  }

  deleteGrn(obj) {
    let reqObj = {}
    reqObj['grnId'] = obj['grnId']
    reqObj['userEmail'] = this.user.email
    reqObj['tenantId'] = this.user.tenantId
    reqObj['restaurantId'] = this.restaurantId
    this.dialog.open(SimpleDialogComponent, {
      data: {
        title: 'Delete GRN',
        msg: 'Are you sure you want to delete?',
        ok: function () {
          this.purchases.checkItemAvailability(reqObj).subscribe(res => {
            if (res.status) {
              if (res.shortage.length > 0) {
                this.dialog.open(InvoiceDialogComponent, {
                  data: {
                    title: 'Shortage',
                    items: res.shortage,
                    ok: function () {
                      window.location.reload();
                    }.bind(this),
                  }
                });
              } else {
                this.purchases.deleteGrn(reqObj).subscribe(res => {
                  if (res['success']) {
                    let reqObj: any = {
                      tenantId: this.user.tenantId,
                      restaurantId: this.restaurantId
                    }
                    this.getGrns(reqObj)
                    this.notifyService.showSuccess(res['message'], "Success");
                  } else {
                    this.notifyService.showError(res['message'], "Error");
                  }
                });
              }
            } else {
              this.utils.snackBarShowInfo(res['message']);
            }
          })
        }.bind(this)
      }
    });
  }


  // getGrns(reqObj, newF = false) {
  //   if (this.filterDate.value) {
  //     let date = new Date(this.filterDate.value);
  //     let isoString = date.toISOString();
  //     // console.log(date);
  //     // date.setHours(date.getHours() + 5);
  //     // date.setMinutes(date.getMinutes() + 30);

  //     // console.log(date.toISOString())
      
  //     reqObj['grnDate'] = isoString;
  //   } else {
  //     reqObj['grnDate'] = null;
  //   }
  //   if (this.selectedvendorId) {
  //     let array = []
  //     array.push(this.vendorName.value)
  //     const filteredData = this.vendorList.filter(item => array[0].includes(item.vendorName));
  //     const data = filteredData.filter(item => item.vendorName !== "All");
  //     const uniqueData = data.filter((obj, index, self) => {
  //       return index === self.findIndex(item => item.vendorName === obj.vendorName);
  //     });
  //     const vendorIds = [];
  //     uniqueData.forEach((obj: any) => {
  //       vendorIds.push(obj.vendorId);
  //     });
  //     if (Object.keys(vendorIds).length === 0) {
  //       reqObj['vendorIdData'] = ['All'];
  //     } else {
  //       if (newF) {
  //         reqObj['vendorIdData'] = ['All'];
  //       } else {
  //         reqObj['vendorIdData'] = vendorIds
  //       }
  //     }
  //   } else {
  //     reqObj['vendorIdData'] = ['All'];
  //   }
  //   // reqObj['grnType'] = this.selectedTypeName ? this.selectedTypeName : null;
  //   if (this.startDate.value && this.endDate.value) {
  //     reqObj['startDate'] = new Date(this.startDate.value).toISOString();
  //     reqObj['endDate'] = new Date(this.endDate.value).toISOString();
  //   } else {
  //     reqObj['startDate'] = null;
  //     reqObj['endDate'] = null;
  //   }
  //   reqObj['piScreen'] = false;

  //   this.purchases.getGrn(reqObj).subscribe(data => {      
  //     if (!this.dataSource)
  //       this.actualData = data;
  //     data.forEach(element => {        
  //       let total = element.grnItems.reduce(function (accumulator, currentValue) {
  //         return accumulator + currentValue.totalPrice;
  //       }, 0);
  //       let otherCharge = element.otherCharges ? element.otherCharges : 0;
  //       element.totalGrnAmount = element.grandTotal ? element.grandTotal : (total + this.utils.truncateNew(otherCharge));
  //     });

  //     if (this.grnList == undefined) {
  //       this.grnList = data;
  //     }

  //     const uniqueItemNames = new Set();
  //     data.forEach((element) => {
  //       element.grnItems.forEach((item) => {
  //         uniqueItemNames.add(item.itemName);
  //       });
  //     });
  //     this.itemNameList = Array.from(uniqueItemNames); 

  //     const savedItemNames = JSON.parse(localStorage.getItem('savedItemNames') || '[]');      
  //     if (savedItemNames.length > 0) {
  //       this.itemName.setValue(savedItemNames);
  //       this.itemNameChange(savedItemNames);
  //     } else {
  //       this.itemName.setValue(this.itemNameList.slice()); 
  //       this.itemNameChange(this.itemName.value);
  //     }

  //     this.itemBank = this.itemNameList
  //     this.itemBanks.next(this.itemBank.slice());
  //     this.itemFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
  //         this.vendorfilterBanks();
  //     });
  //     // this.dataSource.data = data;
  //     this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
  //     this.dataSource.paginator = this.paginator;

      
  //     if ((this.sharedFilterData.restaurantId != this.grnForm.value.branchSelection)) {
  //       this.vendorList = []
  //       this.grnList.forEach(element => {
  //         this.vendorList.push(element);
  //       });
  //       this.vendorList = this.vendorList.filter((k, i, ar) => ar.indexOf(k) === i);
  //       this.VendorData = this.vendorList;
  //       this.vendorNameArray = this.VendorData.map(({ vendorName }) => vendorName)
  //       this.uniqueVendorNames = [...new Set(this.vendorNameArray)];
  //       console.log('//////////////',this.uniqueVendorNames);        
  //       this.grnvendorsMulti.next(this.uniqueVendorNames.slice());
  //       this.vendorMultiFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
  //         this.filterBanksMulti();
  //       });
  //     } 
  //     // this.clearedData = false
  //     if (this.selectVendorAll && !this.sharedFilterData.vendor) {
  //       this.toggleAllSelection(true);
  //     }
  //     this.dataSource.sort = this.sort;
  //     if (this.grnNumber) {
  //       this.findPrObjectById(this.grnNumber);
  //     }
  //     const selectedVendors = (this.vendorName && this.vendorName.value) ? this.vendorName.value.filter(vendor => vendor !== 1) : [];
  //     if (selectedVendors.length === 0) {
  //       this.itemName.setValue([]);
  //       this.dataSource.data = [];
  //     }
      
  //   }, err => { })
  // }

  getGrns(reqObj) {    
    if (this.filterDate.value && this.filterDate.value != undefined) {
      let date = new Date(this.filterDate.value);
      let isoString = date.toISOString();
      // console.log(date);
      // date.setHours(date.getHours() + 5);
      // date.setMinutes(date.getMinutes() + 30);

      // console.log(date.toISOString())
      
      reqObj['grnDate'] = isoString;
    } else {
      reqObj['grnDate'] = null;
    }
    // reqObj['grnType'] = this.selectedTypeName ? this.selectedTypeName : null;
    if (this.startDate.value && this.endDate.value) {
      reqObj['startDate'] = new Date(this.startDate.value).toISOString();
      reqObj['endDate'] = new Date(this.endDate.value).toISOString();
    } else {
      reqObj['startDate'] = null;
      reqObj['endDate'] = null;
    }
    reqObj['vendorIdData'] = ['All'];
    reqObj['piScreen'] = false;

    // reqObj['grnDate'] = null;
    // reqObj['vendorIdData'] = ['All'];
    // reqObj['startDate'] = null;
    // reqObj['endDate'] = null;
    // reqObj['piScreen'] = false;

    this.purchases.getGrn(reqObj).subscribe(data => {     
      this.actualData = data; 
      if (!this.dataSource)        
      data.forEach(element => {        
        let total = element.grnItems.reduce(function (accumulator, currentValue) {
          return accumulator + currentValue.totalPrice;
        }, 0);
        let otherCharge = element.otherCharges ? element.otherCharges : 0;
        element.totalGrnAmount = element.grandTotal ? element.grandTotal : (total + this.utils.truncateNew(otherCharge));
      });

      if (this.grnList == undefined) {
        this.grnList = data;
      }
      const uniqueItemNames = new Set();
      data.forEach(element => {
        element.itemList.forEach(element => uniqueItemNames.add(element));
      });
      this.itemNameList = Array.from(uniqueItemNames); 
      const savedItemNames = JSON.parse(localStorage.getItem('savedItemNames') || '[]');      
      if (savedItemNames.length > 0 && this.restaurantId === this.prevBranchId && !this.dataRefresh) {
        this.itemName.setValue(savedItemNames);
        this.itemNameChange(savedItemNames);
      } else {
        this.itemName.setValue(this.itemNameList.slice()); 
        this.itemNameChange(this.itemName.value);
      }

      this.itemBank = this.itemNameList
      this.itemBanks.next(this.itemBank.slice());
      this.itemFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
          this.vendorfilterBanks();
      });
      this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
      this.dataSource.paginator = this.paginator;

      const uniqueVendorNames = new Set();
      data.forEach((element) => {
        if (element.restaurantId === this.restaurantId) {
          uniqueVendorNames.add(element.vendorName);
        }
      });
      this.vendorNameList = Array.from(uniqueVendorNames); 
      const savedVendorNames = JSON.parse(localStorage.getItem('savedVendorNames') || '[]');      
      if (savedVendorNames.length > 0 && this.restaurantId === this.prevBranchId && !this.dataRefresh) {
        this.vendorName.setValue(savedVendorNames);
      } else {
        this.vendorName.setValue(this.vendorNameList.slice()); 
      }
      this.vendorBank = this.vendorNameList;
      this.grnvendorsMulti.next(this.vendorNameList.slice());
      this.vendorMultiFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
        this.filterBanksMulti();
      });
      this.dataRefresh = false;
      this.dataSource.sort = this.sort;
      if (this.grnNumber) {
        this.findPrObjectById(this.grnNumber);
      }      
    }, err => { })
  }

  itemNameChange(selectedItemNames: string[]) {                   
    if (selectedItemNames && selectedItemNames.length > 0) {
      this.dataSource.data = this.actualData.filter(element =>
        element.itemList.some(item => selectedItemNames.includes(item))
      );
    } else {
      this.dataSource.data = [];
    }
  }

  vendorChange(selectedVendors: string[]) {    
    this.itemName.setValue([]); 
    this.dataSource.data = [];   
    const filteredItemNames = new Set<string>();    
    if (selectedVendors && selectedVendors.length > 0) {
      this.actualData.forEach(grn => {
        if (selectedVendors.includes(grn.vendorName)) {
          grn.itemList.forEach(item => filteredItemNames.add(item));
        }
      });
    } else {
      this.itemNameList.forEach(item => filteredItemNames.add(item));
    }    
    this.itemBank = Array.from(filteredItemNames);
    this.itemBanks.next(this.itemBank.slice());
  }
  
  toggleAllVendors() {
    this.selectAllVendors = !this.selectAllVendors;
    if (this.selectAllVendors) {
      this.vendorName.setValue(this.vendorBank.slice());
    } else {
      this.vendorName.setValue([]);
    }
    this.vendorChange(this.vendorName.value);
  }

  toggleAllItems() {
    this.selectAll = !this.selectAll;
    if (this.selectAll) {
      this.itemName.setValue(this.itemBank.slice());
    } else {
      this.itemName.setValue([]);
    }
    this.itemNameChange(this.itemName.value);
  }

  protected vendorfilterBanks() {
    if (!this.itemBank) {
      return;
    }
    let search = this.itemFilterCtrl.value;
    if (!search) {
      this.itemBanks.next(this.itemBank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.itemBanks.next(
      this.itemBank.filter(item => item.toLowerCase().indexOf(search) > -1)
    );
  }

  protected filterBanksMulti() {
    if (!this.vendorBank) {
      return;
    }
    let search = this.vendorMultiFilterCtrl.value;
    if (!search) {
      this.grnvendorsMulti.next(this.vendorBank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }

    this.grnvendorsMulti.next(
      this.vendorBank.filter(item => item.toLowerCase().indexOf(search) > -1)
    );
  }

  dateFilter(){
    if (this.filterDate.value) {
      this.filterDate.setValue(undefined)
      this.vendorName.setValue('');
      this.itemName.setValue('');
      this.dataSource.data = [];
    }
  }

  findPrObjectById(grnId) {
    this.dataSource.data.find(obj => {
      if (obj['grnId'] === grnId) {
        this.detailedGrn(obj);
        return true;
      }
    })
  }

  sentenceCase(str) {
    if ((str === null) || (str === ''))
      return false;
    else
      str = str.toString();

    return str.replace(/\w\S*/g,
      function (txt) {
        return txt.charAt(0).toUpperCase() +
          txt.substr(1).toLowerCase();
      });
  }

  detailedGrn(obj) {
    let vendorObj = {
      vendorId: this.vendorName.value
    }
    let inputObj = {
      restaurantId: this.grnForm.value.branchSelection,
      selectedStartDate: this.startDate.value,
      selectedEndDate: this.endDate.value,
      clearDate: this.clearDate,
      vendor: vendorObj,
      vendorList: this.vendorList,
      branchFlag: true
    }
    this.sharedFilterService.getFilteredGrn.next(inputObj);
    this.sharedData.changeGrn(obj)
    this.router.navigate(['/home/<USER>'])
  }

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }

  public doDateFilter = (value: string) => {
    this.clearDate = value
        let reqObj: any = {
      tenantId: this.user.tenantId,
      restaurantId: this.restaurantId,
    }
    this.getGrns(reqObj);
  }

  resetForm() {
    this.searchText = ''
    this.selectedTypeName = ''
    this.searchValue = ''
    this.doFilter(this.searchValue);
    this.dataSource.data = this.actualData;
  }

  resetDate() {
    this.searchText = ''
    this.selectedTypeName = ''
    this.searchValue = ''
    this.doFilter(this.searchValue);
    this.clearDate = '';
    this.dataSource.data = this.actualData;
    this.invoiceId = undefined;
  }

  allFilter() {
    let tmp = this.actualData
    let prev = this.actualData
    Object.keys(this.filterKeys).forEach(element => {
      if (this.filterKeys[element] != 'All') {
        tmp = prev.filter(item =>
          item[element].toLowerCase() === this.filterKeys[element].toLowerCase())
        prev = tmp
      }
    });
    this.dataSource.data = tmp
  }

  selectVendor(vendor) {

    this.stopSecondApiCall = true;
    this.selectedvendorId = vendor
    let reqObj: any = {
      tenantId: this.user.tenantId,
      restaurantId: this.restaurantId,
    }
    this.getGrns(reqObj)
  }

  selectType(type) {
    let reqObj: any = {
      tenantId: this.user.tenantId,
      restaurantId: this.restaurantId,
    }
    this.getGrns(reqObj)
  }

  filterByBranch(restId) {
    this.prevBranchId = window.sessionStorage.getItem("restaurantId");
    this.restaurantId = restId.restaurantIdOld;
    this.branchSelected = true
    window.sessionStorage.setItem("restaurantId", this.restaurantId)
    if (this.sharedFilterData != '' && this.sharedFilterData.restaurantId == restId) {
      this.grnForm.get('branchSelection').setValue(this.sharedFilterData.restaurantId);
      this.branches = this.getBranchData
      this.startDate.setValue(this.sharedFilterData.selectedStartDate)
      this.endDate.setValue(this.sharedFilterData.selectedEndDate)
      this.clearDate = this.sharedFilterData.clearDate
      this.filterDate.setValue(this.sharedFilterData.clearDate)
    }
    let reqObj = {
      tenantId: this.user.tenantId,
      restaurantId: this.restaurantId
    }
    this.getGrns(reqObj)
  }

  filterByDate() {
    if (this.startDate.value && this.endDate.value) {
      this.grnList = undefined;
      this.selectVendorAll = true;
      let obj = {
        tenantId: this.user.tenantId,
        restaurantId: this.restaurantId
      }
      this.getGrns(obj)
    }
    else {
      this.utils.snackBarShowError('Please select start date and end date')
    }
  }

  rtvProcess(obj) {
    if (obj.grnType === 'po') {
      let vendorObj = {
        vendorId: this.vendorName.value
      }
      let inputObj = {
        restaurantId: this.grnForm.value.branchSelection,
        selectedStartDate: this.startDate.value,
        selectedEndDate: this.endDate.value,
        clearDate: this.clearDate,
        vendor: vendorObj,
        vendorList: this.vendorList,
        branchFlag: true
      }
      this.sharedFilterService.getFilteredGrn.next(inputObj);
      this.sharedData.changeGrn(obj)
      this.router.navigate(['/home/<USER>'])
    }
    else {
      this.utils.snackBarShowError('This feature is available only for purchase orders')
    }
  }

  getGrnbyId() {
    let reqObj = {}
    reqObj['tenantId'] = this.user.tenantId;
    reqObj['restaurantId'] = this.restaurantId;
    reqObj['invoiceId'] = this.invoiceId.trim();
    if (this.invoiceId && this.invoiceId != '') {
      this.purchases.getGrnbyInvoice(reqObj).subscribe(res => {
        this.dataSource.data = res.data;
        this.dataSource.data.forEach(element => {
          let total = element.grnItems.reduce(function (accumulator, currentValue) {
            return accumulator + currentValue.totalPrice;
          }, 0);
          let otherCharge = element.otherCharges ? element.otherCharges : 0;
          element.totalGrnAmount = element.grandTotal ? element.grandTotal : (total + this.utils.truncateNew(otherCharge));
        });
      })
    } else {
      this.getGrns(reqObj)
    }
  }

  getInvoiceSearchData(val) {
    this.dataSource.filter = val.target.value.trim().toLocaleLowerCase();
    if (val.target.value == '') {
      let reqObj = {}
      reqObj['tenantId'] = this.user.tenantId;
      reqObj['restaurantId'] = this.restaurantId;
      this.getGrns(reqObj)
    }
  }

  refreshdata() {
    this.dataRefresh = true;
    let reqObj = {}
    reqObj['tenantId'] = this.user.tenantId;
    reqObj['restaurantId'] = this.restaurantId;
    this.getGrns(reqObj)
  }

  isButtonDisabled(element: any): boolean {
    let permission = false;
    if (element.stockConversion) {
        let filteredItems = element.grnItems.filter(el => el.stockConversion);  // SC items filtered 
        let acceptableItems = filteredItems.filter(el => el.stockConversionQty === el.receivedQty); // checked received Qty with stockConversion Qty
        permission = filteredItems.length !== acceptableItems.length;
    }

    return element.grnType === "ibt" 
        || permission 
        || element.hasOwnProperty('Ibts') && element['Ibts'].length > 0
        || element['RTV'] === true 
        || (element['directIndent'] === true && element.hasOwnProperty('indentId') && (!Array.isArray(element['indentId']) || element['indentId'].length === 0));
}


detailedRtvList(obj) {
  let vendorObj = {
    vendorId: this.vendorName.value
  }
  let inputObj = {
    restaurantId: this.grnForm.value.branchSelection,
    selectedStartDate: this.startDate.value,
    selectedEndDate: this.endDate.value,
    clearDate: this.clearDate,
    vendor: vendorObj,
    vendorList: this.vendorList,
    branchFlag: true
  }
  this.sharedFilterService.getFilteredGrn.next(inputObj);
  this.sharedData.changeGrn(obj)
  this.router.navigate(['/home/<USER>'])
}


}
