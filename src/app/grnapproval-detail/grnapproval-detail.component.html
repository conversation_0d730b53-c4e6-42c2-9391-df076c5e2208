<div class="title">
  <button mat-raised-button class="button" style="float: left; margin-left: 0px;" (click)="goBack()">
    <mat-icon>keyboard_backspace</mat-icon> Back To List 
  </button>

  <div>
    <button mat-button mat-raised-button id="save-btn" class="button3" style="float: right;"
      [matTooltip]="reason" matTooltipPosition="left" (click)="rejectGrn()" [disabled] = "!showButton || isDone">
      Reject
    </button>
    <button mat-button mat-raised-button id="save-btn" class="button3" style="float: right;"
      [matTooltip]="reason" matTooltipPosition="left" (click)="approveGrn()" [disabled] = "!showButton || isDone">
      Approve
    </button>
  </div>
</div>

<div class="search-table-input fieldcontainer">
    <div class="row">
      <div class="col">
        <table class="table">
          <tbody>
            <tr>
              <th class="topItemkey" scope="row">Vendor Name</th>
              <td>{{ purReq.vendorDetails.vendorName }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="col">
        <table class="table">
          <tbody>
            <tr>
              <th class="topItemkey" scope="row">Order Number</th>
              <td>{{ purReq.poId }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="col">
        <table class="table">
          <tbody>
            <tr>
              <th class="topItemkey" scope="row">Receiving Branch</th>
              <td>{{ restaurantBranch | titlecase }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="col">
        <table class="table">
          <tbody>
            <tr>
              <th class="topItemkey" scope="row">Status</th>
              <td>{{ purReq.progress }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="col">
        <table class="table">
          <tbody>
            <tr>
              <th class="topItemkey" scope="row">Issue Date</th>
              <td>{{purReq.eta | date: "EEEE, MMMM d, y"}}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div style="margin-top: 15px; padding-right: 10px;">
      <span>Select Item Price History :</span>
      <mat-checkbox [(ngModel)] = "allVendorsChecked" (change)="onCheckboxChange('allVendors')">All Vendors</mat-checkbox>
      <mat-checkbox [(ngModel)] = "currentVendorChecked" (change)="onCheckboxChange('currentVendor')">Current Vendor</mat-checkbox>
    </div>
</div>

<mat-card class="matcontent">
  <mat-card-content>
    <table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort>
      <ng-container matColumnDef="index">
        <th mat-header-cell *matHeaderCellDef class="tableId"><b>#</b></th>
        <td mat-cell *matCellDef="let element; let i = index" class="name-cell tableId">
          {{ i + 1 }}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="actionBtns" >
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Actions</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <button mat-icon-button (click)="r(element)">
            <mat-icon>done</mat-icon>
          </button>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="itemName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Inventory Item</b>
        </th>
        <td mat-cell *matCellDef="let element">
          {{ element.itemName | titlecase }}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="pkgName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Pkg Name</b></th>
        <td mat-cell *matCellDef="let element"> {{element.packages[0].packageName | titlecase}} </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="itemCode">
        <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell"><b> Item Code</b> </th>
        <td mat-cell *matCellDef="let element" class="name-cell"> {{element.itemCode}} </td>
        <td mat-footer-cell *matFooterCellDef>Total</td>
      </ng-container>

      <ng-container matColumnDef="orderQty">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Order Quantity</b>
        </th>
        <td mat-cell *matCellDef="let element">
          {{ element.quantity }}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="pendingQty">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Pending Quantity</b>
        </th>
        <td mat-cell *matCellDef="let element">
          {{ element.pendingQty }}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="vendor">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Brand</b></th>
        <td mat-cell *matCellDef="let element">{{ element.brand.name }}</td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="unitPrice">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Unit Cost</b>
        </th>
        <td mat-cell *matCellDef="let element">{{ this.utils.truncateNew(element.packages[0].packagePrice)}}</td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="totalValue">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Total (incl.tax,etc)</b></th>
        <td mat-cell *matCellDef="let element">
          {{this.utils.truncateNew(
          ((element.unitPrice ) *
          element.quantity) + element.cessAmt + element.extraAmt - element.discAmt )
          }}
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getTotal())}}</td>
      </ng-container>

      <ng-container matColumnDef="subTotal">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Total (excl.tax)</b></th>
        <td mat-cell *matCellDef="let element">
          {{this.utils.truncateNew(
          ((element.packages[0].packagePrice ) *
          element.quantity))
          }}
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getSubTotal())}}</td>
      </ng-container>

      <ng-container matColumnDef="taxAmt">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Tax Amt</b></th>
        <td mat-cell *matCellDef="let element">
          {{this.utils.truncateNew(
          ((element.taxAmount/element.quantity ) *
          element.quantity))
          }}
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getTaxTotal())}}</td>
      </ng-container>

      <ng-container matColumnDef="rate">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Tax Rate</b></th>
        <td mat-cell *matCellDef="let element">
          <!--use this if it required as input field-->
          <!-- <input class="input1" type="number" step="0.01" min="0" [(ngModel)]=" element.taxRate"
          (keyup)="getTotalPrCost($event , element)" /> --> 
          {{element.taxRate}}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>  

      <ng-container matColumnDef="priceHistory">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b> Price History</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <button class="mat-icon-button" (click)="togglePriceList(element, $event)">
            <mat-icon>info</mat-icon>
          </button>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>


      <ng-container matColumnDef="Action">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b> Action</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <button *ngIf="
              element.itemStatus != 'complete' && user.uType === 'restaurant'
            " class="mat-icon-button" matTooltip="Add or Edit Packaging Sizes" (click)="displayPackages(element)">
            <mat-icon>edit</mat-icon>
          </button>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      <tr mat-footer-row *matFooterRowDef="displayedColumns"></tr>
    </table>
    <div class="dataMessage" *ngIf="dataSource?.data.length == 0"> No Data Available </div>
  </mat-card-content>
  <div *ngIf="isPriceListVisible" class="autoComplete" style="float: right;">
    <mat-grid-list cols="12" rowHeight="30px">
      <mat-grid-tile colspan="2">GRN ID</mat-grid-tile>
      <mat-grid-tile colspan="2">Date</mat-grid-tile>
      <mat-grid-tile colspan="4">Vendor Name</mat-grid-tile>
      <mat-grid-tile colspan="2">Pkg Name</mat-grid-tile>
      <mat-grid-tile colspan="0.5">Rate</mat-grid-tile>
      <mat-grid-tile colspan="0.5">Qty</mat-grid-tile>
    </mat-grid-list>
    <mat-divider [inset]="true"></mat-divider>
    <mat-option *ngFor="let val of filteredUnitPrice" class="matOption">
      <mat-grid-list cols="12" rowHeight="20px">
        <mat-grid-tile colspan="2">{{ val.grnId || '-' }}</mat-grid-tile>
        <mat-grid-tile colspan="2">{{ val.createTs | date:'dd-MM-yyyy' || '-' }}</mat-grid-tile>
        <mat-grid-tile colspan="4">{{ val.vendorName || '-' }}</mat-grid-tile>
        <mat-grid-tile colspan="2">{{ val.packageName || '-' }}</mat-grid-tile>
        <mat-grid-tile colspan="0.5">{{ val.unitPrice || '-' }}</mat-grid-tile>
        <mat-grid-tile colspan="0.5">{{ val.quantity || '-' }}</mat-grid-tile>
      </mat-grid-list>
    </mat-option>
  </div>

  <div class="mt-3 mb-3">
    <div *ngFor="let tax of purReq.otherTax; let i = index" style="padding-bottom: 22px !important;">
      <span class="otherchargeHeading topItemkey">{{ tax.taxName }}</span>
      <input matInput class="outline otherTax" [(ngModel)]="tax.value" disabled/>
    </div>    
    <div>
      <span class="otherchargeHeading topItemkey">Grand total ₹</span>
      <input matInput class="outline othercharge" [(ngModel)]="purReq.grandTotal" placeholder="Total ₹" disabled />
    </div>
  </div>

</mat-card>