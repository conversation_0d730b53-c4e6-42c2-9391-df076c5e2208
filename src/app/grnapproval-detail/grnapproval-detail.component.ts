import { Component, OnInit, HostListener } from '@angular/core';
import { ShareDataService, PurchasesService, AuthService } from '../_services/';
import { MatSort, Sort, MatTableDataSource, MatDialog } from '@angular/material';
import { SelectionModel } from '@angular/cdk/collections';
import { GlobalsService } from '../_services/globals.service';
import { Location } from '@angular/common';
import { UtilsService } from '../_utils/utils.service';
import { NotificationService } from '../_services/notification.service';
import { Router } from '@angular/router';
import { SimpleDialogComponent } from '../_dialogs/simple-dialog/simple-dialog.component';

@Component({
  selector: 'app-grnapproval-detail',
  templateUrl: './grnapproval-detail.component.html',
  styleUrls: ['./grnapproval-detail.component.scss','./../../common-dark.scss']
})
export class GrnapprovalDetailComponent implements OnInit {
  purReq: any;
  dataSource: MatTableDataSource<any>;
  displayedColumns: String[];
  InvNo: any;
  dialogRef: any;
  restaurantBranch: any;
  dialogRefr: any;
  selection = new SelectionModel<any>(true, []);
  user: any;
  approveFlag: boolean = true
  approveBtnText: String
  reason: String
  allExtraFieldFlag: any;
  showButton: boolean = true ;
  isDone: boolean = false ; 
  allVendorsChecked: boolean = false ;
  currentVendorChecked: boolean = true ;
  filteredUnitPrice: any;
  isPriceListVisible = false;
  constructor(private shareData: ShareDataService,
    private location: Location,
    private notifyService: NotificationService,
    private utils: UtilsService,
    private purchases: PurchasesService,
    private router: Router,
    private auth: AuthService,
    private dialog: MatDialog, 
    ) {
    this.user = this.auth.getCurrentUser()
  }

  ngOnInit() {
    if ('name' in this.user){
      this.approveBtnText = this.user.name + ': Approve'
    }else{
      this.approveBtnText = 'Approve'
    }

    this.displayedColumns = ['index','itemCode', 'itemName', 'pkgName', 'orderQty', 'unitPrice', 'subTotal','rate','taxAmt', 'totalValue','priceHistory']
    this.shareData.currPurOrder.subscribe(order => {
      this.purReq = order      
      this.purReq.approvalDetail ? this.checkApprovalProcess() : null ;
      if (!this.purReq.mId)
        this.location.back();
    });
    this.dataSource = new MatTableDataSource<any>();
    if(this.purReq.hasOwnProperty('poItems'))
      this.processInputData(this.purReq);
    else  
      this.router.navigate(['/home']);
  }


  processInputData(purReq) {
    this.dataSource.data = purReq.poItems;
    this.restaurantBranch = this.purReq.restaurantId.split('@')[1]
    this.dataSource.data.forEach(item => {
      if (!item.hasOwnProperty('cessAmt')) {
        item.cessAmt = 0
      }
      if (!item.hasOwnProperty('discAmt')) {
        item.discAmt = 0
      }
      if (!item.hasOwnProperty('extraAmt')) {
        item.extraAmt = 0
      }
    });
    this.dataSource.data = [...this.dataSource.data];
  }

  cancel() {
    this.location.back()
  }

  checkApprovalProcess() {
    let status = this.purReq.poApprovalDetail.filter(
      (el) => el.role === this.user.role && el.status === "pending"
    );
    this.showButton = status.length == 0 ? false : true;
  }

  setEachItemStatus() {
    this.selection.selected.forEach(selection => {
      selection.pendingQty = selection.quantity - selection.receivedQty
      if (selection.receivedQty > 0)
        selection.pendingQty === 0 ? selection.itemStatus = 'complete' : selection.itemStatus = 'partial'
    });
  }

  goBack() {
    this.location.back()
  }

  getTotal() {
    let totalPrice = 0
    this.dataSource.data.forEach(element => {
      totalPrice += (element.quantity * element.unitPrice)
    });
    return totalPrice
  }

  getSubTotal() {
    let subTotalSum = 0
    this.dataSource.data.forEach(element => {
      subTotalSum += element.quantity * element.packages[0].packagePrice
    });
    return subTotalSum
  }

  getTaxTotal() {
    let taxTotal = 0
    this.dataSource.data.forEach(element => {
      taxTotal += element.taxAmount
    });
    return taxTotal
  }

  getFieldTotal(key: string) {
    return this.utils.getTotal(this.dataSource.data, key);
  }

  approveGrn() {
    let obj = {}
    obj['tenantId'] = this.user.tenantId
    obj['restaurantId'] = this.purReq.restaurantId
    obj['role'] = this.user.role
    obj['poId'] = this.purReq.poId
    obj['appCat'] = this.purReq.approvalCategory
    this.purchases.approveGrn(obj).subscribe(data => {
      if (data.success) {
        this.utils.snackBarShowSuccess(`${data.message}`)
        this.approveFlag = true ;
        this.isDone = true ;
      }
      else {
        this.utils.snackBarShowError('Something went wrong.Please try again later')
      }
    })
  }

  rejectGrn() {
    let obj = {}
    obj['tenantId'] = this.user.tenantId
    obj['restaurantId'] = this.purReq.restaurantId
    obj['role'] = this.user.role
    obj['poId'] = this.purReq.poId
    obj['appCat'] = this.purReq.approvalCategory
    this.dialogRef = this.dialog.open(SimpleDialogComponent, {
      data: {
        title: 'Reject Reason',
        msg: 'Enter Reason(min 10 and max 40 characters allowed)',
        inputFromUser: { 'Reason': '' },
        ok: function () {
          this.dialogRef.afterClosed().subscribe(res => {
            this.reason = res['Reason'];
            if(res['Reason'] == ''){
              this.utils.snackBarShowInfo('Please provide valid reason here..')
            }else{
              obj['reason'] = this.reason;
              this.purchases.rejectGrn(obj).subscribe(data => {
                if (data.success ) {
                  this.utils.snackBarShowSuccess(`${data.message}`)
                  this.approveFlag = true ;
                  this.isDone = true ;
                }
                else {
                  this.utils.snackBarShowError('Something went wrong.Please try again later')
                }
              }, err => console.error(err))
            }
          })
        }.bind(this)
  
      }
    });
  }

  onCheckboxChange(checkbox: string) {
    if (checkbox === 'allVendors') {
      this.currentVendorChecked = false;
      this.isPriceListVisible = false;
    } else if (checkbox === 'currentVendor') {
      this.allVendorsChecked = false;
      this.isPriceListVisible = false;
    }
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    this.isPriceListVisible = false;
  }

  togglePriceList(element: any, event: MouseEvent): void {
    event.stopPropagation(); 
    this.getLastGrnPrice(element);
  }

  getLastGrnPrice(element){
    this.isPriceListVisible = true;
    let params={
      'restaurantId':this.purReq.restaurantId,
      'vendorId':this.purReq.vendorDetails.vendorId,
      'itemCode':element.itemCode,
      'packageName':element.packages[0].packageName,
    }
    if (this.allVendorsChecked) {
      params['allVendors'] = true;
    }
    this.purchases.getLastGrnPrice(params).subscribe(data => {
      if (data['success']){
        this.filteredUnitPrice = data.priceList
      }
    }) 
  }

}
