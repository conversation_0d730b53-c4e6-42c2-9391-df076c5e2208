import { Component, OnInit } from '@angular/core';
import { AuthService } from '../_services/auth.service';
import { UtilsService } from '../_utils/utils.service';
import { GlobalsService } from '../_services';
@Component({
  selector: 'app-home-layout',
  templateUrl: './home-layout.component.html',
  styleUrls: ['./home-layout.component.scss']
})
export class HomeLayoutComponent implements OnInit {

  navLinks = [{ name: 'home', icon: 'home' }];
  user = this.auth.getCurrentUser();
  constructor(private auth: AuthService, private utils: UtilsService) {

  }
  ngOnInit() {

    sessionStorage.setItem(GlobalsService.branches, JSON.stringify(this.auth.getCurrentUser().restaurantAccess));
    if (!(this.user.role === 'purchaseController' || this.user.role === 'vendor')) this.auth.getDbRange(this.utils.getUserReqObj(this.auth.getCurrentUser()))
      .subscribe(
        (res: any) => {
          if((res !=null) && ("desc" in res)){
            if (res.desc === GlobalsService.success)
              sessionStorage.setItem(GlobalsService.dateRange, JSON.stringify(res.dateObj));
          }
        }, err => {
          console.log('dateErr', err);
        }
      );
  }

}
