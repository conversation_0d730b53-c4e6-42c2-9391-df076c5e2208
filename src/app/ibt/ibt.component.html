<div class="topItemsofIBT">
  <form [formGroup]="ibtForm" *ngIf="user.role != 'kitchenManager'">
          <mat-form-field appearance="none">
            <label>Source Branch</label>
            <mat-select formControlName="fromBranch" placeholder="Sender Branch" class="outline" (selectionChange)="selectFromBranch($event.value)">
                <mat-option *ngFor="let branch1 of branchesData" [value]="branch1" [disabled]="branch1.branchName === sourceBranch">
                {{ branch1.branchLocation }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="none" class="ml-2">
            <label>Receipient Branch</label>
            <mat-select class="outline" placeholder="Receipient Branch" formControlName="toBranch" (selectionChange)="selectReceipient($event.value)">
              <mat-option *ngFor="let branch1 of this.branches" [value]="branch1" [disabled]="branch1.branchName === sourceReceipient">
                {{ branch1.branchLocation }} 
              </mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="none" class="ml-2">
            <label>Expected Date</label>
            <input matInput class="outline" [matDatepicker]="picker" formControlName="eta" placeholder="Expected Date" />
            <mat-datepicker-toggle matSuffix [for]="picker">
              <mat-icon matDatepickerToggleIcon><img src="./../../assets/calender.png" /></mat-icon>
            </mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
          </mat-form-field>
  
        <div class="findBtnIbt ml-2">
          <button *ngIf="user.role != 'kitchenManager'" mat-raised-button [disabled]="ibtForm.invalid" (click)="findData()"
            class="button3">
            Find
          </button>
        </div>
  
        <button *ngIf="user.role != 'kitchenManager'" mat-raised-button [disabled]="ibtForm.invalid || this.items.length == 0"
          (click)="generateIbt()" class="button3" style="float: right; margin-top: 43px !important; margin-left: 10px !important;">
          Generate IBT
        </button>
  
          <button *ngIf="user.role === 'kitchenManager'" mat-raised-button (click)="raiseKitchenIndent()" class="button3"
          style="float: right; margin-top: 43px !important;">
          Raise Indent
        </button>
  </form>
</div>

  <mat-card *ngIf="isShowtable">
    <div>
    <mat-form-field appearance="none">
      <label>Search</label>
      <input matInput type="text" (keyup)="doFilter($event.target.value)" placeholder="Search" class="outline"
        [(ngModel)]='searchText' />
      <mat-icon matSuffix (click)="clear()" class="closebtn">close</mat-icon>
    </mat-form-field>

    <mat-form-field appearance="none" class="ml-3">
      <label>Item Type</label>
      <mat-select placeholder="Item Type" [formControl]="ItemType" class="outline">
        <mat-option *ngFor="let itemType of ItemTypeList" [value]="itemType" (click)="selectItemType(itemType)">
          {{ itemType | titlecase }}
        </mat-option>
      </mat-select>
    </mat-form-field>

    <mat-form-field appearance="none" class="ml-2">
      <label>Category</label>
      <mat-select placeholder="Category" class="outline" [formControl]="category">
        <mat-option *ngFor="let cat of categoryList" [value]="cat" (click)="selectCategory(cat)">
          {{ cat | titlecase}}
        </mat-option>
      </mat-select>
    </mat-form-field>

    <mat-form-field appearance="none" class="ml-2">
      <label>Sub Category</label>
      <mat-select placeholder="Subcategory" [formControl]="Subcategory" class="outline">
        <mat-option *ngFor="let subCat of subCategoryList" [value]="subCat" (click)="selectSubCat(subCat)">
          {{ subCat | titlecase }}
        </mat-option>
      </mat-select>
    </mat-form-field>

    <mat-slide-toggle class="togglebutton ml-2" (change)="preview()">Preview</mat-slide-toggle>

    </div>

    <div class="clearfix" style="clear: both;"></div>
    <div>
      <label class="ipText">Default transfer by :  </label>
      <mat-radio-group [(ngModel)]="selectedOption" (change)="portionChange()">
        <mat-radio-button [value]="false">UOM</mat-radio-button>
        <mat-radio-button [value]="true">PORTION</mat-radio-button>
      </mat-radio-group>
    </div>

      <table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort>
        <ng-container matColumnDef="itemName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Item Name</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.itemName.split('|')[2] | titlecase }}
          </td>
        </ng-container>

        <ng-container matColumnDef="entryType">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Entry Type</b>
          </th>
          <td mat-cell *matCellDef="let element">{{ element.entryType }}</td>
        </ng-container>

        <ng-container matColumnDef="itemType">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Item Type</b>
          </th>
          <td mat-cell *matCellDef="let element">{{ element.ItemType }}</td>
        </ng-container>

        <ng-container matColumnDef="category">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Category</b>
          </th>
          <td mat-cell *matCellDef="let element">{{ element.category }}</td>
        </ng-container>

        <ng-container matColumnDef="subCategory">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Sub Category</b>
          </th>
          <td mat-cell *matCellDef="let element">{{ element.subCategory }}</td>
        </ng-container>

        <ng-container matColumnDef="pkgName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Pkg Name</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.packageName }}
          </td>
        </ng-container>

        <ng-container matColumnDef="inStock">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> In Stock</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ this.utils.truncateNew(element.inStock) }}
          </td>
        </ng-container>

        <ng-container matColumnDef="uom">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>UOM</b>
          </th>
          <td mat-cell *matCellDef="let element">{{ element.uom }}</td>
        </ng-container>

        <ng-container matColumnDef="quantity">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Quantity </b>
          </th>
          <td mat-cell *matCellDef="let element">
            <div class="input-svg">
            <div [ngClass]="{'input-down': element.ItemType === 'SubRecipe', 'input-box2': element.ItemType !== 'SubRecipe'}">
            <input [ngClass]="{'input-box1 no-stepper': element.ItemType === 'SubRecipe', 'input-box2': element.ItemType !== 'SubRecipe'}" type="number" step="0.01" min="0" [(ngModel)]="element.quantity"
              (keyup)="getTotalIndentCostQuantity($event , element)" [ngModelOptions]="{standalone: true}"
              (focus)="focusFunctionWithOutForm(element,'quantity')" (focusout)="focusOutFunctionWithOutForm(element,'quantity')" />
              <mat-select *ngIf="element.ItemType === 'SubRecipe' && element.hasOwnProperty('portionWeight')" class="dropdown" [(value)]="element.selectedOption" (selectionChange)="convertToQty(element)">
                <mat-option value="uom">UOM</mat-option>
                <mat-option value="portion">Portion</mat-option>
              </mat-select>
            </div>
            
              <div class="iconDiv" *ngIf="element.ItemType === 'SubRecipe' && element.hasOwnProperty('portionWeight') && element.selectedOption === 'portion'">           
                <svg (mouseover)="onMouseOver(element)" (mouseout)="onMouseOut()" xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-calculator calIcon" viewBox="0 0 20 20">
                  <path d="M12 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1zM4 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2z"/>
                  <path d="M4 2.5a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-.5.5h-7a.5.5 0 0 1-.5-.5zm0 4a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5zm0 3a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5zm0 3a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5zm3-6a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5zm0 3a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5zm0 3a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5zm3-6a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5zm0 3a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5z"/>
                </svg>            
              </div>
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="unitPrice">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> WAC(incl.tax,etc) </b>
          </th>
          <td mat-cell *matCellDef="let element">
            <input class="input1" type="number" step="0.01" min="0"
              [(ngModel)]="element.withTaxPrice" [ngModelOptions]="{standalone: true}" 
              (focus)="focusFunctionWithOutForm(element,'withTaxPrice')" (focusout)="focusOutFunctionWithOutForm(element,'withTaxPrice')"/>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>
    <div class="dataMessage" *ngIf="dataSource?.data.length == 0"> No Data Available </div>
    <mat-paginator [showTotalPages]="5" [pageSize]='10' [pageSizeOptions]="pageSizes"></mat-paginator>
  </mat-card>

  <div class="custom-tooltip" [hidden]="!showTooltip">
    <div  class="parentClass">
      <div class="d-flex m-2" style="font-weight: bold; font-size: small;">
        <div class="custom-tooltip-Heading">
          Per Portion Weight
        </div>
        <div>
          {{ this.truncateNew(hoveredElement?.portionWeight / 1000) }} {{ (hoveredElement?.defaultUOM) }}
        </div>
      </div>
      <div class="d-flex m-2" style="font-weight: bold; font-size: small;">
        <div class="custom-tooltip-Heading">
          Current Quantity
        </div>
        <div>
          {{ this.truncateNew(hoveredElement?.convertedWeight) }} {{ (hoveredElement?.defaultUOM) }}
        </div>
      </div>
      <div class="d-flex m-2" style="font-weight: bold; font-size: small;">
        <div class="custom-tooltip-Heading">
          No of Portion
        </div>
        <div>
          {{ this.truncateNew(hoveredElement?.quantity) }}
        </div>
      </div>
    </div>
  </div>
