// mat-form-field {
//   // width: 29%;
//   font-size: 0.8rem;
//   margin: 1%
// }

// #itemArray mat-form-field{
//   margin: 1%;
//   width: 20%
// }

// mat-card{
//   width: 95%
// }

// button{
//   margin: 0.5%
// }

// fieldset{
//   margin-top: 1%
// }

// h3{
//   margin-left: 5%
// }

// ::ng-deep .deletebtn {
//   background-color: rgba(0, 0, 0, 0) !important;
// }

// ::ng-deep .deletebtn2{
//   background-color: rgba(0, 0, 0, 0) !important;
// }

.togglebutton{
  float: right;
  margin-top: 5px !important;
}
.topInputItems{
  display: flow-root !important;
}

.findBtnIbt{
  display: initial;
  margin-top: 41px;
  position: absolute;
}

.topItemsofIBT{
  margin-left: 2%;
  margin-right: 2%;
  display: flow-root !important;
}

.iconDiv{
  margin-left: 4px;
}

.input-svg{
  display: flex;
  align-items: center;
}

.input-down {
  display: flex;
  border: 1px solid #ccc;
}

.custom-tooltip {
  position: absolute;
  background-color: #333;
  color: #fff;
  padding: 5px;
  border-radius: 4px;
  font-size: 14px;
  z-index: 1000; 
  width: 250px;
  left: 783px;
  top: 108px;
}

.custom-tooltip-Heading{
  width: 150px;
}

.custom-tooltip-Value{
  width: 74px;
}

.no-stepper::-webkit-outer-spin-button,
.no-stepper::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.input-box1{
  text-align: center;
  width: 65px;
  height: 24px;
  background: white;
  border: #191919;
  color: black;
  font-size: 12px !important;
}

.input-box2 {
  text-align: center;
  width: 73px;
  height: 25px;
  background: white;
  border: #191919;
  margin-right: 5px;
  color: black;
  font-size: 12px !important;
}

.ipText{
  text-align: center;
  font-size: medium;
  // font-weight: bold;
  font-size: 16px !important;
  margin-top: 20px !important;
  margin-bottom: 10px !important;
  margin-right: 10px !important;
  opacity: 0.9 !important;
}

::ng-deep .mat-radio-label-content {
  padding-left: 8px;
  margin-right: 10px;
  font-size: 15px;
  font-weight: bold;
  opacity: 0.9 !important;
}
