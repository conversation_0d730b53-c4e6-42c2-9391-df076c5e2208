import { Component, OnInit, ViewChild } from "@angular/core";
import { FormBuilder, FormGroup, Validators,FormControl ,FormArray } from "@angular/forms";
import { AuthService,BranchTransferService, ShareDataService } from "../_services";
import { UtilsService } from "../_utils/utils.service";
import { GlobalsService } from "../_services";
import { Observable } from "rxjs";
import { map, startWith } from "rxjs/operators";
import { PreviewIbtComponent } from "../_dialogs/preview-ibt/preview-ibt.component";
import { MatDialog, MatTableDataSource } from "@angular/material";
import { NotificationService } from '../_services/notification.service';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material';
import { MomentDateModule, MomentDateAdapter } from '@angular/material-moment-adapter';
import { Router } from "@angular/router";
import { MatPaginator, MatSort } from '@angular/material';

export const MY_FORMATS = {
  parse: {
      dateInput: 'LL'
  },
  display: {
      dateInput: 'DD-MM-YYYY',
      monthYearLabel: 'YYYY',
      dateA11yLabel: 'LL',
      monthYearA11yLabel: 'YYYY'
  }
};

@Component({
  selector: "app-ibt",
  templateUrl: "./ibt.component.html",
  styleUrls: ["./ibt.component.scss", "./../../common-dark.scss"],
  providers:[{ provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
            { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }
          ]
})
export class IbtComponent implements OnInit {
  ibtForm: FormGroup;
  maxDispatchLimit: any = 0;
  dataObj: any = {};
  tableType = 0;
  user: any;
  submitAttempted: boolean;
  branches: any[];
  branchInvItems: any[];
  fromBrachFilterOptions: Observable<any[]>;
  toBrachFilterOptions: Observable<any[]>;
  invItemFilterOptions: any[] = [];
  sourceBranch: any;
  sourceReceipient: any;
  dataSource: MatTableDataSource<any>;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  displayedColumns: any[];
  pageSizes: any;
  newData: { quantity: number; };
  isShowButton : boolean = false
  isShowtable : boolean = false
  indentPreview: boolean = false;
  categoryList = ['All'];
  subCategoryList = ['All'];
  ItemTypeList = ['All'];
  searchText: any;
  category = new FormControl('', [Validators.required]);
  Subcategory = new FormControl();
  ItemType = new FormControl();
  filterKeys = { ItemType: 'All', category: 'All', subCategory: 'All' }
  subCatList: any = [];
  initSubCategoryList: any = [];
  initCategoryList: any = [];
  items = [];
  getBranchData: any[]
  branchesData: any[]
  branchesDataRecp: any;
  showTooltip: boolean = false;
  hoveredElement: any = null;
  searchValue: any = ''
  selectedOption: boolean = false;

  constructor(
    private notifyService: NotificationService,
    private auth: AuthService,
    private fb: FormBuilder,
    private branchTransfer: BranchTransferService,
    private dialog: MatDialog,
    private utils: UtilsService,
    private router: Router,
    private sharedData: ShareDataService,

  ) {
    this.user = this.auth.getCurrentUser();

  }

  ngOnInit() {
    this.invItemFilterOptions = [];
    if (this.user.role != GlobalsService.kitchenManager) {
      this.ibtForm = this.fb.group({
        fromBranch: ["", Validators.required],
        toBranch: ["", Validators.required],
        eta: ["", Validators.required],
        items: this.fb.array([]),
      });
      this.branchInvItems = [];
    } else {
      this.ibtForm = this.fb.group({
        fromBranch: ["a", Validators.required],
        toBranch: ["s", Validators.required],
        eta: [new Date(), Validators.required],
        items: this.fb.array([]),
      });

      this.branchTransfer
        .getIbtInvWithPkg({
          tenantId: this.user.tenantId,
          restaurantId: this.user.restaurantAccess[0].restaurantIdOld,
        })
        .subscribe(
          (data) => {            
            if (data)
              this.branchInvItems = data.invItems.filter(
                (item) => item.inStock > 0
              );
            this.addItemToForm();
          },
          (err) => console.error(err)
        );
    }
    this.branchesDataRecp = this.user.restaurantAccess;
    this.sharedData.sharedBranchData.subscribe((val) => {
      this.getBranchData = val;
      if(this.getBranchData.length == 0 ){
        this.branchesData = this.user.restaurantAccess;
      }else if(this.getBranchData.length == 1){
        const toSelect = this.getBranchData.find(data => data.branchName == this.getBranchData[0].branchName);
        this.ibtForm.get('fromBranch').patchValue(toSelect);
        this.branchesData = this.getBranchData
        this.selectFromBranch(this.ibtForm.value.fromBranch);
      }else{
        this.branchesData = this.getBranchData
      }
  });

    this.getBranches();
    this.displayedColumns = ['category','subCategory','itemName','itemType','entryType','pkgName', 'inStock','uom',  'quantity' , 'unitPrice'];
  }

  isFieldInvalid(field: any) {
    let isInvalid: boolean =
      (!this.ibtForm.get(field).valid && this.ibtForm.get(field).touched) ||
      (this.ibtForm.get(field).untouched && this.submitAttempted);
    return isInvalid;
  }

  truncateNew(number, precision = 3) {
    if (typeof number === 'string') {
        number = parseFloat(number);
    }
    if (isNaN(number)) {
        number = 0;
    }
    const factor = Math.pow(10, precision);
    const truncatedNumber = Math.trunc(number * factor) / factor;
    return Math.floor(truncatedNumber * Math.pow(10, precision)) / Math.pow(10, precision);
}

  generateIbt() {
    this.preview();
    const fromBranch = this.ibtForm.value.fromBranch;
    const toBranch = this.ibtForm.value.toBranch;
    const eta = this.utils.dateCorrection(this.ibtForm.value.eta);
    const items = this.dataSource.data.filter(
      (item) => item.name != "" && item.quantity > 0
    );

    items.forEach((item)=> {
      item['price'] = this.utils.truncateNew(item['actualPrice'])
      if ( item['uom'] === 'portion') {
        item['uom'] = item['defaultUOM']
        item['inStock'] = this.utils.truncateNew(item['inStock'])
        item['inStock'] = this.utils.truncateNew(item['currentStock'])
        let conversionCoefficient =  item['defaultUOM'] == 'NOS' ? 1: 1000 ;
        let portionWeight = (item.portionWeight * 1000);
        let convertedWeight = this.utils.truncateNew((portionWeight / conversionCoefficient) * item.quantity);
        item['quantity'] = convertedWeight / 1000
      }
    })

    let obj = {}
    obj['eta'] = eta
    obj['fromBranch'] = fromBranch
    obj['toBranch'] = toBranch
    obj['items'] = items
    if (fromBranch.branchName != toBranch.branchName) {
      if (items.length > 0) {
        this.isShowButton = true;
        this.branchTransfer
          .createIbt({
            tenantId: this.user.tenantId,
            restaurantId: this.user.restaurantAccess[0].restaurantIdOld,
            uId: this.user.mId,
            ibt: obj
          })
          .subscribe((data: any) => {  
            this.preview();          
            this.ibtDialog(data.newIbt);
            this.ibtForm.reset();
          });
      } else {
        this.utils.snackBarShowWarning("Add Quantity to IBT.");
      }
    } else {
      this.utils.snackBarShowError("Both from and to branches are the same");
    }
  }

  displayBranchName(option?: any): string | undefined {
    return option ? option.branchName : undefined;
  }

  displayItemName(option?: any): string | undefined {
    return option ? option.itemName : undefined;
  }
  addItemToForm() {
    const items = this.ibtForm.controls.items as FormArray;
    items.push(
      this.fb.group({
        name: "",
        quantity: 0,
        unitPrice: 0,
        uom: "",
        itemCode: "",
        entryType: "",
        pkgName: "",
        pkgQty: ""
      })
    );
    const newFormGroup = items.at(items.length - 1);
    this.invItemFilterOptions.push(
      newFormGroup.get("name").valueChanges.pipe(
        startWith(""),
        map((value) => this._filter(value, this.branchInvItems, "itemName"))
      )
    );
  }

  getBranches() {
    this.auth.getBranches({
        tenantId: this.user.tenantId,
      }).subscribe(
        (data: any) => {
          if (data) this.branches = data;
          this.intialiseFilters();
        },
        (err) => console.error(err)
      );
  }

  removeItemFromForm(remItem) {
    const items = this.ibtForm.controls.items as FormArray;
    items.removeAt(this.ibtForm.value.items.indexOf(remItem));
  }

  selectFromBranch(e) {   
    this.sourceReceipient = e.branchName;
  }

  selectReceipient(e){
    this.sourceBranch  = e.branchName;
  }

  private intialiseFilters() {
    this.fromBrachFilterOptions = this.ibtForm
      .get("fromBranch")
      .valueChanges.pipe(
        startWith(""),
        map((value) => this._filter(value, this.branches, "branchName"))
      );
    this.toBrachFilterOptions = this.ibtForm.get("toBranch").valueChanges.pipe(
      startWith(""),
      map((value) => this._filter(value, this.branches, "branchName"))
    );
  }

  private _filter(value: string, arr: any[], key: string): any[] {
    const filterValue = typeof value === 'string' ? value.toLowerCase() : '';
    return arr.filter((option) => 
      new RegExp(`${filterValue}`, "gi").test(option[key])
    );
  }


  private ibtDialog(data) {
    this.router.navigate(['/home'])
    this.dialog.open(PreviewIbtComponent, {
      width: "800px",
      data: {
        id: data.ibtId,
        title: "IBT Id",
        items: data.items,
        fromBranch: data.fromBranch,
        toBranch: data.toBranch,
        eta: data.eta,
        createTs: data.createTs,
        ok: function () {
          this.location.back();
        }.bind(this),
      },
    });
  }

  raiseKitchenIndent() {
    const items = this.ibtForm.controls.items as FormArray;
    if (items.length > 0) {
      this.ibtForm.value.items.map((item) => {
        item.name = item.name.itemName;
      });
      let ibt: any = this.ibtForm.value;
      ibt.items = this.utils.arrToObjUpdatingQuantites(
        this.ibtForm.value.items
      );
    } else {
      this.utils.snackBarShowWarning("Add items to IBT.");
    }
  }

  validateDispatchQty(i) {
    const items = this.ibtForm.controls.items as FormArray;
    const selectedItem = items.at(i);
    this.maxDispatchLimit = selectedItem.value.name.inStock;
    if (selectedItem.get("quantity").value > this.maxDispatchLimit) {
      selectedItem.get("quantity").setValue(this.maxDispatchLimit);
    }
  }

  getTotalIndentCostQuantity(event , element) {   

    if(event.keyCode == 190){
      return
    }

    if(element.quantity > element.inStock){
      element.quantity = 0;
    }
   if ( element['uom'] == 'portion'){
    let conversionCoefficient =  element['defaultUOM'] == 'NOS' ? 1: 1000 ;
    let portionWeight = element.portionWeight * 1000;   
    let convertWeight = (portionWeight / conversionCoefficient) * element.quantity;  
    let convertedWeight =  convertWeight / 1000 ;
    element['convertedWeight'] = this.utils.truncateNew(convertedWeight);
   }
    this.items = this.dataSource.data.filter(
      (item) => item.name != "" && item.quantity > 0
    );
  }

  convertToQty(el){
    if (el['selectedOption'] === 'portion')  {
      let conversionCoefficient =  el['defaultUOM'] == 'NOS' ? 1: 1000 ;
      el['uom'] = 'portion'
      el['inStock'] = this.utils.truncateNew(el['currentStock'] / (el.portionWeight / conversionCoefficient))
      el['withTaxPrice'] = this.utils.truncateNew((el['actualPrice'] * (el.portionWeight / conversionCoefficient)),2)
      el['quantity'] = 0
    } else {
      el['quantity'] = 0
      el['uom'] = el['defaultUOM']
      el['inStock'] = this.utils.truncateNew(el['currentStock'])
      el['withTaxPrice'] = this.utils.truncateNew(el['actualPrice'],2)
    }
  }
 
  findData(){
    this.isShowtable = true
    this.sourceReceipient = this.ibtForm.value.fromBranch.branchName;
    this.branchTransfer
      .getIbtInvWithPkg({
        tenantId: this.user.tenantId,
        restaurantId: this.ibtForm.value.fromBranch.restaurantIdOld,
      })
      .subscribe(
        (data) => {          
          if (data) 
          data.invItems['quantity'] = 0;
            this.branchInvItems = data.invItems.filter(
              (item) => item.inStock > 0
            );
          this.dataSource = new MatTableDataSource() ;
          this.dataSource.data =  this.branchInvItems
          this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
          this.dataSource.paginator = this.paginator;
          this.branchInvItems.forEach(item => {
            item['defaultUOM'] = item['uom']
            item['currentStock'] = this.utils.truncateNew(item['inStock'])
            let withTaxPrice = this.utils.truncateNew(item['withTaxPrice'],2)
            item['withTaxPrice'] = withTaxPrice
            item['actualPrice'] = withTaxPrice
            
            if (item.category == null) {
              item.category = 'N/A'
            }
            if (item.ItemType == null) {
              item.ItemType = 'N/A'
            }
            if (item.subCategory == null) {
              item.subCategory = 'N/A'
            }
            this.ItemTypeList.push(item.ItemType)
            this.categoryList.push(item.category)
            this.subCategoryList.push(item.subCategory)
          })
          this.categoryList = this.categoryList.filter((k, i, ar) => ar.indexOf(k) === i);
          this.ItemTypeList = this.ItemTypeList.filter((p, q, arrr) => arrr.indexOf(p) === q);
          this.subCategoryList = this.subCategoryList.filter((j, l, arr) => arr.indexOf(j) === l);
          this.initCategoryList = this.categoryList;
          this.initSubCategoryList = this.subCategoryList;
        },
        (err) => console.error(err)
      );
  }

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }

  preview() {
    if(this.indentPreview == false){
      this.indentPreview = true;
    }else{
      this.indentPreview = false;
    }

    if (this.indentPreview == true) {
      this.dataSource = new MatTableDataSource() ;
      this.dataSource.data = this.branchInvItems.filter(item => item.quantity > 0 && Object.keys(item.workArea))
      this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
      this.dataSource.paginator = this.paginator;
    }
    else {
      this.dataSource = new MatTableDataSource() ;
      this.dataSource.data = this.branchInvItems
      this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
      this.dataSource.paginator = this.paginator;
    }
  }

  portionChange() {
    if (this.selectedOption) {
        this.dataSource.data = this.dataSource.data.map(element => {
            if (element.ItemType === 'SubRecipe' && element.hasOwnProperty('portionWeight')) {
                element['uom'] = 'portion';
                element.selectedOption = 'portion';
                let conversionCoefficient =  element['defaultUOM'] == 'NOS' ? 1: 1000 ;
                element['inStock'] = this.utils.truncateNew(element['currentStock'] / (element.portionWeight / conversionCoefficient))
                element['withTaxPrice'] = this.utils.truncateNew((element['actualPrice'] * (element.portionWeight / conversionCoefficient)),2)
            }
            return element;
        });
    } else {
        this.dataSource.data = this.dataSource.data.map(element => {
            if (element.ItemType === 'SubRecipe' && element.hasOwnProperty('portionWeight')) {
                element['uom'] = 'uom'; 
                element.selectedOption = 'uom';
                element['uom'] = element['defaultUOM']
                element['inStock'] = this.utils.truncateNew(element['currentStock'])
                element['withTaxPrice'] = this.utils.truncateNew(element['actualPrice'],2)
            }
            return element;
        });
    }
  }

  selectItemType(itemType) {
    let filteredCategoryList = []
    let filteredSubCategoryList = []
    if (itemType != 'All') {
      let filteredItem = this.branchInvItems.filter(item => item['ItemType'].toUpperCase() === itemType.toUpperCase());
      filteredItem.forEach(element => {
        filteredCategoryList.push(element.category)
        filteredSubCategoryList.push(element.subCategory)
      });
      this.categoryList = filteredCategoryList.filter((k, i, ar) => ar.indexOf(k) === i);
      this.subCategoryList = filteredSubCategoryList.filter((k, i, ar) => ar.indexOf(k) === i);
      this.categoryList.splice(0, 0, 'All')
      this.subCategoryList.splice(0, 0, 'All')
      this.subCatList = this.subCategoryList
    }
    else {
      this.categoryList = this.initCategoryList;
      this.subCategoryList = this.initSubCategoryList;
    }
    this.filterKeys = { ItemType: itemType, category: 'All', subCategory: 'All' }
    this.allFilter()
  }

  selectCategory(cat) {
    let filteredSubCategoryList = []
    let filteredItem = []
    if (cat != 'All') {
      if (this.filterKeys.ItemType != 'All') {
        filteredItem = this.branchInvItems.filter(item => item['category'].toUpperCase() === cat.toUpperCase()
          && item['ItemType'].toUpperCase() === this.filterKeys.ItemType.toUpperCase());
      }
      else {
        filteredItem = this.branchInvItems.filter(item => item['category'].toUpperCase() === cat.toUpperCase());
      }
      filteredItem.forEach(element => {
        filteredSubCategoryList.push(element.subCategory)
      });
      this.subCategoryList = filteredSubCategoryList.filter((k, i, ar) => ar.indexOf(k) === i);
      this.subCategoryList.splice(0, 0, 'All')
    }
    else if (this.filterKeys.ItemType != 'All') {
      this.subCategoryList = this.subCatList
    }
    else {
      this.subCategoryList = this.initSubCategoryList
    }
    this.filterKeys.category = cat;
    this.filterKeys.subCategory = 'All'
    this.allFilter()
  }

  selectSubCat(subCat) {
    this.filterKeys.subCategory = subCat;
    this.allFilter()
  }

  allFilter() {
    let tmp = this.branchInvItems
    let prev = this.branchInvItems
    Object.keys(this.filterKeys).forEach(element => {
      if (this.filterKeys[element] != 'All') {
        tmp = prev.filter(item =>
          item[element].toLowerCase() === this.filterKeys[element].toLowerCase())
        prev = tmp
      }
    });
    this.dataSource.data = tmp
  }

  clear(){
    this.searchText = ''
    this.searchValue = ''
    this.doFilter(this.searchValue)
  }

  focusFunctionWithOutForm(element , value){
    if(Number(element[value]) === 0){
      element[value] = null;
    }
  }
  
  focusOutFunctionWithOutForm(element , value){
    if(element[value] === null){
      element[value] = 0
    }
  }

  onMouseOver(element: any){    
    this.showTooltip = true;  
    this.hoveredElement = element;     
  }

  onMouseOut(){
    this.showTooltip = false;
  }

}
