<div class="title row">
  <div>
    <form [formGroup]="ibtForm">
    <mat-form-field *ngIf="multiBranchUser" appearance="none" class="topitem">
      <label class="title-palce">Select Branch</label>
      <mat-select placeholder="Select Branch" formControlName="branchSelection"
        (selectionChange)="filterByBranch($event.value)" class="outline">
        <mat-option *ngFor="let rest of branches" [value]="rest">
          {{ rest.branchName }}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </form>
  </div>

  <div>
    <mat-form-field *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" appearance="none"
      style="margin-left: 10px;" class="topitem">
      <label>Start Date</label>
      <input matInput class="outline" [matDatepicker]="picker1" [(ngModel)]="selectedStartDate" placeholder="Start Date"
        [formControl]="startDate" />
      <mat-datepicker-toggle matSuffix [for]="picker1">
        <mat-icon matDatepickerToggleIcon>
          <img src="./../../assets/calender.png" />
        </mat-icon>
      </mat-datepicker-toggle>
      <mat-datepicker #picker1></mat-datepicker>
    </mat-form-field>
  </div>

  <div>
    <mat-form-field *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" appearance="none"
      style="margin-left: 10px;" class="topitem">
      <label>End Date</label>
      <input matInput class="outline" [matDatepicker]="picker2" [(ngModel)]="selectedEndDate"
        [disabled]="!startDate.value" [formControl]="endDate" placeholder="End Date" [readonly]="!startDate.value"
        [min]="startDate.value" />
      <mat-datepicker-toggle matSuffix [for]="picker2">
        <mat-icon matDatepickerToggleIcon>
          <img src="./../../assets/calender.png" />
        </mat-icon>
      </mat-datepicker-toggle>
      <mat-datepicker #picker2></mat-datepicker>
    </mat-form-field>
  </div>

  <div class=" mr-2">
    <button *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" mat-stroked-button
      class="btn-block findButton button3" (click)="filterdates()">
      Find
    </button>
  </div>
  <div>
    <button *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" mat-stroked-button
      class="btn-block findButton button3" (click)="clearDates()">
      Clear
    </button>
  </div>
</div>
<div *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" class="datacontainer">
  <mat-card>
    <mat-card-content>
      <div class="search-table-input">
        <mat-form-field appearance="none">
          <label>Search</label>
          <input matInput type="text" class="outline" (keyup)="doFilter($event.target.value)" [(ngModel)]='searchText'
            placeholder="Search" />
          <mat-icon matSuffix (click)="resetForm()" class="closebtn">close</mat-icon>
        </mat-form-field>
        <mat-form-field appearance="none">
          <label>Delivery Type</label>
          <mat-select placeholder="Type" [(ngModel)]="selectedIbtType" (selectionChange)="selectIbtTypeXX($event.value)" [formControl]="ibtTyp" class="outline">
            <mat-option *ngFor="let type of ibtTypes" [value]="type" (click)="selectIbtType(type)">
              {{ type }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field appearance="none">
          <label>Dispatched Status</label>
          <mat-select placeholder="All" [(ngModel)]="selectedStatus" [formControl]="ibtStat" (selectionChange)="selectDispatchStatus($event.value)" class="outline">
            <mat-option *ngFor="let type of ibtActive" [value]="type" (click)="selectIbtActive(type)">
              {{ type | titlecase }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field appearance="none">
          <label>Received Status</label>
          <mat-select placeholder="All" [(ngModel)]="selectedRecStatus" [formControl]="recStat" (selectionChange)="selectDispatchStatus($event.value)" class="outline">
            <mat-option *ngFor="let type of receivedStatus" [value]="type" >
              {{ type | titlecase }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field appearance="none">
          <label>Type</label>
          <mat-select placeholder="Type" [(ngModel)]="selectedIbtProcessType" [formControl]="ibtProcessTyp" (selectionChange)="selectStatusType($event.value)" class="outline">
            <mat-option *ngFor="let type of processType" [value]="type" (click)="ibtProcessTypes(type)">
              {{ type }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="none">
          <label>Item Name</label>
          <mat-select placeholder="Item Name" [formControl]="itemName" (selectionChange)="itemNameChange($event.value)" 
          class="outline" [multiple]="true" #multiSelect>
            <mat-option>
              <ngx-mat-select-search placeholderLabel="Items..." noEntriesFoundLabel="'no Item found'"
                [formControl]="itemFilterCtrl"></ngx-mat-select-search>
            </mat-option>
            <mat-option (click)="toggleSelectAll()" class="hide-checkbox">
              Select All / Deselect All
            </mat-option>
            <mat-option *ngFor="let item of itemBanks | async" [value]="item">
              {{ item }}
            </mat-option>          
          </mat-select>
        </mat-form-field>

<!-- 
        <mat-form-field appearance="none" *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser">
          <label>Work Area</label>
          <mat-select placeholder="Select Work Area" (selectionChange)="selectIndentArea($event)" multiple
            class="outline" [formControl]="areas">
            <mat-option>
              <ngx-mat-select-search placeholderLabel="Select Work Area..."
                noEntriesFoundLabel="'no Work Area found'"
                [formControl]="destinationBankFilterCtrl"></ngx-mat-select-search>
            </mat-option>
            <mat-option *ngFor="let area of destinationFilteredWorkArea | async" [value]="area">
              {{ area }}
            </mat-option>
          </mat-select>
        </mat-form-field> -->


        <button mat-button class="buttonForRefresh refreshBtn" (click)="refreshData()">Refresh</button>

      </div>
      <section class="example-container-1 mat-elevation-z8">    
      <table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort>

        <ng-container matColumnDef="sNo">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> # </b>
          </th>
          <td mat-cell *matCellDef="let element; let i = index;">
            {{ i + 1 }}
          </td>
        </ng-container>

        <ng-container matColumnDef="ibtId">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> IBT Id</b>
          </th>
          <td mat-cell *matCellDef="let element" class="links" (click)="detailedIbt(element)">
            {{ element.ibtId }}
          </td>
        </ng-container>

        <ng-container matColumnDef="prId">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>PR Id</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.prId?.length ? element.prId[0] : '-' }}
          </td>
        </ng-container>

        <ng-container matColumnDef="fromBranch">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Source </b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.fromBranch.location }}
          </td>
        </ng-container>

        <ng-container matColumnDef="toBranch">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Destination </b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.toBranch.location }}
          </td>
        </ng-container>


        <ng-container matColumnDef="toWorkArea">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Workarea</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.workArea ? element.workArea : 'store' }}
          </td>
        </ng-container>
        

        <ng-container matColumnDef="createTs">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Requested Date </b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ this.utils.formatDateToUTC(element.createTs) }}
          </td>
        </ng-container>

        <ng-container matColumnDef="eta">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Expected Date </b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.eta | date: "EEEE, MMMM d, y" }}
          </td>
        </ng-container>

        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Dispatch Status </b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{element.dispatchStatus}}
          </td>
        </ng-container>

        <ng-container matColumnDef="recStatus">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Receive Status </b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{element.receivedStatus}}
          </td>
        </ng-container>

        <ng-container matColumnDef="deliveryType">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Delivery Type </b>
          </th>
          <td mat-cell *matCellDef="let element">
            <div *ngIf="selectedBranch == element.fromBranch.restaurantId">
              Outgoing
            </div>
            <div *ngIf="selectedBranch != element.fromBranch.restaurantId">
              Incoming
            </div>
          </td>
        </ng-container>

        <!-- <ng-container matColumnDef="approvalStatus">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Approval Status </b>
          </th>
          <td mat-cell *matCellDef="let element">
            <div >
              {{ element.approvalStatus }}
            </div>
          </td>
        </ng-container> -->

        <ng-container matColumnDef="approvalStatus">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Approval Status</b>
          </th>
          <td mat-cell *matCellDef="let element">
            <div
              *ngIf="element?.indentApprovalDetail && element?.indentApprovalDetail != ''">
              <span class="links" (click)="mouseEnter(element)">{{ getDynamicText(element) }}</span>
            </div>
            <div
              *ngIf="!element?.indentApprovalDetail || element?.indentApprovalDetail == ''">
              -
            </div>
          </td>
        </ng-container>

        <!-- <ng-container *ngIf="closeAccess" matColumnDef="Action"> -->
        <ng-container matColumnDef="Action">  
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Actions</b>
          </th>
          <td mat-cell *matCellDef="let element">
            <button mat-icon-button (click)="updateStatus(element, 'Closed')" 
            [disabled]="element.status.dispatched != 'Pending'"
              class="action-btn" *ngIf="closeAccess">
              <mat-icon class="action-print-icon"
              [matTooltip]="element.status.dispatched === 'Closed' ? '' : 'Close Ibt'" matTooltipPosition="right">
                lock
              </mat-icon>
            </button>
          </td>
        </ng-container>

        <ng-container matColumnDef="type">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Type </b>
          </th>
          <td mat-cell *matCellDef="let element">
            <div *ngIf="element.demand">
              Auto IBT
            </div>
            <div *ngIf="!element.demand">
              {{ element.ibtType }}
              <!-- Manual IBT --> 
            </div>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>
      </section>
    </mat-card-content>
    <div class="dataMessage" *ngIf="dataSource?.data.length == 0"> No Data Available </div>
  </mat-card>
  <mat-paginator [showTotalPages]="5" [pageSize]="10" [pageSizeOptions]="pageSizes"></mat-paginator>
</div>