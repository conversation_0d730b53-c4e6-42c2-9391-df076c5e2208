import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { BranchTransferService, AuthService, ShareDataService, PurchasesService } from '../_services';
import { MatTableDataSource, MatPaginator, MatDialog, MatSelect } from '@angular/material';
import { GlobalsService } from '../_services/globals.service';
import { ActivatedRoute,NavigationEnd,NavigationStart,Params, Router } from '@angular/router';
import * as moment from 'moment';
import { UtilsService } from '../_utils/utils.service';
import { FormBuilder, FormControl, FormGroup, Validators } from "@angular/forms";
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material';
import { MomentDateAdapter } from '@angular/material-moment-adapter';
import { ReplaySubject, Subject } from 'rxjs';
import {takeUntil } from 'rxjs/operators';
import { SharedFilterService } from '../_services/shared-filter.service';
import { PreviewIbtComponent } from '../_dialogs/preview-ibt/preview-ibt.component';
import { SimpleDialogComponent } from '../_dialogs/simple-dialog/simple-dialog.component';


export const MY_FORMATS = {
  parse: {
      dateInput: 'LL'
  },
  display: {
      dateInput: 'DD-MM-YYYY',
      monthYearLabel: 'YYYY',
      dateA11yLabel: 'LL',
      monthYearA11yLabel: 'YYYY'
  }
};

@Component({
  selector: 'app-ibts',
  templateUrl: './ibts.component.html',
  styleUrls: ['./ibts.component.scss', "./../../common-dark.scss"],
  providers:[{ provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
            { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }
          ]
})
export class IbtsComponent implements OnInit {
  @ViewChild(MatPaginator) paginator: MatPaginator;
  public selectedBranch:any;
  public selectedIbtType: any;
  public selectedStatus:any;
  public startDate = new FormControl();
  public endDate = new FormControl();
  public user: any;
  public restaurantId: any;
  public multiBranchUser: any; 
  public branchSelected: boolean;
  public dataSource: MatTableDataSource<any>;
  public displayedColumns: string[];
  public ibtTypes: string[];
  public ibtActive: string[];
  public dateRange: any = null;
  public pickerDateRange: any = {};
  public filterKey = { status: 'All', type: 'All', processType : 'All'}
  public ibtStat = new FormControl();
  public recStat = new FormControl();
  public ibtTyp = new FormControl();
  public ibtProcessTyp = new FormControl();
  public date: any;
  public filteredByDateList: any[];
  public ibtType: any;
  public ibts: any;
  public searchValue: any = ''
  public allWorkAreas: string[] = [];

  private readonly dateRanges: any = {
    'Today': [moment(), moment()],
    'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
    'Last 7 Days': [moment().subtract(6, 'days'), moment()],
    'Last 30 Days': [moment().subtract(29, 'days'), moment()],
    'This Month': [moment().startOf('month'), moment().endOf('month')],
    'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
  }
  searchText: string;
  selectedStartDate: any;
  selectedEndDate: any;
  pageSizes= [];
  ibtNumber: any;
  senderBranch: any;
  processType: string[];
  branches: any[] ;
  selectedRecStatus: any;
  getBranchData: any[]
  ibtForm: FormGroup;
  selectedIbtProcessType: any;
  selectedWorkareas =[];
  workareas =[]
  // getIbtsUrl = encodeURI(GlobalsService.getIbts)
  routeFlag: boolean = false;
  windowCurrentUrl: string;
  // private unsubscribe$ = new Subject<void>();
  sharedFilterData: any = {};
  detailedIbtUrl = encodeURI(GlobalsService.detailedIbt);
  private unsubscribe$: Subject<void> = new Subject<void>();
  elementdata: any;
  dispatchStatus: string;
  receivedStatus: string[];
  deleteAccess: boolean;
  editAccess: boolean;
  closeAccess: boolean;
  tenantDetails: any[]
  public destinationBankFilterCtrl: FormControl = new FormControl();
  public areas = new FormControl();
  public destinationFilteredWorkArea: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  protected _onDestroy = new Subject<void>();

  itemNameList: any;
  itemName = new FormControl();
  public itemBank: any[] = [];
  public itemBanks: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public itemFilterCtrl: FormControl = new FormControl();
  allSelected: boolean = true;
  dataRefresh: boolean = false;
  prevBranchId: any;
  constructor(private auth: AuthService,
    private cdref: ChangeDetectorRef ,
    private activateRoute : ActivatedRoute,
    private router: Router,
    private sharedData: ShareDataService,
    private utils: UtilsService,
    private branchTransferService: BranchTransferService,
    private purchases: PurchasesService,
    private fb: FormBuilder,
    private sharedFilterService: SharedFilterService,
    public dialog: MatDialog
    ) {
    this.user = this.auth.getCurrentUser();
    this.multiBranchUser = this.user.multiBranchUser;
    this.user.restaurantAccess.forEach(branch => {
      this.allWorkAreas = this.allWorkAreas.concat(branch.workAreas);
    });
    this.destinationFilteredWorkArea.next(this.allWorkAreas.slice());
    this.activateRoute.params.subscribe((params: Params)=>{
      if(params.senderBranch && params.ibtNumber){
        this.routeFlag = true;
        var windowLocation = window.location.href;
        this.windowCurrentUrl = windowLocation.split('/')[4].split(';')[0]
        if (!this.user.multiBranchUser) {
          this.restaurantId = this.user.restaurantAccess[0].restaurantIdOld;
          this.workareas  = this.user.restaurantAccess[0].workAreas;
          this.selectedWorkareas= this.workareas;
          this.areas.patchValue(this.selectedWorkareas);
        }else{
          this.user.restaurantAccess.forEach(element => {
            if(element.restaurantIdOld == params.senderBranch){
              this.restaurantId = this.user.restaurantAccess[0].restaurantIdOld;
              this.workareas  = this.user.restaurantAccess[0].workAreas;
              this.selectedWorkareas= this.workareas
              this.areas.patchValue(this.selectedWorkareas);
            }
          });
        }
        this.ibtNumber = params.ibtNumber;
        this.senderBranch = params.senderBranch;
        this.branchSelected = true;
      
      }
    });

    this.ibtForm = this.fb.group({
      branchSelection: [null, Validators.required]
    });

    if(!this.routeFlag){
      var windowLocation = window.location.href;
      this.windowCurrentUrl = windowLocation.split('/')[4]
    }

     this.sharedFilterService.getFilteredIbts.pipe(takeUntil(this.unsubscribe$)).subscribe(obj => 
      this.sharedFilterData = obj
    );    

      this.sharedData.sharedBranchData.pipe(takeUntil(this.unsubscribe$)).subscribe((val) => {
        this.getBranchData = val;
        if(this.getBranchData.length == 0 ){
          this.branches = this.user.restaurantAccess;
        }else if(this.getBranchData.length == 1){        
          const toSelect = this.getBranchData.find(data => data.branchName == this.getBranchData[0].branchName);
          if(toSelect != this.sharedFilterData.restaurantId){
            this.sharedFilterData = '';
            this.selectedIbtType = 'All'
            this.selectedStatus = 'All'
            this.selectedRecStatus = 'All'
            this.selectedIbtProcessType = 'All'
            this.selectedWorkareas= this.workareas
            this.areas.patchValue(this.selectedWorkareas);
            this.ibtProcessTyp.setValue('All') 
            this.ibtStat.setValue('All') 
            this.ibtTyp.setValue('All') 
          }
          this.ibtForm.get('branchSelection').setValue(toSelect);
          this.branches = this.getBranchData
          this.filterByBranch(this.ibtForm.value.branchSelection);
          if(this.sharedFilterData){
            this.sharedFilterData.branchFlag = false;
          }
        }else{
          if(this.sharedFilterData.branchFlag == true){
            this.filterByBranch(this.sharedFilterData.restaurantId);
            this.sharedFilterData.branchFlag = false;
          }
          this.branches = this.getBranchData
        }
    });
    this.router.events.subscribe(event => {
      if (event instanceof NavigationStart) {
        if (this.router.url.includes(this.detailedIbtUrl)) {
          localStorage.setItem('savedItemNames', JSON.stringify(this.itemName.value));
        }
      }
    });  
  }

  ngOnInit() {
    this.ibtType = new FormControl();
    this.ibtTypes = GlobalsService.ibtTypes;
    this.ibtActive = ['All', 'Pending','Partial', 'Completed']
    this.receivedStatus = ['All', 'Pending','Partial', 'Completed']
    this.processType = ['All' , 'Auto Ibt' , 'Manual Ibt' , 'Direct']
    this.displayedColumns = GlobalsService.ibtColumns;
    this.tenantDetail();   
    if (!this.user.multiBranchUser) {
      this.restaurantId = this.user.restaurantAccess[0].restaurantIdOld;
      this.workareas  = this.user.restaurantAccess[0].workAreas;
      this.selectedWorkareas= this.workareas;
      this.areas.patchValue(this.selectedWorkareas);
      this.branchSelected = true;
      

      this.getIbts()
    }
  }


  tenantDetail() {
    let obj = {
      tenantId: this.user.tenantId
    }
    this.branchTransferService.tenantDetails(obj).subscribe(res => {
      if (res.result == 'success') {
        this.tenantDetails = res.data[0].permission 
        let deleteAccess = res.data[0] && res.data[0].permission && res.data[0].permission.indentAccess ? res.data[0].permission.indentAccess.delete : false;
        let closeAccess = res.data[0] && res.data[0].permission && res.data[0].permission.indentAccess ? res.data[0].permission.indentAccess.close : false;

        if (deleteAccess) {
          let rolesList = (res.data[0] && res.data[0].permission && res.data[0].permission.indentAccess) ? res.data[0].permission.indentAccess.deleteAccess : [];
          this.deleteAccess = (rolesList.some(role => role.toLowerCase() === this.user.role.toLowerCase())) ? true : false ;
        } else {
          this.deleteAccess = false ;
        }
        if (closeAccess) {
          let rolesList = (res.data[0] && res.data[0].permission && res.data[0].permission.indentAccess) ? res.data[0].permission.indentAccess.closeAccess : [];
          this.closeAccess = (rolesList.some(role => role.toLowerCase() === this.user.role.toLowerCase())) ? true : false ;
        } else {
          this.closeAccess = false ;
        }
        if (this.closeAccess) {
          if (!this.displayedColumns.includes('Action')) {
            this.displayedColumns.push('Action');
          }
        }  
      } else {
        this.deleteAccess = false ;
        this.closeAccess = false ;
      }
    })
  }

  destinationFilterBanks() {
    if (!this.workareas) {
      return;
    }
    let search = this.destinationBankFilterCtrl.value;
    if (!search) {
      this.destinationFilteredWorkArea.next(this.workareas.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.destinationFilteredWorkArea.next(
      this.workareas.filter(bank => bank.toLowerCase().indexOf(search) > -1)
    );
  }

  selectIndentArea(val){
    this.selectedWorkareas = val.value;
    this.areas.patchValue(this.selectedWorkareas);
    this.getIbts()
  }

  ngOnDestroy() {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
    this._onDestroy.next();
    this._onDestroy.complete();
    if(!this.router.url.includes(this.detailedIbtUrl)){
    this.sharedFilterService.getFilteredIbts['_value'] = ''
    localStorage.removeItem('savedItemNames');
    }
  }

  clearDates(){
    this.selectedStartDate = null;
    this.selectedEndDate = null;
    this.selectedIbtType = 'All'
    this.selectedStatus = 'All'
    this.selectedRecStatus = 'All'
    this.selectedIbtProcessType = 'All'
    this.selectedWorkareas= this.workareas;
    this.areas.patchValue(this.selectedWorkareas);
    this.ibtProcessTyp.setValue('All') 
    this.ibtStat.setValue('All') 
    this.ibtTyp.setValue('All') 
    this.getIbts();
  }

  getIbts() {
    let obj = {
      tenantId: this.user.tenantId,
      restaurantId: this.restaurantId,
      ibtType: this.selectedIbtType || 'All',
      ibtStatus: this.selectedStatus || 'All',
      ibtRecStatus: this.selectedRecStatus || 'All',
      ibtProcessType: this.selectedIbtProcessType || 'All',
      ibtWorkareas: this.allWorkAreas.join(',')
    };
    if(this.startDate.value && this.endDate.value){
      obj['startDate'] = this.utils.dateCorrection(this.startDate.value);
      obj['endDate'] = this.utils.dateCorrection(this.endDate.value);
    }else{
      obj['startDate'] = null;
      obj['endDate'] = null;
    }
      this.branchTransferService.getIbts(obj).subscribe(data => {
      if (!this.dataSource)
        this.dataSource = new MatTableDataSource<any>();
      this.ibts = data;      
      data.forEach((element) => {
        const getStatus = () => {
          if (element.indentApprovalDetail && element.indentApprovalDetail.length === 0) {
            return "approved";
          } else if (element.indentApprovalDetail && element.indentApprovalDetail.some((item) => item.status === "rejected")) {
            const rejectedItem = element.indentApprovalDetail.find((item) => item.status === "rejected");
            return `${rejectedItem.level} rejected`;
          } else if (element.indentApprovalDetail &&  element.indentApprovalDetail.some((item) => item.status === "pending")) {
            const pendingItem = element.indentApprovalDetail.find((item) => item.status === "pending");
            return `${pendingItem.level} pending`;
          } else {
            return "approved";
          }
        };
        const status = getStatus();
        element.approvalStatus = status;
        if (typeof element.status.dispatched === 'boolean'){
          element.dispatchStatus = (element.status.dispatched  === true) ? 'Completed' : 'Pending' ;
        } else {
          element.dispatchStatus = element.status.dispatched ;
        }
        if (typeof element.status.delivered === 'boolean'){
          element.receivedStatus = (element.status.delivered  === true) ? 'Completed' : 'Pending' ;
        } else {
          element.receivedStatus = element.status.delivered ;
        }
      });     
       
      const uniqueItemNames = new Set();
      data.forEach(element => {
        element.itemList.forEach(element => uniqueItemNames.add(element));
      });
      this.itemNameList = Array.from(uniqueItemNames); 

      const savedItemNames = JSON.parse(localStorage.getItem('savedItemNames') || '[]');      
      if (savedItemNames.length > 0 && this.restaurantId === this.prevBranchId && !this.dataRefresh) {
        this.itemName.setValue(savedItemNames);
        this.itemNameChange(savedItemNames);
      } else {
        this.itemName.setValue(this.itemNameList.slice()); 
        this.itemNameChange(this.itemName.value);
      }

      this.itemBank = this.itemNameList
      this.itemBanks.next(this.itemBank.slice());
      this.itemFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
          this.vendorfilterBanks();
      });
      this.dataRefresh = false;
      this.ibts = data;
      this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
      this.dataSource.paginator = this.paginator;      
      if(this.senderBranch && this.ibtNumber){
        this.findPrObjectById(this.ibtNumber , this.senderBranch);
      }
    }, err => console.error(err))
  }

  itemNameChange(selectedItemNames: string[]) {           
    if (selectedItemNames && selectedItemNames.length > 0) {
      this.dataSource.data = this.ibts.filter(element =>
        element.itemList.some(item => selectedItemNames.includes(item))
      );
    } else {
      this.dataSource.data = [];
    }
  }

  toggleSelectAll() {
    this.allSelected = !this.allSelected;
    if (this.allSelected) {
      this.itemName.setValue(this.itemBank.slice());
    } else {
      this.itemName.setValue([]);
    }
    this.itemNameChange(this.itemName.value);
  }

  protected vendorfilterBanks() {
    if (!this.itemBank) {
      return;
    }
    let search = this.itemFilterCtrl.value;
    if (!search) {
      this.itemBanks.next(this.itemBank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.itemBanks.next(
      this.itemBank.filter(item => item.toLowerCase().indexOf(search) > -1)
    );
  }

  findPrObjectById(ibtId , senderBranch) {
    this.dataSource.data.find(obj => {
      if (obj['ibtId'] === ibtId && obj['fromBranch']['restaurantId'] === senderBranch){
        this.detailedIbt(obj);
        return true;
      }
 
    })
  }

  detailedIbt(obj) {
    let inputObj = {
      restaurantId : this.ibtForm.value.branchSelection,
      selectedStartDate : this.startDate.value,
      selectedEndDate : this.endDate.value,
      ibtProcessTyp : this.ibtProcessTyp.value,
      ibtStat : this.ibtStat.value,
      ibtTyp : this.ibtTyp.value,
      branchFlag : true
    }    
    this.sharedFilterService.getFilteredIbts.next(inputObj);
    this.branchTransferService.getInStockForIbt(obj).subscribe(data => {
      obj.items = data.items;
      obj.selectedRestaurant = this.restaurantId
      this.sharedData.changeIbt(obj)
      this.router.navigate(['/home/<USER>'])
    }, err => console.error(err))

  }

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }

  allFilter() {
    // this.filteredByDateList = this.dataSource.data = this.ibts;
    if (this.filterKey.type == 'All' && this.filterKey.status == 'All' && this.filterKey.processType == 'All') {
      this.filteredByDateList = this.dataSource.data = this.ibts
    }
    else if (this.filterKey.type == 'All' && this.filterKey.status != 'All') {
      this.filteredByDateList = this.dataSource.data = this.ibts.filter(ibt =>
        ibt.status.orderStatus.toLowerCase() === this.filterKey.status.toLowerCase())
    }
    else if (this.filterKey.type != 'All' && this.filterKey.status == 'All') {
      this.filteredByDateList = this.dataSource.data = this.ibts.filter(ibt => {
        if (this.filterKey.type == 'Incoming'){
          return this.restaurantId === ibt.toBranch.restaurantId;
        } else{
        return this.restaurantId === ibt.fromBranch.restaurantId
        }
  
      })
    }
    else if ((this.filterKey.type == 'All' && this.filterKey.status == 'All') && this.filterKey.processType != 'All') {
      this.filteredByDateList = this.dataSource.data = this.ibts.filter(ibt => {
        if (this.filterKey.processType == 'Auto Ibt')
        // ibt.demand.toLowerCase() === this.filterKey.status.toLowerCase()
          return ibt.demand;
        else
          return !ibt.demand;
      })
    }
    else if ((this.filterKey.type != 'All' && this.filterKey.status != 'All') && this.filterKey.processType != 'All') {
      this.filteredByDateList = this.dataSource.data = this.ibts.filter(ibt => {
        if (this.filterKey.processType == 'Auto Ibt')
          return ibt.demand;
        else
          return !ibt.demand;
      })
    }
    else if ((this.filterKey.type != 'All' && this.filterKey.status == 'All') && this.filterKey.processType != 'All') {
      this.filteredByDateList = this.dataSource.data = this.ibts.filter(ibt => {
        if (this.filterKey.processType == 'Auto Ibt')
          return ibt.demand;
        else
          return !ibt.demand;
      })
    }
    else if ((this.filterKey.type == 'All' && this.filterKey.status != 'All') && this.filterKey.processType != 'All') {
      this.filteredByDateList = this.dataSource.data = this.ibts.filter(ibt => {
        if (this.filterKey.processType == 'Auto Ibt')
          return ibt.demand;
        else
          return !ibt.demand;
      })
    }
    else {
      this.filteredByDateList = this.dataSource.data = this.ibts.filter(ibt => {
        if (this.filterKey.type == 'Incoming')
          return (this.restaurantId === ibt.toBranch.restaurantId && ibt.status.orderStatus.toLowerCase() === this.filterKey.status.toLowerCase())
        else
          return (this.restaurantId === ibt.fromBranch.restaurantId && ibt.status.orderStatus.toLowerCase() === this.filterKey.status.toLowerCase())
      })
    }
  }

  selectIbtType(ibtType) {
    // this.filterKey.type = ibtType
    // this.allFilter()
  }

  selectIbtTypeXX(){
    this.getIbts();
  }

  selectDispatchStatus(){
    this.getIbts();
  }

  selectStatusType(){
    this.getIbts();
  }

  ngAfterContentChecked() {
    this.cdref.detectChanges();
  }

  selectIbtActive(ibtStatus) {
    // this.filterKey.status = ibtStatus
    // this.allFilter()
  }

  ibtProcessTypes(processType){
    // this.filterKey.processType = processType
    // this.allFilter()
  }

  filterdates(){
    if(this.startDate.value && this.endDate.value){
      this.getIbts()
    }else{
      this.utils.snackBarShowError('Please select start date and end date ')
    }
  }

  resetForm() {
    this.searchText = ''
    this.date = '';
    this.filteredByDateList = this.dataSource.data = this.ibts;
    this.ibtTyp.setValue('')
    this.ibtProcessTyp.setValue('')
    this.ibtStat.setValue('')
    this.filterKey.status = 'All'
    this.filterKey.type = 'All'
    this.filterKey.processType = 'All'
    this.searchValue = ''
    this.doFilter(this.searchValue)
    this.dataSource.data = this.ibts;
  }

  filterByBranch(restId) {
    this.prevBranchId = window.sessionStorage.getItem("restaurantId");
    this.restaurantId = restId.restaurantIdOld;
    this.workareas  = restId.workAreas;
    window.sessionStorage.setItem("restaurantId", this.restaurantId)
    this.selectedWorkareas= this.workareas;
    this.areas.patchValue(this.selectedWorkareas);
    if(this.sharedFilterData != '' && this.sharedFilterData.restaurantId == restId){
        this.ibtForm.get('branchSelection').setValue(this.sharedFilterData.restaurantId);
        this.branches = this.getBranchData;
        this.selectedStartDate = this.sharedFilterData.selectedStartDate;
        this.selectedEndDate = this.sharedFilterData.selectedEndDate;
        this.startDate.setValue(this.sharedFilterData.selectedStartDate);
        this.endDate.setValue(this.sharedFilterData.selectedEndDate);
        this.ibtProcessTyp.setValue(this.sharedFilterData.ibtProcessTyp);
        this.ibtStat.setValue(this.sharedFilterData.ibtStat);
        this.ibtTyp.setValue(this.sharedFilterData.ibtTyp);
        this.selectedIbtType = this.sharedFilterData.ibtTyp;
        this.selectedStatus = this.sharedFilterData.ibtStat;
        this.selectedIbtProcessType = this.sharedFilterData.ibtProcessTyp;
      }
    this.branchSelected = true
    this.getIbts()
  }

  refreshData(){
    this.dataRefresh = true;
    this.getIbts();
  }

  mouseEnter(element){
    this.elementdata = element
    this.dialog.open(PreviewIbtComponent, {
      width: "600px",
      data: {
        title: "CSI",
        component: "Purchase Status",
        items: this.elementdata,
        ok: function () {
          this.location.back();
        }.bind(this),
      },
    });
  }

  getCsiStatus(data) {
    if (Object.keys(data).length !== 0) {
      const levelOrder = data.map(item => item.level);
      let statusWithRole = "";
      for (const currentLevel of levelOrder) {
        const matchingData = data.find(item => item.level === currentLevel);
        
        if (matchingData) {
          const { level, status, role } = matchingData;
          
          if (status === "rejected") {
            statusWithRole = `${status.charAt(0).toUpperCase() + status.slice(1)} (${role})`;
            break;
          } else if (status === "pending" && !statusWithRole.includes("rejected")) {
            statusWithRole = `${status.charAt(0).toUpperCase() + status.slice(1)} (${role})`;
          } else if (status === "approved" && !statusWithRole.includes("rejected") && !statusWithRole.includes("pending")) {
            statusWithRole = `${status.charAt(0).toUpperCase() + status.slice(1)} (${role})`;
          }
        }
      }
      return statusWithRole;
    }
  }

  getDynamicText(element: any): string {
    if (element.indentApprovalDetail && element.indentApprovalDetail.length > 0) {
      return this.getCsiStatus(element.indentApprovalDetail);
    } else {
      return 'Click here';
    }
  }

  deleteIbt(obj) {
    obj['deletedBy'] = this.user.role
    this.dialog.open(SimpleDialogComponent, {
      data: {
        title: 'Delete Ibt',
        msg: 'Are you sure you want to delete?',
        ok: function () {
          this.branchTransferService.deleteIbt(obj).subscribe(res => {
            if (res['result'] === 'success') {
              this.utils.snackBarShowSuccess('Ibt deleted successfully!');
            } else {
              if("message" in res){
                this.utils.snackBarShowError(res['message']);
              }else{
                this.utils.snackBarShowError('Something went wrong,please try again');
              }
            }
            this.getIbts();
          })
        }.bind(this)
      }
    });
  }

  updateStatus(element: any, newStatus: string): void {
    let obj = {
      tenantId: element.tenantId,
      ibtId: element.ibtId,
      uId: this.user.mId,
      status: {
        ...element.status,
        dispatched: newStatus,
        delivered: newStatus,
        orderStatus: newStatus
      }
    };
  
    this.dialog.open(SimpleDialogComponent, {
      data: {
        title: 'Close PO',
        msg: 'Are you sure you want to Close?',
        ok: function () {
          this.purchases.updateIbtStatus(obj).subscribe(res => {
            if (res.result) {
              this.utils.snackBarShowSuccess('Ibt closed successfully');
              element.dispatchStatus = newStatus; 
              element.receivedStatus = newStatus;
            } else {
              this.utils.snackBarShowError('Something went wrong');
            }
            this.getIbts();
          });
        }.bind(this)
      }
    });
  }

  
}
