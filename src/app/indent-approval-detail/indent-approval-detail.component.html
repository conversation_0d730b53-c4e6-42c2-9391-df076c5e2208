<div class="title">
  <button mat-raised-button class="button" style="float: left; margin-left: 0px;" (click)="goBack()">
    <mat-icon>keyboard_backspace</mat-icon> Back To List
  </button>

  <div>
    <button mat-button mat-raised-button id="save-btn" class="button3" style="float: right;"
      [matTooltip]="reason" matTooltipPosition="left" (click)="rejectIndent()" [disabled] = "!showButton || isDone">
      Reject
    </button>
    <button mat-button mat-raised-button id="save-btn" class="button3" style="float: right;"
      [matTooltip]="reason" matTooltipPosition="left" (click)="checkApproval()" [disabled] = "!showButton || isDone">
      Approve
    </button>
  </div>
</div>

<div class="search-table-input fieldcontainer">
    <div class="row">
      <div class="col">
        <table class="table">
          <tbody>
            <tr>
              <th class="topItemkey" scope="row">Indent Id</th>
              <td>{{ indentData.indentId }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="col">
        <table class="table">
          <tbody>
            <tr>
              <th class="topItemkey" scope="row">workArea</th>
              <td>{{ indentData.workArea }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="col">
        <table class="table">
          <tbody>
            <tr>
              <th class="topItemkey" scope="row">Status</th>
              <td>{{ indentData.status }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="col">
        <table class="table">
          <tbody>
            <tr>
              <th class="topItemkey" scope="row">IndentDocument Date</th>
              <td>{{indentData.indentDocumentDate | date: "EEEE, MMMM d, y"}}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
</div>
<mat-card class="matcontent">
  <mat-card-content>
    <table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort>
      <ng-container matColumnDef="index">
        <th mat-header-cell *matHeaderCellDef class="tableId"><b>#</b></th>
        <td mat-cell *matCellDef="let element; let i = index" class="name-cell tableId">
          {{ i + 1 }}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="itemName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Item Name</b>
        </th>
        <td mat-cell *matCellDef="let element">
          {{ element.itemName | titlecase }}
        </td>
        <td mat-footer-cell *matFooterCellDef>Total</td>
      </ng-container>

      <ng-container matColumnDef="pkgName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Pkg Name</b></th>
        <td mat-cell *matCellDef="let element"> {{element.packageName | titlecase}} </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="orderQty">
        <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell"><b>Order Quantity</b> </th>
        <td mat-cell *matCellDef="let element" class="name-cell">
          <span *ngIf='!showButton'>
            {{element.issueQty}}
          </span>
          <div *ngIf='showButton'>
            <input class="input1" type="number" step="0.01" min="0" (keyup)="checkOrderQty($event , element)"
              [(ngModel)]="element.issueQty" (focus)="focusFunctionWithOutForm(element,'issueQty')" (focusout)="focusOutFunctionWithOutForm(element,'issueQty')"/>
          </div>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="unitPrice">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Unit Cost</b>
        </th>
        <td mat-cell *matCellDef="let element">{{ this.utils.truncateNew(element.price)}}</td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="totalValue">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Total (incl.tax,etc)</b></th>
        <td mat-cell *matCellDef="let element">
          {{this.utils.truncateNew(
          ((element.unitPrice ) *
          element.quantity) +element.cessAmt + element.extraAmt - element.discAmt)
          }}
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getTotal())}}</td>
      </ng-container>

      <ng-container matColumnDef="subTotal">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Total (excl.tax)</b></th>
        <td mat-cell *matCellDef="let element">
          {{this.utils.truncateNew(
          ((element.issueQty ) *
          element.price))
          }}
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getSubTotal())}}</td>
      </ng-container>

      <ng-container matColumnDef="taxAmt">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Tax Amt</b></th>
        <td mat-cell *matCellDef="let element">
          {{this.utils.truncateNew(
          ((element.taxAmount/element.quantity ) *
          element.quantity))
          }}
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getTaxTotal())}}</td>
      </ng-container>

      <ng-container matColumnDef="Action">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b> Action</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <button *ngIf="
              element.itemStatus != 'complete' && user.uType === 'restaurant'
            " class="mat-icon-button" matTooltip="Add or Edit Packaging Sizes" (click)="displayPackages(element)">
            <mat-icon>edit</mat-icon>
          </button>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      <tr mat-footer-row *matFooterRowDef="displayedColumns"></tr>
    </table>
  </mat-card-content>
</mat-card>