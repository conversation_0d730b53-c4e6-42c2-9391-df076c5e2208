import { Component, OnInit } from '@angular/core';
import { ShareDataService, PurchasesService, AuthService } from '../_services/';
import { MatSort, Sort, MatTableDataSource, MatDialog } from '@angular/material';
import { SelectionModel } from '@angular/cdk/collections';
import { GlobalsService } from '../_services/globals.service';
import { Location } from '@angular/common';
import { UtilsService } from '../_utils/utils.service';
import { NotificationService } from '../_services/notification.service';
import { Router } from '@angular/router';
import { SimpleDialogComponent } from '../_dialogs/simple-dialog/simple-dialog.component';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-indent-approval-detail',
  templateUrl: './indent-approval-detail.component.html',
  styleUrls: ['./indent-approval-detail.component.scss','./../../common-dark.scss']
})
export class IndentApprovalDetailComponent implements OnInit {
  indentData: any;
  dataSource: MatTableDataSource<any>;
  displayedColumns: String[];
  InvNo: any;
  restaurantBranch: any;
  dialogRefr: any;
  selection = new SelectionModel<any>(true, []);
  user: any;
  approveFlag: boolean = true
  approveBtnText: String
  reason: String
  allExtraFieldFlag: any;
  showButton: boolean = true ;
  isDone: boolean = false ; 
  dialogRef: any;

  constructor(private shareData: ShareDataService,
    private location: Location,
    private notifyService: NotificationService,
    private utils: UtilsService,
    private purchases: PurchasesService,
    private router: Router,
    private auth: AuthService,
    private dialog: MatDialog,
    ) {
    this.user = this.auth.getCurrentUser()
  }

  ngOnInit() {    
    if ('name' in this.user){
      this.approveBtnText = this.user.name + ': Approve'
    }else{
      this.approveBtnText = 'Approve'
    }
    this.displayedColumns = Object.create(GlobalsService.approveIndentDtlColumns);
    this.shareData.indentsDetails.subscribe(order => {      
      this.indentData = order
      if (Object.keys(this.indentData).length > 0){
        // this.location.back();
        this.checkApprovalProcess()
        this.dataSource = new MatTableDataSource<any>();
        this.dataSource.data = this.indentData.indentItems;
        this.getApprovalStatus();
      }else{
        this.router.navigate(['/home/<USER>']);
      }

    });
  }

  getApprovalStatus(){
    let role = this.user.role
    const status = this.indentData.indentApprovalDetail.filter(function(element) {
      return element.role === role;
    });
    this.showButton = status[0].status === 'pending' ? true : false ;
  }

  processInputData(indentData) {
    this.dataSource.data = indentData.indentItems;
    this.indentData.approvalDetail.forEach(el=>{
      if (el.status === "pending") {
        this.showButton = true ;
      } else {
        this.showButton = false ;
      }
  })
  
  }

  cancel() {
    this.location.back()
  }

  checkApprovalProcess() {
    if (this.indentData.isPoCreated == false && this.user.hasOwnProperty('approvalLevel') &&
      this.indentData.poApprovalDetail[this.user.approvalLevel] == 'pending') {
      let levelNum = this.user.approvalLevel.split('')[1]
      if (levelNum == 1) {
        this.approveFlag = true
      }
      else {
        for (let i = levelNum - 1; i >= 1; i--) {
          let level = this.user.approvalLevel.split('')[0] + i
          if (this.indentData.poApprovalDetail[level] == 'approved') {
            this.approveFlag = true
          }
          else {
            this.approveFlag = true
            this.reason = 'Pending from ' + this.user.approvalLevel.split('')[0] + i
            break
          }
        }
      }
    }
  }


  setEachItemStatus() {
    this.selection.selected.forEach(selection => {
      selection.pendingQty = selection.quantity - selection.receivedQty
      if (selection.receivedQty > 0)
        selection.pendingQty === 0 ? selection.itemStatus = 'complete' : selection.itemStatus = 'partial'
    });
  }

  goBack() {
    this.location.back()
  }

  getTotal() {
    let totalPrice = 0
    this.dataSource.data.forEach(element => {
      totalPrice += (element.quantity * element.unitPrice)
    });
    return totalPrice
  }

  getSubTotal() {
    if(this.dataSource.data){
      let subTotalSum = 0
      this.dataSource.data.forEach(element => {
        subTotalSum += element.issueQty * element.price
      });
      return subTotalSum
    }
  }

  getTaxTotal() {
    let taxTotal = 0
    this.dataSource.data.forEach(element => {
      taxTotal += element.taxAmount
    });
    return taxTotal
  }

  getFieldTotal(key: string) {
    return this.utils.getTotal(this.dataSource.data, key);
  }

  approveIndent() {
    let obj = {}
    obj['tenantId'] = this.user.tenantId
    obj['restaurantId'] = this.indentData.restaurantId
    obj['role'] = this.user.role
    obj['indentId'] = this.indentData.indentId
    obj['appCat'] = this.indentData.approvalCategory
    this.purchases.indentApproval(obj).subscribe(data => {
      if (data.success == true) {
        this.utils.snackBarShowSuccess(`Approved Successfully`)
        this.approveFlag = true ;
        this.isDone = true ;
      }
      else {
        this.utils.snackBarShowError('Something went wrong.Please try again later')
      }
    })
  }

  rejectIndent() {
    let obj = {}
    obj['tenantId'] = this.user.tenantId
    obj['restaurantId'] = this.indentData.restaurantId
    obj['role'] = this.user.role 
    obj['indentId'] = this.indentData.indentId
    obj['appCat'] = this.indentData.approvalCategory
    this.dialogRef = this.dialog.open(SimpleDialogComponent, {
      data: {
        title: 'Reject Reason',
        msg: 'Enter Reason(min 10 and max 40 characters allowed)',
        inputFromUser: { 'Reason': '' },
        ok: function () {
          this.dialogRef.afterClosed().subscribe(res => {
            this.reason = res['Reason'];
            if(res['Reason'] == ''){
              this.utils.snackBarShowInfo('Please enter Reason')
            }else{
              obj['reason'] = this.reason;
              this.purchases.indentRejection(obj).subscribe(data => {
                if (data.success == true) {
                  this.utils.snackBarShowSuccess(`Rejected Successfully`)
                  this.approveFlag = true ;
                  this.isDone = true ;
                }
                else {
                  this.utils.snackBarShowError('Something went wrong.Please try again later')
                }
              }, err => console.error(err))
            }
          })
        }.bind(this)
      }
    });
  }

  checkOrderQty(event , element){
  if(event.keyCode == 190){
    return
  }
  this.utils.truncateNew(element.issueQty)
  element.issueQty < 0 ? element.issueQty = 0 : null ;
  element.pendingQty = element.issueQty ;
  }

  modifyIndent() {
    let obj = {}
    obj['tenantId'] = this.user.tenantId ;
    obj['restaurantId'] = this.indentData.restaurantId ;
    obj['items'] = this.dataSource.data ;
    obj['indentId'] = this.indentData.indentId ;
    obj['role'] = this.user.role ;
    obj['user'] = this.user ;
    obj['userEmail'] = this.user.userEmail ;
    obj['baseUrl'] = environment.baseUrl ;
    this.purchases.modifyIndent(obj).subscribe(data => {
      data.success
      ? this.utils.snackBarShowSuccess('Updated and approved successfully')
      : this.utils.snackBarShowError('Something went wrong. Please try again later');
        })
  }


  checkApproval() {
    this.dataSource.data.forEach((el)=> {
      !el.hasOwnProperty('originalQty') ? el['originalQty'] = el['issueQty'] : null ;
    })

    let filteredItem = this.dataSource.data.filter((el) => {
      return el["originalQty"] != el["issueQty"];
    });
    filteredItem.length == 0 ? this.approveIndent() : this.modifyIndent();
  }

  focusFunctionWithOutForm(element , value){
    if(Number(element[value]) === 0){
      element[value] = null;
    }
  }
  
  focusOutFunctionWithOutForm(element , value){
    if(element[value] === null){
      element[value] = 0
    }
  }
}
