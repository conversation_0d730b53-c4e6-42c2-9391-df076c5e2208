import { Component, OnInit, Input, ViewChild } from "@angular/core";
import { Router } from "@angular/router";
import { GlobalsService } from "../_services";
import {
  MatTableDataSource,
  MatPaginator,
  MatSort,
  MatDialog,
  MatOption,
} from "@angular/material";
import { SelectionModel } from "@angular/cdk/collections";
import { UtilsService } from "../_utils/utils.service";
import { ShareDataService } from "../_services/share-data.service";
import { PurchasesService, AuthService } from "../_services/";
import { NotificationService } from "../_services/notification.service";
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from "@angular/forms";
import { ReplaySubject, Subject } from "rxjs";
import { BranchTransferService } from "../_services/";
import { SimpleDialogComponent } from "../_dialogs/simple-dialog/simple-dialog.component";
import { takeUntil } from "rxjs/operators";
import { SharedFilterService } from "../_services/shared-filter.service";

@Component({
  selector: "app-indent-approval",
  templateUrl: "./indent-approval.component.html",
  styleUrls: ["./indent-approval.component.scss", "./../../common-dark.scss"],
})
export class IndentApprovalComponent implements OnInit {
  indentApprovalList: any = [];
  @ViewChild(MatSort) sort: MatSort;
  searchText: string;
  searchValue: string;
  indentArea: any;
  indentAreas: any[] = ["All"];
  tempData: any;
  selectedWorkarea: any;
  @ViewChild(MatPaginator) set paginator(value: MatPaginator) {
    if (this.dataSource) {
      this.dataSource.paginator = value;
    }
  }
  restaurantId: any;
  selectedBranch: any;
  selectedApprovalStatus: any;
  selectedOrderStatus: any;
  selectedStartDate: any;
  selectedEndDate: any;
  multiBranchUser;
  branchSelected: boolean;
  dataSource = new MatTableDataSource();
  displayedColumns: string[];
  selection = new SelectionModel<any>(true, []);
  purchaseStatus: string[] = [
    "All",
    "Complete",
    "Partial",
    "shortage",
    "Pending",
  ];
  vendorsList: any[] = ["All"];
  filteredByDateList: any[];
  filterKeys = { status: { orderStatus: "All" } };
  date: any = { startDate: "", endDate: "" };
  pageSizes: any[];
  user: any;
  vendors = new FormControl();
  startDate = new FormControl();
  endDate = new FormControl();
  branches: any[];
  getBranchData: any[];
  indentApprovalForm: FormGroup;
  checkSelection: boolean;
  dialogRef: any;
  IndentApproval = encodeURI(GlobalsService.IndentApproval);
  workAreaForm = new FormControl();
  indentReqUrl = encodeURI(GlobalsService.indentRequests);
  IndentApprovalDetailUrl = encodeURI(GlobalsService.IndentApprovalDetail);
  indentReqFlag: boolean;
  private unsubscribe$ = new Subject<void>();
  sharedFilterData: any = {};
  public Bank: any[] = [];
  public VendorBank: any[] = [];
  public workAreaFilterCtrl: FormControl = new FormControl();
  public workAreaBanks: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  protected _onDestroy = new Subject<void>();
  @ViewChild('allSelected') private allSelected: MatOption;

  constructor(
    private utils: UtilsService,
    private router: Router,
    private auth: AuthService,
    private purchases: PurchasesService,
    private notifyService: NotificationService,
    private sharedData: ShareDataService,
    private fb: FormBuilder,
    private branchTransfer: BranchTransferService,
    private dialog: MatDialog,
    private sharedFilterService: SharedFilterService
  ) {
    this.user = this.auth.getCurrentUser();
    this.multiBranchUser = this.user.multiBranchUser;
    this.indentApprovalForm = this.fb.group({
      branchSelection: [null, Validators.required],
    });

    this.sharedFilterService.getFilteredIndentApproval
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((obj) => (this.sharedFilterData = obj));

    this.sharedData.sharedBranchData
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((val) => {
        this.getBranchData = val;
        if (this.getBranchData.length == 0) {
          this.branches = this.user.restaurantAccess;
        } else if (this.getBranchData.length == 1) {
          const toSelect = this.getBranchData.find(
            (data) => data.branchName == this.getBranchData[0].branchName
          );
          if (toSelect != this.sharedFilterData.restaurantId) {
            this.sharedFilterData = "";
            this.workAreaForm.setValue("All");
            this.startDate.setValue(null);
            this.endDate.setValue(null);
            this.selectedApprovalStatus = null;
          }
          this.indentApprovalForm.get("branchSelection").setValue(toSelect);
          this.branches = this.getBranchData;
          this.filterByBranch(this.indentApprovalForm.value.branchSelection);
        } else {
          this.branches = this.getBranchData;
        }
      });
    // }

    // Added from indent list screen

    if (this.router.url.includes(this.indentReqUrl)) {
      this.indentReqFlag = true;
    } else {
      this.indentReqFlag = false;
    }
  }

  ngOnInit() {
    if (this.branchSelected) {
    } else {
      if (!this.user.multiBranchUser) {
        this.restaurantId = this.user.restaurantAccess[0].restaurantIdOld;
        this.branchSelected = true;
        this.setupCall();
      }
    }
    this.user.restaurantAccess.forEach((element) => {
      if (element.restaurantIdOld == this.restaurantId) {
        element.workAreas.forEach((item) => this.indentAreas.push(item));
      }
    });
    this.displayedColumns = [
      "indentId",
      "workArea",
      "indentDocumentDate",
      "indentStatus",
      "status",
    ];
      }

  ngOnDestroy() {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
    if (!this.router.url.includes(this.IndentApprovalDetailUrl)) {
      this.sharedFilterService.getFilteredIndentApproval["_value"] = "";
    }
  }

  selectWorkArea(workarea) {
    this.selectedWorkarea = workarea;
    if (this.selectedApprovalStatus != undefined) {
      if (this.sharedFilterData.approvalStatus) {
        this.sharedFilterData.approvalStatus = null;
      }
    }
    this.setupCall();
  }

  setupCall() {
    this.user = this.auth.getCurrentUser();
    this.multiBranchUser = this.user.multiBranchUser;
    let obj = {
      tenantId: this.user.tenantId,
      uType: this.user.uType,
      restaurantId: this.restaurantId,
      userEmail: this.auth.getCurrentUser().email,
    };

    if (this.vendors.value && this.tempData) {
      let array = [];
      array.push(this.vendors.value);
      array = array[0].filter((item) => item !== "All");

      // "filter data using vendorname"
      const filteredData = this.tempData.filter((item) =>
        array.includes(item.vendorDetails.vendorName)
      );

      // "remove Duplicate data"
      const uniqueData = filteredData.filter((obj, index, self) => {
        return (
          index ===
          self.findIndex(
            (item) =>
              item.vendorDetails.vendorName === obj.vendorDetails.vendorName
          )
        );
      });

      const vendorIds = [];
      uniqueData.forEach((obj: any) => {
        vendorIds.push(obj.vendorDetails.vendorId);
      });

      if (vendorIds.length === 0) {
        obj["vendorId"] = ["All"];
        this.vendors.setValue(["All"]);
      } else {
        obj["vendorId"] = vendorIds;
      }
    } else {
      obj["vendorId"] = ["All"];
    }

    if (this.startDate.value && this.endDate.value) {
      obj["startDate"] =  this.utils.dateCorrection(this.startDate.value);
      obj["endDate"] = this.utils.dateCorrection(this.endDate.value);
    } else {
      obj["startDate"] = null;
      obj["endDate"] = null;
    }
    if (this.workAreaForm.value != undefined && this.workAreaForm.value != '1') {
      obj["workArea"] = this.workAreaForm.value;
    } else {
      obj["workArea"] = 'All';
    }
    this.branchTransfer.getIndentRequestList(obj).subscribe((data) => {
      this.filteredByDateList = data;
      this.indentApprovalList = data;
      this.pageSizes = this.utils.getPageSizes(this.indentApprovalList);
      if (this.tempData == undefined) {
        this.tempData = data;
      }
      this.getIndentList();
    });
  }

  getIndentList() {
    this.vendorsList = ["All"];
    this.indentApprovalList.forEach(async (element, index) => {
      await this.getApprovalStatus(element);
      if (this.indentApprovalList.length - 1 == index) {
        this.filteredByDateList = this.indentApprovalList;
        this.dataSource.sort = this.sort;
      }
    });
    if (this.selectedApprovalStatus == undefined) {
      this.selectedApprovalStatus = "pending";
    } else {
      if (this.sharedFilterData.approvalStatus) {
        this.selectedApprovalStatus = this.sharedFilterData.approvalStatus;
      }
    }
    this.filterByStatus(this.selectedApprovalStatus);
  }

  async getApprovalStatus(element: any) {
    element.indentApprovalDetail.forEach((el) => {
      if (el.role === this.user.role) {
        element["approvalStatus"] = `${el.status}`;
      }
    });
  }

  doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleUpperCase();
  };

  receiveIndent(obj) {
    let uniqueArray = [];
    this.tempData.forEach((obj) => {
      // Check if an object with the same id already exists in uniqueArray
      if (!uniqueArray.some((item) => item.prId === obj.prId)) {
        uniqueArray.push(obj); // Push the object to uniqueArray if it's not a duplicate
      }
    });

    let inputObj = {
      restaurantId: this.indentApprovalForm.value.branchSelection,
      selectedStartDate: this.startDate.value,
      selectedEndDate: this.endDate.value,
      approvalStatus: this.selectedApprovalStatus,
      workArea: this.workAreaForm.value,
      data: uniqueArray,
    };
    this.sharedFilterService.getFilteredIndentApproval.next(inputObj);
    this.sharedData.changeIndentDetails(obj);
    this.router.navigate(["/home/<USER>"]);
  }

  allFilter() {
    let tmp = this.filteredByDateList;
    let prev = this.filteredByDateList;
    Object.keys(this.filterKeys).forEach((element) => {
      Object.keys(this.filterKeys[element]).forEach((nestElement) => {
        if (this.filterKeys[element][nestElement] != "All") {
          tmp = prev.filter((purOrder) => {
            if (purOrder.status !== undefined) {
              return (
                purOrder.status.toLowerCase() ===
                this.filterKeys[element][nestElement].toLowerCase()
              );
            }
          });
          prev = tmp;
        }
      });
    });
    this.dataSource.data = tmp;
  }

  selectPurchaseStatus(status) {
    this.filterKeys.status = { orderStatus: status };
    this.allFilter();
  }

  filterByDate() {
    this.setupCall();
  }

  resetForm() {
    this.searchText = "";
    this.date = "";
    this.filteredByDateList = this.dataSource.data = this.indentApprovalList;
    this.vendors.setValue("");
    this.filterKeys.status.orderStatus = "All";
    this.searchValue = "";
    this.doFilter(this.searchValue);
    this.dataSource.data = this.indentApprovalList;
  }

  filterByBranch(restId) {
    this.VendorBank = restId.workAreas;
    this.workAreaBanks.next(this.VendorBank.slice());
    this.workAreaFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
      this.vendorfilterBanks();
    });
    this.branchSelected = true;
    if (this.sharedFilterData != "") {
      if (this.sharedFilterData.restaurantId == restId) {
        this.indentApprovalForm
          .get("branchSelection")
          .setValue(this.sharedFilterData.restaurantId);
      } else {
        this.indentApprovalForm.get("branchSelection").setValue(restId);
      }
      this.indentApprovalForm
        .get("branchSelection")
        .setValue(this.sharedFilterData.restaurantId);
      this.branches = this.getBranchData;
      this.selectedStartDate = this.sharedFilterData.selectedStartDate;
      this.selectedEndDate = this.sharedFilterData.selectedEndDate;
      this.startDate.setValue(this.sharedFilterData.selectedStartDate);
      this.endDate.setValue(this.sharedFilterData.selectedEndDate);
      this.selectedApprovalStatus = this.sharedFilterData.approvalStatus;
      this.tempData = this.sharedFilterData.data;
    } else {
      this.workAreaForm.setValue("All");
      this.startDate.setValue(null);
      this.endDate.setValue(null);
      this.selectedApprovalStatus = null;
    }
    if (this.sharedFilterData == "") {
      this.workAreaForm.setValue("All");
    } else {
      if (this.sharedFilterData.workArea) {
        this.workAreaForm.setValue(this.sharedFilterData.workArea);
      }
    }
    this.restaurantId = this.indentApprovalForm.value.branchSelection.restaurantIdOld;
    this.setupCall();
  }

  protected vendorfilterBanks() {
    if (!this.VendorBank) {
      return;
    }
    let search = this.workAreaFilterCtrl.value;
    if (!search) {
      this.workAreaBanks.next(this.VendorBank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.workAreaBanks.next(
      this.VendorBank.filter(
        (VendorBank) => VendorBank.toLowerCase().indexOf(search) > -1
      )
    );
  }

  filterByStatus(event) {
    this.selectedApprovalStatus = event;
    if (event == "All") {
      this.displayedColumns = ['indentId', 'workArea', 'indentDocumentDate','indentStatus', 'status'];
      this.dataSource.data = this.indentApprovalList;
      this.pageSizes = this.utils.getPageSizes(this.dataSource.data);
      this.selection = new SelectionModel<any>(true, this.dataSource.data);
      this.masterToggle();
    } else {
      this.dataSource.data = [];
      this.dataSource.data =
        event === "pending"
          ? this.indentApprovalList.filter(
              (request) =>
                request.approvalStatus != "rejected" &&
                request.approvalStatus != "approved"
            )
          : this.indentApprovalList.filter(
              (request) => request.approvalStatus === event
            );
      this.pageSizes = this.utils.getPageSizes(this.dataSource.data);
      this.displayedColumns =
        event === "pending"
          ? ['select','indentId', 'workArea', 'indentDocumentDate','indentStatus', 'status']
          : ['indentId', 'workArea', 'indentDocumentDate','indentStatus', 'status'];
      this.checkSelection = this.dataSource.data.length > 0 ? true : false;
      if (this.checkSelection && event == "pending") {
        this.isAllSelected();
        this.masterToggle();
      } else {
        !this.isAllSelected()
          ? ((this.selection = new SelectionModel<any>(
              true,
              this.dataSource.data
            )),
            this.masterToggle())
          : null;
      }
    }
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  masterToggle() {
    this.isAllSelected()
      ? this.selection.clear()
      : this.dataSource.data.forEach((row) => this.selection.select(row));
  }

  clear() {
    this.workAreaForm.setValue("All");
    this.startDate.setValue(null);
    this.endDate.setValue(null);
    this.selectedApprovalStatus = null;
    this.setupCall();
  }

  approveBulkIndents() {
    let obj = {
      tenantId: this.user.tenantId,
      restaurantId: this.restaurantId,
      approvalData: this.selection.selected,
      role: this.user.role,
    };
    this.purchases.indentBulkApproval(obj).subscribe((data) => {
      data.result === "success"
        ? this.utils.snackBarShowSuccess(data.message)
        : this.utils.snackBarShowError(data.message);
      this.setupCall();
    });
  }

  rejectBulkIndent() {
    let obj = {
      tenantId: this.user.tenantId,
      restaurantId: this.restaurantId,
      approvalData: this.selection.selected,
      role: this.user.role,
    };
    this.dialogRef = this.dialog.open(SimpleDialogComponent, {
      data: {
        title: "Reject Reason",
        msg: "Enter Reason(min 10 and max 40 characters allowed)",
        inputFromUser: { Reason: "" },
        ok: function () {
          this.dialogRef.afterClosed().subscribe((res) => {
            this.reason = res["Reason"];
            if (res["Reason"] == "") {
              this.utils.snackBarShowInfo("please provide the vaild reason here..");
            } else {
              obj["reason"] = this.reason;
              this.purchases.indentBulkRejection(obj).subscribe(
                (data) => {
                  data.result === "success"
                    ? this.utils.snackBarShowSuccess(data.message)
                    : this.utils.snackBarShowError(data.message);
                    this.setupCall();
                },
                (err) => console.error(err)
              );
            }
          });
        }.bind(this),
      },
    });
  }

  toggleAllSelection(manual = false) {
    if (this.allSelected && this.allSelected.selected ) {
      this.workAreaForm.patchValue([...this.VendorBank, 1]);
    } else if (manual){
      // this.workAreaForm.patchValue([...this.user.restaurantAccess.map(item => item.branchName), 1]);
      // const singleData = [this.user.restaurantAccess[0]]
      this.workAreaForm.patchValue([...this.VendorBank,1]);
    }else {
      this.workAreaForm.patchValue([]);
    }
  }

}
