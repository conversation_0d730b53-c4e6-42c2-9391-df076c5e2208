<div class="title">
  <form [formGroup]="indentPredictionForm" class="topHeadInputs">
    <mat-form-field *ngIf="multiBranchUser" appearance="none" class="topitem">
      <label>Select Branch</label>
      <mat-select placeholder="Select Branch" class="outline" formControlName="branchSelection" (selectionChange)="filterByBranch($event.value)">
        <mat-option *ngFor="let rest of branches" [value]="rest">
          {{ rest.branchName }}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </form>

  <mat-form-field appearance="none" *ngIf="specialFlag == true" style="margin-left: 10px;" class="topitem">
    <label>Select Work Area</label>
    <mat-select placeholder="Select Work Area" (selectionChange)="selectIndentArea($event)" class="outline">
      <mat-option>
        <ngx-mat-select-search placeholderLabel="Work Area..."
          noEntriesFoundLabel="'Work Area Not Found'"
          [formControl]="workAreaFilterCtrl"></ngx-mat-select-search>
      </mat-option>
      <mat-option *ngFor="let area of workAreasBanks | async" [value]="area">
        {{ area }}
      </mat-option>
    </mat-select>
  </mat-form-field>

  <button mat-button mat-raised-button class="button3" *ngIf=buttonAccess (click)="issueIndentReq()"
    style="float: right;">
    Raise Indent
  </button>

  <button mat-button mat-raised-button class="button" *ngIf=buttonAccess (click)="exportToExcel()"
    style="float: right;">
    Export
  </button>
  <button mat-button mat-raised-button class="button" *ngIf=buttonAccess (click)="printToPdf()" style="float: right;">
    Print
  </button>
  <!-- <div > -->
    <mat-slide-toggle *ngIf='workAreaSelected' style="float: right;" [(ngModel)]="indentPreview" (change)="preview()">Preview Indent
    </mat-slide-toggle>
  <!-- </div> -->
</div>

<div *ngIf="(branchSelected && multiBranchUser && workAreaSelected) || (!multiBranchUser && workAreaSelected)"
  class="datacontainer">
  <mat-card>
    <mat-card-content>
      <br>
      <div class="search-table-input">
        <mat-form-field appearance="none">
          <label>Search</label>
          <input matInput type="text" (keyup)="doFilter($event.target.value)" placeholder="Search" class="outline"
            [(ngModel)]='searchText' />
            <mat-icon matSuffix (click)="clear()" class="closebtn">close</mat-icon>
        </mat-form-field>
        <mat-form-field appearance="none">
          <label>Type</label>
          <mat-select placeholder="Item Type" [(ngModel)]="itmtype" [formControl]="ItemType" class="outline">
            <mat-option *ngFor="let itemType of ItemTypeList" [value]="itemType" (click)="selectItemType(itemType)">
              {{ itemType | titlecase }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field appearance="none">
          <label>Category</label>
          <mat-select placeholder="Category" class="outline" [(ngModel)]="cat" [formControl]="category">
            <mat-option *ngFor="let cat of categoryList" [value]="cat" (click)="selectCategory(cat)">
              {{ cat | titlecase}}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field appearance="none">
          <label>Sub Category</label>
          <mat-select placeholder="Subcategory" [formControl]="Subcategory" [(ngModel)]="subCat" class="outline">
            <mat-option *ngFor="let subCat of subCategoryList" [value]="subCat" (click)="selectSubCat(subCat)">
              {{ subCat | titlecase }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- <button mat-stroked-button class="clrButton button3" (click)=clear()>Clear</button> -->
        <button mat-button class="buttonForRefresh refreshBtn" (click)="refreshdata()">Refresh</button>
        
      </div>
      <table #table mat-table [dataSource]="dataSource" matSortActive="itemCode" matSortDirection="asc" matSort>
        <ng-container matColumnDef="itemName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Item Code</b>
          </th>
          <td mat-cell *matCellDef="let element" (click)="displayPackages(element)">
            {{ element.itemName | titlecase }}
          </td>
        </ng-container>

        <ng-container matColumnDef="inStock">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> In Store</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ this.utils.truncateNew(element.inStock) }}
          </td>
        </ng-container>
        <ng-container matColumnDef="index">
          <th mat-header-cell *matHeaderCellDef mat-sort-header class="tableId"><b>#</b></th>
          <td mat-cell *matCellDef="let element; let i = index" class="tableId">
            {{ i + 1 + paginator.pageIndex * paginator.pageSize }}
          </td>
        </ng-container>
        <ng-container matColumnDef="PredictedQty">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Predicted Consumption</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ this.utils.truncateNew(element.predictedQty ) }}
          </td>
        </ng-container>

        <ng-container matColumnDef="estimatedQty">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Required Qty</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ this.utils.truncateNew(element.estimatedQty) }}
          </td>
        </ng-container>

        <ng-container matColumnDef="workAreaStock">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> WorkArea Stock</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ this.utils.truncateNew(element.workAreaStock) }}
          </td>
        </ng-container>

        <ng-container matColumnDef="pkgName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Pkg Name</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.packageName }}
          </td>
        </ng-container>

        <ng-container matColumnDef="entryType">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Entry Type</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.entryType }}
          </td>
        </ng-container>


        <ng-container matColumnDef="projectedSales">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Projected Consumption</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ this.utils.truncateNew(element.projectedSales) }}
          </td>
        </ng-container>
        <ng-container matColumnDef="reqQty">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Indent Quantity</b>
          </th>
          <td mat-cell *matCellDef="let element">
            <input class="input1" type="number" step="any"  min="0"  [(ngModel)]="element.issueQty"
              onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)" />
          </td>
        </ng-container>
        <ng-container matColumnDef="uom">
          <th mat-header-cell *matHeaderCellDef mat-sort-header><b> Uom</b></th>
          <td mat-cell *matCellDef="let element">{{ element.uom }}</td>
        </ng-container>
        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>
    </mat-card-content>
    <div class="dataMessage" *ngIf="dataSource?.data.length == 0"> No Data Available </div>
  </mat-card>
  <mat-paginator [showTotalPages]="5" [pageSize]="10" [pageSizeOptions]="pageSizes"></mat-paginator>
</div>