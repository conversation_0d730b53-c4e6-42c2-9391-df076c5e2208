import { Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService, PurchasesService, BranchTransferService, ShareDataService } from '../_services';
import { GlobalsService } from '../_services/globals.service';
import { UtilsService } from '../_utils/utils.service';
import { MatTableDataSource, MatPaginator } from '@angular/material';
import { NotificationService } from '../_services/notification.service';
import { FormBuilder, FormControl, FormGroup, Validators } from "@angular/forms";
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { interval, Subscription,ReplaySubject} from 'rxjs';

@Component({
  selector: 'app-indent-prediction',
  templateUrl: './indent-prediction.component.html',
  styleUrls: ['./indent-prediction.component.scss', './../../common-dark.scss']
})
export class IndentPredictionComponent implements OnInit {
  IndentAreas: string[] = [];
  buttonAccess: boolean = false;
  indentArea: any;
  specialFlag: boolean;
  defaultArea: any;
  all = "ALL";
  indentPreview: boolean = false;
  curDataSource: any[];
  indentPredictionsUrl = encodeURI(GlobalsService.indentPredictions)
  user: any;
  inventoryItems: any[];
  displayedColumns;
  title;
  restaurantId: any;
  savedItems: any;
  workAreaSelected: boolean = false;
  initData: any;
  branchSelected: any;
  selectedWorkArea: any;
  multiBranchUser: any;
  categoryList = ['All'];
  subCategoryList = ['All'];
  ItemTypeList = ['All'];
  initSubCategoryList: any = [];
  initCategoryList: any = [];
  subCatList: any = [];
  searchText: any
  filterKeys = { ItemType: 'All', category: 'All', subCategory: 'All' }
  category = new FormControl('', [Validators.required]);
  Subcategory = new FormControl();
  ItemType = new FormControl();
  dataSource: MatTableDataSource<any>;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  pageSizes = [];
  branches: any[];
  getBranchData: any[]
  indentPredictionForm: FormGroup;
  itmtype: any;
  cat: any;
  subCat: any;
  private unsubscribe$ = new Subject<void>();
  public workAreaFilterCtrl: FormControl = new FormControl();
  public workAreasBanks: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  protected _onDestroy = new Subject<void>();
  public workAreaBank: any[] = [];

  constructor(private auth: AuthService, private purchases: PurchasesService,
    private branchTransfer: BranchTransferService,
    private notifyService: NotificationService, private utils: UtilsService,
    private router: Router,
    private sharedData: ShareDataService,
    private fb: FormBuilder,) {
    this.user = this.auth.getCurrentUser()
    this.indentPredictionForm = this.fb.group({
      branchSelection: [null, Validators.required]
    });
    var windowLocation = window.location.href;
    let windowCurrentUrl = windowLocation.split('/')[4]
    
      this.sharedData.sharedBranchData.pipe(takeUntil(this.unsubscribe$)).subscribe((val) => {
        this.getBranchData = val;
        if(this.getBranchData.length == 0 ){
          this.branches = this.user.restaurantAccess;
        }else if(this.getBranchData.length == 1){        
          const toSelect = this.getBranchData.find(data => data.branchName == this.getBranchData[0].branchName);
          this.indentPredictionForm.get('branchSelection').setValue(toSelect);
          this.branches = this.getBranchData
          this.filterByBranch(this.indentPredictionForm.value.branchSelection);
        }else{
          this.branches = this.getBranchData
        }
    });
  }

  ngOnInit() {
    this.multiBranchUser = this.user.multiBranchUser    
    if (!this.user.multiBranchUser) {
      this.restaurantId = this.user.restaurantAccess[0].restaurantIdOld
    }
    this.user.restaurantAccess.forEach(element => {
      if (element.restaurantIdOld == this.restaurantId) {
        this.IndentAreas = element.workAreas;
        this.workAreaBank = this.IndentAreas;
        this.workAreasBanks.next(this.workAreaBank.slice());
        this.workAreaFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
            this.workAreafilterBanks();
        });
      }
    });
    this.defaultArea = 'Kitchen';
    this.specialFlag = this.router.url.includes(this.indentPredictionsUrl);
    this.dataSource = new MatTableDataSource()
    this.displayedColumns = GlobalsService.indentPredictionsColumns;
    this.title = "Indent Predictions"
  }

  ngOnDestroy() {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  selectIndentArea(val) {
    this.branchSelected = true;
    this.workAreaSelected = true;
    this.selectedWorkArea = val.value;    
    this.getIndentPrediction();
  }

  getIndentPrediction() {
    let obj = {};
    obj['tenantId'] = this.user['tenantId'];
    obj['restaurantId'] = this.restaurantId;
    obj['workArea'] = this.selectedWorkArea;
    this.branchTransfer.getIndentPrediction(obj).subscribe(res => {
      if (res['result'] == 'success') {
        this.inventoryItems = res['invItems']
        this.inventoryItems.map((item: any) => {
          item.issueQty = this.utils.truncateNew(item.estimatedQty)
          return item
        });
        this.dataSource.data = this.inventoryItems
        this.curDataSource = this.dataSource.data
        this.inventoryItems.forEach(item => {
          if (item.category == null) {
            item.category = 'N/A'
          }
          if (item.ItemType == null) {
            item.ItemType = 'N/A'
          }
          if (item.subCategory == null) {
            item.subCategory = 'N/A'
          }
          this.ItemTypeList.push(item.ItemType)
          this.categoryList.push(item.category)
          this.subCategoryList.push(item.subCategory)
        })
        if(this.itmtype){
          this.selectItemType(this.itmtype);
        }
        if(this.cat){
          this.selectCategory(this.cat);
        }
        if(this.subCat){
          this.selectSubCat(this.subCat);
        }
        this.categoryList = this.categoryList.filter((k, i, ar) => ar.indexOf(k) === i);
        this.ItemTypeList = this.ItemTypeList.filter((p, q, arrr) => arrr.indexOf(p) === q);
        this.subCategoryList = this.subCategoryList.filter((j, l, arr) => arr.indexOf(j) === l);
        this.initCategoryList = this.categoryList;
        this.initSubCategoryList = this.subCategoryList;
        this.buttonAccess = true;
        this.pageSizes = this.utils.getPageSizes(this.inventoryItems)
        this.dataSource.paginator = this.paginator;
      }
      else {
        this.dataSource.data = []
        this.inventoryItems = []
      }
    }, err => { console.log(err) }
    );
  }

  filterByBranch(restId) {
    this.restaurantId = restId.restaurantIdOld;
    this.branchSelected = true;
    this.user.restaurantAccess.forEach(element => {
      if (element.restaurantIdOld == this.restaurantId) {
        this.IndentAreas = element.workAreas;
        this.workAreaBank = this.IndentAreas;
        this.workAreasBanks.next(this.workAreaBank.slice());
        this.workAreaFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
            this.workAreafilterBanks();
        });
      }
    });
    this.category.setValue('')
    this.dataSource = new MatTableDataSource()
  }

  issueIndentReq() {
    let itemsToIssue = this.inventoryItems.filter(item => item.issueQty > 0)
    if (itemsToIssue.length > 0) {
      this.branchTransfer.issueIndentReq({
        invItems: itemsToIssue,
        userEmail: this.user.email,
        tenantId: this.user.tenantId,
        indentArea: this.selectedWorkArea,
        restaurantId: this.restaurantId,
        autoIndent: true
      }).subscribe(data => {
        if (data.hasOwnProperty('updatedIndent')) {
          this.savedItems = [data.updatedIndent]
        }
        this.utils.snackBarShowSuccess('Indent Sent');
        this.dataSource.data = this.inventoryItems
          .map((item: any) => { item.issueQty = 0; return item })
      }, err => console.error(err))
    }
    else {
      this.utils.snackBarShowWarning('No Items to issue. Please add Item');
    }
  }

  preview() {
    if (this.indentPreview == true) {
      this.curDataSource = this.dataSource.data
      this.dataSource.data = this.inventoryItems.filter(item => item.issueQty > 0)
    }
    else {
      this.dataSource.data = this.curDataSource
    }
  }

  selectItemType(itemType) {
    let filteredCategoryList = []
    let filteredSubCategoryList = []
    if (itemType != 'All') {
      let filteredItem = this.curDataSource.filter(item => item['ItemType'].toUpperCase() === itemType.toUpperCase());
      filteredItem.forEach(element => {
        filteredCategoryList.push(element.category)
        filteredSubCategoryList.push(element.subCategory)
      });
      this.categoryList = filteredCategoryList.filter((k, i, ar) => ar.indexOf(k) === i);
      this.subCategoryList = filteredSubCategoryList.filter((k, i, ar) => ar.indexOf(k) === i);
      this.categoryList.splice(0, 0, 'All')
      this.subCategoryList.splice(0, 0, 'All')
      this.subCatList = this.subCategoryList
    }
    else {
      this.categoryList = this.initCategoryList;
      this.subCategoryList = this.initSubCategoryList;
    }
    this.filterKeys = { ItemType: itemType, category: 'All', subCategory: 'All' }
    this.allFilter()
  }

  selectCategory(cat) {
    let filteredSubCategoryList = []
    let filteredItem = []
    if (cat != 'All') {
      if (this.filterKeys.ItemType != 'All') {
        filteredItem = this.curDataSource.filter(item => item['category'].toUpperCase() === cat.toUpperCase()
          && item['ItemType'].toUpperCase() === this.filterKeys.ItemType.toUpperCase());
      }
      else {
        filteredItem = this.curDataSource.filter(item => item['category'].toUpperCase() === cat.toUpperCase());
      }
      filteredItem.forEach(element => {
        filteredSubCategoryList.push(element.subCategory)
      });
      this.subCategoryList = filteredSubCategoryList.filter((k, i, ar) => ar.indexOf(k) === i);
      this.subCategoryList.splice(0, 0, 'All')
    }
    else if (this.filterKeys.ItemType != 'All') {
      this.subCategoryList = this.subCatList
    }
    else {
      this.subCategoryList = this.initSubCategoryList
    }
    this.filterKeys.category = cat;
    this.filterKeys.subCategory = 'All'
    this.allFilter()
  }

  selectSubCat(subCat) {
    this.filterKeys.subCategory = subCat;
    this.allFilter()
  }

  allFilter() {
    let tmp = this.curDataSource
    let prev = this.curDataSource
    Object.keys(this.filterKeys).forEach(element => {
      if (this.filterKeys[element] != 'All') {
        tmp = prev.filter(item =>
          item[element].toLowerCase() === this.filterKeys[element].toLowerCase())
        prev = tmp
      }
    });
    this.dataSource.data = tmp
  }

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }

  clear() {
    this.category.setValue('')
    this.Subcategory.setValue('')
    this.ItemType.setValue('')
    this.searchText = ''
    this.dataSource.data = this.inventoryItems
    this.doFilter(this.searchText)
  }

  printToPdf() {
    let inventoryList = {};
    inventoryList['inventoryItems'] = [];
    this.dataSource.data.forEach(function (item) {
      if (item['issueQty'] > 0) {
        inventoryList['inventoryItems'].push(item);
      }
    });
    if (inventoryList['inventoryItems'].length == 0) {
      this.utils.snackBarShowWarning('please enter indent values');
      return;
    }
    inventoryList['user'] = this.user;
    this.purchases.printpdfs(inventoryList, 'Indent').subscribe(data => {
      // window.open('data:application/pdf;base64,' + data.eFile);
      this.purchases.globalPrintPdf(data.eFile);
    });
  }

  exportToExcel() {
    let inventoryList = {}
    inventoryList['indentArea'] = this.selectedWorkArea;
    inventoryList['inventoryItems'] = this.dataSource.data;
    inventoryList['user'] = this.user;
    this.purchases.exportToExcel(inventoryList, 'Indent').subscribe(data => {
      window.open('data:text/csv;base64,' + data.eFile);
    });
  }

  validateIssueQty(element) {
    if(element.issueQty == null){
      element.issueQty = 0;
    }
    element.inStock < element.issueQty ? element.issueQty = element.inStock : element;
  }

  refreshdata(){
    this.getIndentPrediction();
  }

  protected workAreafilterBanks() {
    if (!this.workAreaBank) {
      return;
    }
    let search = this.workAreaFilterCtrl.value;
    if (!search) {
      this.workAreasBanks.next(this.workAreaBank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.workAreasBanks.next(
      this.workAreaBank.filter(workAreaBank => workAreaBank.toLowerCase().indexOf(search) > -1)
    );
  }
}

