<div class="title">
  <button mat-raised-button class="button" style="float: left; margin-left: 0px;" (click)=goBack()>
    <mat-icon>keyboard_backspace</mat-icon> Back to Indent List
  </button>
  <div *ngIf=" !isRequest">
    <div style="float: right;" *ngIf='!indentReqFlag' [matTooltip]="centralIndent  ? 'Central Store Indent' : ''">
      <button *ngIf='!indentReqFlag' mat-button mat-raised-button class="button3" style="float: right;"
        (click)="dispatchIndent()"
        [disabled]="disableIndent || indent?.status == 'completed' || indent?.status == 'closed'|| !activeIndent || inValidIndent || centralIndent || !partialAccess">
        Issue Indent
      </button>
    </div>
  </div>

  <div *ngIf="indent?.status == 'closed' &&  !isRequest && indent?.autoClosure && !indent?.childId" >
    <div style="float: right;" *ngIf='!indentReqFlag'>
      <button *ngIf='!indentReqFlag' mat-button mat-raised-button class="button3" style="float: right;"
        (click)="reopenIndent()"  [disabled]="disableCloning">
        Clone Indent
      </button>
    </div>
  </div>

  <button mat-button mat-raised-button *ngIf="!centralIndent" class="button" (click)="exportToExcel()"
    style="float: right;">
    Export
  </button>
  <button mat-button mat-raised-button *ngIf="!centralIndent" class="button" (click)="printToPdf()"
    style="float: right;">
    Print
  </button>
</div>
<div class="search-table-input fieldcontainer" *ngIf='!finalSubmit'>
  <div class="row">
    <div class="col">
      <table class="table">
        <tbody>
          <tr>
            <th class="topItemkey" scope="row">Indent Id</th>
            <td>{{ indent?.indentId }}</td>
          </tr>
          <tr>
            <th class="topItemkey" scope="row">Issue Id</th>
            <td>{{ indent?.issueId ? indent?.issueId : 'N/A' }}</td>
          </tr>
          <tr>
            <th class="topItemkey" scope="row">Requested Date</th>
            <td>{{ this.utils.formatDateToUTC(indent?.createTs) }}</td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="col">
      <table class="table">
        <tbody>
          <tr>
            <th class="topItemkey" scope="row">Work Area</th>
            <td>{{ indent?.workArea }}</td>
          </tr>
          <tr>
            <th class="topItemkey" scope="row">Issued Date</th>
            <td>{{ indent?.modTs ? (this.utils.formatDateToUTC(indent?.modTs)): 'N/A' }}</td>
          </tr>
          <tr *ngIf = "indent?.childId">
            <th class="topItemkey" scope="row">Child Indent</th>
            <td>{{indent?.childId ? indent?.childId : '-' }}</td>
          </tr>
          <tr *ngIf = "indent?.parentId">
            <th class="topItemkey" scope="row">Parent Indent</th>
            <td>{{indent?.parentId ? indent?.parentId : '-' }}</td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="col">
      <table class="table">
        <tbody>
          <tr *ngIf='dataLoaded'>
            <th class="topItemkey" scope="row">Created By</th>
            <td>{{ indent.creator || '-'}}</td>
          </tr>
          <tr *ngIf='dataLoaded'>
            <th class="topItemkey" scope="row"> Indent Date</th>
            <td>{{ (indent.indentDocumentDate | date: "EEEE, MMMM d, y") || 'N/A'}}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <div class="row">
    <div class="col">
      <table class="table">
        <tbody>
          <tr aria-rowspan="3">
            <th class="topItemkey" scope="row">Indent Remarks</th>
            <td>{{ indent?.remarks }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>

<mat-card class="matcontent" *ngIf='!finalSubmit'>
  <div class="searchInput">
    <mat-form-field appearance="none">
      <!-- <label>Search</label> -->
      <input matInput type="text" class="outline" placeholder="Search" [(ngModel)]='searchText'
        (keyup)="doFilter($event.target.value)" />
      <mat-icon matSuffix (click)="resetForm()" class="closebtn">close</mat-icon>
    </mat-form-field>
  </div>

  <section class="example-container-1 mat-elevation-z8">
    <table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort>
      <ng-container matColumnDef="index">
        <th mat-header-cell *matHeaderCellDef class="tableId"><b>#</b></th>
        <td mat-cell *matCellDef="let element; let i = index" class="name-cell tableId">
          {{ i + 1 }}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="itemName" sticky>
        <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell">
          <b> Item Name</b>
        </th>
        <td mat-cell *matCellDef="let element" class="name-cell">
          {{ element.itemName | titlecase }}
        </td>
        <td mat-footer-cell *matFooterCellDef>
          <ng-container *ngIf="element?.status != 'completed'">
            Total
          </ng-container>
        </td>
      </ng-container>

      <ng-container matColumnDef="pkgName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Pkg Name</b>
        </th>
        <td mat-cell *matCellDef="let element">
          {{ element?.packageName |titlecase}}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="uom">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>UOM</b>
        </th>
        <td mat-cell *matCellDef="let element">
          {{ (element.uom || element.packageName || '-') | titlecase }}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="entryType">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Entry Type</b>
        </th>
        <td mat-cell *matCellDef="let element">
          {{ (element.entryType || '-') | titlecase }}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="inStore">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b> In Store</b>
        </th>
        <td mat-cell *matCellDef="let element">
          {{this.purchases.truncateValue(element.inStore) }}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="unitPrice">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b> Unit Price</b>
        </th>
        <td mat-cell *matCellDef="let element">
          {{this.purchases.truncateValue(element.price)}}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="unitCost">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b> Unit Price</b>
        </th>
        <td mat-cell *matCellDef="let element">
          {{this.purchases.truncateValue(element.unitPrice)}}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="totalPrice">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b> Total Price</b>
        </th>
        <td mat-cell *matCellDef="let element">

          <div *ngIf="element?.status == 'completed'">
            {{ this.purchases.truncateValue(element.price * (element.issueQty - element.pendingQty))}}
          </div>
          <div *ngIf="element?.status != 'completed'">
            {{ this.purchases.truncateValue(element.price * (element.dispatchedQty))}}
          </div>
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.purchases.truncateValue(getTotal())}}</td>
      </ng-container>

      <ng-container matColumnDef="totalAmount">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b> Total Price</b>
        </th>
        <td mat-cell *matCellDef="let element">
          {{getItemTotal(element)}}
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.purchases.truncateValue(getTotalForCompletedIndent())}}</td>
      </ng-container>

      <ng-container matColumnDef="reqQty">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b> Req Qty</b>
        </th>
        <td mat-cell *matCellDef="let element">
          {{ this.purchases.truncateValue(element.issueQty) }}
          <span *ngIf="element.issueQty > 0 && portionValueReqQty(element) !== ''">
            ({{ this.purchases.truncateValue(portionValueReqQty(element)) }} portions)
          </span>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="penQty">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b> Pending Qty</b>
        </th>
        <td mat-cell *matCellDef="let element">
          {{ this.purchases.truncateValue(element.pendingQty) || 0 }}
          <span *ngIf="element.pendingQty > 0 && portionValuePendingQty(element) !== ''">
            ({{ this.purchases.truncateValue(portionValuePendingQty(element)) }} portions)
          </span>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="remarks">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b> Remark</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <textarea matInput rows="2" maxlength="200" wrap="soft" cols="10">
            {{ element?.remarks | titlecase}}  </textarea>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>


      <ng-container matColumnDef="issueQty">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b> Issue Qty</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <div *ngIf="element?.status != 'completed'">
            <input type="number" style="text-align: center;" step="0.01" min="0" class="input1"
              (keyup)="validateDispatchQty($event,element)" [(ngModel)]="element.dispatchedQty"
              [class.invalid-border]="element.inStock < element.dispatchedQty"
              [disabled]='element.hasOwnProperty("available") && element.available == false  || element.status == "completed"'
              (focus)="focusFunctionWithOutForm(element,'dispatchedQty')"
              (focusout)="focusOutFunctionWithOutForm(element,'dispatchedQty')" />

          </div>
          <div *ngIf="element?.status == 'completed'">
            <span>{{this.purchases.truncateValue(element.issueQty - element.pendingQty)}}</span>
          </div>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="issueQtyPortion">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b> Issue Qty (Portion)</b>
        </th>
        <td mat-cell *matCellDef="let element" >
          <div *ngIf="element?.status != 'completed'">
            <input
              type="number" style="text-align: center;" step="0.01" min="0" class="input1" 
              (keyup)="validateDispatchQtyPortion($event,element)"
              [(ngModel)]="element.dispatchedQtyPortion" 
              (ngModelChange)="dispatchedQtyPortionChange(element)"
              [class.invalid-border]="element.inStock < element.dispatchedQtyPortion"
              [disabled] = 'element.hasOwnProperty("available") && element.available == false  || element.status == "completed"'
              [ngClass]="{'disabled-input': element.selectedOption == 'uom'}"
              (focus)="focusFunctionWithOutForm(element,'dispatchedQtyPortion')" (focusout)="focusOutFunctionWithOutForm(element,'dispatchedQtyPortion')"/>  
            </div>
            <div *ngIf="element?.status == 'completed'">
              <span>{{this.utils.truncateNew(element.issueQtyPortion - element.pendingQtyPortion)}}</span>
            </div>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="adequate">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b> Adequate</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <div *ngIf='element.enoughStock'>
            <mat-icon style="color: #33cc33;">check_circle</mat-icon>
          </div>
          <div *ngIf='!element.enoughStock'>
            <mat-icon style="color:#ff0000;">highlight_off</mat-icon>
          </div>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="availability">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b> Availability</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <div *ngIf='element.available'>
            <mat-icon style="color: #33cc33;">check_circle</mat-icon>
          </div>
          <div *ngIf='!element.available'>
            <mat-icon style="color:#ff0000;">highlight_off</mat-icon>
          </div>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true;"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns" [ngClass]="{ 'disabled-row': row.disabled }"></tr>
      <tr mat-footer-row *matFooterRowDef="displayedColumns"></tr>
    </table>
    <div class="dataMessage" *ngIf="dataSource?.data.length == 0"> No Data Available </div>
  </section>

  <mat-card-actions> </mat-card-actions>
</mat-card>

<div *ngIf='finalSubmit'>
  <br>
  <br>
  <mat-card>{{finalMsg}}
    <button mat-button mat-raised-button id="save-btn" class="button2 button3" (click)="redirect()">Click Here</button>
  </mat-card>
  <div *ngFor="let item of missItemList" style="margin-left: 20px;">
    <span>{{item}}</span>
  </div>
</div>

<ng-template #openStatusDialog>
  <button mat-icon-button matDialogClose="yes" matTooltip="Close" class="CloseBtn">
    <mat-icon>close</mat-icon>
  </button>
  <h2 mat-dialog-title>
    <b>Approve Status</b>
  </h2>

  <mat-dialog-content class="mat-typography">
    <div class="col">
      <table class="table">
        <tbody>
          <tr *ngFor="let item of indent.indentApprovalDetail">
            <th class="topItemkey" style="width: 65px !important; background: none;" scope="row">Level</th>
            <td>{{ item.level }}</td>
            <th class="topItemkey" style="width: 65px !important; background: none;" scope="row">Reason</th>
            <td>{{ item.rejectionReason || '-'}}</td>
            <th class="topItemkey" style="width: 65px !important; background: none;" scope="row">Status</th>
            <td>{{ item.status }}</td>
          </tr>
        </tbody>
      </table>
    </div>

  </mat-dialog-content>

  <mat-dialog-actions align='center'>
    <div class="reqBtn">
      <button mat-raised-button class="button3 mb-2 mx-2" matDialogClose="yes" (click)="okDialog()">
        Ok
      </button>
      <!-- <button mat-raised-button class="button3 m-2" matDialogClose="yes" (click)="okDialog()">
				Ok
			</button> -->
    </div>
  </mat-dialog-actions>
</ng-template>