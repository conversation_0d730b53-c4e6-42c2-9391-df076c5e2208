input[type=number]::-webkit-inner-spin-button, 
input[type=number]::-webkit-outer-spin-button { 
  -webkit-appearance: none; 
}

input[type=number] {
  -moz-appearance: textfield;
}

button:disabled {
    background-color: #c2c2a3;
 }

.dataText{
  font-family: Public Sans;
  font-size: 12px;
}
.CloseBtn{
  float: right;
  margin-bottom: -1px;
}

.disabled-row {
  opacity: 0.5; /* styling to indicate disabled state */
  pointer-events: none; /* disables pointer events on the row */
}

.example-container-1{
  max-height: 510px;
  overflow-y: auto;
}

.CloseBtn{
  float: right;
  margin-bottom: -1px;
}

.searchInput{
  display: flex;
  margin-top: -25px !important;
}

.invalid-border {
  border-color: red; /* Change this to your desired color */
}

.disabled-input {
  opacity: 0.6;
  pointer-events: none;
}

::ng-deep .uomAdj{
  padding-right: 10px !important;
  margin-top: 10px !important;
  margin-bottom: 10px !important;
  font-size: 12px !important;
}