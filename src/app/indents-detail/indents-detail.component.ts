import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { GlobalsService } from '../_services/globals.service';
import { AuthService, BranchTransferService, PurchasesService } from '../_services/';
import { MatDialog, MatTableDataSource } from '@angular/material';
import { ShareDataService } from '../_services/share-data.service';
import { IndentItem } from '../_models/';
import { Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { first } from 'rxjs/operators';
import { NotificationService } from '../_services/notification.service';
import { UtilsService } from '../_utils/utils.service';
import { SimpleDialogComponent } from '../_dialogs/simple-dialog/simple-dialog.component';
import { filter } from 'gulp-typescript';
@Component({
  selector: 'app-indents-detail',
  templateUrl: './indents-detail.component.html',
  styleUrls: ['./indents-detail.component.scss', "./../../common-dark.scss"]
})
export class IndentsDetailComponent implements OnInit {

  @ViewChild('openStatusDialog') openStatusDialog: TemplateRef<any>;
  openStatus: boolean = false;
  indent: any
  displayedColumns: any
  dataSource: MatTableDataSource<IndentItem>;
  disableIndent: any = false;
  inValidIndent: any = false;
  stockSeparation: boolean;
  available: boolean;
  activeIndent: boolean = true;
  partialAccess: boolean = true;
  indentReqFlag: boolean;
  finalSubmit: boolean = false;
  disableCloning: boolean = false;
  dataLoaded: boolean = false;
  isRequest: boolean;
  finalMsg: String;
  missItemList: Array<any> = [];
  queryobj: any;
  searchText: any;
  private specialKeys: Array<string> = ['Backspace', 'Tab', 'End', 'Home', 'ArrowLeft', 'ArrowRight', 'Del', 'Delete'];
  private regex: RegExp = new RegExp(/^\d*\.?\d{0,2}$/g);
  showPrint: boolean = false;
  indentStatus: any;
  user: any;
  requestedDate: any;
  centralIndent: boolean = false ;
  inStorePortion: number;
  constructor(
    private notifyService: NotificationService,
    private shareData: ShareDataService,
    private auth: AuthService, private router: Router,
    private branchTransfer: BranchTransferService,
    private purchases: PurchasesService, private route: ActivatedRoute,
    private loc: Location,
    private dialog: MatDialog,
    private utils: UtilsService,
  ) {
    this.user = this.auth.getCurrentUser();
  }

  ngOnInit() {
    this.shareData.currIndent.pipe(first()).subscribe(values => {
      let indent = values[0]
      this.indentReqFlag = values[1]
      this.indentStatus = values[0]
      if (indent == undefined)
        this.loc.back()
      else {
        this.branchTransfer.getCurrentStockForIndent({
          tenantId: indent.tenantId,
          restaurantId: indent.restaurantId,
          indentItems: indent.indentItems,
          status: indent.status
        }).subscribe(indentWithInStore => {
          indent.indentItems = indentWithInStore
          this.indent = indent;
          this.centralIndent = (this.indent.type === 'centralStoreIndent') ? true : false ; 
          this.partialAccess = !this.indentReqFlag && values[0]['tenantDetails']['indentAccess']['partial'] === false && values[0]['status'] === 'shortage' ? false : true; 
          this.dataLoaded = true;
          this.dataSource = new MatTableDataSource<IndentItem>();
          this.dataSource.data = this.indent.indentItems;
          this.dataSource.data.forEach((el) => {
            if (el.hasOwnProperty('selectedOption') && el.selectedOption === 'portion') {
              el.selectedOption = 'portion';
              el.defaultUOM = 'portion';
            } else {
              el.selectedOption = 'uom';
              el.defaultUOM = 'uom';
            }   
            if(this.indent.hasOwnProperty('type') && this.indent.type === 'directIndent'){
              el['unitPrice'] = this.utils.truncateNew(el['totalPrice'] / el['receivedQty']);
            }
            if (!(el.hasOwnProperty('status'))) {
              el['status'] = 'completed';
            }
            if (!(el.hasOwnProperty('pendingQty'))) {
              el['pendingQty'] = 0;
            }
            el['dispatchedQty'] = this.utils.truncateNew(el['pendingQty']);

            if (el.hasOwnProperty('portionWeight'))  {
              const conversionCoefficient =  el['uom'] == 'NOS' ? 1: 1000 ;
              el['dispatchedQtyPortion'] = this.utils.truncateNew ((el['pendingQty'] * conversionCoefficient) / this.utils.truncateNew(el['portionWeight'],0));
              el['inStorePortion'] = this.utils.truncateNew ((el['inStore'] * conversionCoefficient) / this.utils.truncateNew(el['portionWeight'],0));
              el['pendingQtyPortion'] = this.utils.truncateNew ((el['pendingQty'] * conversionCoefficient) / this.utils.truncateNew(el['portionWeight'],0));
              el['issueQtyPortion'] = this.utils.truncateNew ((el['issueQty'] * conversionCoefficient) / this.utils.truncateNew(el['portionWeight'],0));
            }
            el['disabled'] = (el['pendingQty'] == 0) ? true : false;
          })
          if (this.indentReqFlag) {
            if (indent.status == 'pending') {
              this.displayedColumns = GlobalsService.indentReqPendingColoumns;
              if (!this.dataSource.data[0].hasOwnProperty('price')) {
                this.displayedColumns = GlobalsService.indentReqPendingColoumnsExclPrice;
              }
            }
            else {
              this.displayedColumns = GlobalsService.indentReqCompleteColoumns;
              if (!this.dataSource.data[0].hasOwnProperty('price')) {
                this.displayedColumns = GlobalsService.indentReqCompleteColoumnsExclPrice;
              }
            }
          }
          else {
            if (indent.status != 'completed') {
              this.displayedColumns = GlobalsService.closeIndentColoumns;
              if (!this.dataSource.data[0].hasOwnProperty('price')) {
                this.displayedColumns = GlobalsService.closeIndentColoumnsExclPrice;
              }
            }
            else {
              this.displayedColumns = ['index', 'itemName', 'entryType','pkgName','uom', 'unitPrice', 'reqQty', 'penQty', 'issueQty','issueQtyPortion', 'totalAmount'];
              if (!this.dataSource.data[0].hasOwnProperty('price')) {
                this.displayedColumns = ['index', 'itemName', 'entryType','pkgName','uom', 'unitCost', 'reqQty', 'penQty', 'issueQty','issueQtyPortion', 'totalAmount'];
              }
            }
          }
          this.indent ? (this.getIndentStatus()) : null;
          this.validateDispatchQuantity();
          this.validateDispatchPortionQuantity();
          if (this.indent.type && this.indent.type != 'specialIndent' && (!this.indent.hasOwnProperty('modTs'))) {
            this.indent['modTs'] = this.indent['createTs'];
          }
        })

      }
    });
    this.route.queryParamMap.subscribe((params) => {
      this.queryobj = { ...params.keys, ...params };
      if (this.queryobj.params.type == 'request') {
        this.isRequest = true;
      }
    })
  }

  goBack() {
    this.loc.back()
  }

  getIndentStatus() {
    let filteredData = this.indent.indentApprovalDetail ? this.indent.indentApprovalDetail.filter((el) => {
      return ((el.status == "rejected" || "pending") && el.status != "approved")
    }) : [];
    this.disableIndent = filteredData.length > 0 ? true : false;
  }


  dispatchIndent() {
    this.validateInStoreStock();
  }


  reopenIndent(){
    this.disableCloning =  true ;
    let obj = {
      indentId: this.indent.indentId,
      restaurantId: this.indent.restaurantId
    }
    this.branchTransfer.cloneIndent(obj).subscribe(res => {
      if (res['success']) {
        this.utils.snackBarShowSuccess(`${res['message']}`);
      } else {
        this.utils.snackBarShowWarning(`${res['message']}`);
      }
    })
  }

  validateDispatchQuantity() {
    let count = 0
    this.dataSource.data.forEach(element => {

      if ((element.dispatchedQty > element.inStore) && (element['status'] != 'completed')) {
        count = count + 1;
      }
    });
    this.inValidIndent = (count === 0) ? false : true;
  }

  validateDispatchPortionQuantity() {
    let count = 0
    this.dataSource.data.forEach(element => {
      if (element.hasOwnProperty('portionWeight'))  {
        const conversionCoefficient =  element['uom'] == 'NOS' ? 1: 1000 ;
        this.inStorePortion = this.utils.truncateNew (element['inStore'] * conversionCoefficient) / element['portionWeight'];
      }
      if ((element.dispatchedQtyPortion > this.inStorePortion) && (element['status'] != 'completed')) {
        count = count + 1;
      }
    });
    this.inValidIndent = (count === 0) ? false : true;
  }

  //  Stock expiry related work pls do not perform any actions ---------------------------------------------------------------------------------------------

  getTenantData() {
    let obj = {
      tenantId: this.indent.tenantId
    }
    this.branchTransfer.tenantDetails(obj).subscribe(res => {
      this.stockSeparation = res.data[0].stockSeparation;
      this.stockSeparation ? this.checkAvailability() : null;
    })
  }

  checkAvailability() {
    this.dataSource.data.forEach((el) => {
      let difference = this.getDaysBetweenTwoTimeStamps(this.indent.createTs)
      this.available = el['expiry'] - difference > 0 ? true : false;
      el['available'] = this.available
      if (el['available'] == false) {
        el['dispatchedQty'] = 0
        el['dispatchedQtyPortion'] = 0
      }
    })
    this.displayedColumns = [
      "index",
      "itemName",
      "entryType",
      "pkgName",
      "uom",
      "unitPrice",
      "inStore",
      "reqQty",
      "issueQty",
      "issueQtyPortion",
      "totalPrice",
      "adequate",
      "availability"
    ]
    let filter = this.dataSource.data.filter((data) => {
      return data['available'] == true
    })
    this.activeIndent = filter.length > 0 ? true : false;

  }

  getDaysBetweenTwoTimeStamps(created) {
    // Set the ISO strings to compare
    const isoString1 = created;
    const utcTime = new Date().toISOString(); // get current UTC time
    const istTime = new Date(utcTime);
    istTime.setHours(istTime.getHours() + 5); // add 5 hours for IST
    istTime.setMinutes(istTime.getMinutes() + 30); // add 30 minutes for IST
    const istTimeString = istTime.toISOString(); // convert to ISO string
    const isoString2 = istTimeString;
    // Parse the ISO strings into date objects
    const date1 = new Date(isoString1);
    const date2 = new Date(isoString2);

    // Calculate the time difference in milliseconds
    const timeDiff = date2.getTime() - date1.getTime();

    // Convert the time difference from milliseconds to days
    const dayDiff = timeDiff / (1000 * 3600 * 24);
    return dayDiff
  }

  validateDispatchQty(event, element) {
    if (element.dispatchedQty > element.originalQty) {
      element.dispatchedQty = 0;
    }
    if (element.dispatchedQty > element.pendingQty) {
      element.dispatchedQty = 0;
    }

    if (element.dispatchedQty <= element.inStore) {
      element.enoughStock = true;
    }
    else {
      // element.dispatchedQty = 0;
      element.enoughStock = false;
    }
    if (element.hasOwnProperty('portionWeight'))  {
      const conversionCoefficient =  element['uom'] == 'NOS' ? 1: 1000 ;
      const inStorePortion = this.utils.truncateNew (element['inStore'] * conversionCoefficient) / element['portionWeight'];
      const currentQtyInPortion = this.utils.truncateNew (element['dispatchedQty'] * conversionCoefficient) / element['portionWeight'];
      element['dispatchedQtyPortion'] = currentQtyInPortion
      if (element.dispatchedQtyPortion <= inStorePortion) {
        element.enoughStock = true;
      }
      else {
        element.enoughStock = false;
      }
    }
    this.validateDispatchQuantity();
  }

  validateDispatchQtyPortion(event, element) {
    if (element.hasOwnProperty('portionWeight'))  {
      const conversionCoefficient =  element['uom'] == 'NOS' ? 1: 1000 ;
      const inStorePortion = this.utils.truncateNew (element['inStore'] * conversionCoefficient) / element['portionWeight'];
      if (element.dispatchedQtyPortion <= inStorePortion) {
        element.enoughStock = true;
      }
      else {
        element.enoughStock = false;
      }
    }
    if (element.hasOwnProperty('portionWeight'))  {
      const conversionCoefficient =  element['uom'] == 'NOS' ? 1: 1000 ;
      const pendingQtyPortion = this.utils.truncateNew (element['pendingQty'] * conversionCoefficient) / element['portionWeight'];
      const originalQtyPortion = this.utils.truncateNew (element['originalQty'] * conversionCoefficient) / element['portionWeight'];

      if (element.dispatchedQtyPortion > originalQtyPortion) {
        element.dispatchedQtyPortion = 0;
      }
      if (element.dispatchedQtyPortion > pendingQtyPortion) {
        element.dispatchedQtyPortion = 0;
      }
    }
    this.validateDispatchPortionQuantity();
  }

  dispatchedQtyPortionChange(element) {  
    if (element.hasOwnProperty('portionWeight'))  {  
      let conversionCoefficient =  element['uom'] == 'NOS' ? 1: 1000 ; 
      const portionWeight = (element.portionWeight * 1000) / 1000;
      if (element.dispatchedQtyPortion != null) {
        element.dispatchedQty = this.utils.truncateNew((element.dispatchedQtyPortion * portionWeight) / conversionCoefficient);      
      }
    }
  }

  validateAndIncrmntDispatchQty(element) {
    element.dispatchedQty = element.dispatchedQty + 1
    element.dispatchedQty > element.issueQty ? element.dispatchedQty = element.issueQty : element.dispatchedQty;
    if (element.dispatchedQty <= element.inStore) {
      element.enoughStock = true
    }
    else {
      element.enoughStock = false
    }
  }

  validateAndDcrsDispatchQty(element) {
    element.dispatchedQty = element.dispatchedQty - 1
    if (element.dispatchedQty < 0)
      element.dispatchedQty = 0
    if (element.dispatchedQty <= element.inStore) {
      element.enoughStock = true
    }
    else {
      element.enoughStock = false
    }
  }

  getTotal() {
    let totalPrice = 0;
    this.dataSource.data.forEach(element => {
      const qty = element.dispatchedQty != null ? element.dispatchedQty : (element.dispatchedQtyPortion || 0);
      const total = qty * element.price;
      totalPrice += total;
    });
    return this.utils.truncateNew(totalPrice);
  }

  getTotalForCompletedIndent() {
    let totalPrice = 0
    totalPrice = this.dataSource.data.reduce(function (accumulator, currentValue) {
      const itemPrice = currentValue.price !== undefined ? currentValue.price : currentValue['unitPrice'];
      return accumulator + (currentValue.issueQty * itemPrice);
    }, 0);
    return this.utils.truncateNew(totalPrice);
  }

  printToPdf() {
    let inventoryList = {};
    inventoryList['inventoryItems'] = [];
    inventoryList['inventoryItems'] = this.dataSource.data
    if (inventoryList['inventoryItems'].length == 0) {
      this.utils.snackBarShowWarning('please enter indent values');
      return;
    }
    inventoryList['user'] = this.auth.getCurrentUser();
    inventoryList['restaurantId'] =  this.indent.restaurantId;
    inventoryList['indentListView'] = this.isRequest ? false : true
    inventoryList['status'] = this.indent.status
    inventoryList['recipientArea'] = this.indent.workArea
    inventoryList['indentId'] = this.indent.indentId
    inventoryList['raiseDate'] = this.indent['createTs'];
    ("modTs" in this.indent) ? inventoryList['issueDate'] = this.indent['modTs'] : undefined;
    inventoryList['indentDocumentDate'] = this.indent.indentDocumentDate
    this.purchases.printpdfs(inventoryList, 'Indent').subscribe(data => {
      this.purchases.globalPrintPdf(data.eFile);
    });
  }

  exportToExcel() {
    let inventoryList = {}
    inventoryList['indentArea'] = this.indent.indentArea;
    inventoryList['inventoryItems'] = this.dataSource.data;
    inventoryList['user'] = this.auth.getCurrentUser();
    this.purchases.exportToExcel(inventoryList, 'Indent').subscribe(data => {
      window.open('data:text/csv;base64,' + data.eFile);
    });
  }

  openFilter() {
    if (this.openStatus == false) {
      this.openStatus = true;
    } else {
      this.openStatus = false;
    }

    let dialogRefTemplate = this.dialog.open(this.openStatusDialog);
    dialogRefTemplate.afterClosed().subscribe(result => {
      // if (result === 'yes') {
      //   this.dialog.closeAll();
      // }
    })
  }

  okDialog() {
    this.openStatus = false;
  }

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }

  resetForm() {
    this.searchText = ''
    this.dataSource = new MatTableDataSource<IndentItem>();
    this.dataSource.data = this.indent.indentItems;
  }

  getItemTotal(element) {    
    let result;
    if (element.price) {
      if (element.hasOwnProperty('pendingQty')) {
        result = element.price * (element.issueQty - element.pendingQty);
      } else {
        result = element.price * (element.issueQty - element.recPendingQty);
      }
    } else {
      result = element.unitPrice * element.receivedQty;
    }

    return this.utils.truncateNew(result)
  }

  validateInStoreStock() {
    let zeroQuantityItems = this.dataSource.data.filter(item => item.dispatchedQty === 0 || item.dispatchedQtyPortion === 0);
    if (zeroQuantityItems.length === this.indent.indentItems.length) {
      this.utils.snackBarShowWarning('You cannot dispatch zero quantity for all items');
    return;  
  }
    this.branchTransfer.getCurrentStockForIndent({
      tenantId: this.indent.tenantId,
      restaurantId: this.indent.restaurantId,
      indentItems: this.indent.indentItems,
      status: this.indent.status
    }).subscribe(data => {
      if (data.length > 0) {
        let missingItems = [];
        this.dataSource.data.forEach((item) => {
          let requiredItem = data.find((el) => el.itemCode == item.itemCode && el.packageName == item.packageName)
          if (requiredItem) {
            item['inStore'] = this.utils.truncateNew(requiredItem['inStore']);
            item['enoughStock'] = (item.dispatchedQty <= item.inStore || item.dispatchedQtyPortion <= item.inStorePortion) ? true : false;
          } else {
            missingItems.push(item.itemCode);
          }
        })
        let shortage = this.dataSource.data.filter((item) => item['inStore'] < item['dispatchedQty'] || item['inStorePortion'] < item['dispatchedQtyPortion']);
        if (shortage.length > 0) {
          this.utils.snackBarShowWarning('Requested stock not available for the highlighted items');
          this.disableIndent = false;
        } else {
          if (missingItems.length > 0) {
            let itemCodes = missingItems.join(',')
            this.utils.snackBarShowWarning(`${itemCodes} missing from inventory.`);
            this.disableIndent = false;
          } else {

            let count = 0
            this.dataSource.data.forEach(element => {
              if (element.dispatchedQty > 0 && element['pendingQty'] > 0) {
                element['pendingQty'] -= element.dispatchedQty
              }
              if ((element.dispatchedQty > element.inStore) && (element['status'] != 'completed')) {
                count = count + 1
              }
              if (element.dispatchedQtyPortion > 0 && element['pendingQtyPortion'] > 0) {
                element['pendingQtyPortion'] -= element.dispatchedQtyPortion
              }
              if ((element.dispatchedQtyPortion > element.inStorePortion) && (element['status'] != 'completed')) {
                count = count + 1
              }
            });
            if (count == 0) {
              this.branchTransfer.dispatchIndent({
                tenantId: this.indent.tenantId,
                restaurantId: this.indent.restaurantId,
                workArea: this.indent.workArea,
                indentId: this.indent.indentId,
                uid: this.user.mId,
                indentItems: this.dataSource.data
              }).subscribe(res => {
                if (res.result == 'success') {
                  if (res.hasOwnProperty('missItems')) {
                    this.finalSubmit = true
                    this.finalMsg = 'Indent dispatched successfully. Please Note these items are absent in masterSheet and so quantities are not added to workArea.'
                    Object.values(res['missItems']).forEach(element => {
                      this.missItemList.push(element)
                    });
                  }
                  this.disableIndent = true
                  this.indent.status = res.indentStatus
                  this.utils.snackBarShowSuccess('Indent Dispatched');
                } else {
                  this.utils.snackBarShowError(res['message']) ;
                  this.disableIndent = true
                }
              }, err => console.log(err))
            }
            else {
              this.utils.snackBarShowError('There are ' + count + ' items with insufficient InStore Qty. Please adjust the issue Qty accordingly.');
            }
            this.showPrint = true;
          }
        }
      } else {
        this.utils.snackBarShowWarning("Unable to check item stock, Please try again later!")
      }
    })
  }

  focusFunctionWithOutForm(element , value){
    if(Number(element[value]) === 0){
      element[value] = null;
    }
  }
  
  focusOutFunctionWithOutForm(element , value){
    if(element[value] === null){
      element[value] = 0
    }
  }

  portionValueReqQty(element){    
    if (element.hasOwnProperty('selectedOption') && element['selectedOption'] === 'portion' && element.hasOwnProperty('portionWeight'))  {
      let conversionCoefficient =  element['uom'] == 'NOS' ? 1: 1000 ;
      const portionWeight = (element.portionWeight * 1000) / 1000;
      let calculatedQuantity = (element['issueQty'] * conversionCoefficient) / portionWeight;
      return this.utils.truncateNew(calculatedQuantity)
    }
    return ''
  } 

  portionValuePendingQty(element){
    if (element.hasOwnProperty('selectedOption') && element['selectedOption'] === 'portion' && element.hasOwnProperty('portionWeight'))  {
      let conversionCoefficient =  element['uom'] == 'NOS' ? 1: 1000 ;
      const portionWeight = (element.portionWeight * 1000) / 1000;
      let calculatedQuantity = (element['pendingQty'] * conversionCoefficient) / portionWeight;
      return this.utils.truncateNew(calculatedQuantity)
    }
    return ''
  }

  portionValueInstore(element){    
    if (element.hasOwnProperty('selectedOption') && element['selectedOption'] === 'portion' && element.hasOwnProperty('portionWeight'))  {
      let conversionCoefficient =  element['uom'] == 'NOS' ? 1: 1000 ;
      const portionWeight = (element.portionWeight * 1000) / 1000;
      let calculatedQuantity = (element['inStore'] * conversionCoefficient) / portionWeight;
      return this.utils.truncateNew(calculatedQuantity)
    }
    return ''
  } 

  disableUom(element){
    return element.defaultUOM == "uom" ? true : false
  }

}