<mat-card>
  <div  class="infoMessage" style="align-items: center !important;">
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
      class="bi bi-info-circle-fill infoSvgIcon" viewBox="0 0 16 16">
      <path
        d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm.93-9.412-1 4.705c-.07.34.029.533.304.533.194 0 .487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703 0-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381 2.29-.287zM8 5.5a1 1 0 1 1 0-2 1 1 0 0 1 0 2z" />
    </svg>
    <p class="ml-2 mb-0">
      Open or partial indents from last month have been closed. 
    </p>
    <p class="ml-2 mb-0" style="display: contents !important;">
      <span class="ml-2">Please use the clone</span> <mat-icon class="mx-2" [ngClass]="{'enabled-icon': true}">control_point_duplicate</mat-icon>option to re-create them.
    </p> 
  </div>
  
  <div class="title row">
    <div *ngIf="this.user.tenantId != '100000'">
      <form [formGroup]="indentListForm">
        <mat-form-field appearance="none" class="topitem">
          <label>Select Branch</label>
          <mat-select placeholder="Restaurant" formControlName="branchSelection"
            (selectionChange)="filterByBranch($event.value)" class="outline">
            <mat-option *ngFor="let rest of branches" [value]="rest">
              {{ rest.branchName }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </form>
    </div>
    <div>
      <mat-form-field *ngIf="branchSelected" appearance="none" style="margin-left: 10px" class="topitem">
        <label>Start Date</label>
        <!-- [(ngModel)]="selectedStartDate" -->
        <input matInput class="outline" [matDatepicker]="picker1" placeholder="Start Date" [formControl]="startDate" />
        <mat-datepicker-toggle matSuffix [for]="picker1">
          <mat-icon matDatepickerToggleIcon>
            <img src="./../../assets/calender.png" />
          </mat-icon>
        </mat-datepicker-toggle>
        <mat-datepicker #picker1></mat-datepicker>
      </mat-form-field>
    </div>
    <div>
      <mat-form-field *ngIf="branchSelected" appearance="none" style="margin-left: 10px" class="topitem">
        <label>End Date</label>
        <!-- [(ngModel)]="selectedEndDate" -->
        <input matInput class="outline" [matDatepicker]="picker2" [formControl]="endDate" placeholder="End Date"
          [readonly]="!startDate.value" [disabled]="!startDate.value" [min]="startDate.value" />
        <mat-datepicker-toggle matSuffix [for]="picker2">
          <mat-icon matDatepickerToggleIcon>
            <img src="./../../assets/calender.png" />
          </mat-icon>
        </mat-datepicker-toggle>
        <mat-datepicker #picker2></mat-datepicker>
      </mat-form-field>
    </div>
    <div>
      <button *ngIf="branchSelected" mat-stroked-button class="btn-block findButton button3" (click)="filterByDate()">
        Find
      </button>
    </div>
    <div class="ml-2">
      <button *ngIf="branchSelected" mat-stroked-button class="btn-block findButton button3" (click)="clear()">
        clear
      </button>
    </div>
  </div>
  </mat-card>
  <div *ngIf="branchSelected" class="datacontainer">
    <mat-card>
      <mat-card-content>
        <div class="search-table-input">
          <mat-form-field appearance="none">
            <label>Search</label>
            <input matInput type="text" class="outline" (keyup)="doFilter($event.target.value)" [(ngModel)]="searchText"
              placeholder="Search" />
            <mat-icon matSuffix (click)="resetForm()" class="closebtn">close</mat-icon>
          </mat-form-field>
  
          <mat-form-field appearance="none">
            <label>Indent Status</label>
            <!-- [(ngModel)]="selectedIndentListStatus" -->
            <mat-select placeholder="Indent Status" [formControl]="indentListStatus" class="outline">
              <mat-option *ngFor="let status of indentStatus" [value]="status" (click)="selectIndentStatus(status)">
                {{ status }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="none">
            <label>Item Name</label>
            <mat-select placeholder="Item Name" [formControl]="itemName" (selectionChange)="itemNameChange($event.value)" 
            class="outline" [multiple]="true" #multiSelect>
              <mat-option>
                <ngx-mat-select-search placeholderLabel="Items..." noEntriesFoundLabel="'no Item found'"
                  [formControl]="itemFilterCtrl"></ngx-mat-select-search>
              </mat-option>
              <mat-option (click)="toggleSelectAll()" class="hide-checkbox">
                Select All / Deselect All
              </mat-option>
              <mat-option *ngFor="let item of itemBanks | async" [value]="item">
                {{ item }}
              </mat-option>          
            </mat-select>
          </mat-form-field>

          <button mat-button class="buttonForRefresh refreshBtn" (click)="refreshData()">
            Refresh
          </button>
        </div>
        <table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort>
  
          <ng-container matColumnDef="sNo">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> # </b>
            </th>
            <td mat-cell *matCellDef="let element; let i = index;">
              {{ i + 1 }}
            </td>
          </ng-container>
  
          <ng-container matColumnDef="indentId">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Indent Id</b>
            </th>
            <td mat-cell *matCellDef="let element" (click)="detailedIndent(element)" class="links">
              {{ element.indentId }}
            </td>
          </ng-container>
  
          <ng-container matColumnDef="creator">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Created By</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.creator ? element.creator : '-' }}
            </td>
          </ng-container>
  
  
          <ng-container matColumnDef="recipientArea">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Work Area</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.workArea }}
            </td>
          </ng-container>
  
          <ng-container matColumnDef="date">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Requested Date</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ this.utils.formatDateToUTC(element.createTs)}} 
            </td>
          </ng-container>
  
          <ng-container matColumnDef="documentDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Indent Date</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ (element?.indentDocumentDate) ? (this.utils.formatDateToUTC(element?.indentDocumentDate,true)) : 'N/A' }}


              <!-- {{ (element?.indentDocumentDate) ? (this.utils.timesStampReduction(element?.indentDocumentDate) | date:
                "EEEE, MMMM d, y") : 'N/A' }} -->
            </td>
          </ng-container>
  
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Indent Status</b>
            </th>
            <td mat-cell *matCellDef="let element">
              <ng-container *ngIf="element.status === 'pending'">
                Pending
              </ng-container>
              <ng-container *ngIf="element.status === 'shortage'">
                Partial
              </ng-container>
              <ng-container *ngIf="element.status === 'completed'">
                Completed
              </ng-container>
              <ng-container *ngIf="element.status === 'closed'">
                Closed
              </ng-container>
            </td>
          </ng-container>
  
          <!-- <ng-container *ngIf="deleteAccess || closeAccess" matColumnDef="Action"> -->
          <ng-container matColumnDef="Action">   
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Actions</b>
            </th>
            <td mat-cell *matCellDef="let element">
              <button mat-icon-button (click)="deleteIndent(element)" class="delete-button"
              [disabled]="['centralStoreIndent', 'directIndent'].includes(element.type) || element.hasOwnProperty('autoClosure')"
              matTooltip="Delete Indent" matTooltipPosition="left"
                *ngIf="deleteAccess">
                <mat-icon>delete_outline</mat-icon>
              </button>
              <button mat-icon-button (click)="updateStatus(element, 'closed')"
              [disabled]="element.status === 'closed' || element.status === 'completed'"
                class="action-btn" *ngIf="closeAccess">
                <mat-icon class="action-print-icon"
                [matTooltip]="element.status === 'closed' ? '' : 'Close Indent'" matTooltipPosition="right">
                  lock
                </mat-icon>
              </button>
  
              <button mat-icon-button 
              (click)="reopenIndent(element)"
              [disabled]="element.hasOwnProperty('childId')"
              class="action-btn" 
              *ngIf="element.status === 'closed' && element.hasOwnProperty('autoClosure')">
          <mat-icon class="action-print-icon" 
                    [ngClass]="{'enabled-icon': element.status === 'closed' && element.hasOwnProperty('autoClosure') && !element.hasOwnProperty('childId')}"
                    [matTooltip]="element.status === 'closed' && element.hasOwnProperty('autoClosure') && !element.hasOwnProperty('childId')? 'Clone Indent' : ''" 
                    matTooltipPosition="right">
              control_point_duplicate
          </mat-icon>
      </button>
  
            </td>
          </ng-container>
  
          <ng-container matColumnDef="checkStatus">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Approval Status</b>
            </th>
            <td mat-cell *matCellDef="let element">
              <div *ngIf="
                  element?.indentApprovalDetail &&
                  element?.indentApprovalDetail != ''
                ">
                <span class="links" (click)="mouseEnter(element)">Check Status</span>
              </div>
              <div *ngIf="
                  !element?.indentApprovalDetail ||
                  element?.indentApprovalDetail == ''
                ">
                -
              </div>
            </td>
          </ng-container>
  
          <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        </table>
        <div class="dataMessage" *ngIf="dataSource?.data?.length == 0">
          No Data Available
        </div>
        <mat-paginator [showTotalPages]="5" [pageSize]="10" [pageSizeOptions]="pageSizes"></mat-paginator>
      </mat-card-content>
    </mat-card>
  </div>
  
  <ng-template #openStatusDialog>
    <table class="table tablecenterData">
      <tbody *ngFor="let item of this.elementdata.indentApprovalDetail">
        <tr>
          <th class="topItemkey" scope="row">Role</th>
          <td>{{ item.role }}</td>
        </tr>
        <tr>
          <th class="topItemkey" scope="row">Level</th>
          <td>{{ item.level }}</td>
        </tr>
  
        <tr>
          <th class="topItemkey" scope="row">Status</th>
          <td>{{ item.status }}</td>
        </tr>
  
        <tr>
          <th class="topItemkey" scope="row">Reason</th>
          <td>{{ item.rejectionReason || "-" }}</td>
        </tr>
      </tbody>
    </table>
  </ng-template>