.findButton{
  margin: 0px 20px 0px 20px!important;
  padding: 2px;
}

  .title{
    align-items: end;
  }

  .indent-find-button{
    text-align: center;
  }

  .topInput{
    display: flow-root;
    margin-left: 2%;
    margin-right: 2%;
  }

  .example-container-1{
    max-height: 450px;
    overflow-y: auto;
  }

  .tablecenterData{
    width: 365px;
    margin-top: 80px;
    position: relative;
    border: 1px solid;
}

.topItemkeyPs{
  width: 175px;
}

.enabled-icon {
  color: #0d6efd;  /* Bootstrap primary blue */
}

::ng-deep .hide-checkbox .mat-pseudo-checkbox {
  display: none !important;
}