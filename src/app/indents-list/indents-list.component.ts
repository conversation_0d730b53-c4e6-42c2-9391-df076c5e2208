import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { GlobalsService } from '../_services/globals.service';
import { AuthService, BranchTransferService, PurchasesService } from '../_services/';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog } from '@angular/material';
import { UtilsService } from '../_utils/utils.service';
import { ShareDataService } from '../_services/share-data.service';
import { NavigationStart, Router } from '@angular/router';
import { IndentItem } from '../_models/';
import { MasterdataupdateService } from '../_services/masterdataupdate.service';
import { NotificationService } from '../_services/notification.service';
import { FormBuilder, FormControl, FormGroup, Validators } from "@angular/forms";
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material';
import { MomentDateModule, MomentDateAdapter } from '@angular/material-moment-adapter';
import { takeUntil } from 'rxjs/operators';
import { ReplaySubject, Subject } from 'rxjs';
import { PreviewIbtComponent } from '../_dialogs/preview-ibt/preview-ibt.component';
import { SimpleDialogComponent } from '../_dialogs/simple-dialog/simple-dialog.component';
import { SharedFilterService } from '../_services/shared-filter.service';

export const MY_FORMATS = {
  parse: {
    dateInput: 'LL'
  },
  display: {
    dateInput: 'DD-MM-YYYY',
    monthYearLabel: 'YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'YYYY'
  }
};

@Component({
  selector: 'app-indents-list',
  templateUrl: './indents-list.component.html',
  styleUrls: ['./indents-list.component.scss', "./../../common-dark.scss"],
  providers: [{ provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
  { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }
  ]
})
export class IndentsListComponent implements OnInit {
  data: any;
  user: any;
  tenantId: any;
  indent: any;
  searchText: string;
  searchValue: string;
  getFullIndentData: any;
  selectedIndentListStatus: any;
  elementdata: any;
  deleteAccess: boolean;
  closeAccess: boolean;
  prevBranchId: any;
  @ViewChild(MatPaginator) set paginator(value: MatPaginator) {
    if (this.dataSource) {
      this.dataSource.paginator = value;
    }
  };
  pageSizes: any;
  clientTimeZone: any;
  selectedBranch: any;
  selectedClients: any;
  multiBranchUser: any;
  restaurantId: any;
  branchSelected: any;
  selectedStartDate: any;
  selectedEndDate: any;
  indentsListFlag: boolean;
  indentDeleteAccess: boolean;
  indentReqFlag: boolean;
  indentsListUrl = encodeURI(GlobalsService.indentsList)
  indentReqUrl = encodeURI(GlobalsService.indentRequests)
  dataSource: MatTableDataSource<IndentItem>;
  displayedColumns: any = GlobalsService.indentListColumns
  private startDate = new FormControl();
  private endDate = new FormControl();
  clients: any = [];
  selectedClient = ""
  tenantDetails: any[]
  branches: any[];
  getBranchData: any[]
  indentListForm: FormGroup;
  indentStatus: string[] = ['All', 'Complete', 'shortage', 'pending'];
  filterKeys = { status: { indentListStatus: 'All' } }
  indentListStatus = new FormControl();
  private unsubscribe$ = new Subject<void>();
  @ViewChild('openStatusDialog') openStatusDialog: TemplateRef<any>;
  sharedFilterData: any = {};
  indentsDetailUrl = encodeURI(GlobalsService.indentsdetail)
  itemNameList: any;
  itemName = new FormControl();
  public itemBank: any[] = [];
  public itemBanks: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public itemFilterCtrl: FormControl = new FormControl();
  allSelected: boolean = true;
  protected _onDestroy = new Subject<void>();
  dataRefresh: boolean = false;
  constructor(
    private notifyService: NotificationService,
    private masterDataService: MasterdataupdateService,
    private auth: AuthService,
    private branchTransfer: BranchTransferService,
    private utils: UtilsService, private router: Router,
    private sharedData: ShareDataService,
    private purchases: PurchasesService,
    private fb: FormBuilder,
    public dialog: MatDialog,
    private sharedFilterService: SharedFilterService,
  ) {
    this.clientTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    if (this.router.url.includes(this.indentReqUrl)) {
      this.indentReqFlag = true
    } else {
      this.indentReqFlag = false
    }
    this.user = this.auth.getCurrentUser();
    this.multiBranchUser = this.user.multiBranchUser;

    this.indentListForm = this.fb.group({
      branchSelection: [null, Validators.required]
    });

    this.sharedFilterService.getFilteredIndentsList.subscribe(obj =>
      this.sharedFilterData = obj
    );

    this.sharedData.sharedBranchData.pipe(takeUntil(this.unsubscribe$)).subscribe((val) => {
      this.getBranchData = val;
      if (this.getBranchData.length == 0) {
        this.branches = this.user.restaurantAccess;
      } else if (this.getBranchData.length == 1) {
        const toSelect = this.getBranchData.find(data => data.branchName == this.getBranchData[0].branchName);
        if (toSelect != this.sharedFilterData.restaurantId) {
          this.sharedFilterData = '';
          this.indentListStatus.setValue(null);
          this.startDate.setValue(null);
          this.endDate.setValue(null);
        }
        this.indentListForm.get('branchSelection').setValue(toSelect);
        this.branches = this.getBranchData
        this.filterByBranch(this.indentListForm.value.branchSelection);
      } else {
        if (this.sharedFilterData.branchFlag == true) {
          this.filterByBranch(this.sharedFilterData.restaurantId);
          this.sharedFilterData.branchFlag = false;
        }
        this.branches = this.getBranchData
      }
    });

    this.router.events.subscribe(event => {
      if (event instanceof NavigationStart) {
        if (this.router.url.includes(this.indentsDetailUrl)) {
          localStorage.setItem('savedItemNames', JSON.stringify(this.itemName.value));          
        }
      }
    }); 
  }

  ngOnInit() {
    if (!this.multiBranchUser) {
      this.restaurantId = this.user.restaurantId
      window.sessionStorage.setItem("restaurantId", this.restaurantId);
      this.branchSelected = true;
      window.sessionStorage.setItem("branchSelected", this.branchSelected.toString());
      this.getIndentList();
    }
  }

  ngOnDestroy() {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
    this._onDestroy.next();
    this._onDestroy.complete();
    if (!this.router.url.includes(this.indentsDetailUrl)) {
      this.sharedFilterService.getFilteredIndentsList['_value'] = ''
      localStorage.removeItem('savedItemNames');
    }
  }


  reopenIndent(element){
    let obj = {
      indentId: element.indentId,
      restaurantId: element.restaurantId
    }
    this.dialog.open(SimpleDialogComponent, {
      data: {
        title: 'Clone Indent',
        msg: 'Are you sure you want to clone',
        ok: function () {
          this.branchTransfer.cloneIndent(obj).subscribe(res => {
            if (res['success']) {
              this.utils.snackBarShowSuccess(`${res['message']}`);
            } else {
              this.utils.snackBarShowWarning(`${res['message']}`);
            }
            this.getIndentList();
          })
        }.bind(this)
      }
    });
  }


  getIndentList() {
    this.getIndentReqList();
  }

  getIndentReqList() {
    let obj = {
      tenantId: this.user.tenantId,
      restaurantId: this.restaurantId,
      userEmail: this.user.email,
      indentReqFlag: this.indentReqFlag
    }
    if (this.startDate.value && this.endDate.value) {
      obj['startDate'] = this.utils.dateCorrection(this.startDate.value);
      obj['endDate'] = this.utils.dateCorrection(this.endDate.value);
    }
    this.branchTransfer.getIndentList(obj).subscribe(data => {
      if (!this.dataSource)
        this.dataSource = new MatTableDataSource<IndentItem>()
        this.getFullIndentData = data
      if (data) { 
        if (this.router.url.includes(this.indentReqUrl)) {
          this.displayedColumns = ['sNo','indentId', 'recipientArea', 'date','documentDate','creator', 'status', 'checkStatus'];
        } else {
          this.displayedColumns = ['sNo','indentId', 'recipientArea', 'date','documentDate', 'status', 'checkStatus'];
          this.tenantDetail();       
        }
        const uniqueItemNames = new Set();
        data.forEach(element => {
          element.itemList.forEach(element => uniqueItemNames.add(element));
        });
        this.itemNameList = Array.from(uniqueItemNames); 

        const savedItemNames = JSON.parse(localStorage.getItem('savedItemNames') || '[]');        
        if (savedItemNames.length > 0 && this.restaurantId === this.prevBranchId && !this.dataRefresh) {
          this.itemName.setValue(savedItemNames);
          this.itemNameChange(savedItemNames);
        } else {
          this.itemName.setValue(this.itemNameList.slice()); 
          this.itemNameChange(this.itemName.value);
        }
        this.itemBank = this.itemNameList
        this.itemBanks.next(this.itemBank.slice());
        this.itemFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
            this.vendorfilterBanks();
        });
        this.dataRefresh = false;
        this.getFullIndentData = data
        this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
        if (this.indentListStatus.value) {
          this.selectIndentStatus(this.indentListStatus.value);
        }
      }
    });
  }

  itemNameChange(selectedItemNames: string[]) {      
    if (selectedItemNames && selectedItemNames.length > 0) {
      this.dataSource.data = this.getFullIndentData.filter(element =>
        element.itemList.some(item => selectedItemNames.includes(item))
      );
    } else {
      this.dataSource.data = [];
    }
  }

  toggleSelectAll() {
    this.allSelected = !this.allSelected;
    if (this.allSelected) {
      this.itemName.setValue(this.itemBank.slice());
    } else {
      this.itemName.setValue([]);
    }
    this.itemNameChange(this.itemName.value);
  }

  protected vendorfilterBanks() {
    if (!this.itemBank) {
      return;
    }
    let search = this.itemFilterCtrl.value;
    if (!search) {
      this.itemBanks.next(this.itemBank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.itemBanks.next(
      this.itemBank.filter(item => item.toLowerCase().indexOf(search) > -1)
    );
  }

  filterByBranch(restId) {    
    this.prevBranchId = window.sessionStorage.getItem("restaurantId");
    this.restaurantId = restId.restaurantIdOld;
    this.branchSelected = true
    window.sessionStorage.setItem("restaurantId", this.restaurantId)
    window.sessionStorage.setItem("branchSelected", this.branchSelected.toString())    
    
    if (this.sharedFilterData != '' && this.sharedFilterData.restaurantId == restId) {
      this.indentListForm.get('branchSelection').setValue(this.sharedFilterData.restaurantId);
      this.branches = this.getBranchData
      this.startDate.setValue(this.sharedFilterData.selectedStartDate)
      this.endDate.setValue(this.sharedFilterData.selectedEndDate)
      this.indentListStatus.setValue(this.sharedFilterData.status)
    } else {
      this.startDate.setValue(null);
      this.endDate.setValue(null);
      this.indentListStatus.setValue(null);
    }
    this.getIndentReqList()
  }

  detailedIndent(obj) {
    let inputObj = {
      restaurantId: this.indentListForm.value.branchSelection,
      selectedStartDate: this.startDate.value,
      selectedEndDate: this.endDate.value,
      status: this.indentListStatus.value,
      branchFlag: true
    }
    this.sharedFilterService.getFilteredIndentsList.next(inputObj);
    obj['tenantDetails'] =  this.tenantDetails
    if (this.router.url.includes(this.indentReqUrl)) {
      this.sharedData.changeIndent(obj, this.indentReqFlag);
      this.router.navigate(['/home/<USER>'], { queryParams: { type: 'request' } });
    } else {
      this.sharedData.changeIndent(obj, this.indentReqFlag);
      this.router.navigate(['/home/<USER>']);
    }
  }

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }

  resetForm() {
    this.searchText = ''
    this.searchValue = ''
    this.doFilter(this.searchValue)
    this.dataSource.data = this.data
  }

  filterByDate() {
    window.sessionStorage.setItem("startDate-IL", this.startDate.value);
    window.sessionStorage.setItem("endDate-IL", this.endDate.value);
    if (this.startDate.value && this.endDate.value) {
      this.getIndentReqList()
    } else {
      this.utils.snackBarShowWarning('Please select start date and end date');
    }
  }

  deleteIndent(obj) {
    obj['deletedBy'] = this.user.role
    this.dialog.open(SimpleDialogComponent, {
      data: {
        title: 'Delete Indent',
        msg: 'Are you sure you want to delete?',
        ok: function () {
          this.branchTransfer.deleteIndent(obj).subscribe(res => {
            if (res['result'] === 'success') {
              this.utils.snackBarShowSuccess('Indent deleted successfully!');
            } else {
              if("message" in res){
                this.utils.snackBarShowError(res['message']);
              }else{
                this.utils.snackBarShowError('Something went wrong,please try again');
              }
            }
            this.getIndentList();
          })
        }.bind(this)
      }
    });
  }

  selectIndentStatus(status) {
    if (status == 'All') {
      this.dataSource = new MatTableDataSource<IndentItem>()
      this.dataSource.data = this.getFullIndentData;
      this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
    } else {
      this.dataSource.filter = status.trim().toLocaleLowerCase();
    }
  }

  tenantDetail() {
    let obj = {
      tenantId: this.user.tenantId
    }
    this.branchTransfer.tenantDetails(obj).subscribe(res => {
      if (res.result == 'success') {
        this.tenantDetails = res.data[0].permission 
        let deleteAccess = res.data[0] && res.data[0].permission && res.data[0].permission.indentAccess ? res.data[0].permission.indentAccess.delete : false;
        let closeAccess = res.data[0] && res.data[0].permission && res.data[0].permission.indentAccess ? res.data[0].permission.indentAccess.close : false; 

        if (deleteAccess) {
          let rolesList = (res.data[0] && res.data[0].permission && res.data[0].permission.indentAccess) ? res.data[0].permission.indentAccess.deleteAccess : [];
          this.deleteAccess = (rolesList.some(role => role.toLowerCase() === this.user.role.toLowerCase())) ? true : false ;
        } else {
          this.deleteAccess = false ;
        }
        if (closeAccess) {
          let rolesList = (res.data[0] && res.data[0].permission && res.data[0].permission.indentAccess) ? res.data[0].permission.indentAccess.closeAccess : [];
          this.closeAccess = (rolesList.some(role => role.toLowerCase() === this.user.role.toLowerCase())) ? true : false ;
        } else {
          this.closeAccess = false ;
        }
        let autoClosure = this.dataSource.data.filter((item)=> item.hasOwnProperty('autoClosure'))
        if (this.deleteAccess || this.closeAccess || autoClosure.length > 0) {
          if (!this.displayedColumns.includes('Action')) {
            this.displayedColumns.push('Action');
          }
        } 
      } else {
        this.deleteAccess = false ;
        this.closeAccess = false ;
      }
    })
  }

  refreshData() {
    this.dataRefresh = true;
    this.getIndentList();
  }

  clear() {
    this.startDate.setValue(null);
    this.endDate.setValue(null);
    this.indentListStatus.setValue(null);
    this.getIndentList();
  }

  mouseEnter(element) {
    this.elementdata = element
    this.dialog.open(PreviewIbtComponent, {
      width: "600px",
      data: {
        title: "Indents List",
        component: "Purchase Status",
        items: this.elementdata,
        ok: function () {
          this.location.back();
        }.bind(this),
      },
    });
  }

  updateStatus(element, status){
    let obj = {}
    obj['tenantId'] = element.tenantId
    obj['restaurantId'] = element.restaurantId
    obj['indentId'] = element.indentId
    obj['uId'] = this.user.mId
    obj['indentStatus'] = status
    this.dialog.open(SimpleDialogComponent, {
      data: {
        title: 'Close PO',
        msg: 'Are you sure you want to Close?',
        ok: function () {
          this.purchases.updateIndentStatus(obj).subscribe(res => {
            res.result ? (this.utils.snackBarShowSuccess('Indent closed successfully'),this.refreshData()) : this.utils.snackBarShowError('Something went wrong')
          })
        }.bind(this)
      }
    });
  }

}