<table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort>

  <ng-container matColumnDef="itemName">
    <th mat-header-cell *matHeaderCellDef mat-sort-header><b> Item Name</b></th>
    <td mat-cell *matCellDef="let element"> {{element.itemName | titlecase}} </td>
  </ng-container>
  <ng-container matColumnDef="index">
    <th mat-header-cell *matHeaderCellDef mat-sort-header class="tableId"><b>#</b></th>
    <td mat-cell *matCellDef="let element; let i = index" class="tableId"> {{i+1}} </td>
  </ng-container>
  <ng-container matColumnDef="indentId">
    <th mat-header-cell *matHeaderCellDef mat-sort-header><b> In Stock</b></th>
    <td mat-cell *matCellDef="let element"> {{element.intendId}} </td>
  </ng-container>
  <ng-container matColumnDef="status">
    <th mat-header-cell *matHeaderCellDef mat-sort-header><b> Uom</b></th>
    <td mat-cell *matCellDef="  let element"> {{element.status}} </td>
  </ng-container>
  <ng-container matColumnDef="createTs">
    <th mat-header-cell *matHeaderCellDef mat-sort-header><b> Issue Date
    </b></th>
    <td mat-cell *matCellDef="let element" > {{element.createTs | date : 'EEEE, MMMM d, y'}} </td>
  </ng-container>
  <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
  <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>


</table>
<div class="dataMessage" *ngIf="dataSource?.data.length == 0"> No Data Available </div>
<mat-paginator [showTotalPages]="5" [pageSize]="10" [pageSizeOptions]="pageSizes"></mat-paginator>