import { Component, OnInit, ViewChild } from '@angular/core';
import { AuthService, PurchasesService, BranchTransferService } from '../_services';
import { GlobalsService } from '../_services/globals.service';
import { UtilsService } from '../_utils/utils.service';
import { MatDialog } from '@angular/material';
import { MatTableDataSource, MatPaginator } from '@angular/material';
@Component({
  selector: 'app-indents',
  templateUrl: './indents.component.html',
  styleUrls: ['./indents.component.scss']
})
export class IndentsComponent implements OnInit {

  user: any;
  inventoryItems: any[];
  displayedColumns = GlobalsService.indentListColumns
  dataSource: MatTableDataSource<any>;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  pageSizes: number[];
  constructor(private auth: AuthService, private purchases: PurchasesService,
    private branchTransfer: BranchTransferService,
    private dialog: MatDialog, private utils: UtilsService) {
    this.user = this.auth.getCurrentUser()
  }

  ngOnInit() {
    this.getBranchInv()
    this.dataSource = new MatTableDataSource()
  }

  ngAfterViewInit() {
    this.pageSizes = this.utils.getPageSizes(this.inventoryItems)
    this.dataSource.paginator = this.paginator;
    }

  getBranchInv() {
    this.branchTransfer.getBranchInv({
      tenantId: this.user.tenantId,
      restaurantId: this.user.restaurantAccess[0].restaurantIdOld,
      uId: this.user.mId
    }).subscribe(data => {
      if (data)
        this.inventoryItems = data;
      this.dataSource.data = this.inventoryItems
      this.pageSizes = this.utils.getPageSizes(this.inventoryItems)
      this.dataSource.paginator = this.paginator;

    },
      err => console.error(err))
  }

}
