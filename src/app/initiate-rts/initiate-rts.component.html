<div class="title">
  <mat-form-field *ngIf="multiBranchUser" appearance="none" class="topitem">
    <label>Select Branch</label>
    <mat-select placeholder="Select Branch" class="outline" (selectionChange)="filterByBranch($event.value)">
      <mat-option *ngFor="let rest of this.user.restaurantAccess" [value]="rest.restaurantIdOld">
        {{ rest.branchName }}
      </mat-option>
    </mat-select>
  </mat-form-field>
  <mat-form-field appearance="none" *ngIf="specialFlag == true" style="margin-left: 10px;" class="topitem">
    <label>Select Work Area</label>
    <mat-select placeholder="Select Work Area" (selectionChange)="selectWorkArea($event)" class="outline">
      <mat-option *ngFor="let area of IndentAreas" [value]="area">
        {{ area }}
      </mat-option>
    </mat-select>
  </mat-form-field>
  <button mat-button mat-raised-button class="button3" (click)="returnReq()" style="float: right;">
    Return
  </button>

  <button mat-button mat-raised-button class="button" (click)="exportToExcel()" style="float: right;">
    Export
  </button>
  <button mat-button mat-raised-button class="button" (click)="printToPdf()" style="float: right;">
    Print
  </button>
  <div *ngIf='workAreaSelected'>
    <mat-slide-toggle style="float: right;" [(ngModel)]="rtsPreview" (change)="preview()">Preview</mat-slide-toggle>
  </div>

</div>
<div *ngIf="(branchSelected && multiBranchUser && workAreaSelected) || (!multiBranchUser && workAreaSelected)"
  class="datacontainer">
  <mat-card>
    <mat-card-content>
      <div class="search-table-input">
        <span style="float: left; color: #3586ca;font-size: 16px;font-style: italic;">
          TOTAL (in Rs): {{ this.utils.truncateNew(totalIndentCost)}}
        </span>
      </div>
      <div class="search-table-input">
        <mat-form-field appearance="none">
          <label>Search</label>
          <input matInput type="text" (keyup)="doFilter($event.target.value)" placeholder="Search" class="outline"
            [(ngModel)]='searchText' />
          <mat-icon matSuffix (click)="clear()" class="closebtn">close</mat-icon>
        </mat-form-field>
        <mat-form-field appearance="none">
          <label>Type</label>
          <mat-select placeholder="Item Type" [formControl]="ItemType" class="outline">
            <mat-option *ngFor="let itemType of ItemTypeList" [value]="itemType" (click)="selectItemType(itemType)">
              {{ itemType | titlecase }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field appearance="none">
          <label>Category</label>
          <mat-select placeholder="Category" class="outline" [formControl]="category">
            <mat-option *ngFor="let cat of categoryList" [value]="cat" (click)="selectCategory(cat)">
              {{ cat | titlecase}}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field appearance="none">
          <label>Sub Category</label>
          <mat-select placeholder="Subcategory" [formControl]="Subcategory" class="outline">
            <mat-option *ngFor="let subCat of subCategoryList" [value]="subCat" (click)="selectSubCat(subCat)">
              {{ subCat | titlecase }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- <button mat-raised-button class="button2 btn-margin" (click)=clear()>
          Clear
        </button> -->
      </div>
      <table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort>
        <ng-container matColumnDef="itemName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Item Name</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.itemName | titlecase }}
          </td>
        </ng-container>

        <ng-container matColumnDef="index">
          <th mat-header-cell *matHeaderCellDef mat-sort-header class="tableId"><b>#</b></th>
          <td mat-cell *matCellDef="let element; let i = index" class="tableId">
            {{ i + 1 + paginator.pageIndex * paginator.pageSize }}
          </td>
        </ng-container>

        <ng-container matColumnDef="workAreaStock">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> WorkArea Stock</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ this.utils.truncateNew(element.workArea[this.indentArea]) }}
          </td>
        </ng-container>

        <ng-container matColumnDef="unitPrice">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Unit Price </b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ this.utils.truncateNew(element.price) }}
          </td>
        </ng-container>

        <ng-container matColumnDef="totalPrice">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Total Price </b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ this.utils.truncateNew(element.returnQty * element.price) }}
          </td>
        </ng-container>

        <ng-container matColumnDef="pkgName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Pkg Name</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.packageName }}
          </td>
        </ng-container>

        <ng-container matColumnDef="returnQty">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Return Quantity</b>
          </th>
          <td mat-cell *matCellDef="let element">
            <input class="input1" type="number" step="any" (keyup)="getTotalReturnCost(element)" min="0"
              [(ngModel)]="element.returnQty" (focus)="focusFunctionWithOutForm(element,'returnQty')" (focusout)="focusOutFunctionWithOutForm(element,'returnQty')"
              />
              <!-- onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)"  -->
          </td>
        </ng-container>
        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>
      <mat-paginator [showTotalPages]="5" [pageSize]="10" [pageSizeOptions]="pageSizes"></mat-paginator>
    </mat-card-content>
  </mat-card>
</div>