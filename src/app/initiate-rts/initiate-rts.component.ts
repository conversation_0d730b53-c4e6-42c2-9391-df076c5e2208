import { Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService, PurchasesService, BranchTransferService } from '../_services';
import { FormControl, Validators } from '@angular/forms';
import { GlobalsService } from '../_services/globals.service';
import { UtilsService } from '../_utils/utils.service';
import { MatDialog } from '@angular/material';
import { SimpleDialogComponent } from '../_dialogs/simple-dialog/simple-dialog.component';
import { MatTableDataSource, MatPaginator, MatSort } from '@angular/material';
import { NotificationService } from '../_services/notification.service';
@Component({
  selector: 'app-initiate-rts',
  templateUrl: './initiate-rts.component.html',
  styleUrls: ['./initiate-rts.component.scss', './../../common-dark.scss']
})
export class InitiateRtsComponent implements OnInit {
  IndentAreas: string[] = [];
  indentArea: any;
  specialFlag: boolean;
  all = "ALL";
  rtsPreview: boolean = false;
  curDataSource: any[];
  initiateRtsUrl = encodeURI(GlobalsService.initiateRts)
  user: any;
  inventoryItems: any[];
  displayedColumns;
  title;
  pageSizes = []
  restaurantId: any;
  savedItems: any;
  workAreaSelected: boolean = false;
  initData: any;
  branchSelected: any;
  multiBranchUser: any;
  categoryList = ['All'];
  subCategoryList = ['All'];
  ItemTypeList = ['All'];
  initSubCategoryList: any = [];
  initCategoryList: any = [];
  subCatList: any = [];
  searchText: any
  filterKeys = { ItemType: 'All', category: 'All', subCategory: 'All' }
  category = new FormControl('', [Validators.required]);
  Subcategory = new FormControl();
  ItemType = new FormControl();
  dataSource: MatTableDataSource<any>;
  totalIndentCost: any = 0;
  @ViewChild(MatPaginator) set paginator(value: MatPaginator) {
    if (this.dataSource) {
      this.dataSource.paginator = value;
    }
  };
  
  @ViewChild('table') private _table: any;
  constructor(private auth: AuthService, private purchases: PurchasesService,
    private branchTransfer: BranchTransferService,
    private dialog: MatDialog, private utils: UtilsService,
    private notifyService: NotificationService,
    private router: Router) {
    this.user = this.auth.getCurrentUser()
  }

  ngOnInit() {
    this.multiBranchUser = this.user.multiBranchUser
    if (!this.user.multiBranchUser) {
      this.restaurantId = this.user.restaurantAccess[0].restaurantIdOld
      this.getBranchInv()
    }
    this.specialFlag = this.router.url.includes(this.initiateRtsUrl);
    this.dataSource = new MatTableDataSource()
    this.displayedColumns = GlobalsService.initiateRtsColoumn;
    this.title = "Special Indent"
  }

  ngAfterViewInit() {
    // this.dataSource.paginator = this.paginator;
    // this.dataSource.sort = this.sort;
  }

  selectWorkArea(val) {
    this.rtsPreview = false
    this.indentArea = val.value;
    this.workAreaSelected = true
    this.dataSource.data = this.inventoryItems.filter(item => Object.keys(item.workArea).includes(val.value))
      .map((item: any) => { item.returnQty = 0; return item })
    this.curDataSource = this.dataSource.data
    this.getTotalReturnCost(null)
    this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
    this.dataSource.paginator = this.paginator;
  }

  getBranchInv() {
    this.branchTransfer.getRtsInv({
      tenantId: this.user.tenantId,
      restaurantId: this.restaurantId,
      userEmail: this.user.email,
      uId: this.user.mId,
      specialFlag: this.specialFlag,
      reqQty: true
    }).subscribe(data => {
      if (data) {
        this.initData = data.invItems
        this.savedItems = data.savedItems
        this.inventoryItems = data.invItems
        this.inventoryItems
          .map((item: any) => {
            if (!item.hasOwnProperty('packageName')) {
              item.packageName = item.uom
              item.packageQty = 1
            }
            item.returnQty = 0
            return item
          }
          )
      }

      this.dataSource.data = this.inventoryItems
      this.user.restaurantAccess.forEach(element => {
        if (element.restaurantIdOld == this.restaurantId) {
          this.IndentAreas = element.workAreas
          if (element.workAreas == undefined) {
            this.IndentAreas = data.workAreas;
          }
        }
      });

      this.inventoryItems.forEach(item => {
        if (item.category == null) {
          item.category = 'N/A'
        }
        if (item.ItemType == null) {
          item.ItemType = 'N/A'
        }
        if (item.subCategory == null) {
          item.subCategory = 'N/A'
        }
        this.ItemTypeList.push(item.ItemType)
        this.categoryList.push(item.category)
        this.subCategoryList.push(item.subCategory)
      })
      this.categoryList = this.categoryList.filter((k, i, ar) => ar.indexOf(k) === i).sort();
      this.ItemTypeList = this.ItemTypeList.filter((p, q, arrr) => arrr.indexOf(p) === q).sort();
      this.subCategoryList = this.subCategoryList.filter((j, l, arr) => arr.indexOf(j) === l).sort();
      this.initCategoryList = this.categoryList;
      this.initSubCategoryList = this.subCategoryList;
      this.pageSizes = this.utils.getPageSizes(this.inventoryItems)
      this.dataSource.paginator = this.paginator;
    },
      err => console.error(err))
  }
  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }

  printToPdf() {
    let inventoryList = {};
    inventoryList['inventoryItems'] = [];
    this.dataSource.data.forEach(function (item) {
      if (item['returnQty'] > 0) {
        inventoryList['inventoryItems'].push(item);
      }
    });
    if (inventoryList['inventoryItems'].length == 0) {
      this.utils.snackBarShowWarning('please enter indent values');
      return;
    }
    inventoryList['user'] = this.user;
    inventoryList['recipientArea'] = this.indentArea
    this.purchases.printpdfs(inventoryList, 'Indent').subscribe(data => {
      // window.open('data:application/pdf;base64,' + data.eFile);
      this.purchases.globalPrintPdf(data.eFile);
    });
  }

  exportToExcel() {
    let inventoryList = {}
    inventoryList['indentArea'] = this.indentArea;
    inventoryList['inventoryItems'] = this.dataSource.data;
    inventoryList['user'] = this.user;
    this.purchases.exportToExcel(inventoryList, 'Indent').subscribe(data => {
      window.open('data:text/csv;base64,' + data.eFile);
    });
  }

  validateIssueQty(element) {
    element.inStock < element.returnQty ? element.returnQty = element.inStock : element;
  }

  filterByBranch(restId) {
    this.restaurantId = restId;
    this.branchSelected = true
    this.category.setValue('')
    this.getBranchInv()
    this.dataSource = new MatTableDataSource()

  }

  allFilter() {
    let tmp = this.curDataSource
    let prev = this.curDataSource
    Object.keys(this.filterKeys).forEach(element => {
      if (this.filterKeys[element] != 'All') {
        tmp = prev.filter(item =>
          item[element].toLowerCase() === this.filterKeys[element].toLowerCase())
        prev = tmp
      }
    });
    this.dataSource.data = tmp
  }

  selectItemType(itemType) {
    let filteredCategoryList = []
    let filteredSubCategoryList = []
    if (itemType != 'All') {
      let filteredItem = this.curDataSource.filter(item => item['ItemType'].toUpperCase() === itemType.toUpperCase());
      filteredItem.forEach(element => {
        filteredCategoryList.push(element.category)
        filteredSubCategoryList.push(element.subCategory)
      });
      this.categoryList = filteredCategoryList.filter((k, i, ar) => ar.indexOf(k) === i).sort();
      this.subCategoryList = filteredSubCategoryList.filter((k, i, ar) => ar.indexOf(k) === i).sort();
      this.categoryList.splice(0, 0, 'All')
      this.subCategoryList.splice(0, 0, 'All')
      this.subCatList = this.subCategoryList
    }
    else {
      this.categoryList = this.initCategoryList;
      this.subCategoryList = this.initSubCategoryList;
    }
    this.filterKeys = { ItemType: itemType, category: 'All', subCategory: 'All' }
    this.allFilter()
  }

  selectCategory(cat) {
    let filteredSubCategoryList = []
    let filteredItem = []
    if (cat != 'All') {
      if (this.filterKeys.ItemType != 'All') {
        filteredItem = this.curDataSource.filter(item => item['category'].toUpperCase() === cat.toUpperCase()
          && item['ItemType'].toUpperCase() === this.filterKeys.ItemType.toUpperCase());
      }
      else {
        filteredItem = this.curDataSource.filter(item => item['category'].toUpperCase() === cat.toUpperCase());
      }
      filteredItem.forEach(element => {
        filteredSubCategoryList.push(element.subCategory)
      });
      this.subCategoryList = filteredSubCategoryList.filter((k, i, ar) => ar.indexOf(k) === i).sort();
      this.subCategoryList.splice(0, 0, 'All')
    }
    else if (this.filterKeys.ItemType != 'All') {
      this.subCategoryList = this.subCatList.sort()
    }
    else {
      this.subCategoryList = this.initSubCategoryList
    }
    this.filterKeys.category = cat;
    this.filterKeys.subCategory = 'All'
    this.allFilter()
  }

  selectSubCat(subCat) {
    this.filterKeys.subCategory = subCat;
    this.allFilter()
  }

  returnReq() {
    let faultItemCount = 0
    let itemsToReturn = this.inventoryItems.filter(item => item.returnQty > 0 && Object.keys(item.workArea).includes(this.indentArea))
    itemsToReturn.forEach(element => {
      if (element.returnQty > element.workArea[this.indentArea]) {
        faultItemCount = faultItemCount + 1
      }
    });
    if (faultItemCount > 0) {
      this.dialog.open(SimpleDialogComponent, {
        data: {
          title: 'Return Alert',
          msg: 'There are ' + faultItemCount + ' items with return Qty greater than workarea stock. Please adjust accordingly.',
          ok: function () {
          }.bind(this)
        }
      });

    }
    else {
      this.raiseReturnReq(itemsToReturn)

    }



  }

  raiseReturnReq(itemsToReturn) {
    if (itemsToReturn.length > 0) {
      this.branchTransfer.rtsReq({
        invItems: itemsToReturn,
        userEmail: this.user.email,
        tenantId: this.user.tenantId,
        indentArea: this.indentArea,
        restaurantId: this.restaurantId
      }).subscribe(data => {
        this.utils.snackBarShowSuccess('Indent Sent successfully')
        this.dataSource.data = this.inventoryItems.filter(item => Object.keys(item.workArea).includes(this.indentArea))
          .map((item: any) => { item.returnQty = 0; return item })
      }, err => console.error(err))
    }
    else {
      this.utils.snackBarShowWarning('No Items to issue. Please add Item')
    }
  }

  validateAndDcrsReqQty(element) {
    element.returnQty = element.returnQty - 1
    if (element.returnQty < 0)
      element.returnQty = 0
    this.getTotalReturnCost(null)
  }
  validateAndIncrsReqQty(element) {
    if ((element.returnQty < element.workArea[this.indentArea]))
      element.returnQty = element.returnQty + 1
    this.getTotalReturnCost(null)
  }

  clear() {
    this.category.setValue('')
    this.Subcategory.setValue('')
    this.ItemType.setValue('')
    this.searchText = ''
    this.dataSource.data = this.inventoryItems.filter(item => Object.keys(item.workArea).includes(this.indentArea))
    this.doFilter(this.searchText)
  }

  preview() {
    if (this.rtsPreview == true) {
      this.curDataSource = this.dataSource.data
      this.dataSource.data = this.inventoryItems.filter(item => item.returnQty > 0 && Object.keys(item.workArea).includes(this.indentArea))
      this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
      this.dataSource.paginator = this.paginator;
    }
    else {
      this.dataSource.data = this.curDataSource
      this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
      this.dataSource.paginator = this.paginator;
    }
  }

  clearSearchText() {
    this.searchText = ''
    this.doFilter(this.searchText)
  }

  getTotalReturnCost(element) {
    let sum = 0;
    if (element != null)
      element.returnQty > element.workArea[this.indentArea] ? element.returnQty = element.workArea[this.indentArea] : element.returnQty;
    this.dataSource.data.forEach(element => {
      if (element.returnQty > 0 && element.price > 0) {
        sum = sum + element.price * element.returnQty
      }
    });
    this.totalIndentCost = sum
  }

  focusFunctionWithOutForm(element , value){
    if(Number(element[value]) === 0){
      element[value] = null;
    }
  }
  
  focusOutFunctionWithOutForm(element , value){
    if(element[value] === null){
      element[value] = 0
    }
  }

}
