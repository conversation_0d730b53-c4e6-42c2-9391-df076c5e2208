<div class="title">
  <button mat-raised-button class="button" style="float: left; margin-left: 0px;" (click)=goBack()>
    <mat-icon>keyboard_backspace</mat-icon> Back to GRN
  </button>
  <button mat-button mat-raised-button id="save-btn" class="button3" style="float: right;" 
  [matTooltip]="(!disableButton && creditNo.value == null)  ? 'Enter Credit Note No' : '' "
  (click)="generateRtv()" [disabled]="disableButton || creditNo.value == null || creditNo.value == '' || returnQtyMax()">
    Generate RTV
  </button>
  <!-- <button mat-raised-button class="button" style="float: right;" (click)="detailedRtvList()">
    RTV List
  </button> -->
</div>

<div class="search-table-input fieldcontainer">
    <div class="row">
      <div class="col" style="margin-left: 8px;">
        <table class="table">
          <tbody>
            <tr>
              <th class="topItemkey" scope="row">GRN ID</th>
              <td>{{ grn.grnId }}</td>
            </tr>
            <tr>
              <th class="topItemkey" scope="row">System Entry Date</th>
              <td>{{ grn.createTs | date: "EEEE, MMMM d, y" }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    
      <div class="col">
        <table class="table">
          <tbody>
            <tr>
              <th class="topItemkey" scope="row">PO ID</th>
              <td>{{ grn.details?.poId ? grn.details.poId : '-' }}</td>
            </tr>
            <tr>
              <th class="topItemkey" scope="row">Document Date</th>
              <td>{{ (grn.grnDocumentDate | date: "EEEE, MMMM d, y") || '-' }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    
      <div class="col">
        <table class="table">
          <tbody>
            <tr>
              <th class="topItemkey" scope="row">Invoice No</th>
              <td>{{ grn.invoiceId }}</td>
            </tr>
            <tr>
              <th class="topItemkey" scope="row"> Invoice Date</th>
              <td>{{ grn.invoiceDate | date: "EEEE, MMMM d, y" }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div class="topItemsInputs">
      <mat-form-field appearance="outline" class="rightInputs m-2">
        <mat-label>Credit Note No</mat-label>
        <input matInput placeholder="Credit Note No" [formControl]="creditNo"/>
      </mat-form-field>

      <mat-form-field appearance="outline" class="rightInputs m-2">
        <mat-label>Credit Note Date</mat-label>
        <input matInput [matDatepicker]="picker" tabindex="-1" placeholder="Credit Date"
          [formControl]="creditDate" [(ngModel)]="delDate" [max]="startDate"/>
        <mat-datepicker-toggle matSuffix [for]="picker" tabindex="-1">
          <mat-icon matDatepickerToggleIcon><img src="./../../assets/calender.png" /></mat-icon>
        </mat-datepicker-toggle>
        <mat-datepicker #picker disabled="false"></mat-datepicker>
        <mat-error>
          Select Credit Date
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="rightInputs m-2">
        <mat-label>Return Date</mat-label>
        <input matInput [matDatepicker]="picker1" tabindex="-1" placeholder="Return Date"
          [formControl]="returnDate" readonly='true'/>
        <mat-datepicker-toggle matSuffix [for]="picker1" tabindex="-1">
          <mat-icon matDatepickerToggleIcon><img src="./../../assets/calender.png" /></mat-icon>
        </mat-datepicker-toggle>
        <mat-datepicker #picker1 disabled="true"></mat-datepicker>
        <mat-error>
          Select Return Date
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="rightInputs m-2">
        <mat-label> Remarks </mat-label>
        <textarea matInput style="font-size: 15px; height: 22px;" [formControl]="remarks" maxlength="100"></textarea>
      </mat-form-field>
    </div>
</div>

<!-- <div class="matNavList mb-2">
  <mat-nav-list>
    <mat-expansion-panel (opened)="panelOpenState = true" (closed)="panelOpenState = false">
      <mat-expansion-panel-header>
        <mat-panel-title class="topItemkey" style="font-size: 15px; font-weight: bold;">
          GRN DETAILS
        </mat-panel-title>
        <mat-panel-description>
          <mat-icon *ngIf="panelOpenState == true">visibility</mat-icon>
          <mat-icon *ngIf="panelOpenState == false">visibility_off</mat-icon>
        </mat-panel-description>
      </mat-expansion-panel-header>
        <div class="search-table-input fieldcontainer">
          <div class="row">
          <div class="col">
            <table class="table">
              <tbody>
                <tr>
                  <th class="topItemkey" scope="row">GRN ID</th>
                  <td>{{ grn.grnId }}</td>
                </tr>
                <tr>
                  <th class="topItemkey" scope="row">System Entry Date</th>
                  <td>{{ grn.createTs | date: "EEEE, MMMM d, y" }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        
          <div class="col">
            <table class="table">
              <tbody>
                <tr>
                  <th class="topItemkey" scope="row">PO ID</th>
                  <td>{{ grn.details.poId }}</td>
                </tr>
                <tr>
                  <th class="topItemkey" scope="row">Document Date</th>
                  <td>{{ (grn.grnDocumentDate | date: "EEEE, MMMM d, y") || '-' }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        
          <div class="col">
            <table class="table">
              <tbody>
                <tr>
                  <th class="topItemkey" scope="row">INV ID</th>
                  <td>{{ grn.invoiceId }}</td>
                </tr>
                <tr>
                  <th class="topItemkey" scope="row"> Invoice Date</th>
                  <td>{{ grn.invoiceDate | date: "EEEE, MMMM d, y" }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        </div>
    </mat-expansion-panel>
  </mat-nav-list>
</div> -->

<mat-card class="matcontent" style="margin-top: 10px !important;">
  <table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort>
   
    <ng-container matColumnDef="index">
      <th mat-header-cell *matHeaderCellDef><b>S.No</b></th>
      <td mat-cell *matCellDef="let element; let i = index" style="padding-right: 30px;">
        {{ i + 1 }}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="itemCode">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Item Code</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ element.itemCode }}
      </td>
      <td mat-footer-cell *matFooterCellDef>Total</td>
    </ng-container>

    <ng-container matColumnDef="itemName">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Item Name</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ element.itemName }}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="pkgName">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Pkg Name</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ element.packages[0].packageName }}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="receivedQty">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Received Quantity</b>
      </th>
      <td mat-cell *matCellDef="let element">
        <!-- {{ element.receivedQty }} -->
        {{element.hasOwnProperty('RTVQty') ? element['receivedQty'] - element['RTVQty'] : element['receivedQty']}}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="returnQty">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>RTV Qty</b>
      </th>
      <td mat-cell *matCellDef="let element">
        <input class="input1" type="number" min="0" [(ngModel)]="element.returnQty" (keyup)="validateReturnQty(element)" 
        [step]="element.uom === 'NOS' ? '1' : 'any'"
        (focus)="focusFunctionWithOutForm(element , 'returnQty')" (focusout)="focusOutFunctionWithOutForm(element , 'returnQty')"
        onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)"
         />
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="unitCost">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Unit Cost</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{element.packages[0].packagePrice}}
        <!-- {{ element.unitPrice }} -->
        <!-- <input class="input1" type="number" step="0.01" min="0" [(ngModel)]="element.unitPrice" (keyup)="validateReturnQty(element)"
         (focus)="focusFunctionWithOutForm(element , 'unitPrice')" (focusout)="focusOutFunctionWithOutForm(element , 'unitPrice')"
         onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)"
         /> -->
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="taxRate">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Tax Rate</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ element.taxRate }}
        <!-- <input class="input1" type="number" step="0.01" min="0" [(ngModel)]="element.taxRate" (keyup)="validateReturnQty(element)"
         (focus)="focusFunctionWithOutForm(element , 'taxRate')" (focusout)="focusOutFunctionWithOutForm(element , 'taxRate')"
         onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)"
         /> -->
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="taxAmount">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Tax Amt</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ this.utils.truncateNew(element.taxAmount) }}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="extra">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Extra Amt</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ element.extraAmt }}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="disc">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Discount</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ (element.discAmt) }}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="cess">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Cess Amt</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ (element.cessAmt) }}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="totalPrice">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Total</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ this.utils.truncateNew(element.totalPrice) }}
      </td>
      <td mat-footer-cell *matFooterCellDef> {{ this.utils.truncateNew(getTotal('totalPrice'))}} </td>
    </ng-container>

    <ng-container matColumnDef="inStock">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>In Store</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ this.utils.truncateNew(element.inStock) }}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="reason">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b> Remarks </b>
      </th>
      <td mat-cell *matCellDef="let element" class="text-Input">
          <textarea rows="2" maxlength="200" wrap="soft" cols="20" maxlength="100"
          [(ngModel)]="element.reason"  style="font-size: 15px;"></textarea>
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
    <tr mat-footer-row *matFooterRowDef="displayedColumns"></tr>
  </table>

  <mat-card-actions>
    <!-- <button <mat-butto></mat-butto>n color="primary" (click)="approvePr()" [disabled]="disableApprBtn">{{user.uType == 'vendor'? 'Approve Pr' : 'Create PO'}}</button> -->
  </mat-card-actions>
</mat-card>