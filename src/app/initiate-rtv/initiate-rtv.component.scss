input[type=number]::-webkit-inner-spin-button, 
input[type=number]::-webkit-outer-spin-button { 
  -webkit-appearance: none; 
}

input[type=number] {
  -moz-appearance: textfield;
}

.matNavList{
  margin-left: 2%;
  margin-right: 2%;
}

.firstCard{
  margin-top: -10px;
}

::ng-deep td.mat-cell:last-of-type, td.mat-footer-cell:last-of-type, th.mat-header-cell:last-of-type {
  padding-right: 10px !important;
}

example-headers-align .mat-expansion-panel-header-title, 
.example-headers-align .mat-expansion-panel-header-description {
  flex-basis: 0;
}

.example-headers-align .mat-expansion-panel-header-description {
  justify-content: flex-end !important;
}

::ng-deep span.mat-expansion-indicator.ng-trigger.ng-trigger-indicatorRotate.ng-star-inserted {
  display: none !important;
}

::ng-deep mat-panel-description.mat-expansion-panel-header-description {
  justify-content: flex-end !important;
}

input:disabled {
  background-color: #c2c2a3;
}

.text-Input{
  padding-top: 10px;
  padding-bottom: 5px;
}
