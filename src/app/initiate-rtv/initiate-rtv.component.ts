import { Component, OnInit } from '@angular/core';
import { PurchasesService, ShareDataService, AuthService } from '../_services';
import { GlobalsService } from '../_services/globals.service';
import { MatSort, Sort, MatTableDataSource } from '@angular/material';
import { SelectionModel } from '@angular/cdk/collections';
import { Location } from '@angular/common';
import { UtilsService } from '../_utils/utils.service';
import { Router } from '@angular/router';
import { NotificationService } from '../_services/notification.service';
import { FormControl } from '@angular/forms';
@Component({
  selector: 'app-initiate-rtv',
  templateUrl: './initiate-rtv.component.html',
  styleUrls: ['./initiate-rtv.component.scss', "./../../common-dark.scss"]
})
export class InitiateRtvComponent implements OnInit {

  grn: any;
  user: any;
  dataSource: MatTableDataSource<any>;
  displayedColumns: string[];
  receiveBranch: string;
  vendorBranch: string;
  creditDate = new FormControl();
  returnDate = new FormControl();
  creditNo = new FormControl();
  remarks = new FormControl();
  delDate: any;
  selection = new SelectionModel<any>(true, []);
  startDate: Date;
  panelOpenState: boolean = false;
  disableButton: boolean = false;
  selectedElement: any;
  constructor(private sharedData: ShareDataService, private auth: AuthService,private notifyService: NotificationService,
    private loc: Location, private router: Router, private utils: UtilsService, private purchases: PurchasesService) {

  }

  ngOnInit() {
    this.sharedData.currGrn.subscribe(grn => {
      if (!grn.grnId)
        this.loc.back();
      this.grn = grn;
      this.receiveBranch = grn['restaurantId'].split('@')[1];
      if (grn['vendorBranch'] != null) {
        this.vendorBranch = grn['vendorBranch'].split('@')[1];
      }
      else {
        this.vendorBranch = '';
      }
      this.user = this.auth.getCurrentUser();
      this.grn.deliveryDate = new Date(grn.deliveryDate);
      this.grn.createTs = new Date(grn.createTs);
      this.dataSource = new MatTableDataSource<any>();
      let queryObj = {}
      queryObj['tenantId'] = this.grn.tenantId
      queryObj['restaurantId'] = this.grn.restaurantId
      queryObj['items'] = this.grn.grnItems
      this.purchases.getInstoreVal(queryObj).subscribe(items => {
        if (items.length > 0) {
          items.forEach(invItem => {
            this.grn.grnItems.forEach(grnItem => {
              if (grnItem.itemCode == invItem.itemCode && grnItem.packages[0].packageName == invItem.packageName) {
                grnItem.inStock = invItem.inStock
              }
              grnItem.returnQty = this.utils.truncateNew(grnItem.returnQty)
              let receivedQty = grnItem.hasOwnProperty('RTVQty') ? grnItem['receivedQty'] - grnItem['RTVQty'] : grnItem['receivedQty']
              grnItem.maxQty = Math.min(receivedQty, grnItem.inStock);
              grnItem.returnQty = grnItem.maxQty;
              this.validateReturnQty(grnItem);
            });
          });
          this.dataSource.data = this.grn.grnItems;
        }
      }, err => console.error(err));
      this.displayedColumns = Object.create(GlobalsService.detailedRtvGrnColumns);
      this.grn.grnItems.forEach(item => {        
        item.unitPrice = this.utils.truncateNew(item.unitPrice)
        item.inStock = this.utils.truncateNew(item.inStock)
        item.totalCost = this.utils.truncateNew(item.totalPrice)
        item.tax = this.utils.truncateNew(item.taxRate);
        item.taxAmt = this.utils.truncateNew(item.taxAmount);
        item.cess = this.utils.truncateNew(item.cessAmt);
        item.disc = this.utils.truncateNew(item.discAmt);
        item.extra = this.utils.truncateNew(item.extraAmt);
        item.returnQty = 0
        item.unitReturnRate = item.unitPrice
      });
    }, err => {
      console.error(err)
    });
    this.dateEntry();
  }

  dateEntry(){
    this.startDate = new Date();
    this.creditDate.setValue(this.startDate);
    this.returnDate.setValue(this.startDate);
    let date = new Date();
    date.setHours(0, 0, 0, 0);
    this.delDate = date;
  }

  printpdf() {
    this.purchases.printpdfs(this.grn, 'Grn').subscribe(data => {
      // window.open('data:application/pdf;base64,' + data.eFile);
      this.purchases.globalPrintPdf(data.eFile);
    });

  }
  exportToExcel() {
    this.purchases.exportToExcel(this.grn, 'Grn').subscribe(data => {
      window.open('data:text/csv;base64,' + data.eFile);
    });
  }

  getTotal(key: string) {
    return this.utils.getTotal(this.dataSource.data, key);
  }

  goBack() {
    this.router.navigate(['/home/<USER>'])
  }

  checkboxLabel(row?: any): string {
    if (!row) {
      return `${this.isAllSelected() ? 'select' : 'deselect'} all`;
    }
    return `${this.selection.isSelected(row) ? 'deselect' : 'select'} row ${row.itemCode + 1}`;
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  masterToggle() {
    this.isAllSelected() ?
      this.selection.clear() :
      this.dataSource.data.forEach(row => this.selection.select(row));
  }

  getTotalReturnRate(element) {
    element.returnQty > element.receivedQty ? element.returnQty = element.receivedQty : element.returnQty;
    let totalPrice = 0
    this.selection.selected.forEach(element => {
      totalPrice = totalPrice + element.returnQty * element.unitReturnRate
    });
    return totalPrice
  }

  generateRtv() {
    const rtvQty = this.grn.grnItems.every(item => item.returnQty === 0 || item.returnQty == null);
    if (rtvQty) {
      this.utils.snackBarShowInfo('Please Add Required Return Quantity');
      return;
    }
    let obj = {}
      obj['tenantId'] = this.user.tenantId
      obj['restaurantId'] = this.grn.restaurantId
      obj['tenantName'] = this.user.name
      obj['items'] = this.grn.grnItems
      obj['creditNo'] = this.creditNo.value
      obj['grnId'] = this.grn.grnId
      obj['vendorName'] = this.grn.vendorName
      obj['vendorId'] = this.grn.vendorId
      obj['invoiceId'] = this.grn.invoiceId
      obj['creditDate'] = this.creditDate.value
      obj['returnDate'] = this.returnDate.value
      obj['remarks'] = this.remarks.value
      obj['createdBy'] = this.user.role
      this.purchases.createRtv(obj).subscribe(data => {
        if (data.result == true) {
          this.utils.snackBarShowSuccess('RTV Created Successfully');
          this.router.navigate(['/home/<USER>'])
          this.disableButton = true;
        } else {
          this.utils.snackBarShowError(`${data['message']}`);
        }
      }, err => console.error(err));
  }
 
  validateAndDcrsRetQty(element) {
    if (element.returnQty > 0) {
      element.returnQty = element.returnQty - 1;
    }
  }

  validateAndIncrsRetQty(element) {
    if (element.returnQty < element.receivedQty) {
      element.returnQty = element.returnQty + 1;
    }
  }

  focusFunctionWithOutForm(element , value){
    if(Number(element[value]) === 0){
      element[value] = null;
    }
  }
  
  focusOutFunctionWithOutForm(element , value){
    if(element[value] === null){
      element[value] = 0
    }
  }

  validateReturnQty(element){
    if (element.returnQty < 0) {
      element.returnQty = 0;
    }
    element.returnQty = this.utils.truncateNew(element.returnQty)
    if (element.uom === 'NOS') {
      element.returnQty = Math.floor(element.returnQty);
    }
    if(element.returnQty > element.maxQty){
      element.returnQty = element.maxQty;
    }
    if (element.returnQty > 0) {
      const value = element.returnQty / element.receivedQty;
      element.taxAmount = this.utils.truncateNew(element.taxAmt * value);
      element.cessAmt = this.utils.truncateNew(element.cess * value);
      element.discAmt = this.utils.truncateNew(element.disc * value);
      element.extraAmt = this.utils.truncateNew(element.extra * value);
      element.totalPrice = this.utils.truncateNew(element.totalCost * value);
    }   
    element.unitPrice = element.packages[0].packagePrice
    return element
  }

  returnQtyMax(){
    return this.dataSource.data.some((element) => {
      const checkDecimal = (element.returnQty * 1000) % 1 !== 0; 
      return element.returnQty > element.maxQty || checkDecimal;
    });
  }

  detailedRtvList() {
    this.router.navigate(['/home/<USER>'])
  }

}
