<div class="title row">
  <div>
    <form [formGroup]="branchTransferForm">
      <mat-form-field appearance="none" class="topitem">
        <label>Select Branch</label>
        <mat-select placeholder="Select Branch" class="outline" tabindex="0" #branch (focus)="branch.open()" tabindex="1"
          formControlName="branchSelection" (selectionChange)="filterByBranch($event.value)"
          (keydown)="moveToDelivery($event)">
          <mat-option *ngFor="let rest of branches" [value]="rest">
            {{ rest.branchName }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </form>
  </div>
  <div>
    <mat-form-field *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" 
      appearance="none" style="margin-left: 10px" class="topitem">
      <label>Start Date</label>
      <input matInput class="outline" [matDatepicker]="picker1" placeholder="Start Date" [formControl]="startDate" />
      <mat-datepicker-toggle matSuffix [for]="picker1">
        <mat-icon matDatepickerToggleIcon>
          <img src="./../../assets/calender.png" />
        </mat-icon>
      </mat-datepicker-toggle>
      <mat-datepicker #picker1></mat-datepicker>
    </mat-form-field>
  </div>
  <div>
    <mat-form-field *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser"
      appearance="none" style="margin-left: 10px" class="topitem">
      <label>End Date</label>
      <input matInput class="outline" [matDatepicker]="picker2" [formControl]="endDate" placeholder="End Date"
        [readonly]="!startDate.value" [disabled]="!startDate.value" [min]="startDate.value" />
      <mat-datepicker-toggle matSuffix [for]="picker2">
        <mat-icon matDatepickerToggleIcon>
          <img src="./../../assets/calender.png" />
        </mat-icon>
      </mat-datepicker-toggle>
      <mat-datepicker #picker2></mat-datepicker>
    </mat-form-field>
  </div>
  <div>
    <button *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser"
      mat-stroked-button class="btn-block findButton button3" (click)="filterByDate()">
      Find
    </button>
  </div>
  <div class="ml-2">
    <button *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser"
      mat-stroked-button class="btn-block findButton button3" (click)="clear()">
      clear
    </button>
  </div>
</div>

<div *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" class="datacontainer">
  <mat-card>
    <mat-card-content>
      <div class="search-table-input">
        <mat-form-field appearance="none">
          <label>Search</label>
          <div class="search-container">
            <input matInput type="text" class="outline" (keyup)="doFilter($event.target.value)" 
            [(ngModel)]='searchText' placeholder="Search" />
            <mat-icon matSuffix class="closebtn" (click)="resetForm()">close</mat-icon>
          </div>
        </mat-form-field>
      </div>
      <section class="example-container-1 mat-elevation-z8">    
        <table #table mat-table [dataSource]="dataSource" matSortDirection="desc" matSort>

          <ng-container matColumnDef="index">
            <th mat-header-cell *matHeaderCellDef><b>S.No</b></th>
            <td mat-cell *matCellDef="let element; let i = index" style="padding-right: 30px;">
              {{ i + 1 }}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>

          <ng-container matColumnDef="id">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Id </b>
            </th>
            <td mat-cell *matCellDef="let element" class="links" (click)="detailedView(element)">
              {{ element.intraBranchId }}
            </td>
          </ng-container>

          <ng-container matColumnDef="createdDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Created Date </b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ this.utils.formatDateToUTC(element.date) }}

            </td>
          </ng-container>

          <ng-container matColumnDef="createdBy">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Created By </b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.creator || '-' }}
            </td>
          </ng-container>

          <ng-container matColumnDef="action">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Action</b>
            </th>
            <td mat-cell *matCellDef="let element">
                <button mat-icon-button class="delete-button" matTooltip="Delete" (click)="delete(element)" matTooltipPosition="left">
                  <mat-icon>delete_outline</mat-icon>
                </button>
            </td>
          </ng-container>
          
          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        </table>
        <div class="dataMessage" *ngIf="dataSource?.data.length == 0"> No Data Available </div>
        <mat-paginator [showTotalPages]="5" [pageSize]="10" [pageSizeOptions]="pageSizes"></mat-paginator>
      </section>
    </mat-card-content>
  </mat-card>
</div>
