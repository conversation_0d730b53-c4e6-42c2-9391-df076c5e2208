import { Component, OnInit, ViewChild } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { PurchasesService } from '../_services/purchases.service';
import { Router } from '@angular/router';
import { UtilsService } from '../_utils/utils.service';
import { SimpleDialogComponent } from '../_dialogs/simple-dialog/simple-dialog.component';
import { MatDialog, MatPaginator, MatSelect } from '@angular/material';
import { AuthService } from '../_services/auth.service';
import { ShareDataService } from '../_services/share-data.service';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-intra-branch-list',
  templateUrl: './intra-branch-list.component.html',
  styleUrls: ['./intra-branch-list.component.scss', "./../../common-dark.scss"]
})
export class IntraBranchListComponent implements OnInit {
  public dataSource: MatTableDataSource<any>;
  private unsubscribe$ = new Subject<void>();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild('deliveryTo') deliveryTo: MatSelect;
  @ViewChild('branch') branch: MatSelect;
  pageSizes = []
  actualData: any[] = [];
  displayedColumns: string[];
  user: any;
  restaurantId: any;
  searchText: string;
  public searchValue: any = ''
  branchTransferForm: FormGroup;
  startDate = new FormControl();
  endDate = new FormControl();
  getBranchData: any[];
  branches: any[];
  multiBranchUser; branchSelected: boolean;

  constructor(private purchases: PurchasesService, 
    private sharedData: ShareDataService,
    private router: Router, 
    public utils: UtilsService,
    public dialog: MatDialog,
    private auth: AuthService,
    private fb: FormBuilder,
  ) {
    this.user = this.auth.getCurrentUser();
    this.multiBranchUser = this.user.multiBranchUser; 
    this.branchTransferForm = this.fb.group({
      branchSelection: [null, Validators.required]
    });
    
    this.sharedData.sharedBranchData.pipe(takeUntil(this.unsubscribe$)).subscribe((val) => {
      this.getBranchData = val;
      if (this.getBranchData.length == 0) {
        this.branches = this.user.restaurantAccess;
      } else if (this.getBranchData.length == 1) {
        const toSelect = this.getBranchData.find(data => data.branchName == this.getBranchData[0].branchName);
        this.branchTransferForm.get('branchSelection').setValue(toSelect);
        this.branches = this.getBranchData
        this.filterByBranch(this.branchTransferForm.value.branchSelection);
      } else {
        this.branches = this.getBranchData
      }
    });
   }

  ngOnInit() {
    if (!this.dataSource)
      this.dataSource = new MatTableDataSource<any>();
    this.displayedColumns = ['index','id','createdDate','createdBy','action']; 
    this.getData();  
  }

  filterByBranch(restId) {
    this.dataSource = new MatTableDataSource();
    this.branchSelected = true
    this.startDate.setValue(null);
    this.endDate.setValue(null);
    this.restaurantId = restId.restaurantIdOld;
    let reqObj: any = {
      tenantId: this.user.tenantId,
      restaurantId: this.restaurantId
    }
    this.getDataList(reqObj)
  }

  filterByDate() {
    if (this.startDate.value && this.endDate.value) {
      let reqObj: any = {
        tenantId: this.user.tenantId,
        restaurantId: this.restaurantId
      }
      this.getDataList(reqObj);
    } else {
      this.utils.snackBarShowWarning('Please select start date and end date');
    }
  }

  clear() {
    this.startDate.setValue(null);
    this.endDate.setValue(null);
    let reqObj: any = {
      tenantId: this.user.tenantId,
      restaurantId: this.restaurantId
    }
    this.getDataList(reqObj);
  }

  moveToDelivery(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
      if (this.branch) {
        this.branch.close();
      }
      this.deliveryTo.focus()
    }
  }

  getData(){
    if (this.branchSelected) {
    } else {
      if (!this.user.multiBranchUser) {
        this.restaurantId = this.user.restaurantAccess[0].restaurantIdOld;
        this.branchSelected = true;
        let reqObj: any = {
          tenantId: this.user.tenantId,
          restaurantId: this.restaurantId
        }
        this.getDataList(reqObj)
      }
    }
  }

  getDataList(reqObj) { 
    this.branchSelected = true
    if (this.startDate.value && this.endDate.value) {
      reqObj['startDate'] = this.utils.dateCorrection(this.startDate.value);
      reqObj['endDate'] = this.utils.dateCorrection(this.endDate.value);
    }   
    this.purchases.getIntraBranchList(reqObj).subscribe(res => {   
    const data = res.data; 
    this.dataSource.data = data; 
    this.pageSizes = this.utils.getPageSizes(this.dataSource.data);
    this.dataSource.paginator = this.paginator;
    }, err => { })
  }

  delete(obj) {      
    let reqObj = {}
      reqObj['id'] = obj['intraBranchId'];
      reqObj['restaurantId'] = obj['restaurantId'];
      reqObj['tenantId'] = obj['tenantId'];
      reqObj['user'] = this.user.mId;

    this.dialog.open(SimpleDialogComponent, {
      data: {
        title: 'Delete Workarea Transfer',
        msg: 'Are you sure you want to delete?',
        ok: function () {
          this.purchases.deleteIntraBranch(reqObj).subscribe(res => {
            if (res['result'] === true) {
              this.utils.snackBarShowSuccess('Deleted successfully!');
            } else {
              this.utils.snackBarShowError(`${res['message']}`);
            }
            let reqObj: any = {
              tenantId: this.user.tenantId,
              restaurantId: this.restaurantId
            }
            this.getDataList(reqObj);
          })
        }.bind(this)
      }
    });
  }

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }

  resetForm() {
    this.searchText = ''
    this.searchValue = ''
    this.doFilter(this.searchValue)
  }

  detailedView(obj){
    this.sharedData.changeIntraBranch(obj)
    this.router.navigate(['/home/<USER>'])
  }

}
