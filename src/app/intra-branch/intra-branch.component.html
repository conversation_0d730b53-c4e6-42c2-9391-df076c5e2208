<mat-card>
  <div class="infoMessage ">
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
      class="bi bi-info-circle-fill infoSvgIcon" viewBox="0 0 16 16">
      <path
        d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm.93-9.412-1 4.705c-.07.34.029.533.304.533.194 0 .487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703 0-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381 2.29-.287zM8 5.5a1 1 0 1 1 0-2 1 1 0 0 1 0 2z" />
    </svg>
    <p class="ml-2 mb-0"> Hey, the list contains items that are mapped to both the source and destination workraea</p>
  </div>

  <div class="row marginClass">
    <div>
      <form [formGroup]="intraBranchForm">
        <mat-form-field *ngIf="multiBranchUser" appearance="none">
          <label class="title-palce">Select Branch</label>
          <mat-select placeholder="Select Branch" class="outline" formControlName="branchSelection"
            (selectionChange)="filterByBranch($event.value)">
            <mat-option *ngFor="let rest of branches" [value]="rest">
              {{ rest.branchName }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </form>
    </div>

    <div>
      <mat-form-field appearance="none" style="margin-left: 10px;"
        *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser">
        <label>Source Work Area</label>
        <mat-select placeholder="Select Work Area" (selectionChange)="selectSourceIndentArea($event)" class="outline">
          <mat-option>
            <ngx-mat-select-search placeholderLabel="Select Source Work Area..."
              noEntriesFoundLabel="'no Source Work Area found'"
              [formControl]="sourceBankFilterCtrl"></ngx-mat-select-search>
          </mat-option>
          <mat-option *ngFor="let area of sourceFilteredWorkArea | async" [value]="area"
            [disabled]="area == destinationIndentArea">
            {{ area }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>

    <div>
      <mat-form-field appearance="none" style="margin-left: 10px;"
        *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser">
        <label>Destination Work Area</label>
        <mat-select placeholder="Select Work Area" (selectionChange)="selectDestinationIndentArea($event)"
          class="outline">
          <mat-option>
            <ngx-mat-select-search placeholderLabel="Select Destination Work Area..."
              noEntriesFoundLabel="'no Destination Work Area found'"
              [formControl]="destinationBankFilterCtrl"></ngx-mat-select-search>
          </mat-option>
          <mat-option *ngFor="let area of destinationFilteredWorkArea | async" [value]="area"
            [disabled]="area == sourceIndentArea">
            {{ area }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <div>
    <mat-form-field appearance="none" style="margin-left: 10px;"
      *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser">
      <label>Transfer Date</label>
        <input matInput class="outline" [matDatepicker]="picker1" [(ngModel)]="selectedDate" placeholder="Transfer Date"
         [formControl]="transferDate" [matDatepickerFilter]="dateFilter"/>
        <mat-datepicker-toggle matSuffix [for]="picker1">
          <mat-icon matDatepickerToggleIcon>
            <img src="./../../assets/calender.png" />
          </mat-icon>
        </mat-datepicker-toggle>
        <mat-datepicker #picker1></mat-datepicker>
    </mat-form-field>
    </div>
    
    <div>
      <button mat-stroked-button class="btn-block findButton button3"
        *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" (click)="getBranchInv()">
        Find
      </button>
    </div>

    <div class="grep" *ngIf="isShow">
      <mat-slide-toggle class="mr-2" *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser"
        [(ngModel)]="transferWorkAreaPreview" (change)="preview()">Preview
      </mat-slide-toggle>

      <button mat-raised-button *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" class="button"
        (click)="transferCheck()" [disabled]="allowTransfer">
        Transfer
      </button>
    </div>

  </div>
</mat-card>

<div *ngIf="((branchSelected && multiBranchUser) || !multiBranchUser) && isShow" class="datacontainer">
  <mat-card>
    <mat-card-content>
      <div class="search-table-input">
        <mat-form-field appearance="none">
          <label>Search</label>
          <input matInput type="text" (keyup)="doFilter($event.target.value)" placeholder="Search" autocomplete="off"
            class="outline" [(ngModel)]='searchText' />
          <mat-icon matSuffix (click)="clearFilter()" class="closebtn">close</mat-icon>
        </mat-form-field>

        <mat-form-field id="branch-select" appearance="none">
          <label>Item Type</label>
          <mat-select placeholder="Item Type" [formControl]="ItemType" class="outline">
            <mat-option *ngFor="let itemType of ItemTypeList" [value]="itemType" (click)="selectItemType(itemType)">
              {{ itemType | titlecase }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="none">
          <label>Category</label>
          <mat-select placeholder="Category" [formControl]="Category" class="outline">
            <mat-option *ngFor="let cat of categoryList" [value]="cat" (click)="selectCategory(cat)">
              {{ cat | titlecase }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="none">
          <label>Sub Category</label>
          <mat-select placeholder="Subcategory" [formControl]="Subcategory" class="outline">
            <mat-option *ngFor="let subCat of subCategoryList" [value]="subCat" (click)="selectSubCat(subCat)">
              {{ subCat | titlecase }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="none" style="padding-top: 2px;">
          <label> Remarks </label>
          <textarea matInput style="font-size: 15px; height: 41px;" 
            [(ngModel)]="remarks" class="outline" maxlength="100" required #descriptionTextarea></textarea>
        </mat-form-field>

        <!-- <button mat-stroked-button class="clrButton button3" (click)="clearFilter()">
          Clear
        </button> -->
      </div>
      <div class="table-responsive">
        <table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort>
          <ng-container matColumnDef="itemName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Item Name</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.itemName | titlecase }}
            </td>
          </ng-container>
          <ng-container matColumnDef="index">
            <th mat-header-cell *matHeaderCellDef mat-sort-header class="tableId"><b>#</b></th>
            <td mat-cell *matCellDef="let element; let i = index" class="tableId">
              {{ i + 1 + paginator.pageIndex * paginator.pageSize }}
            </td>
          </ng-container>

          <ng-container matColumnDef="pkgName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Pkg Name</b>
            </th>
            <td mat-cell *matCellDef="let element">
              <ng-container *ngIf='element.packageName != null'>
                {{element.packageName | titlecase}}
              </ng-container>
              <ng-container *ngIf='element.packageName == null'>
                {{ element.uom | titlecase}}
              </ng-container>
            </td>
          </ng-container>

          <ng-container matColumnDef="entryType">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Entry Type</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.entryType | titlecase }}
            </td>
          </ng-container>

          <ng-container matColumnDef="inStore">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> In Store</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ this.utils.truncateNew(element.inStock ) }}
            </td>
          </ng-container>
          <ng-container matColumnDef="inKitchen">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> In Kitchen</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ this.utils.truncateNew(element.inKitchen) }}
            </td>
          </ng-container>
          <ng-container matColumnDef="projectedSales">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Projected Sales</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ this.utils.truncateNew(element.projectedSales) }}
            </td>
          </ng-container>
          <ng-container matColumnDef="uom">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Units</b>
            </th>
            <td mat-cell *matCellDef="let element">{{ element.uom }}</td>
          </ng-container>

          <ng-container matColumnDef="transferQty">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Transfer Qty(pkgName)</b>
            </th>
            <td mat-cell *matCellDef="let element">
              <input class="input1" type="number" (keydown)="checkNumericInput($event,element)"
                [step]="element.packageName.toUpperCase() === 'NOS' ? '1' : '0.01'" min="0"
                [(ngModel)]="element.transferQty" (focus)="focusFunctionWithOutForm(element)" (focusout)="focusOutFunctionWithOutForm(element)"
                (keyup)="getTransferQty(element)"
                />
                <!-- onkeypress="(event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)"  -->
            </td>
          </ng-container>

          <ng-container matColumnDef="transferQtyPlates">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Transfer Qty(plates)</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ this.utils.truncateNew(getPlatesVal(element)) }}
            </td>
          </ng-container>
          <ng-container *ngFor="let area of indentAreas" matColumnDef="{{ area }}">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> {{ area }}</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ this.utils.truncateNew(getIndentAreaVal(element.workArea, area)) }}
            </td>
          </ng-container>
          <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky : true"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        </table>
        <div class="dataMessage" *ngIf="dataSource?.data.length == 0"> No Data Available </div>
        <!-- </section> -->
      </div>
    </mat-card-content>
  </mat-card>
  <mat-paginator [showTotalPages]="5" [pageSize]="10" [pageSizeOptions]="pageSizes"></mat-paginator>
</div>