<div class="title title2 " *ngIf="!stockConversionValue">
    <form [formGroup]="inventoryListForm" class="topHeadInputs">
      <mat-form-field style="margin-top: -15px;" *ngIf="multiBranchUser" appearance="none">
        <mat-select placeholder="Select Branch" class="outline" formControlName="branchSelection" (selectionChange)="filterByBranch($event.value)">
          <mat-option *ngFor="let rest of branches" [value]="rest">
            {{ rest.branchName }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </form>

    <mat-form-field appearance="none" *ngIf="branchselect && !isStore" style="margin-left: 10px; margin-top: -15px;">
      <mat-select placeholder="Select Work Area" [(ngModel)]="selectedWorkArea" (selectionChange)="selectIndentArea($event.value)" class="outline">
        <mat-option>
          <ngx-mat-select-search placeholderLabel="Select Work Area..." noEntriesFoundLabel="'no Work Area found'"
            [formControl]="bankFilterCtrl"></ngx-mat-select-search>
        </mat-option>
        <mat-option *ngFor="let area of filteredWorkArea | async" [value]="area">
          {{ area }}
        </mat-option>
      </mat-select>
    </mat-form-field>

    <mat-form-field id="branch-select" appearance="none" style="margin-left: 10px; margin-top: -15px;">
      <!-- <label>Item Type</label>  [(ngModel)]="selectedItemType"-->
      <mat-select placeholder="Item Type" [formControl]="ItemType" class="outline" multiple>
        <mat-option #allSelectedItemType (click)="toggleAllSelectionItemType()" [value]="1">All</mat-option>
        <mat-option *ngFor="let itemType of itemType" [value]="itemType" >
          {{ itemType | titlecase }}
        </mat-option>
      </mat-select>
    </mat-form-field>

    <button mat-raised-button class="button grep" (click)="getBranchInv()" >
      Find
    </button>
 
  <!-- <button mat-raised-button class="button grep" 
  *ngIf="(((branchSelected && multiBranchUser) || !multiBranchUser) && showTable) && isShowContent"
    (click)="exportToExcel()" style="float: right;">
    Export
  </button>
  <button mat-raised-button (click)="printpdf()" 
  *ngIf="(((branchSelected && multiBranchUser) || !multiBranchUser) && showTable) && isShowContent"
   class="button grep" style="float: right;">
    Print
  </button> -->

  <!-- <mat-slide-toggle
  *ngIf="(((branchSelected && multiBranchUser) || !multiBranchUser ) && showTable) && isShowContent"
  style="float: right;"
  [(ngModel)]="stockConversion"
  (change)="getStockConvertibleItems()"
  >Convertible Only 
</mat-slide-toggle> -->
  
  <!-- <mat-slide-toggle
  *ngIf="(((branchSelected && multiBranchUser) || !multiBranchUser) && isStore) && showTable"
    style="float: right;" [(ngModel)]="showDetails" (change)="changed()">
    WorkArea
  </mat-slide-toggle> -->

  <mat-slide-toggle
    *ngIf="(((branchSelected && multiBranchUser) || !multiBranchUser ) && showTable) && isShowContent"
    style="float: right;"
    [(ngModel)]="showInStore"
    (change)="inStoreValue($event)"
    >Available Stock 
  </mat-slide-toggle>

  <mat-slide-toggle
    *ngIf="(((branchSelected && multiBranchUser) || !multiBranchUser ) && showTable) && isShowContent"
    style="float: right;"
    [(ngModel)]="showParLevel"
    (change)="toggleParLevel($event)"
    >Below Par-Level
  </mat-slide-toggle>

</div>

<div *ngIf="((branchSelected && multiBranchUser) || !multiBranchUser) && showTable" class="datacontainer">
  <mat-card>
    <mat-card-content>
      <div class="search-table-input" *ngIf="!stockConversionValue">
        <mat-form-field appearance="none">
          <label>Search</label>
          <input matInput type="text" (keyup)="doFilter($event.target.value)" placeholder="Search" autocomplete="off"
            class="outline" [(ngModel)]="search"/>
            <mat-icon matSuffix (click)="clearFilter()" class="closebtn">close</mat-icon>
        </mat-form-field>

        <mat-form-field appearance="none">
          <label>Categories</label>
          <mat-select placeholder="Categories" [formControl]="Category"
            (selectionChange)="selectCategory($event.value)" class="outline" [multiple]="true" #multiSelect>
            <mat-option>
              <ngx-mat-select-search placeholderLabel="Select Category..." noEntriesFoundLabel="'No Category Found'"
                [formControl]="categoryFilterCtrl"></ngx-mat-select-search>
            </mat-option>
            <mat-option (click)="toggleAllCategory()" class="hide-checkbox">
              Select All / Deselect All 
            </mat-option>
            <mat-option *ngFor="let cat of categoryMulti | async" [value]="cat">
              {{ cat }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="none">
          <label>Sub Category</label>
          <mat-select placeholder="Subcategory" [formControl]="Subcategory"
            (selectionChange)="selectSubCat($event.value)" class="outline" [multiple]="true" #multiSelect>
            <mat-option>
              <ngx-mat-select-search placeholderLabel="Select SubCategory..." noEntriesFoundLabel="'No SubCategory Found'"
                [formControl]="subCategoryFilterCtrl"></ngx-mat-select-search>
            </mat-option>
            <mat-option (click)="toggleAllSubCategory()" class="hide-checkbox">
              Select All / Deselect All
            </mat-option>
            <mat-option *ngFor="let subCat of subCategoryMulti | async" [value]="subCat">
              {{ subCat }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="none" *ngIf="isStore">
          <label>Select Work Area</label>
          <mat-select placeholder="Select Work Area" [formControl]="workAreaForm" (selectionChange)="selectIndentArea($event.value)" class="outline" multiple>
            <mat-option>
              <ngx-mat-select-search placeholderLabel="Select Work Area..." noEntriesFoundLabel="'no Work Area found'"
                [formControl]="multiBankFilterCtrl"></ngx-mat-select-search>
            </mat-option>
            <mat-option (click)="toggleAllWorkArea()" class="hide-checkbox">
              Select All / Deselect All
            </mat-option>
            <mat-option *ngFor="let area of multiFilteredWorkArea | async" [value]="area">
              {{ area }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <button mat-button class="buttonForRefresh refreshBtn" (click)="refreshdata()">Refresh</button>
        
      </div>
        <div class="table-responsive" style="padding-top: 15px;">
        <table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort>

          <ng-container matColumnDef="index">
            <th mat-header-cell *matHeaderCellDef mat-sort-header class="tableId"><b>#</b></th>
            <td mat-cell *matCellDef="let element; let i = index" class="tableId">
              {{ i + 1 + paginator.pageIndex * paginator.pageSize }}
            </td>
            <!-- <td mat-footer-cell *matFooterCellDef></td> -->
          </ng-container>

          <ng-container matColumnDef="category">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Category</b>
            </th>
            <td mat-cell *matCellDef="let element">
              <ng-container>
                {{element.category | titlecase}}
              </ng-container>
            </td>
            <!-- <td mat-footer-cell *matFooterCellDef></td> -->
          </ng-container>

          <ng-container matColumnDef="subCategory">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Sub Category</b>
            </th>
            <td mat-cell *matCellDef="let element">
              <ng-container>
                {{element.subCategory | titlecase}}
              </ng-container>
            </td>
            <!-- <td mat-footer-cell *matFooterCellDef></td> -->
          </ng-container>

          <ng-container matColumnDef="itemName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Item Name</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.itemName | titlecase }}
            </td>
            <!-- <td mat-footer-cell *matFooterCellDef> Total </td> -->
          </ng-container>

          <ng-container matColumnDef="itemCode">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Item Code</b>
            </th>
            <td mat-cell *matCellDef="let element">
              <ng-container>
                {{element.itemCode}}
              </ng-container>
            </td>
            <!-- <td mat-footer-cell *matFooterCellDef></td> -->
          </ng-container>
   

          <ng-container matColumnDef="pkgName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Pkg Name</b>
            </th>
            <td mat-cell *matCellDef="let element">
              <ng-container *ngIf='element.packageName != null'>
                {{element.packageName | titlecase}}
              </ng-container>
              <ng-container *ngIf='element.packageName == null'>
                {{ element.uom | titlecase}}
              </ng-container>
            </td>
            <!-- <td mat-footer-cell *matFooterCellDef></td> -->
          </ng-container>

          <ng-container matColumnDef="entryType">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Entry Type</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.entryType | titlecase }}
            </td>
            <!-- <td mat-footer-cell *matFooterCellDef></td> -->
          </ng-container>

          <!-- <ng-container matColumnDef="uom">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> UOM</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.uom }}
            </td>
          </ng-container> -->

          <ng-container matColumnDef="inStore">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> In Store</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ this.utils.truncateNew(element.inStock ) }}
              <span *ngIf="portionValueQty(element) !== ''">
                ({{ portionValueQty(element) }} portions)
              </span>
            </td>
            <!-- <td mat-footer-cell *matFooterCellDef></td> -->
          </ng-container>

          <ng-container matColumnDef="parLevel">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Par-Level </b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ this.utils.truncateNew(element.parLevel ) }}
            </td>
            <!-- <td mat-footer-cell *matFooterCellDef></td> -->
          </ng-container>

<!-- 
          <ng-container matColumnDef="price">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Unit Price</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ this.utils.truncateNew(element.price ) }}
            </td>
          </ng-container>

          <ng-container matColumnDef="taxRate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Tax(%)</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ this.utils.truncateNew(element.taxRate ) }}
            </td>
          </ng-container>

          <ng-container matColumnDef="withTaxPrice">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Unit Cost(incl.tax)</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ this.utils.truncateNew(element.withTaxPrice ) }}
            </td>
          </ng-container> -->


          <ng-container matColumnDef="withTaxPrice">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>WAC(incl.tax,etc)</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ this.utils.truncateNew(element.withTaxPrice,2) }}
            </td>
            <!-- <td mat-footer-cell *matFooterCellDef></td> -->
          </ng-container>

          <ng-container matColumnDef="totalPrice">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Total Price</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ this.utils.truncateNew(totalValue(element),2) }}
            </td>
            <!-- <td mat-footer-cell *matFooterCellDef>{{getTotalPrice()}}</td> -->
          </ng-container>
          
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Status</b>
            </th>
            <td mat-cell *matCellDef="let element">
              <div *ngIf="element.status == 'active'">
                {{element.status | titlecase }}<mat-icon class="check_circle">check_circle</mat-icon>
              </div>
              <div *ngIf="element.status != 'active'">
                {{element.status | titlecase }}<mat-icon class="check_circle_error">error</mat-icon>
              </div>
            </td>
            <!-- <td mat-footer-cell *matFooterCellDef></td> -->
          </ng-container>

          <ng-container matColumnDef="stockConversion">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Stock Conversion</b>
            </th>
            <td mat-cell *matCellDef="let element">
              <ng-container *ngIf=" element.stockConversion ; else elseBlock">
                <div style="display: flex; cursor: pointer;">
                  Convert <mat-icon [disabled]="element.inStock == 0" class="ml-1" matTooltip="click to Stock Conversion" (click)="showScList(element)"> play_circle_outline</mat-icon>
                </div>
              </ng-container>
              <ng-template #elseBlock>
                {{'-'}}
              </ng-template>
            </td>
            <!-- <td mat-footer-cell *matFooterCellDef></td> -->
          </ng-container>

            <ng-container matColumnDef="history">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>History</b>
            </th>
            <td mat-cell *matCellDef="let element">
              <ng-container *ngIf=" element.stockConversion ; else elseBlock">
                <div style="display: flex;">
                  <mat-icon class="ml-1" matTooltip="click to view history conversion" (click)="history(element)"> history </mat-icon>
                </div>
              </ng-container>
              <ng-template #elseBlock>
                {{'-'}}
              </ng-template>
            </td>
            <!-- <td mat-footer-cell *matFooterCellDef></td> -->
          </ng-container>
          
          <ng-container matColumnDef="inKitchen">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> In Kitchen</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ this.utils.truncateNew(element.inKitchen) }}
            </td>
            <!-- <td mat-footer-cell *matFooterCellDef></td> -->
          </ng-container>
          <ng-container matColumnDef="projectedSales">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Projected Sales</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ this.utils.truncateNew(element.projectedSales) }}
            </td>
            <!-- <td mat-footer-cell *matFooterCellDef></td> -->
          </ng-container>

          <ng-container matColumnDef="uom">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> UOM</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.uom }}
              <span *ngIf="element.hasOwnProperty('portionWeight')">
                (portion)
              </span>
            </td>
          </ng-container>

          <ng-container *ngFor="let area of indentAreas" matColumnDef="{{ area }}">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> {{ area }}</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ this.utils.truncateNew(getIndentAreaVal(element.workArea, area)) }}
            </td>
            <!-- <td mat-footer-cell *matFooterCellDef></td> -->
          </ng-container>
          <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky : true"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
          <!-- <tr mat-footer-row *matFooterRowDef="displayedColumns" [class.hidden]="!showFooter" ></tr> -->
        </table>
        <div class="dataMessage" *ngIf="dataSource.data.length == 0">
          No Data Found
        </div>
      </div>
    </mat-card-content>
  <!-- <div class="dataMessage" *ngIf="dataSource?.data?.length == 0"> No Data Available </div> -->
  </mat-card>
  <mat-paginator [showTotalPages]="5" [pageSize]="10" [pageSizeOptions]="pageSizes"></mat-paginator>
</div>