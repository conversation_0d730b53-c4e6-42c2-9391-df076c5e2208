::ng-deep .mat-slide-toggle-label {
   margin-right: 15px !important;
}

.clrButton{
   margin-top: 38px;
   margin-bottom: 11px;
  }

  .inputHeightCorrection{
   height: 30px !important;
  }

  .CloseBtn{
   float: right;
   margin-bottom: -1px;
 }

 .dialongContentClass{
   display: table-cell;
   width: 94vh;
   /* margin: -21px; */
   padding: 11px;
   height: 60vh;
 }

 .check_circle{
  color:green;
  font-size: 18px;
  position: absolute;
  margin-left: 5px;
}

.check_circle_error{
  color: #A52A2A;
  font-size: 19px;
  position: absolute;
  margin-left: 5px;
}

.hidden {
  display: none;
}

::ng-deep .hide-checkbox .mat-pseudo-checkbox {
  display: none !important;
}