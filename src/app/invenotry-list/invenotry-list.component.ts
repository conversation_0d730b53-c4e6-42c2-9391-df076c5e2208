import { Component, Input, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { AuthService, PurchasesService, BranchTransferService, ShareDataService } from '../_services';
import { GlobalsService } from '../_services/globals.service';
import { UtilsService } from '../_utils/utils.service';
import { MatDialog, MatOption, MatSlideToggleChange } from '@angular/material';
import { MatTableDataSource, MatPaginator, MatSort } from '@angular/material';
import { ActivatedRoute, Router } from '@angular/router';
import { FormControl, FormGroup, FormBuilder, Validators } from '@angular/forms';
import { ReplaySubject, Subject } from 'rxjs';
import {takeUntil } from 'rxjs/operators';
import { SharedFilterService } from '../_services/shared-filter.service';

@Component({
  selector: 'app-invenotry-list',
  templateUrl: './invenotry-list.component.html',
  styleUrls: ['./invenotry-list.component.scss', './../../common-dark.scss']
})
export class InventoryListComponent implements OnInit {
  all = "ALL";
  storeUrl = encodeURI(GlobalsService.getInventory)
  kitchenStockUrl = encodeURI(GlobalsService.kitchenStock)
  selectedWorkAreas: any;
  filterInStore: any[];
  filterParLevel: any[];
  showInStore: boolean = false;
  showParLevel: boolean = false;
  searchValue: any = ''
  ItemTypeList = ['All'];
  categoryList: any[] = [];
  subCategoryList: any[] = [];
  workAreasColoumn = [];
  enableSelectWorkArea: boolean = false
  showTable: boolean = false
  subCatList = [];
  title: String = 'Inventory List';
  showDetails: boolean = false;
  showTableDetails: boolean = false;
  indentAreas: string[] = [];
  user: any;
  inventoryItems: any[];
  displayedColumns: any[];
  scDisplayedColumns: any[];
  dataSource : MatTableDataSource<any>;
  scDataSource: MatTableDataSource<any>;
  categories: any[];
  category = new FormControl('', [Validators.required]);
  pageSizes = []
  ItemType = new FormControl();
  Category = new FormControl();
  Subcategory = new FormControl();
  restaurantId: any;
  searchText: any;
  filteredWorkAreasList: any;
  multiBranchUser; branchSelected: boolean;
  branchselect: boolean = false;
  isStore: boolean = false;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  search: string;
  indentArea: any;
  inventoryListForm: FormGroup;
  branches: any[];
  getBranchData: any[]
  selectedCategory: any;
  selectedItemType: any;
  selectedSubCategory: any;
  filteredData: any = [];
  stockConversion: boolean = false;
  public Bank: any[] = [];
  public VendorBank: any[] = [];
  public bankFilterCtrl: FormControl = new FormControl();
  public filteredWorkArea: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public multiBankFilterCtrl: FormControl = new FormControl();
  public multiFilteredWorkArea: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  protected _onDestroy = new Subject<void>();
  selectedWorkArea: any;
  private unsubscribe$ = new Subject<void>();
  @ViewChild('openScDialog') openScDialog: TemplateRef<any>;
  totalAmount: any;
  itemCategory: any;
  itemType : any;
  selectedCategories: any;
  sharedFilterData: any =  {};
  @ViewChild('allSelected') private allSelected: MatOption;
  @ViewChild('allSelectedItemType') private allSelectedItemType: MatOption;
  workAreaForm = new FormControl();
  isShowContent : boolean = false;
  filteryByCategory: any[];
  filteredByCategory: any[];
  StockConversionUrl = encodeURI(GlobalsService.StockConversion)
  @Input() stockConversionValue!: any;
  showFooter: boolean = false;
  queryobj: any;
  selectAllCategory: boolean = true;
  selectAllSubCategory: boolean = true;
  selectAllWorkArea: boolean = true;
  public catBank: any[] = [];
  public categoryMulti: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public categoryFilterCtrl: FormControl = new FormControl();

  public subCatBank: any[] = [];
  public subCategoryMulti: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public subCategoryFilterCtrl: FormControl = new FormControl();

  constructor(private auth: AuthService, private purchases: PurchasesService,
    private branchTransfer: BranchTransferService,
    private dialog: MatDialog, private utils: UtilsService,
    public router: Router, private sharedData: ShareDataService, private fb: FormBuilder,
    private sharedFilterService: SharedFilterService,
    private route: ActivatedRoute
    ) {
    this.user = this.auth.getCurrentUser()
    this.inventoryListForm = this.fb.group({
      branchSelection: [null, Validators.required]
    });

    var windowLocation = window.location.href;
    let windowCurrentUrl = windowLocation.split('/')[4]

    this.sharedFilterService.getFilteredInventory.pipe(takeUntil(this.unsubscribe$)).subscribe(obj => {
      this.sharedFilterData = obj
    }
    );   

      this.sharedData.sharedBranchData.pipe(takeUntil(this.unsubscribe$)).subscribe((val) => {
        this.getBranchData = val;
        if (this.getBranchData.length == 0) {
          this.branches = this.user.restaurantAccess;
        } else if (this.getBranchData.length == 1) {
          const toSelect = this.getBranchData.find(data => data.branchName == this.getBranchData[0].branchName);          
          if(toSelect != this.sharedFilterData.restaurantId){
            this.sharedFilterData = '';
          }
          this.inventoryListForm.get('branchSelection').setValue(toSelect);
          this.branches = this.getBranchData ; 
          this.filterByBranch(this.inventoryListForm.value.branchSelection);
        } else {
          if(this.sharedFilterData.branchFlag == true){
            this.filterByBranch(this.sharedFilterData.restaurantId);
            this.sharedFilterData.branchFlag = false;
          }
          this.branches = this.getBranchData
        }
      });
  }
   
  ngOnInit() {
    this.dataSource = new MatTableDataSource();
    this.multiBranchUser = this.user.multiBranchUser
    if (this.router.url.includes(this.storeUrl)) {
      this.isStore = true
    }
    this.dataSource = new MatTableDataSource()
    if (this.user.role.includes(GlobalsService.kitchenManager)) {
      this.title = 'Kitchen Stock'
    }
    this.displayedColumns = ['index','category','subCategory', 'itemName', 'itemCode', 'entryType', 'pkgName', 'uom', 'inStore','parLevel','withTaxPrice' ,'totalPrice','status'];
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
  }

  getBranchInv() {
    let ItemType = this.ItemType.value.filter(item => item !== 1);
    let obj = {}
    if(this.stockConversionValue){
      obj['tenantId'] = this.user.tenantId,
      obj['restaurantId'] = this.restaurantId,
      obj['stockConversion'] = this.stockConversionValue,
      obj['ItemType'] = ["Inventory"],
      obj['workArea'] = this.selectedWorkAreas
    }else{
      obj['tenantId'] = this.user.tenantId,
      obj['restaurantId'] = this.restaurantId,
      obj['ItemType'] = ItemType,
      obj['workArea'] = this.selectedWorkAreas
    }
   
    this.branchTransfer.getPkgStockVal(obj).subscribe(data => {
      if (data) {
        this.inventoryItems = data.invItems;
      }
      this.user.restaurantAccess.forEach(element => {
        if (element.restaurantIdOld == this.restaurantId) {
          this.indentAreas = element.workAreas
          if (element.workAreas == undefined) {
            this.indentAreas = data.workAreas;
          }
        }
      });
      if(this.sharedFilterData.restaurantId != this.inventoryListForm.value.branchSelection){
        this.indentAreas = [...new Set(this.indentAreas)];
        this.filteredWorkAreasList = this.indentAreas
        this.Bank = this.filteredWorkAreasList;
        this.multiFilteredWorkArea.next(this.Bank.slice());
        this.multiBankFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
          this.multiFilterBanks();
      });
      }

      this.workAreasColoumn = ['index', 'itemName', 'entryType', 'pkgName', 'inStore'].concat(this.indentAreas);
      this.route.queryParamMap.subscribe((params) => {
        this.queryobj = { ...params.keys, ...params };
        if (this.queryobj.params.type == 'alert') {
          this.showParLevel = true;
          this.selectSubCat(this.Subcategory.value || []);
        }
      })
      this.categories = []
      this.inventoryItems.forEach(item => {
        this.categories.push(item.invCategory)
        if (item.category == null) {
          item.category = 'N/A'
        }
        if (item.ItemType == null) {
          item.ItemType = 'N/A'
        }
        if (item.subCategory == null) {
          item.subCategory = 'N/A'
        }
        this.ItemTypeList.push(item.ItemType)
        this.categoryList.push(item.category)
        this.subCategoryList.push(item.subCategory)
        if (!item.uom)
          item.uom = "units"
      })
      this.categoryList = this.categoryList.filter((k, i, ar) => ar.indexOf(k) === i);
      this.ItemTypeList = this.ItemTypeList.filter((p, q, arrr) => arrr.indexOf(p) === q);
      this.subCategoryList = this.subCategoryList.filter((j, l, arr) => arr.indexOf(j) === l);      
      this.catBank = this.categoryList;
      this.Category.setValue(this.catBank.slice());
      this.categoryMulti.next(this.categoryList.slice());
      this.categoryFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
        this.categoryFilter();
      });
      this.subCatBank = this.subCategoryList;
      this.Subcategory.setValue(this.subCatBank.slice());
      this.selectSubCat(this.Subcategory.value)
      this.subCategoryMulti.next(this.subCategoryList.slice());
      this.subCategoryFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
        this.subCategoryFilter();
      });
      this.pageSizes = this.utils.getPageSizes(this.inventoryItems)
      this.dataSource.paginator = this.paginator;

      if(this.sharedFilterData.category ){
          this.selectCategory(this.sharedFilterData.category);
        }
        if(this.sharedFilterData.workArea != undefined){
          this.selectIndentArea(this.sharedFilterData.workArea);
        }
        if(this.sharedFilterData.convert == true || this.stockConversionValue === true){
          this.stockConversion = true;
          this.getStockConvertibleItems()
        }
    },
      err => console.error(err))
      this.isShowContent = true
  }

  protected categoryFilter() {
    if (!this.catBank) {
      return;
    }
    let search = this.categoryFilterCtrl.value;
    if (!search) {
      this.categoryMulti.next(this.catBank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.categoryMulti.next(
      this.catBank.filter(item => item.toLowerCase().indexOf(search) > -1)
    );
  }

  protected subCategoryFilter() {
    if (!this.subCatBank) {
      return;
    }
    let search = this.subCategoryFilterCtrl.value;
    if (!search) {
      this.subCategoryMulti.next(this.subCatBank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.subCategoryMulti.next(
      this.subCatBank.filter(item => item.toLowerCase().indexOf(search) > -1)
    );
  }

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }

  toggleAllCategory() {
    this.selectAllCategory = !this.selectAllCategory;
    if (this.selectAllCategory) {
      this.Category.setValue(this.catBank.slice());
    } else {
      this.Category.setValue([]);
    }
    this.selectCategory(this.Category.value);
  }

  toggleAllSubCategory() {
    this.selectAllSubCategory = !this.selectAllSubCategory;
    if (this.selectAllSubCategory) {
      this.Subcategory.setValue([]);
    } else {
      this.Subcategory.setValue(this.subCatBank.slice());
    }
    this.selectSubCat(this.Subcategory.value);
  }

  toggleAllWorkArea(){
    this.selectAllWorkArea = !this.selectAllWorkArea;
    if (this.selectAllWorkArea) {
      this.workAreaForm.setValue([]);
    } else {
      this.workAreaForm.setValue(this.Bank.slice());
    }
    this.selectIndentArea(this.workAreaForm.value);
  }

  changed() {
    if (this.showDetails) {
      if (this.indentAreas.length > 5) {
        this.enableSelectWorkArea = true
      }
      else {
        this.displayedColumns =  ['index','category','subCategory', 'itemName', 'itemCode', 'entryType', 'pkgName', 'uom', 'inStore' ,'parLevel','withTaxPrice','totalPrice','status'];
      }
    }
    else {
      this.enableSelectWorkArea = false
      this.displayedColumns = GlobalsService.inventoryListColumns
    }
  }

  getIndentAreaVal(obj, area) {
    if (obj.hasOwnProperty(area))
      return obj[area]
  }

  selectCategory(selectedCat: string[]) {
    const filteredCat = new Set<string>();        
    if (selectedCat && selectedCat.length > 0) {
      this.inventoryItems.forEach(item => {
        if (selectedCat.includes(item.category)) {
          filteredCat.add(item.subCategory);
        }
      });  
    } else {
      this.Subcategory.setValue('')
      this.dataSource.data = []
    }    
    this.subCatBank = Array.from(filteredCat);
    this.subCategoryMulti.next(this.subCatBank.slice());
    const selectedSubCat = this.subCatBank.filter((subCat) =>
      (this.Subcategory.value || []).includes(subCat)
    );
  
    this.selectSubCat(selectedSubCat);
  }
  
  selectSubCat(selectedSubCat: string[]) {
    let filteredData = [];  
    if (selectedSubCat && selectedSubCat.length > 0) {
      filteredData = this.inventoryItems.filter((item) =>
        selectedSubCat.includes(item.subCategory)
      );
    }  
    if (this.showInStore) {
      filteredData = filteredData.filter((item) => item.inStock > 0).sort((a, b) => b.inStock - a.inStock);
    }
    if (this.showParLevel) {
      filteredData = filteredData.filter((element) => element.inStock < element.parLevel);
    }  
    this.dataSource.data = filteredData.length > 0 ? filteredData : [];
    this.pageSizes = this.utils.getPageSizes(this.dataSource.data);
    this.dataSource.paginator = this.paginator;
  }
  
  inStoreValue(event: MatSlideToggleChange) {
    this.showInStore = event.checked;
    this.selectSubCat(this.Subcategory.value || []);
  }

  toggleParLevel(event: MatSlideToggleChange) {
    this.showParLevel = event ? event.checked : true;
    this.selectSubCat(this.Subcategory.value || []);
  }
  
  printpdf() {
    let inventoryList = {}
    inventoryList['inventoryItems'] = this.dataSource.data;
    inventoryList['user'] = this.user;
    inventoryList['selectedWorkAreas'] = this.selectedWorkAreas;
    this.purchases.printpdfs(inventoryList, 'Inventory').subscribe(data => {
      // window.open('data:application/pdf;base64,' + data.eFile);
      this.purchases.globalPrintPdf(data.eFile);
    });

  }
  exportToExcel() {
    let inventoryList = {}
    inventoryList['inventoryItems'] = this.dataSource.data;
    inventoryList['user'] = this.user;
    inventoryList['selectedWorkAreas'] = this.selectedWorkAreas;
    this.purchases.exportToExcel(inventoryList, 'Inventory').subscribe(data => {
      window.open('data:text/csv;base64,' + data.eFile);
    });
  }

  filterByBranch(restId) {
    this.dataSource = new MatTableDataSource();
    this.branchselect = true;
    this.restaurantId = restId.restaurantIdOld ? restId.restaurantIdOld : restId;    
    this.branchSelected = true;
    this.user.restaurantAccess.forEach(element => {
      if (element.restaurantIdOld == this.restaurantId) {
        this.indentAreas = element.workAreas
        this.Bank = this.indentAreas;
        this.filteredWorkArea.next(this.Bank.slice());
        this.bankFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
            this.filterBanks();
        });
        if (element.workAreas == undefined) {
          this.indentAreas = element.workAreas;
          this.Bank = this.indentAreas;
          this.filteredWorkArea.next(this.Bank.slice());
          this.bankFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
            this.filterBanks();
        });
        }
      }
    });
      this.displayedColumns =  ['index','category','subCategory', 'itemName', 'itemCode', 'entryType', 'pkgName', 'uom', 'inStore','parLevel','withTaxPrice','totalPrice','status'];
      this.showTable = true;
      this.indentAreas = [...new Set(this.indentAreas)];
      this.selectedWorkAreas = null;
      if(this.stockConversionValue === false){
        this.getCategories() ;
      }
      this.getItemsType() ;
      this.showTableDetails = true;
  if (this.router.url.includes(this.storeUrl)) {
      if(this.sharedFilterData != '' && this.sharedFilterData.restaurantId == restId){
          this.inventoryListForm.get('branchSelection').setValue(this.sharedFilterData.restaurantId);
          this.branches = this.getBranchData;
          this.ItemType.setValue(this.sharedFilterData.ItemType);
          this.getBranchInv();
          this.Category.setValue(this.sharedFilterData.category);
          this.Subcategory.setValue(this.sharedFilterData.subcategory);
          this.indentAreas = this.sharedFilterData.workAreaData;
          this.indentAreas = [...new Set(this.indentAreas)];
          this.filteredWorkAreasList = this.indentAreas
          this.Bank = this.filteredWorkAreasList;
          this.multiFilteredWorkArea.next(this.Bank.slice());
          this.multiBankFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
            this.multiFilterBanks();
        });
        this.workAreaForm.setValue(this.sharedFilterData.workArea);
      }
    }
  }

  clearFilter() {
    this.search = ''
    this.ItemType.setValue('')
    this.Category.setValue('')
    this.Subcategory.setValue('')
    this.workAreaForm.setValue('')
    this.searchValue = ''
    this.searchText = ''
    this.showInStore = false;
    this.showParLevel = false;
    this.doFilter(this.searchValue)
    this.dataSource.data = this.inventoryItems
  }

  selectIndentArea(val) {
    if (this.router.url.includes(this.storeUrl)) {
      this.indentAreas = [...new Set(this.indentAreas)];
      this.selectedWorkAreas = val;
      this.displayedColumns =  ['index','category','subCategory', 'itemName', 'itemCode', 'entryType', 'pkgName', 'uom', 'inStore' ,'parLevel','withTaxPrice','totalPrice','status'].concat(val);
    }else if(this.stockConversionValue === true){
      this.indentAreas = [...new Set(this.indentAreas)];
      this.selectedWorkAreas = val;
      this.displayedColumns =  ['index','category','subCategory', 'itemName', 'itemCode', 'entryType', 'pkgName', 'uom', 'inStore' ,'parLevel','withTaxPrice','totalPrice','status'].concat(val);
    } else {
      this.indentAreas = [...new Set(this.indentAreas)];
      this.selectedWorkAreas = val;
      this.displayedColumns =  ['index','category','subCategory', 'itemName', 'itemCode', 'entryType', 'pkgName', 'uom', 'inStore' ,'parLevel','withTaxPrice','totalPrice','status'].concat(val);
      this.showTable = true;
    }
  }

  filterWorkArea() {
    this.filteredWorkAreasList = this.indentAreas.filter(
      (wa) =>
        wa.toLowerCase().includes(this.searchText.toLowerCase())
    );
  }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
    this.unsubscribe$.next();
    this.unsubscribe$.complete(); 
    // if(!this.router.url.includes(this.StockConversionUrl)){
      this.sharedFilterService.getFilteredInventory['_value'] = ''
    // }
  }

  protected filterBanks() {
    if (!this.Bank) {
      return;
    }
    let search = this.bankFilterCtrl.value;
    if (!search) {
      this.filteredWorkArea.next(this.Bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.filteredWorkArea.next(
      this.Bank.filter(bank => bank.toLowerCase().indexOf(search) > -1)
    );
  }

  protected multiFilterBanks() {
    if (!this.Bank) {
      return;
    }
    let search = this.multiBankFilterCtrl.value;
    if (!search) {
      this.multiFilteredWorkArea.next(this.Bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.multiFilteredWorkArea.next(
      this.Bank.filter(bank => bank.toLowerCase().indexOf(search) > -1)
    );
  }

  refreshdata(){
    this.showInStore = false;
    this.showParLevel = false;
    if (this.router.url.includes(this.storeUrl)) {
      this.selectedWorkAreas = null;
      this.getBranchInv();
    }
    else{
      this.getBranchInv();
    }
  }

  showScList(element){

    let inputObj = {
      restaurantId : this.inventoryListForm.value.branchSelection,
      ItemType: this.ItemType.value,
      workArea: this.selectedWorkAreas,
      category : this.Category.value,
      subcategory : this.Subcategory.value,
      branchFlag : true,
      workAreaData : this.indentAreas,
      inventoryItems : this.inventoryItems,
      convert : true
    }  
    this.sharedFilterService.getFilteredInventory.next(inputObj);
    element.restaurantId = this.restaurantId;
    element.tenantId = this.user.tenantId;
    this.sharedData.stockConversion(element);
    this.router.navigate(['/home/<USER>'])
    }

    getStockConvertibleItems(){
      let filteredData = this.dataSource.data.filter((item)=> item.stockConversion == true && item.inStock > 0);
      this.dataSource.data = this.stockConversion ?  filteredData : this.inventoryItems ;
    }

    getCategories() {
      this.branchTransfer.getCategories({
        restaurantId: this.restaurantId,
      }
      ).subscribe(data => {
        this.itemCategory =  data.success ? data.data : [] ;
      })
    }
  
    getItemsType() {
      this.branchTransfer.getItemType({
        restaurantId: this.restaurantId,
      }
      ).subscribe(data => {
        this.itemType =  data.success ? data.data : [] ;         
        if(this.sharedFilterData.ItemType == undefined){
          this.toggleAllSelectionItemType(true)
        }
      })
    }

    toggleAllSelectionItemType(manual = false){
        if (this.allSelectedItemType && this.allSelectedItemType.selected || manual) {
          this.ItemType.patchValue([]);
          this.ItemType.patchValue([...this.itemType, 1]);
        }else {
          this.ItemType.patchValue([]);
        }
          this.getBranchInv();
    }

    totalValue(element: any): number {
      const withTaxPrice = element.withTaxPrice ? element.withTaxPrice : 0;
      const inStock = element.inStock ? element.inStock : 0;
      element.totalPrice = withTaxPrice * inStock;
      return withTaxPrice * inStock;
    }
    
    history(element){
      let inputObj = {
        restaurantId : this.inventoryListForm.value.branchSelection,
        ItemType: this.ItemType.value,
        workArea: this.selectedWorkAreas,
        category : this.Category.value,
        subcategory : this.Subcategory.value,
        branchFlag : true,
        workAreaData : this.indentAreas,
        inventoryItems : this.inventoryItems,
        convert : true
      }  
      this.sharedFilterService.getFilteredInventory.next(inputObj);
      this.sharedData.stockConversionList(element);
      this.router.navigate(['/home/<USER>'])
    }

    getTotalPrice(){
      return this.utils.truncateNew(this.dataSource.data.map(element => element.withTaxPrice * element.inStock).reduce((acc, value) => acc + value, 0) )
    }

    portionValueQty(element){
      if (element.hasOwnProperty('portionWeight'))  {
        let conversionCoefficient =  element['uom'] == 'NOS' ? 1: 1000 ;
        let calculatedQuantity = this.utils.truncateNew((element.inStock * conversionCoefficient) / element.portionWeight);
        return calculatedQuantity
      }
      return ''
    } 
}