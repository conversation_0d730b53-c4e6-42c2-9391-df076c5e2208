<div class="splIndentTopDiv mt-2">
  <form [formGroup]="indentForm" class="topHeadInputs">
    <mat-form-field appearance="none" class="topitem">
      <label>Select Branch</label>
      <mat-select placeholder="Select Branch" class="outline" tabindex="0" #branch (focus)="branch.open()" tabindex="1"
        formControlName="branchSelection" (selectionChange)="filterByBranch($event.value)"
        (keydown)="moveToDelivery($event)">
        <mat-option *ngFor="let rest of branches" [value]="rest">
          {{ rest.branchName }}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </form>

  <mat-form-field appearance="none" class="topitem ml-2" *ngIf='stockSeparation'>
    <label>Stock Type</label>
    <mat-select placeholder="Stock Type" class="outline" [(ngModel)]="stockType" (selectionChange)="stockChange($event)"
      [disabled]=true>
      <mat-option value="Stockable">Stockable Items</mat-option>
      <!-- <mat-option value="Non-Stockable">Non-Stockable Items</mat-option> -->
    </mat-select>
  </mat-form-field>

  <mat-form-field appearance="none" *ngIf="specialFlag == true" class="topitem ml-2">
    <label>Delivered To</label>
    <mat-select placeholder="Delivered To" #deliveryto (focus)="deliveryto.open()" tabindex="2"
      (keydown.space)="onSpaceKey($event)" (selectionChange)="onWorkAreaSelectionChange($event)" class="outline"
      [(ngModel)]="currentWorkArea" multiple>

      <mat-option>
        <ngx-mat-select-search placeholderLabel="Select Work Area..." noEntriesFoundLabel="'No Work Area found'"
          (keydown.space)="preventSpaceInSearch($event)" [formControl]="workAreaFilterCtrl"></ngx-mat-select-search>
      </mat-option>

      <mat-option *ngFor="let area of filteredWorkArea | async" [value]="area" (focus)="focusedOption = area">
        {{ area }}
      </mat-option>

    </mat-select>
  </mat-form-field>



  <!-- <ng-template #confirmationDialog>
    <h2 mat-dialog-title>Confirmation</h2>
    <mat-dialog-content>
      <p>All entered data will be lost. Are you sure you want to proceed?</p>
    </mat-dialog-content>
    <mat-dialog-actions align="end">
      <button mat-button (click)="onCancelWorkArea()">Cancel</button>
      <button mat-button color="warn" (click)="onConfirmWorkArea()">OK</button>
    </mat-dialog-actions>
  </ng-template> -->

  <ng-template #confirmationDialog>
    <h2 mat-dialog-title class="dialog-header">
      <b>Alert</b>
      <button mat-icon-button (click)="onCancelWorkArea()" class="close-button">
        <mat-icon>close</mat-icon>
      </button>
    </h2>
    <mat-dialog-content class="dialog-content">
      <p>Are you sure you want to proceed?</p>
      <p style="font-size: 12px;">(All entered data will be lost.)</p>
    </mat-dialog-content>

    <mat-dialog-actions class="dialog-actions">
      <div class="action-container">
        <button mat-button color="primary" (click)="onConfirmWorkArea()">Confirm</button>
      </div>
    </mat-dialog-actions>
  </ng-template>

  <mat-form-field appearance="none" *ngIf="buttonAccess && workAreaSelected" class="topitem ml-2">
    <label>Indent Date</label>
    <input matInput [matDatepicker]="picker1" class="outline" placeholder="Indent Date" [formControl]="documentDate"
      tabindex="3" [(ngModel)]="today" /> <!-- [max]="currentTime" -->
    <mat-datepicker-toggle #dateCal (keydown)="moveToItem($event)" matSuffix [for]="picker1" tabindex="4">
      <mat-icon matDatepickerToggleIcon>
        <img class="datepickIcon" src="./../../assets/calender.png" />
      </mat-icon>
    </mat-datepicker-toggle>
    <mat-datepicker #picker1></mat-datepicker>
  </mat-form-field>

  <button mat-button mat-raised-button class="uIbutton button3 ml-2" *ngIf="buttonAccess && workAreaSelected"
    (click)="issueIndentReq(element)" [disabled]='(!allowOrder ) || !isButtonDisabled() || isDone'>
    Raise Indent
  </button>

  <!-- <mat-slide-toggle *ngIf='workAreaSelected' class="togglebutton ml-2" [(ngModel)]="indentPreview"
    (change)="preview()">Preview</mat-slide-toggle> -->
</div>

<div
  *ngIf="(branchSelected && multiBranchUser && workAreaSelected && currentWorkArea) || (!multiBranchUser && workAreaSelected)"
  class="datacontainer">
  <mat-card>
    <mat-card-content>
      <div class="search-table-input">
        <!-- <mat-form-field appearance="none" style="padding-right: 6px;">
          <label>Search</label>
          <input matInput type="text" #search (focus)="search.open()" (keyup)="doFilter($event.target.value)"
            placeholder="Search" class="outline" [(ngModel)]='searchText' />
          <mat-icon matSuffix (click)="clear()" class="closebtn">close</mat-icon>
        </mat-form-field> -->

        <!-- <mat-form-field appearance="none">
          <label>Type</label>
          <mat-select placeholder="Item Type" #type (focus)="type.open()" [formControl]="ItemType" [(ngModel)]="itmType"
            class="outline">
            <mat-option *ngFor="let itemType of ItemTypeList" [value]="itemType" (click)="selectItemType(itemType)">
              {{ itemType | titlecase }}
            </mat-option>
          </mat-select>
        </mat-form-field> -->

        <!-- <mat-form-field appearance="none">
          <label>Category</label>
          <mat-select placeholder="Category" class="outline" [formControl]="category" [(ngModel)]="cat">
            <mat-option *ngFor="let cat of categoryList" [value]="cat" (click)="selectCategory(cat)">
              {{ cat | titlecase}}
            </mat-option>
          </mat-select>
        </mat-form-field> -->

        <!-- <mat-form-field appearance="none">
          <label>Sub Category</label>
          <mat-select placeholder="Subcategory" #subcategory (focus)="subcategory.open()" [formControl]="Subcategory"
            class="outline" [(ngModel)]="subCat">
            <mat-option *ngFor="let subCat of subCategoryList" [value]="subCat" (click)="selectSubCat(subCat)">
              {{ subCat | titlecase }}
            </mat-option>
          </mat-select>
        </mat-form-field> -->

        <mat-form-field appearance="none" style="padding-top: 2px;">
          <label> Indent Remarks </label>
          <textarea matInput style="font-size: 15px; height: 41px;" [(ngModel)]="remarks" class="outline" required
            #descriptionTextarea maxlength="50"></textarea>
        </mat-form-field>

        <button mat-button class="buttonForRefresh refreshBtn" tabindex="-1" (click)="refreshdata()">Reset</button>
      </div>

      <ng-template #dialogTemplate *ngIf="modalShow">
        <h2 mat-dialog-title class="dialog-header">
          <b>Confirmation Reset</b>
          <button mat-icon-button (click)="onCancel()" class="close-button">
            <mat-icon>close</mat-icon>
          </button>
        </h2>
        <mat-dialog-content class="dialog-content">
          <p>Are you sure you want to reset?</p>
          <p style="font-size: 12px;">(All entered information will be lost and cannot be recovered!)</p>
        </mat-dialog-content>

        <mat-dialog-actions class="dialog-actions">
          <div class="action-container">
            <button mat-button color="primary" (click)="onConfirm()">Confirm</button>
          </div>
        </mat-dialog-actions>
      </ng-template>

      <div style="margin-top: 20px;">
        <label class="ipText">Entry type : </label>
        <mat-radio-group [(ngModel)]="selectOption" (change)="entryTypeChange()">
          <mat-radio-button value="open">OPEN</mat-radio-button>
          <mat-radio-button value="package">PACKAGE</mat-radio-button>
        </mat-radio-group>

        <mat-radio-group style="float: right;" [(ngModel)]="selectedOption" (change)="portionChange()">
          <mat-radio-button [value]="false">UOM</mat-radio-button>
          <mat-radio-button [value]="true">PORTION</mat-radio-button>
        </mat-radio-group>
        <label class="ipText" style="float: right;">Default transfer by : </label>
      </div>

      <!-- <div class="search-table-input" style="display: none;">
        <input matInput type="text" #search (focus)="itemName.open()" tabindex="5" (keyup)="doFilter(search.value)"
          placeholder="Search" class="outline" [(ngModel)]="searchText" style="width: 20%;" />

        <mat-icon matSuffix (click)="clear()" class="closebtn">close</mat-icon>
      </div> -->

      <section class="example-container-1 mat-elevation-z8">
        <!-- Item Name Dropdown -->
        <mat-form-field *ngIf="dataSource" appearance="none">
          <div class="select-icon-container">
            <label>Item Name</label>
            <mat-icon matTooltip="Item Name | Package Name | In Stock" matTooltipPosition="above" aria-label="Help Info"
              class="info-icon">
              help
            </mat-icon>
          </div>
          <mat-select placeholder="Inventory Item" class="outline" #itemName (focus)="itemName.open()" tabindex="5"
            [(ngModel)]="selectedItemName" (selectionChange)="onItemSelect($event)" (keydown)="moveToInput($event)">
            <mat-option>
              <ngx-mat-select-search placeholderLabel="Select Item..." noEntriesFoundLabel="'No Item found'"
                [formControl]="itemFilterCtrl">
              </ngx-mat-select-search>
            </mat-option>
            <mat-option *ngFor="let element of filteredItems | async" [value]="element">
              {{ element.itemName | titlecase }} | {{ element.packageName }} | {{ truncateValue(element.inStock) }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="none" class="splOrderInputs">
          <label>Pkg Name</label>
          <input matInput class="outline" type="text" placeholder="Package Name" [value]="pkgNames" disabled readonly />
        </mat-form-field>

        <mat-form-field appearance="none" class="splOrderInputs">
          <label>In Stock</label>
          <input matInput class="outline" type="text" placeholder="In Stock" [value]="inStock" disabled readonly />
        </mat-form-field>

        <!-- Unit Price Input Field -->
        <mat-form-field appearance="none" class="splOrderInputs">
          <label>Unit Price</label>
          <input matInput class="outline" type="text" placeholder="Unit Price" [value]="unitPrice" disabled readonly />
        </mat-form-field>

        <mat-form-field appearance="none" class="splOrderInputs">
          <label>UOM</label>
          <input matInput class="outline" type="text" placeholder="Uom" [value]="Uom" disabled readonly />
        </mat-form-field>
        <label></label>
        <label style="margin-top: 20px;"></label>

        <table #table mat-table *ngIf="!indentPreview && arrayPkg" [dataSource]="arrayPkg" matSort>
          <ng-container matColumnDef="index">
            <th mat-header-cell *matHeaderCellDef mat-sort-header class="tableId"><b>#</b></th>
            <td mat-cell *matCellDef="let element; let i = index" class="tableId">
              {{ i + 1 + paginator?.pageIndex * paginator?.pageSize }}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>

          <ng-container matColumnDef="totalPrice">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Total Price</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ truncateValue((element.withTaxPrice) * (getWorkAreaSum(element))) }}
            </td>
            <td mat-footer-cell *matFooterCellDef>
              {{ this.purchases.truncateValue(getTotal('totalPrice')) }}
            </td>
          </ng-container>

          <ng-container *ngFor="let area of selectedWorkArea">
            <ng-container [matColumnDef]="area">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                <b>{{ area }}</b>
              </th>
              <td mat-cell *matCellDef="let element">
                <div class="input-svg">
                  <div
                    [ngClass]="{'input-down': element.ItemType === 'SubRecipe', 'input-box2': element.ItemType !== 'SubRecipe'}"
                    style="display: flex; align-items: center;">
                    <input #dynamicInput class="input-box2" type="number"
                      [step]="element.packageName === 'NOS' || element.packageName === 'nos' ? '1' : '0.01'" min="0"
                      tabindex="6" (keyup)="getTotalIndentCost(element[area], element)" [(ngModel)]="element[area]"
                      (keypress)="restrictDecimal($event, element.packageName)"
                      (keydown.enter)="moveToTextarea($event, $event.target)"
                      (focus)="element[area] === 0 ? element[area] = '' : null"
                      (focusout)="element[area] === '' || element[area] === null ? element[area] = 0 : undefined"
                      [disabled]="!element.workArea.hasOwnProperty(area)"
                      [ngClass]="{'disabled-input': !element.workArea.hasOwnProperty(area)}"
                      [ngStyle]="element.ItemType !== 'SubRecipe' ? {'background-color': !element.workArea.hasOwnProperty(area) ? 'grey' : ''} : {}" />
                    <mat-select
                      *ngIf="element.ItemType === 'SubRecipe' && element.hasOwnProperty('portionWeight') && element.workArea.hasOwnProperty(area)"
                      [(value)]="element.selectedOption" class="dropdown" (selectionChange)="convertToQty(element)"
                      style="margin-left: 3px;">
                      <mat-option value="uom">UOM</mat-option>
                      <mat-option value="portion">Portion</mat-option>
                    </mat-select>
                  </div>
                  <div class="iconDiv"
                    *ngIf="element.ItemType === 'SubRecipe' && element.hasOwnProperty('portionWeight') && element.workArea.hasOwnProperty(area) && element.selectedOption === 'portion' && this.selectedWorkArea">
                    <mat-icon class="info-icon" (mouseover)="onMouseOver(element[area],element, area)"
                      (mouseout)="onMouseOut()">
                      info
                    </mat-icon>
                  </div>
                </div>
              </td>
              <td mat-footer-cell *matFooterCellDef></td>
            </ng-container>
          </ng-container>

          <ng-container matColumnDef="uom">
            <th mat-header-cell *matHeaderCellDef mat-sort-header><b> Uom</b></th>
            <td mat-cell *matCellDef="let element">{{ element.uom }}</td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>

          <ng-container matColumnDef="remarks">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Remarks </b>
            </th>
            <td mat-cell *matCellDef="let element">
              <textarea matInput class="repeat textarea-margin" rows="2" maxlength="50" wrap="soft" cols="10"
                tabindex="7" [(ngModel)]="element.remarks" (keydown)="handleTabPress($event, $event.target)">
              </textarea>
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="['index'].concat(selectedWorkArea, ['totalPrice', 'remarks'])"></tr>
          <tr mat-row *matRowDef="let row; columns: ['index'].concat(selectedWorkArea, ['totalPrice', 'remarks'])"></tr>
          <tr mat-footer-row *matFooterRowDef="['index'].concat(selectedWorkArea, ['totalPrice', 'remarks'])"
            [class.hidden]="!showFooter"></tr>
        </table>

        <!-- <div style="display: flex;align-items: end;justify-content: end;margin-top: 10px;margin-right: 20px;">
          <button mat-raised-button *ngIf="!indentPreview" color="primary" (click)="addEntry()">Add</button>
        </div> -->

        <br />
        <label class="ipText" style="margin-top: 5px; margin-bottom: 20px;">Preview</label>
        <div class="table-container">
          <table #table mat-table
            *ngIf="tableShow && !indentPreview && dataSource && dataSource.data && filteredDataSource.length > 0"
            [dataSource]="filteredDataSource" matSortActive="itemName" matSortDirection="asc" matSort>
            <ng-container matColumnDef="index">
              <th mat-header-cell *matHeaderCellDef mat-sort-header class="tableId"><b>#</b></th>
              <td mat-cell *matCellDef="let element; let i = index" class="tableId">
                {{ i + 1 + paginator?.pageIndex * paginator?.pageSize }}
              </td>
              <td mat-footer-cell *matFooterCellDef></td>
            </ng-container>
            <ng-container matColumnDef="itemName">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                <b> Item Name</b>
              </th>
              <td mat-cell *matCellDef="let element">
                {{ element.itemName | titlecase }}
              </td>
              <td mat-footer-cell *matFooterCellDef> Total </td>
            </ng-container>
            <ng-container matColumnDef="moq">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                <b> Minimum Issue Quantity</b>
              </th>
              <td mat-cell *matCellDef="let element">{{ element.moq }}</td>
              <td mat-footer-cell *matFooterCellDef></td>
            </ng-container>
            <ng-container matColumnDef="inStock">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                <b> In Stock</b>
              </th>
              <td mat-cell *matCellDef="let element">
                {{ truncateValue(element.inStock) }}
              </td>
              <td mat-footer-cell *matFooterCellDef></td>
            </ng-container>

            <ng-container matColumnDef="unitPrice">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                <b> Unit Price </b>
              </th>
              <td mat-cell *matCellDef="let element">
                {{ truncateValue(element.withTaxPrice) }}
              </td>
              <td mat-footer-cell *matFooterCellDef></td>
            </ng-container>

            <ng-container matColumnDef="totalPrice">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                <b>Total Price</b>
              </th>
              <td mat-cell *matCellDef="let element">
                {{ truncateValue((element.withTaxPrice) * (getWorkAreaSum(element))) }}
              </td>
              <td mat-footer-cell *matFooterCellDef>
                {{ this.purchases.truncateValue(getTotal('totalPrice')) }}
              </td>
            </ng-container>

            <ng-container matColumnDef="pkgName">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                <b> Pkg Name</b>
              </th>
              <td mat-cell *matCellDef="let element">
                {{ element.packageName }}
              </td>
              <td mat-footer-cell *matFooterCellDef></td>
            </ng-container>

            <ng-container matColumnDef="entryType">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                <b>Entry Type</b>
              </th>
              <td mat-cell *matCellDef="let element">
                {{ element.entryType ? (element.entryType | titlecase) : '-' }}
              </td>
              <td mat-footer-cell *matFooterCellDef></td>
            </ng-container>

            <ng-container matColumnDef="projectedSales">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                <b> Projected Consumption</b>
              </th>
              <td mat-cell *matCellDef="let element">
                {{ truncateValue(element.projectedSales)}}
              </td>
              <td mat-footer-cell *matFooterCellDef></td>
            </ng-container>

            <ng-container *ngFor="let area of selectedWorkArea">
              <ng-container [matColumnDef]="area">
                <th mat-header-cell *matHeaderCellDef mat-sort-header>
                  <b>{{ area }}</b>
                </th>
                <td mat-cell *matCellDef="let element">
                  <div class="input-svg">
                    <div
                      [ngClass]="{'input-down': element.ItemType === 'SubRecipe', 'input-box2': element.ItemType !== 'SubRecipe'}"
                      style="display: flex; align-items: center;">
                      <input class="input-box2" type="number" step="0.01" min="0"
                        (keyup)="getTotalIndentCost(element[area], element)" [(ngModel)]="element[area]"
                        (focus)="element[area] === 0 ? element[area] = '' : null"
                        (focusout)="element[area] === '' || element[area] === null ? element[area] = 0 : undefined"
                        [ngClass]="{'disabled-input': !element.workArea.hasOwnProperty(area)}"
                        [disabled]="!element.workArea.hasOwnProperty(area)"
                        [ngStyle]="element.ItemType !== 'SubRecipe' ? {'background-color': !element.workArea.hasOwnProperty(area) ? 'grey' : ''} : {}"
                        style="flex: 1;" />
                      <mat-select
                        *ngIf="element.ItemType === 'SubRecipe' && element.hasOwnProperty('portionWeight') && element.workArea.hasOwnProperty(area)"
                        [(value)]="element.selectedOption" class="dropdown" (selectionChange)="convertToQty(element)"
                        style="margin-left: 3px; flex: 0;">
                        <mat-option value="uom">UOM</mat-option>
                        <mat-option value="portion">Portion</mat-option>
                      </mat-select>
                    </div>
                    <div class="iconDiv"
                      *ngIf="element.ItemType === 'SubRecipe' && element.hasOwnProperty('portionWeight') && element.workArea.hasOwnProperty(area) && element.selectedOption === 'portion' && this.selectedWorkArea">
                      <mat-icon class="info-icon" (mouseover)="onMouseOver(element[area],element, area)"
                        (mouseout)="onMouseOut()">
                        info
                      </mat-icon>
                    </div>
                  </div>
                </td>
                <td mat-footer-cell *matFooterCellDef></td>
              </ng-container>
            </ng-container>

            <ng-container matColumnDef="uom">
              <th mat-header-cell *matHeaderCellDef mat-sort-header><b> UOM</b></th>
              <td mat-cell *matCellDef="let element">{{ element.uom }}</td>
              <td mat-footer-cell *matFooterCellDef></td>
            </ng-container>

            <ng-container matColumnDef="remarks">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                <b> Remarks </b>
              </th>
              <td mat-cell *matCellDef="let element">
                <textarea matInput class="textarea-margin" rows="2" maxlength="50" wrap="soft" cols="10"
                  style="background-color: white;color:black" [(ngModel)]=element.remarks></textarea>
              </td>
              <td mat-footer-cell *matFooterCellDef></td>
            </ng-container>

            <ng-container matColumnDef="action">
              <th mat-header-cell *matHeaderCellDef>
                <b>Action</b>
              </th>
              <td mat-cell *matCellDef="let element">
                <button mat-icon-button (click)="deleteRow(element)">
                  <mat-icon>delete</mat-icon>
                </button>
              </td>
              <td mat-footer-cell *matFooterCellDef></td>
            </ng-container>
            <tr mat-header-row
              *matHeaderRowDef="['index', 'itemName', 'entryType', 'pkgName', 'inStock', 'unitPrice', 'uom'].concat(selectedWorkArea, ['totalPrice', 'remarks', 'action'])">
            </tr>
            <tbody class="scrollable-table-body">
              <tr mat-row
                *matRowDef="let row; columns: ['index', 'itemName', 'entryType', 'pkgName', 'inStock', 'unitPrice', 'uom'].concat(selectedWorkArea, ['totalPrice', 'remarks', 'action'])">
              </tr>
            </tbody>
            <tr mat-footer-row
              *matFooterRowDef="['index', 'itemName', 'entryType', 'pkgName', 'inStock', 'unitPrice', 'uom'].concat(selectedWorkArea, ['totalPrice', 'remarks', 'action'])"
              [class.hidden]="showFooter"></tr>
          </table>
        </div>

        <!-- <table #table mat-table *ngIf="indentPreview && dataSource && dataSource.data" [dataSource]="dataSource"
          matSortActive="itemName" matSortDirection="asc" matSort>
          <ng-container matColumnDef="index">
            <th mat-header-cell *matHeaderCellDef mat-sort-header class="tableId"><b>#</b></th>
            <td mat-cell *matCellDef="let element; let i = index" class="tableId">
              {{ i + 1 + paginator?.pageIndex * paginator?.pageSize }}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>
          <ng-container matColumnDef="itemName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Item Name</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.itemName | titlecase }}
            </td>
            <td mat-footer-cell *matFooterCellDef> Total </td>
          </ng-container>
          <ng-container matColumnDef="moq">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Minimum Issue Quantity</b>
            </th>
            <td mat-cell *matCellDef="let element">{{ element.moq }}</td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>
          <ng-container matColumnDef="inStock">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> In Stock</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ truncateValue(element.inStock) }}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>

          <ng-container matColumnDef="unitPrice">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Unit Price </b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ truncateValue(element.price) }}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>

          <ng-container matColumnDef="totalPrice">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Total Price</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ truncateValue((element.price) * (getWorkAreaSum(element))) }}
            </td>
            <td mat-footer-cell *matFooterCellDef>
              {{ this.purchases.truncateValue(getTotal('totalPrice')) }}
            </td>
          </ng-container>

          <ng-container matColumnDef="pkgName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Pkg Name</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.packageName }}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>

          <ng-container matColumnDef="entryType">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Entry Type</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.entryType || '-'}}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>

          <ng-container matColumnDef="projectedSales">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Projected Consumption</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ truncateValue(element.projectedSales)}}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>

          <ng-container *ngFor="let area of selectedWorkArea">
            <ng-container [matColumnDef]="area">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                <b>{{ area }}</b>
              </th>
              <td mat-cell *matCellDef="let element">
                <div class="input-svg">
                  <div
                    [ngClass]="{'input-down': element.ItemType === 'SubRecipe', 'input-box2': element.ItemType !== 'SubRecipe'}">
                    <input class="input-box2" type="number" step="0.01" min="0"
                      (keyup)="getTotalIndentCost(element[area], element)" [(ngModel)]="element[area]"
                      (focus)="element[area] === 0 ? element[area] = '' : null"
                      (focusout)="element[area] === '' || element[area] === null ? element[area] = 0 : undefined"
                      [disabled]="!element.workArea.hasOwnProperty(area)"
                      [ngClass]="{'disabled-input': !element.workArea.hasOwnProperty(area)}" />
                    <mat-select
                      *ngIf="element.ItemType === 'SubRecipe' && element.hasOwnProperty('portionWeight') && element.workArea.hasOwnProperty(area)"
                      [(value)]="element.selectedOption" class="dropdown" (selectionChange)="convertToQty(element)">
                      <mat-option value="uom">UOM</mat-option>
                      <mat-option value="portion">Portion</mat-option>
                    </mat-select>
                  </div>
                  <div class="iconDiv"
                    *ngIf="element.ItemType === 'SubRecipe' && element.hasOwnProperty('portionWeight') && element.workArea.hasOwnProperty(area) && element.selectedOption === 'portion'">
                    <svg (mouseover)="onMouseOver(element)" (mouseout)="onMouseOut()" xmlns="http://www.w3.org/2000/svg"
                      width="20" height="20" fill="currentColor" class="bi bi-calculator calIcon" viewBox="0 0 20 20">
                      <path
                        d="M12 1a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1zM4 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2z" />
                      <path
                        d="M4 2.5a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-.5.5h-7a.5.5 0 0 1-.5-.5zm0 4a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5zm0 3a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5zm0 3a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5zm3-6a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5zm0 3a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5zm0 3a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5zm3-6a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5zm0 3a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v4a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5z" />
                    </svg>
                  </div>
                </div>
              </td>
              <td mat-footer-cell *matFooterCellDef></td>
            </ng-container>
          </ng-container>

          <ng-container matColumnDef="uom">
            <th mat-header-cell *matHeaderCellDef mat-sort-header><b> Uom</b></th>
            <td mat-cell *matCellDef="let element">{{ element.uom }}</td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>
          <tr mat-header-row
            *matHeaderRowDef="['index', 'itemName', 'entryType', 'pkgName', 'inStock', 'unitPrice', 'uom'].concat(selectedWorkArea, ['totalPrice'])">
          </tr>
          <tr mat-row
            *matRowDef="let row; columns: ['index', 'itemName', 'entryType', 'pkgName', 'inStock', 'unitPrice', 'uom'].concat(selectedWorkArea, ['totalPrice'])">
          </tr>
          <tr mat-footer-row
            *matFooterRowDef="['index', 'itemName', 'entryType', 'pkgName', 'inStock', 'unitPrice', 'uom'].concat(selectedWorkArea, ['totalPrice'])"
            [class.hidden]="!showFooter"></tr>
        </table> -->
      </section>
      <div class="dataMessage" *ngIf="dataSource?.data.length == 0"> No Data Available </div>
    </mat-card-content>
  </mat-card>
  <mat-paginator [showTotalPages]="5" [pageSize]='10' [pageSizeOptions]="pageSizes"></mat-paginator>

</div>

<div class="custom-tooltip" [hidden]="!showTooltip">
  <div class="parentClass">
    <div class="d-flex m-2" style="font-weight: bold; font-size: small;">
      <div class="custom-tooltip-Heading">
        Per Portion Weight
      </div>
      <div>
        {{ hoveredElement?.portionWeight / 1000 }} {{ (hoveredElement?.defaultUOM) }}
      </div>
    </div>
    <div class="d-flex m-2" style="font-weight: bold; font-size: small;">
      <div class="custom-tooltip-Heading">
        Current Quantity
      </div>
      <div>
        {{ (hoveredElement?.portionWeight / 1000) * this.hoveredValue }} {{ (hoveredElement?.defaultUOM) }}
      </div>
    </div>
    <div class="d-flex m-2" style="font-weight: bold; font-size: small;">
      <div class="custom-tooltip-Heading">
        No of Portion
      </div>
      <div>
        {{ this.hoveredValue }}
      </div>
    </div>
  </div>
</div>