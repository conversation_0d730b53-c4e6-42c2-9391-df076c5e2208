input[type=number]::-webkit-inner-spin-button, 
input[type=number]::-webkit-outer-spin-button { 
  -webkit-appearance: none; 
}

input[type=number] {
  -moz-appearance: textfield;
}

.uIbutton {
  float: right !important;
}

.uIbutton:disabled {
  background-color:  #c2c2a3 !important;
  color: black !important;
}

.exprbutton {
  min-width: 78px ;
  float: right;
}

.togglebutton{
  float: right;
}

.toggle{
  float: right;
  // background-color: rgb(69, 73, 73);
  border-radius: 5px;
}

.text{
  float: right;
}

.splIndentTopDiv{
  overflow: hidden;
  width: 96%;
  margin-left: 2% !important;
  margin-right: 2% !important;
}

.hidden {
  display: none;
}

.example-container-1{
  overflow-y: auto;
}

.disabled-input {
  opacity: 0.6;
}

.iconDiv{
  margin-left: 4px;
}

.input-svg{
  display: flex;
  align-items: center;
}

.dropdown {
  border: 1px solid #ccc;
  height: 25px;
  width: 22px;
}

.input-box2 {
  text-align: center;
  width: 65px;
  height: 25px;
  background: white;
  border: #191919;
  color: black;
  font-size: 12px !important;
}

.custom-tooltip {
  position: absolute;
  background-color: #333;
  color: #fff;
  padding: 5px;
  border-radius: 4px;
  font-size: 14px;
  z-index: 1000; 
  width: 250px;
  left: 710px;
  top: 108px;
}

.custom-tooltip-Heading{
  width: 150px;
}

.custom-tooltip-Value{
  width: 74px;
}

.ipText{
  text-align: center;
  font-size: medium;
  // font-weight: bold;
  font-size: 16px !important;
  margin-bottom: 10px !important;
  margin-right: 10px !important;
  opacity: 0.9 !important;
}

::ng-deep .mat-radio-label-content {
  padding-left: 8px;
  margin-right: 10px;
  font-size: 15px;
  font-weight: bold;
  opacity: 0.9 !important;
}

::ng-deep .search-table-input {
  margin: 0 auto;
  margin-bottom: 15px;
  overflow: hidden;

  .mat-form-field {
    margin-right: 10px;
  }
}

.table-container {
  max-height: 400px;
  overflow-y: auto;
  position: relative;
}

.scrollable-table-body {
  display: block;
  max-height: 300px;
  overflow-y: auto;
}

.scrollable-table-body tr {
  display: table;
  width: 100%;
  table-layout: fixed;
}

.select-icon-container {
  display: flex;
  align-items: center;
}

.select-icon-container label {
  margin: 0; 
  font-size: 16px;
}

.info-icon {
  cursor: pointer;
  margin-left: 8px;
  color: #757575;
  vertical-align: middle;
  font-size: 20px;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.close-button {
  position: absolute;
  right: 0;
  top: 0;
}

.dialog-content {
  text-align: center;
  margin: 5px 10px;
}

.dialog-actions {
  display: flex;
  justify-content: center;
  padding: 16px 0;
}

.action-container {
  display: flex;
  justify-content: center;
  width: 100%;
  margin-bottom: 10px;
}

.textarea-margin {
  margin: 8px -4px;
  background-color: white;
  color: black;
}










