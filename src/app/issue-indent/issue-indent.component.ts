import { ChangeDetectorR<PERSON>, Component, OnInit, ViewChild, OnChanges, SimpleChanges, ElementRef, TemplateRef } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService, PurchasesService, BranchTransferService, ShareDataService } from '../_services';
import { GlobalsService } from '../_services/globals.service';
import { UtilsService } from '../_utils/utils.service';
import { MatDialogRef, MatDialog, MatOption, MatSelect, MatInput } from '@angular/material';
import { SimpleDialogComponent } from '../_dialogs/simple-dialog/simple-dialog.component';
import { MatTableDataSource, MatPaginator, MatSort } from '@angular/material';
import { PackageDialogComponent } from '../_dialogs/package-dialog/package-dialog.component';
import { NotificationService } from '../_services/notification.service';
import { Form<PERSON><PERSON>er, FormControl, FormGroup, Validators } from "@angular/forms";
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material';
import { MomentDateModule, MomentDateAdapter } from '@angular/material-moment-adapter';
import { environment } from 'src/environments/environment';
import { ReplaySubject, Subject, BehaviorSubject } from 'rxjs';
import { takeUntil, startWith, map, debounceTime, distinctUntilChanged } from 'rxjs/operators';

export const MY_FORMATS = {
  parse: {
    dateInput: 'LL'
  },
  display: {
    dateInput: 'DD-MM-YYYY',
    monthYearLabel: 'YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'YYYY'
  }
};

@Component({
  selector: 'app-issue-indent',
  templateUrl: './issue-indent.component.html',
  styleUrls: ['./issue-indent.component.scss', './../../common-dark.scss'],
  providers: [{ provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
  { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }
  ]
})
export class IssueIndentComponent implements OnInit {
  IndentAreas: string[] = [];
  indentArea: any;
  specialFlag: boolean;
  buttonAccess: boolean = false;
  all = "ALL";
  indentPreview: boolean = false;
  showFooter: boolean = false;
  curDataSource: any[];
  specialIndentUrl = encodeURI(GlobalsService.specialIndents);
  storeIndents = encodeURI(GlobalsService.storeIndents);
  kitchenIndent = encodeURI(GlobalsService.kitchenIndent);

  user: any;
  inventoryItems: any[];
  displayedColumns: any[];
  grnPriceList: any = {};
  title: string;
  restaurantId: any;
  savedItems: any;
  workAreaSelected: boolean = false;
  stockSeparation: boolean;
  stockType: any = 'Stockable';
  currentWorkArea: any;
  selectedCategory: any;
  initData: any;
  branchSelected: any;
  multiBranchUser: boolean = false;
  categoryList = ['All'];
  subCategoryList = ['All'];
  ItemTypeList = ['All'];
  initSubCategoryList: any = [];
  initCategoryList: any = [];
  subCatList: any = [];
  searchText: string = '';
  searchValue: any = ''
  selectedWorkArea: any;
  filterKeys = { ItemType: 'All', category: 'All', subCategory: 'All' }
  category = new FormControl('', [Validators.required]);
  Subcategory = new FormControl();
  documentDate = new FormControl();
  ItemType = new FormControl();
  dataSource: MatTableDataSource<any>;
  totalIndentCost: any = 0;
  branches: any[];
  getBranchData: any[]
  indentForm: FormGroup;
  totalRoles: any;
  allowOrder: boolean;
  roles: any = [];
  appCat: any;
  selectedRole: any;
  searchWorkAreaText: any;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  today: any;
  currentTime: any;
  private pageSizes: any;
  private specialKeys: Array<string> = ['Backspace', 'Tab', 'End', 'Home', 'ArrowLeft', 'ArrowRight', 'Del', 'Delete'];
  private regex: RegExp = new RegExp(/^\d*\.?\d{0,2}$/g);
  itmType: any;
  cat: any;
  subCat: any;
  private unsubscribe$ = new Subject<void>();
  categoryData: any;
  isTrue = true;
  multiCategory: any;
  remarks: any;
  @ViewChild('categoryAllSelected') private categoryAllSelected: MatOption;
  tempData: any[];
  showTooltip: boolean = false;
  hoveredElement: any = null;
  selectOption: any = 'package';
  selectedOption: boolean = false;
  public Bank: any[] = [];
  public workAreaFilterCtrl: FormControl = new FormControl();
  public filteredWorkArea: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  isDone: boolean;

  selectedItemName: any = null;
  pkgNames: string = "";
  filteredPackages: any[];
  unitPrice: number = 0;
  inStock: number = 0;
  Uom: any = null;
  selectedPackageName: any = null;
  arrayPkg: any[];
  //@ViewChild('itemName', { read: MatSelect }) itemNameSelect: MatSelect;
  @ViewChild('search') searchInput!: ElementRef;
  @ViewChild('itemName') itemName!: MatSelect;
  bankFilterCtrl: FormControl = new FormControl();
  tableShow = true;
  @ViewChild('dialogTemplate') dialogTemplate!: TemplateRef<any>;
  private dialogRef: any;
  private dialogRefWorkArea!: MatDialogRef<any>;
  itemFilterCtrl: FormControl = new FormControl();
  filteredItems: BehaviorSubject<any[]> = new BehaviorSubject([]);
  modalShow = true;
  previousWorkArea: any = null;
  previousWorkAreaSelection: any;
  @ViewChild('confirmationDialog') confirmationDialog!: TemplateRef<any>;
  modalWorkArea = false;
  crtWorkArea: any[] = [];
  tempWorkArea: any[] = [];
  hoveredArea: any;
  hoveredValue: number;
  storedValues: { [key: string]: { [workArea: string]: number } } = {};

  @ViewChild('remarksTextarea') remarksTextarea: ElementRef;
  @ViewChild('dynamicInput') dynamicInput: MatInput;
  @ViewChild('deliveryto') deliveryto: MatSelect;
  @ViewChild('branch') branch: MatSelect;
  @ViewChild('indentDate') indentDate: ElementRef;
  @ViewChild('dateCal') dateCal: ElementRef;
  @ViewChild('picker1') picker1: any;
  isDatepickerOpen = false;

  moveToTextarea(event: KeyboardEvent, currentInput: HTMLTextAreaElement | HTMLInputElement) {
    if (event.key === 'Tab' || event.code === 'Tab' || event.key === 'Enter' || event.code === 'Enter') {
      event.preventDefault();
      const elements = Array.from(document.querySelectorAll('input.input-box2, textarea.repeat')) as (HTMLInputElement | HTMLTextAreaElement)[];
      const currentIndex = elements.indexOf(currentInput);

      for (let i = currentIndex + 1; i < elements.length; i++) {
        if (!elements[i].disabled) {
          elements[i].focus();
          return;
        }
      }
      this.remarksTextarea.nativeElement.focus();
    }
  }

  moveToInput(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
      if (this.itemName) {
        this.itemName.close();
      }
      setTimeout(() => {
        const inputs = Array.from(document.querySelectorAll('input.input-box2')) as HTMLInputElement[];
        for (const input of inputs) {
          if (!input.disabled) {
            input.focus();
            break;
          }
        }
      }, 0);
    }
  }

  moveToDelivery(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();
      if (this.branch) {
        this.branch.close();
      }
      this.deliveryto.focus()
    }
  }

  moveToItem(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      event.preventDefault();

      if (this.isDatepickerOpen) {
        this.picker1.close();
        this.isDatepickerOpen = false;

        if (this.itemName) {
          this.itemName.focus();
        } else {
          console.error('Item Name dropdown not found');
        }
      } else {
        console.log('Opening datepicker');
        this.picker1.open();
        this.isDatepickerOpen = true;
      }
    }
  }

  focusInput(): void {
    if (this.dynamicInput) {
      setTimeout(() => {
        this.dynamicInput.focus();
      }, 0);
    }
  }

  handleTabPress(event: KeyboardEvent, currentInput: HTMLTextAreaElement) {
    if (event.key === 'Tab' || event.code === 'Tab' || event.key === 'Enter' || event.code === 'Enter') {
      event.preventDefault();
      const inputs = Array.from(document.querySelectorAll('textarea.repeat')) as HTMLTextAreaElement[];
      const currentIndex = inputs.indexOf(currentInput);
      for (let i = currentIndex + 1; i < inputs.length; i++) {
        if (!inputs[i].disabled) {
          inputs[i].focus();
          return;
        }
      }
      this.focusSearchInput();
      this.clearSearchText();
    }
  }

  focusSearchInput() {
    if (this.itemName) {
      this.itemName.focus();
    }
  }


  constructor(private auth: AuthService,
    private purchases: PurchasesService,
    private notifyService: NotificationService,
    private branchTransfer: BranchTransferService,
    private cdref: ChangeDetectorRef,
    private dialog: MatDialog,
    private utils: UtilsService,
    public router: Router,
    private fb: FormBuilder,
    private sharedData: ShareDataService) {
    if (this.router.url.includes(this.storeIndents)) {
      this.specialIndentUrl = encodeURI(GlobalsService.storeIndents);
    }
    let date = new Date();
    date.setHours(0, 0, 0, 0);
    this.today = date;
    this.currentTime = date;
    this.user = this.auth.getCurrentUser()
    this.multiBranchUser = this.user.multiBranchUser;
    this.indentForm = this.fb.group({ branchSelection: [null, Validators.required] });

    var windowLocation = window.location.href;
    let windowCurrentUrl = windowLocation.split('/')[4]

    this.sharedData.sharedBranchData.pipe(takeUntil(this.unsubscribe$)).subscribe((val) => {
      this.getBranchData = val;
      if (this.getBranchData.length == 0) {
        this.branches = this.user.restaurantAccess;
      } else if (this.getBranchData.length == 1) {
        const toSelect = this.getBranchData.find(data => data.branchName == this.getBranchData[0].branchName);
        this.indentForm.get('branchSelection').setValue(toSelect);
        this.branches = this.getBranchData
        this.filterByBranch(this.indentForm.value.branchSelection);
      } else {
        this.branches = this.getBranchData
      }
    });
  }

  ngAfterContentChecked() {
    this.cdref.detectChanges();
  }


  truncateValue(num) {
    if (typeof num !== 'number' && typeof num !== 'string') {
      return 0;
    }
    const number = parseFloat(num as string);
    if (isNaN(number) || Math.abs(number) < 0.01) {
      return 0;
    }
    const truncatedNumber = number.toString().match(/^-?\d+(?:\.\d{0,2})?/)[0];
    return parseFloat(truncatedNumber);
  }

  ngOnInit() {
    this.initializeFilter();
    this.specialFlag = this.router.url.includes(this.specialIndentUrl);
    if (!this.router.url.includes(this.specialIndentUrl)) {
      this.displayedColumns = GlobalsService.issueIndentColumns;
      this.title = "Kitchen Indent"
    }
    else {
      this.displayedColumns = GlobalsService.specialIndentColumns;
      this.title = "Special Indent"
    }
    this.user.restaurantAccess.forEach(element => {
      if (element.restaurantIdOld == this.restaurantId) {
        this.IndentAreas = element.workAreas
        if (element.workAreas == undefined) {
          this.IndentAreas = element.workAreas;
        }
      }
    });
    if (!this.user.multiBranchUser) {
      if (this.user.restaurantAccess[0].workAreas.length == 1) {
        this.restaurantId = this.user.restaurantAccess[0].restaurantIdOld;
        this.category.setValue('');
        this.indentPreview = false;
        this.indentArea = this.user.restaurantAccess[0].workAreas[0];
        this.workAreaSelected = false;
      }
      else if (this.user.restaurantAccess[0].workAreas.length > 1) {
        this.restaurantId = this.user.restaurantAccess[0].restaurantIdOld;
        this.category.setValue('');
        this.IndentAreas = this.user.restaurantAccess[0].workAreas;
      }
      this.getSpecialIndentData();
    }
    this.user.restaurantAccess.forEach(element => {
      if (element.restaurantIdOld == this.restaurantId) {
        if (this.IndentAreas.length === 1) {
          this.currentWorkArea = this.selectedWorkArea = [this.IndentAreas[0]];
          this.multiBranchUser = true;
          this.workAreaSelected = true;
          this.buttonAccess = true;
          this.branchSelected = true;
          this.getSpecialIndentData();
          this.updateDisplayedColumns();
        }
      }
    });
    this.stockType = 'Stockable'
    // if (this.dataSource && this.dataSource.data) {
    //   this.filteredItems.next(this.dataSource.data);
    //   console.log("Datasourec", this.dataSource.data)
    // }

    this.itemFilterCtrl.valueChanges.pipe(
      startWith(''),
      map(search => this.filterItems(search))
    ).subscribe();
  }

  ngOnDestroy() {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.selectedWorkArea) {
      this.updateDisplayedColumns();
    }
    if (changes.dataSource && this.dataSource && this.dataSource.data) {
      this.populateItems();
    }
  }

  populateItems(): void {
    if (this.dataSource && this.dataSource.data) {
      this.filteredItems.next(this.dataSource.data);
    }
  }

  protected filterBanks() {
    if (!this.Bank) {
      return;
    }
    let search = this.workAreaFilterCtrl.value;
    if (!search) {
      this.filteredWorkArea.next(
        this.Bank.slice().sort((a, b) => a.toLowerCase().localeCompare(b.toLowerCase()))
      );
      return;
    } else {
      search = search.toLowerCase();
    }
    this.filteredWorkArea.next(
      this.Bank
        .filter(bank => bank.toLowerCase().includes(search))
        .sort((a, b) => a.toLowerCase().localeCompare(b.toLowerCase()))
    );
  }

  searchFilter() {
    this.user.restaurantAccess.forEach(element => {
      if (element.restaurantIdOld === this.restaurantId) {
        this.IndentAreas = element.workAreas;
        this.Bank = this.IndentAreas;
        this.filteredWorkArea.next(
          this.Bank.slice().sort((a, b) => a.toLowerCase().localeCompare(b.toLowerCase()))
        );
        this.workAreaFilterCtrl.valueChanges
          .pipe(takeUntil(this.unsubscribe$))
          .subscribe(() => {
            this.filterBanks();
          });
      }
    });
  }

  updateDisplayedColumns(): void {
    const baseColumns = GlobalsService.specialIndentColumns.filter(column => column !== 'totalPrice');
    this.displayedColumns = [...baseColumns, ...this.selectedWorkArea, 'totalPrice'];
  }

  onWorkAreaSelectionChange(event: any) {
    if (this.filteredDataSource.length > 0) {
      this.tempWorkArea = [...this.crtWorkArea];

      this.dialogRefWorkArea = this.dialog.open(this.confirmationDialog);
      this.modalWorkArea = true;

      this.dialogRefWorkArea.afterClosed().subscribe(result => {
        if (result) {
          this.resetFormData(event);
        } else {
          this.crtWorkArea = [...this.tempWorkArea];
        }
      });
    } else {
      this.selectIndentArea(event);
    }
  }


  resetFormData(event: any) {
    this.selectIndentArea(event);
  }

  onConfirmWorkArea(): void {
    if (this.dialogRefWorkArea) {
      this.dialogRefWorkArea.close(true);
    } else {
      console.error('dailogRefWorkArea is undefined in onConfirm');
    }
  }

  onCancelWorkArea(): void {
    if (this.dialogRefWorkArea) {
      this.dialogRefWorkArea.close(false);
    } else {
      console.error('dailogRefWorkArea is undefined in onCancel');
    }
  }


  selectIndentArea(val: any) {
    this.indentPreview = false;
    this.selectedWorkArea = val.value;
    this.previousWorkArea = val.value;
    this.indentArea = val.value;
    this.workAreaSelected = this.selectedWorkArea.length > 0;

    if (this.workAreaSelected) {
      this.getSpecialIndentData();
      this.updateDisplayedColumns();
      this.getFilteredItemsChange();
      this.filteredPackages = [];
      this.resetFilters();
    } else {
      this.dataSource.data = [];
    }
  }

  getSpecialIndentData() {
    let obj = {
      tenantId: this.user.tenantId,
      workArea: this.selectedWorkArea,
      restaurantId: this.restaurantId,
      userEmail: this.user.email,
      uId: this.user.mId,
      specialFlag: this.specialFlag,
      reqQty: true
    };
    this.branchTransfer.getSpecialIndentData(obj).subscribe(data => {
      if (data) {
        this.initData = data.invItems;
        this.inventoryItems = data.invItems;
        this.inventoryItems.forEach(item => {
          item['defaultUOM'] = item['uom'];
          item['currentStock'] = item['inStock'];
          item['actualPrice'] = item['price'];
        });
        this.dataSource.data = this.inventoryItems.filter(item => {
          if (!this.router.url.includes(this.specialIndentUrl)) {
            return item.projectedSales > 0;
          }
          return true;
        }).map(item => {
          if (!item.hasOwnProperty('packageName')) {
            item.packageName = item.uom;
            item.packageQty = 1;
          }
          if (this.router.url.includes(this.specialIndentUrl)) {
            item.issueQty = 0;
          } else {
            item.issueQty = item.projectedSales;
            if (item.miq > item.issueQty) {
              item.issueQty = item.miq;
            }
            if (item.issueQty > item.inStock) {
              item.issueQty = item.inStock;
            }
          }

          item.issueQty = Math.ceil(item.issueQty);
          return item;
        });
        if (this.inventoryItems && this.selectedWorkArea) {
          this.inventoryItems.forEach(item => {
            const key = `${item.itemName}_${item.itemCode}_${item.packageName}`;
            this.selectedWorkArea.forEach(area => {
              if (!item.hasOwnProperty(area)) {
                item[area] = 0;
              }
              if (this.storedValues[key] && this.storedValues[key][area] !== undefined) {
                item[area] = this.storedValues[key][area];
              }
            });
          });
        }
        this.filteredItems.next(this.dataSource.data);
        this.inventoryItems.sort((a, b) => {
          const keyA = `${a.itemName}_${a.itemCode}_${a.packageName}`;
          const keyB = `${b.itemName}_${b.itemCode}_${b.packageName}`;
          let sumA = 0;
          let sumB = 0;
          if (this.storedValues[keyA]) {
            sumA = this.selectedWorkArea.reduce((sum, area) => {
              return sum + (this.storedValues[keyA][area] || 0);
            }, 0);
          }
          if (this.storedValues[keyB]) {
            sumB = this.selectedWorkArea.reduce((sum, area) => {
              return sum + (this.storedValues[keyB][area] || 0);
            }, 0);
          }
          return sumB - sumA;
        });
        this.inventoryItems.forEach(item => {
          item.category = item.category || 'N/A';
          item.ItemType = item.ItemType || 'N/A';
          item.subCategory = item.subCategory || 'N/A';

          this.ItemTypeList.push(item.ItemType);
          this.categoryList.push(item.category);
          this.subCategoryList.push(item.subCategory);
        });
        this.categoryList = this.categoryList.filter((k, i, ar) => ar.indexOf(k) === i);
        this.ItemTypeList = this.ItemTypeList.filter((p, q, arrr) => arrr.indexOf(p) === q);
        this.subCategoryList = this.subCategoryList.filter((j, l, arr) => arr.indexOf(j) === l);
        this.initCategoryList = this.categoryList;
        this.initSubCategoryList = this.subCategoryList;
        if (Array.isArray(this.indentArea)) {
          this.dataSource.data = this.inventoryItems.filter(item =>
            this.indentArea.some(area => Object.keys(item.workArea).includes(area))
          ).map(item => {
            item.issueQty = 0;
            return item;
          });
        }
        this.pageSizes = this.utils.getPageSizes(this.dataSource.data);
        this.dataSource.paginator = this.paginator;
        this.curDataSource = this.dataSource.data;
        this.buttonAccess = true;
        this.multiBranchUser = true;
        this.workAreaSelected = true;
        this.branchSelected = true;
        if (this.itmType) {
          this.selectItemType(this.itmType);
        }
        if (this.selectedCategory) {
          this.selectCategory(this.selectedCategory);
        }
        if (this.subCat) {
          this.selectSubCat(this.subCat);
        }
      }
      this.entryTypeChange();
    },
      err => console.error(err));
  }

  displayPackages(data) {
    const dialogRef = this.dialog.open(PackageDialogComponent, {
      data: data
    });
    dialogRef.afterClosed().subscribe(
      data => {
        let dialogIssueQty = 0;
        data.packagingSizes.forEach((item) => {
          if (item.orderedPackages != undefined) {
            item.orderedPackages = parseInt(item.orderedPackages, 10)
            dialogIssueQty = dialogIssueQty + (item.orderedPackages * item.pkgQty)
          }

        });
        data.issueQty = dialogIssueQty;
        if (data.issueQty > data.inStock) {
          // this.notifyService.showError('Total Issue Qty(i.e \'' + data.issueQty + '\') is greater than instock value(i.e \'' + data.inStock + '\'). Issue Qty is set to inStock Value.','')
          this.utils.snackBarShowError('Total Issue Qty(i.e \'' + data.issueQty + '\') is greater than instock value(i.e \'' + data.inStock + '\'). Issue Qty is set to inStock Value.')
          data.issueQty = data.inStock
        }
      });
  }

  issueIndent() {
    if (this.router.url.includes(this.specialIndentUrl) && this.indentArea == undefined) {
      this.utils.snackBarShowWarning("Please select an indent area")
    }
    else {
      let itemsToIssue = this.dataSource.data.filter(item => item.issueQty > 0)
      this.branchTransfer.issueIndent({
        invItems: itemsToIssue,
        uId: this.user.mId,
        tenantId: this.user.tenantId,
        indentArea: this.indentArea
      }).subscribe(data => {
        this.utils.openSnackBar('Indents Sent', null, 3000)
      }, err => console.error(err))
    }

  }

  public doFilter(value: string) {
    this.searchText = value;
    if (value) {
      this.itemName.open(); // Open the select dropdown
    }
  }

  printToPdf() {
    let inventoryList = {};
    inventoryList['inventoryItems'] = [];
    this.dataSource.data.forEach(function (item) {
      if (item['issueQty'] > 0) {
        inventoryList['inventoryItems'].push(item);
      }
    });
    if (inventoryList['inventoryItems'].length == 0) {
      this.utils.snackBarShowWarning('please enter indent values')
      return;
    }
    inventoryList['user'] = this.user;
    inventoryList['recipientArea'] = this.indentArea
    this.purchases.printpdfs(inventoryList, 'Indent').subscribe(data => {
      // window.open('data:application/pdf;base64,' + data.eFile);
      this.purchases.globalPrintPdf(data.eFile);
    });
  }

  exportToExcel() {
    let inventoryList = {}
    inventoryList['indentArea'] = this.indentArea;
    inventoryList['inventoryItems'] = this.dataSource.data;
    inventoryList['user'] = this.user;
    this.purchases.exportToExcel(inventoryList, 'Indent').subscribe(data => {
      window.open('data:text/csv;base64,' + data.eFile);
    });
  }

  validateIssueQty(element) {
    element.inStock < element.issueQty ? element.issueQty = element.inStock : element;
  }

  filterByBranch(restId) {
    this.dataSource = new MatTableDataSource<any>();
    this.restaurantId = restId.restaurantIdOld;
    this.currentWorkArea = undefined;
    this.branchSelected = true;
    this.user.restaurantAccess.forEach(element => {
      if (element.restaurantIdOld == this.restaurantId) {
        this.IndentAreas = element.workAreas;
      }
    });
    this.category.setValue('');
    this.getUsers();
    this.appCat = undefined;
    this.paginator = undefined;
    this.searchFilter();
  }

  allFilter() {
    let tmp = this.curDataSource
    let prev = this.curDataSource
    Object.keys(this.filterKeys).forEach(element => {
      if (this.filterKeys[element] != 'All') {
        tmp = prev.filter(item =>
          item[element].toLowerCase() === this.filterKeys[element].toLowerCase())
        prev = tmp
      }
    });
    this.dataSource.data = tmp
  }

  selectItemType(itemType) {
    let filteredCategoryList = []
    let filteredSubCategoryList = []
    if (itemType != 'All') {
      let filteredItem = this.curDataSource.filter(item => item['ItemType'].toUpperCase() === itemType.toUpperCase());
      filteredItem.forEach(element => {
        filteredCategoryList.push(element.category)
        filteredSubCategoryList.push(element.subCategory)
      });
      this.categoryList = filteredCategoryList.filter((k, i, ar) => ar.indexOf(k) === i);
      this.subCategoryList = filteredSubCategoryList.filter((k, i, ar) => ar.indexOf(k) === i);
      this.categoryList.splice(0, 0, 'All')
      this.subCategoryList.splice(0, 0, 'All')
      this.subCatList = this.subCategoryList
    }
    else {
      this.categoryList = this.initCategoryList;
      this.subCategoryList = this.initSubCategoryList;
    }
    this.filterKeys = { ItemType: itemType, category: 'All', subCategory: 'All' }
    this.allFilter()
    this.filteredPackages = [];
    this.getFilteredItemsChange();
  }

  selectCategory(category) {

    // const subcategories = [...new Set(this.dataSource.data.map(item => item.subCategory))];

    // category.forEach(cat => {

    //   let filteredSubCategoryList = []
    //   let filteredItem = []
    //   if (cat != 'All') {
    //     if (this.filterKeys.ItemType != 'All') {
    //       filteredItem = this.curDataSource.filter(item => item['category'].toUpperCase() === cat.toUpperCase()
    //         && item['ItemType'].toUpperCase() === this.filterKeys.ItemType.toUpperCase());
    //     }
    //     else {
    //       filteredItem = this.curDataSource.filter(item => item['category'].toUpperCase() === cat.toUpperCase());
    //     }
    //     filteredItem.forEach(element => {
    //       filteredSubCategoryList.push(element.subCategory)
    //     });
    //     this.subCategoryList = filteredSubCategoryList.filter((k, i, ar) => ar.indexOf(k) === i);
    //     this.subCategoryList.splice(0, 0, 'All')
    //   }
    //   else if (this.filterKeys.ItemType != 'All') {
    //     this.subCategoryList = this.subCatList
    //   }
    //   else {
    //     this.subCategoryList = this.initSubCategoryList
    //   }
    //   this.filterKeys.category = cat;
    //   this.filterKeys.subCategory = 'All'
    // });

    // this.allFilter()
  }

  selectSubCat(subCat) {
    this.filterKeys.subCategory = subCat;
    this.allFilter()
  }

  saveItems() {
    let itemsToSave = this.inventoryItems.filter(item => item.issueQty > 0)
    this.branchTransfer.saveIndentReq({
      invItems: itemsToSave,
      userEmail: this.user.email,
      tenantId: this.user.tenantId,
      indentArea: this.indentArea,
      restaurantId: this.restaurantId
    }).subscribe(data => {
      this.savedItems = [data['savedItems']]
      this.utils.openSnackBar('Items Saved', null, 3000)
    }, err => console.error(err))
  }

  issueIndentReq() {
    this.isDone = true;
    let faultItemCount = 0
    let itemsToIssue: any[] = [];

    this.dataSource.data.forEach((element: any) => {
      const allItemsToIssue = this.selectedWorkArea.map(area => ({
        workArea: area,
        value: element[area] || 0,
        obj: { ...element }
      })).filter(input => input.value > 0);

      allItemsToIssue.forEach(item => {
        this.getTotalIndentCost(item.value, item.obj, item.workArea);
        item.obj['price'] = item.obj['actualPrice'];
        if (item.obj['uom'] === 'portion') {
          item.obj['uom'] = item.obj['defaultUOM'];
          item.obj['inStock'] = item.obj['currentStock'];
          let portionConvertedWeight = item.obj['portionWeight'];
          item.portionConvertedWeight = portionConvertedWeight;
          let conversionCoefficient = (item.obj['defaultUOM'] === 'NOS' || item.obj['defaultUOM'] === 'ML') ? 1 : 1000;
          let portionWeight = (item.obj.portionWeight * 1000);
          let convertedWeight = (item.obj.portionWeight / conversionCoefficient) * item.value;
          // item.value = this.utils.truncateNew(convertedWeight / 1000);
        } else {
          item.obj['inStock'] = item.obj['inStock'];
        }
        itemsToIssue.push(item);
        if (item.value > item.obj.inStoreQty) {
          faultItemCount++;
        }
      });
    });

    if (faultItemCount > 0) {
      this.dialog.open(SimpleDialogComponent, {
        data: {
          title: 'Indent Alert',
          msg: 'There are ' + faultItemCount + ' items with indent Qty greater than inStore Qty. Are you sure you want to raise Indent?',
          ok: function () {
            this.raiseIndent(itemsToIssue)
          }.bind(this)
        }
      });
      //this.modalShow = false
    }
    else {
      this.raiseIndent(itemsToIssue)
      this.modalShow = false
    }
  }

  raiseIndent(itemsToIssue: any[]) {
    const transformedInvItems = itemsToIssue.reduce((acc, item) => {
      if (!acc[item.workArea]) {
        acc[item.workArea] = [];
      }
      const itemObject = { ...item.obj };
      itemObject[item.workArea] = item.value;

      acc[item.workArea].push(itemObject);
      return acc;
    }, {});

    if (itemsToIssue.length > 0) {
      let obj = {
        invItems: transformedInvItems,
        userEmail: this.user.email,
        tenantId: this.user.tenantId,
        indentArea: this.indentArea,
        indentDocumentDate: this.utils.dateCorrection(this.today),
        restaurantId: this.restaurantId,
        remarks: this.remarks
      };

      if (this.roles.length > 0) {
        const uniqueItemCategories = [...new Set(itemsToIssue.map(obj => obj.obj.category))];
        const getAppCatRoles = (data, appCat) => {
          const filteredObj = data.find(obj => obj.appCat === appCat);
          return filteredObj ? filteredObj.roles.map(role => role.value) : [];
        };
        let rolesArray = [], appCat;
        if (uniqueItemCategories.length > 1) {
          appCat = "DEFAULT";
          rolesArray = getAppCatRoles(this.roles, appCat);
        } else if (uniqueItemCategories.length === 1) {
          appCat = uniqueItemCategories[0];
          rolesArray = getAppCatRoles(this.roles, appCat);
          if (rolesArray.length == 0) {
            appCat = "DEFAULT";
            rolesArray = getAppCatRoles(this.roles, appCat);
          }
        } else {
          rolesArray = [];
          appCat = undefined;
        }
        this.selectedRole = rolesArray;
        this.appCat = appCat;
      }

      if (this.selectedRole && this.selectedRole.length > 0) {
        obj['role'] = this.selectedRole;
      } else {
        obj['role'] = [];
      }
      obj['approvalCategory'] = this.appCat;
      obj['userRole'] = this.user.role;
      obj['user'] = this.user;
      obj['baseUrl'] = environment.baseUrl;

      this.branchTransfer.issueIndentRequest(obj).subscribe({
        next: (data) => {
          this.isDone = false;
          if (data.hasOwnProperty('updatedIndent')) {
            this.savedItems = [data.updatedIndent];
          }
          this.utils.openSnackBar('Indent Sent', null, 3000);
          this.modalShow = false
          this.dataSource.data = this.inventoryItems.filter(
            item => Object.keys(item.workArea).includes(this.indentArea)
          ).map((item: any) => {
            this.selectedWorkArea.forEach(area => {
              item[area] = 0;
            });
            return item;
          });
        },
        error: (err) => {
          console.error(err);
        },
        complete: () => {
          this.arrayPkg = []
          this.router.navigateByUrl('/home', { skipLocationChange: true }).then(() => {
            this.router.navigate(['home', GlobalsService.specialIndents]);
          });
          this.selectedItemName = null
        }
      });
    }
    else {
      this.utils.snackBarShowWarning('No Items to issue. Please add Item');
    }
  }


  clear() {
    this.category.setValue('')
    this.Subcategory.setValue('')
    this.ItemType.setValue('')
    this.searchText = ''
    this.searchValue = ''
    this.doFilter(this.searchValue)
    this.entryTypeChange();
    this.searchText = '';
    this.selectedItemName = null;
  }

  // preview() {
  //   this.showFooter = !this.showFooter;
  //   if (this.indentPreview == true) {
  //     this.curDataSource = this.dataSource.data
  //     this.dataSource.data = this.dataSource.data.filter(element => this.selectedWorkArea.some(area => element[area] > 0)
  //     );
  //   }
  //   // else {
  //   //   this.dataSource.data = this.curDataSource
  //   // }
  // }

  portionChange() {
    if (this.selectedOption) {
      this.dataSource.data = this.dataSource.data.map(element => {
        if (element.ItemType === 'SubRecipe' && element.hasOwnProperty('portionWeight')) {
          element['uom'] = 'portion';
          element.selectedOption = 'portion';
          let conversionCoefficient = element['defaultUOM'] == 'NOS' ? 1 : 1000;
          element['inStock'] = element['currentStock'] / (element.portionWeight / conversionCoefficient)
          element['price'] = element['actualPrice'] * (element.portionWeight / conversionCoefficient)
        }
        return element;
      });
    } else {
      this.dataSource.data = this.dataSource.data.map(element => {
        if (element.ItemType === 'SubRecipe' && element.hasOwnProperty('portionWeight')) {
          element['uom'] = 'uom';
          element.selectedOption = 'uom';
          element['uom'] = element['defaultUOM']
          element['inStock'] = element['currentStock']
          element['price'] = element['actualPrice']
        }
        return element;
      });
    }
  }

  entryTypeChange() {
    const filterType = this.selectOption;
    this.dataSource.filterPredicate = (element: any, filter: string) => {
      const entryTypeFilter = filterType ? (element.hasOwnProperty('entryType') ? element.entryType === filterType : true) : true;
      const searchFilter = filter ? JSON.stringify(element).toLowerCase().includes(filter.toLowerCase()) : true;
      return entryTypeFilter && searchFilter;
    };
    this.dataSource.filter = filterType ? filterType : '';
    this.selectedItemName = [];
    this.arrayPkg = [];
    this.unitPrice = 0;
    this.inStock = 0;
    this.Uom = "";
    this.pkgNames = "";
    this.getFilteredItemsChange();
    this.filterItems(this.itemFilterCtrl.value);
  }

  isButtonDisabled(): boolean {
    if (!this.dataSource || !this.dataSource.data || !this.selectedWorkArea || this.selectedWorkArea.length === 0) {
      return true;
    }
    return this.dataSource.data.some(item =>
      this.selectedWorkArea.some(area => item[area] && item[area] > 0)
    );
  }

  clearSearchText() {
    this.searchText = ''
    this.doFilter(this.searchText)
  }

  getUsers() {
    let inputData: any = {
      tenantId: this.auth.getCurrentUser().tenantId,
      restaurantId: this.restaurantId,
      type: "indentApproval"
    }
    this.purchases.getSelectedUsers(inputData).subscribe(data => {
      this.stockSeparation = data.TenantDetails[0].permission.stockSeparation ? data.TenantDetails[0].permission.stockSeparation : false;
      this.multiCategory = data
      // multiCategory.TenantDetails[0].permission.multiCategory ?  data.TenantDetails[0].permission.multiCategory : false;
      let role = []
      data.data.forEach(function (element) {
        role.push(element.role)
      });
      this.totalRoles = data.data;
      this.roles = this.totalRoles;
      if (this.roles.length === 0) {
        this.allowOrder = true;
      }
      if (this.roles.length > 0) {
        this.allowOrder = false;
      }
      this.allowOrder = true;
    })
  }


  stockChange(event) {
    this.dataSource = new MatTableDataSource();
    this.currentWorkArea = undefined;
  }

  catChange(event) {
    this.dataSource = new MatTableDataSource();
    this.currentWorkArea = undefined;
  }

  filterWorkArea() {
    this.IndentAreas = this.IndentAreas.filter(
      (wa) =>
        wa.toLowerCase().includes(this.searchWorkAreaText.toLowerCase())
    );
  }

  numberOnly(event): boolean {
    let value = event.target.value;
    if (this.specialKeys.indexOf(event.key) !== -1) {
      return;
    }
    let current = value;
    const position = event.target.selectionStart;
    const next = [current.slice(0, position), event.key == 'Decimal' ? '.' : event.key, current.slice(position)].join('');
    if (next && !(next).match(this.regex)) {
      event.preventDefault();
    }
  }

  // refreshdata() {
  //   this.getSpecialIndentData();
  // }

  searchCategory() {
    let obj = {
      tenantId: this.auth.getCurrentUser().tenantId,
      restaurantId: this.indentForm.value.branchSelection.restaurantIdOld
    }
    this.purchases.fetchCategoryWithoutVendor(obj).subscribe(res => {
      if (res.result) {
        this.categoryData = res.data;
        if (this.multiCategory && this.multiCategory.TenantDetails[0].permission.multiCategory == true) {
          this.toggleAllSelectionCategory(true)
        }
      } else {
        this.utils.snackBarShowInfo('Category not found')
      }
    })
  }

  toggleAllSelectionCategory(manual = false) {
    if (this.categoryAllSelected && this.categoryAllSelected.selected) {
      this.selectedCategory = [...this.categoryData, 1];
    } else if (manual) {
      this.selectedCategory = [...this.categoryData, 1];
    } else {
      this.selectedCategory = [];
    }
  }

  getTotal(key: string): number {
    if (!this.showFooter) {
      const totalAmount = this.dataSource.data.reduce((total, item) => {
        const areaSum = this.selectedWorkArea.reduce((sum, area) => {
          return sum + (item[area] || 0);
        }, 0);
        return total + (areaSum * item.withTaxPrice);
      }, 0);
      return totalAmount;
    }
    return 0;
  }

  getWorkAreaSum(element: any): number {
    if (!this.selectedWorkArea || !Array.isArray(this.selectedWorkArea)) {
      return 0;
    }
    return this.selectedWorkArea.reduce((sum, area) => {
      return sum + (element[area] || 0);
    }, 0);
  }

  getTotalIndentCost(value: number, element: any, area: string): void {

    const key = `${element.itemName}_${element.itemCode}_${element.packageName}`;
    if (!this.storedValues[key]) {
      this.storedValues[key] = {};
    }
    this.storedValues[key][area] = element[area];

    let conversionCoefficient = (element['defaultUOM'] === 'NOS' || element['defaultUOM'] === 'ML') ? 1 : 1000;
    let portionWeight = element.portionWeight * 1000;
    let convertWeight = (portionWeight / conversionCoefficient) * value;
    let convertedWeight = convertWeight / 1000;
    if (!element['convertedWeight']) {
      element['convertedWeight'] = {};
    }
    element['convertedWeight'][area] = convertedWeight;
    element['portionMass'] = element.portionWeight || 0;
    const areaSum = this.selectedWorkArea.reduce((sum, workArea) => {
      return sum + (element[workArea] || 0);
    }, 0);
  }

  convertToQty(el) {
    if (el['selectedOption'] === 'portion') {
      let conversionCoefficient = (el['defaultUOM'] === 'NOS' || el['defaultUOM'] === 'ML') ? 1 : 1000;
      el['uom'] = 'portion'
      el['inStock'] = el['currentStock'] / (el.portionWeight / conversionCoefficient)
      el['withTaxPrice'] = el['actualPrice'] * (el.portionWeight / conversionCoefficient)
      el['quantity'] = 0
    } else {
      el['quantity'] = 0
      el['uom'] = el['defaultUOM']
      el['inStock'] = el['currentStock']
      el['withTaxPrice'] = el['actualPrice']
    }
  }

  onMouseOver(value: number, element: any, area: string) {
    this.showTooltip = true;
    this.hoveredElement = element;
    this.hoveredArea = area;
    this.hoveredValue = value;
  }

  onMouseOut() {
    this.showTooltip = false;
  }

  get currentConvertedWeight(): number {
    if (this.hoveredElement && this.hoveredElement.convertedWeight && this.hoveredArea) {
      return this.hoveredElement.convertedWeight[this.hoveredArea] || 0;
    }
    return 0;
  }

  getFilteredItemsChange() {
    return this.filteredItems.asObservable();
  }


  getUniqueItems(items: any[]) {
    return items.filter(
      (item: any, index: number, self: any[]) =>
        index === self.findIndex((i: any) => i.itemName === item.itemName)
    );
  }

  onItemSelect(event: any) {
    this.selectedItemName = event.value;

    this.pkgNames = this.selectedItemName.packageName;

    this.filteredPackages = this.dataSource.data.filter(
      (item: any) =>
        item.itemName === this.selectedItemName.itemName &&
        (!this.itmType || this.itmType === 'All' || item.ItemType === this.itmType)
    );

    this.selectedPackageName = null;
    this.arrayPkg = [this.selectedItemName];
    this.unitPrice = Number(parseFloat(this.selectedItemName.withTaxPrice).toFixed(3));
    this.inStock = this.selectedItemName.inStock;
    this.Uom = this.selectedItemName.uom;
    this.cdref.detectChanges();
  }

  restrictDecimal(event: KeyboardEvent, packageName: string) {
    const key = event.key;
    if ((packageName === 'NOS' || packageName === 'nos') && key === '.') {
      event.preventDefault();
    }
  }

  // onPackageSelect(event: any) {
  //   const selectedPackageName = event.value;
  //   const selectedPackage = this.dataSource.data.find(
  //     (item: any) =>
  //       item.itemName === this.selectedItemName.itemName &&
  //       item.packageName === selectedPackageName &&
  //       (!this.itmType || this.itmType === 'All' || item.ItemType === this.itmType)
  //   );
  //   this.arrayPkg = [selectedPackage];
  //   this.unitPrice = selectedPackage ? selectedPackage.price : 0;
  //   this.inStock = selectedPackage ? selectedPackage.inStock : 0;
  //   this.Uom = selectedPackage ? selectedPackage.uom : "";
  //   this.cdref.detectChanges();
  // }

  get filteredDataSource(): any[] {
    if (!this.dataSource || !this.dataSource.data || !this.selectedWorkArea) {
      return [];
    }

    const selectedAreas = this.selectedWorkArea;
    return this.dataSource.data.reduce((accumulator: any[], element) => {
      const matches = selectedAreas.some(area => area in element && (element[area] > 0 || element[area] !== 0));
      if (matches) {
        accumulator.push(element);
      }
      return accumulator;
    }, []).reverse();
  }

  deleteRow(element: any): void {
    const index = this.dataSource.data.indexOf(element);
    if (index >= 0) {
      this.selectedWorkArea.forEach(area => {
        if (element[area] !== undefined) {
          element[area] = 0;
        }
      });
      this.dataSource = new MatTableDataSource([...this.dataSource.data]);
    }
  }

  refreshdata(): void {
    this.dialogRef = this.dialog.open(this.dialogTemplate);
    this.modalShow = true
    this.dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.performReset();
      }
    });
  }

  onConfirm(): void {
    if (this.dialogRef) {
      this.dialogRef.close(true);
    } else {
      console.error('dialogRef is undefined in onConfirm');
    }
  }

  onCancel(): void {
    if (this.dialogRef) {
      this.dialogRef.close(false);
    } else {
      console.error('dialogRef is undefined in onCancel');
    }
  }

  performReset(): void {
    this.getSpecialIndentData();
    this.resetFilters()
  }

  resetFilters() {
    this.filteredItems.next([]);
  }

  filterItems(search: string): void {
    if (!this.dataSource || !Array.isArray(this.dataSource.data)) {
      return;
    }

    let filteredItems = this.dataSource.data;
    if (this.selectOption) {
      if (this.selectOption === 'open') {
        filteredItems = filteredItems.filter(item => item.entryType === 'open');
      } else if (this.selectOption === 'package') {
        filteredItems = filteredItems.filter(item => item.entryType === 'package' || item.entryType !== 'open');
      }
    }
    if (search && search.trim() !== '') {
      filteredItems = filteredItems.filter(item =>
        item.itemName.toLowerCase().includes(search.toLowerCase()) ||
        item.packageName.toLowerCase().includes(search.toLowerCase())
      );
    }
    this.filteredItems.next(filteredItems);
  }


  initializeFilter() {
    this.bankFilterCtrl.valueChanges.subscribe(() => {
      this.getFilteredItemsChange();
    });
  }

}

