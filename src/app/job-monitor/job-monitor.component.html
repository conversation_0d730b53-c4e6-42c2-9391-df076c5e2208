<mat-card>
  <span mat-card-title class="headTag">Job-Monitor Dashboard </span>
  <br>
  <div class="container">
    <div fxLayout="row" fxLayoutAlign="center">
      <div class="each">
        <h5>Select Client </h5>
        <mat-form-field>
          <mat-select [(value)]="selectedClient" placeholder="Client">
            <mat-option *ngFor="let client of clients" [value]="client.tenantId">
              {{client.full}}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div class="each">
        <h5>Select Event</h5>
        <mat-form-field>
          <mat-select [(value)]="selectedSheets" placeholder="Event">
            <mat-option *ngFor="let sheet of selectEvent" [value]="sheet">
              {{sheet}}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </div>
    <br>

    <div fxLayout="row" fxLayoutAlign="center">
      <button mat-raised-button class="checkStatsusButton button3" (click)="checkStatus()">Check Status</button>
    </div>

    <br>

    <!-- <div style="text-align: center;">
      <p>Do you need a help? <a [routerLink]="" (click)="openHelpFile()">Click here</a></p>
      <div id="outer">
        <div class="inner">
          <button mat-stroked-button class="btn-block" (click)="createJob()"
            [disabled]="selectedClient=='' || selectedSheets.length==0">
            Request Update
          </button>
        </div>
        <div class="inner">
          <button mat-stroked-button class="btn-block" (click)="reset()">
            Reset Selection
          </button>
        </div>
      </div>
    </div>
  </div> -->

    <div class="example-container mat-elevation-z8">
      <br>
      <span mat-card-title class="headTag">Job-Monitor Dashboard Status</span>
      <br>
      <div class="example-header">
        <mat-form-field>
          <input matInput (keyup)="applyFilter($event)" [(ngModel)]="inputData" placeholder="search here.." #input>
        </mat-form-field>
        <button mat-button id="refButton" class="buttonForRefresh" (click)="getJobs()">Refresh</button>
      </div>
      <mat-table #table [dataSource]="dataSource">
        <ng-container matColumnDef="id">
          <mat-header-cell *matHeaderCellDef> Update Id </mat-header-cell>
          <mat-cell *matCellDef="let element"> {{element.id}} </mat-cell>
        </ng-container>
        <ng-container matColumnDef="client">
          <mat-header-cell *matHeaderCellDef> Client </mat-header-cell>
          <mat-cell *matCellDef="let element"> {{element.client}} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="category">
          <mat-header-cell *matHeaderCellDef> Category </mat-header-cell>
          <mat-cell *matCellDef="let element"> {{element.category}} </mat-cell>
        </ng-container>
        <!-- -------------------------------------------------------------------------------- -->
        <ng-container matColumnDef="tenantId">
          <mat-header-cell *matHeaderCellDef> tenantId </mat-header-cell>
          <mat-cell *matCellDef="let element"> {{element.tenantId}} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="event">
          <mat-header-cell *matHeaderCellDef> event </mat-header-cell>
          <mat-cell *matCellDef="let element"> {{element.event}}</mat-cell>
        </ng-container>

        <ng-container matColumnDef="createTs">
          <mat-header-cell *matHeaderCellDef> Created Date </mat-header-cell>
          <mat-cell *matCellDef="let element"> {{element.createTs | date:'MMM d, h:mm:ss a'}} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="startime">
          <mat-header-cell *matHeaderCellDef> Starting Time </mat-header-cell>
          <mat-cell *matCellDef="let element"> {{element.startTime | date:'MMM d, h:mm:ss a'}} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="endtime">
          <mat-header-cell *matHeaderCellDef> Ending Time </mat-header-cell>
          <mat-cell *matCellDef="let element"> {{element.endTime | date:'MMM d, h:mm:ss a'}} </mat-cell>
        </ng-container>

        <ng-container matColumnDef="status">
          <mat-header-cell *matHeaderCellDef> Status </mat-header-cell>
          <mat-cell *matCellDef="let element">
            <!-- {{element.pssi}} -->
            <div *ngIf="element.pssi===true">Completed</div>
            <div *ngIf="element.pssi===false">On Progress</div>
          </mat-cell>
        </ng-container>
        <!-- ------------------------------------------------------------------------------ -->
        <ng-container matColumnDef="errorLog">
          <mat-header-cell *matHeaderCellDef> Error Log </mat-header-cell>
          <mat-cell *matCellDef="let element">
            <span (click)="getErrorLog(element)" matTooltip="click to download">
              <mat-icon>file_download</mat-icon>
            </span>
          </mat-cell>
        </ng-container>
        <mat-header-row *matHeaderRowDef="displayedColumns"></mat-header-row>
        <mat-row *matRowDef="let row; columns: displayedColumns;"></mat-row>
      </mat-table>
      <mat-paginator [showTotalPages]="5" [pageSize]="10" [pageSizeOptions]="pageSizes"></mat-paginator>
    </div>
  </div>
</mat-card>