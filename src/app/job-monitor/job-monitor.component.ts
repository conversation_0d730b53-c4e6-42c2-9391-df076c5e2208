import { AuthService } from '../_services/auth.service';
import { NotificationService } from '../_services/notification.service';
import { Component, OnInit, ViewChild } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatDialog, MatPaginator, MatTableDataSource } from '@angular/material';
import { MasterdataupdateService } from '../_services/masterdataupdate.service';
import { UtilsService } from '../_utils/utils.service';


@Component({
  selector: 'app-job-monitor',
  templateUrl: './job-monitor.component.html',
  styleUrls: ['./job-monitor.component.scss']
})
export class JobmonitorComponent implements OnInit {
  serverControl = new FormControl();
  clientControl = new FormControl();
  sheetsControl = new FormControl();
  servers: any;
  clients: any = [];
  scenarios: any = [];
  selectedClient = ""
  selectedSheets: any = [];
  selectEvent: any;
  event: any;
  pageSizes = []
  user: any;
  displayedColumns: string[];
  dataSource = new MatTableDataSource();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  dbName: any;
  tenantId: any;
  inputData: string;
  constructor(
    private notifyService: NotificationService,
    private auth: AuthService,
    public dialog: MatDialog,
    private utils: UtilsService,
    private masterDataService: MasterdataupdateService) {
    this.user = this.auth.getCurrentUser();
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }

  ngOnInit() {
    this.masterDataUpdateConfig();
    this.getJobsEvent();

  }

  openHelpFile() {
    window.open('../../assets/help.txt', '_blank');
  }

  reset() {
    this.selectedClient = "";
    this.selectedSheets = [];
  }

  masterDataUpdateConfig() {
    let obj = this.user;
    this.masterDataService.masterDataUpdateConfig(obj).subscribe((response: any) => {
      if (response.success) {
        this.servers = response.data.servers
        this.clients = response.data.clients
        this.scenarios = response.data.sheets
        this.dbName = response.data.dbName
      }
    });
  }

  getJobsEvent() {
    let obj = this.user;
    this.masterDataService.getJobsEvent(obj).subscribe((response: any) => {
      if (response.success) {
        this.selectEvent = response.data[0].events;
      }
    });
  }

  checkStatus() {
    this.getJobs();
  }

  getJobs() {
    let obj = {};
    obj['tenantId'] = this.selectedClient;
    obj['event'] = this.selectedSheets;
    this.masterDataService.getJobs(obj).subscribe((response: any) => {
      if (response.success) {
        this.dataSource.data = response.data
        this.displayedColumns = ['tenantId', 'event', 'createTs','startime','endtime', 'status'];
        this.pageSizes = this.utils.getPageSizes(this.dataSource.data )
        this.dataSource.paginator = this.paginator;
      }
    });
  }

  getErrorLog(eleObj) {
    let obj = this.user;
    obj["masterDataUpdateId"] = eleObj["id"]
    this.masterDataService.getErrorLog(obj).subscribe((response: any) => {
      if (response.success) {
        var downloadLink = document.createElement("a");
        downloadLink.href = 'data:application/txt;base64,' + response.eFile;
        downloadLink.download = "error_log_" + eleObj["id"] + ".txt";
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
      } else {
        this.utils.snackBarShowSuccess(response.message);
      }
    });
  }

}

