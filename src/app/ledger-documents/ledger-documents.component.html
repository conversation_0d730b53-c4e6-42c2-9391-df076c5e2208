<div class="row title">
  <form [formGroup]="ledgerForm">
    <!-- <mat-form-field  appearance="none"
      class="matFormFieldTopItems">
      <mat-select placeholder="Select Branch" formControlName="branchSelection"
        (selectionChange)="getSpoilageData()" class="outline">
        <mat-option *ngFor="let rest of branchesData" [value]="rest">
          {{ rest.branchName }}
        </mat-option>
      </mat-select>
    </mat-form-field> -->

    <mat-form-field appearance="none" style="position: relative; top: -13px;">
      <mat-select placeholder="Select Branch" formControlName="branchSelection" class="outline" (selectionChange)="getSpoilageData()">
        <mat-option *ngFor="let rest of branches" [value]="rest">
          {{ rest.branchName }}
        </mat-option>
      </mat-select>
    </mat-form-field>


  <mat-form-field appearance="none" class="ml-2 matFormFieldTopItems">
    <input matInput class="outline" [matDatepicker]="picker1" placeholder="Start Date" formControlName="startDate" />
    <mat-datepicker-toggle matSuffix [for]="picker1">
      <mat-icon matDatepickerToggleIcon>
        <img src="./../../assets/calender.png" />
      </mat-icon>
    </mat-datepicker-toggle>
    <mat-datepicker #picker1></mat-datepicker>
  </mat-form-field>

  <mat-form-field appearance="none" class="ml-2 matFormFieldTopItems">
    <input matInput class="outline" [matDatepicker]="picker2" formControlName="endDate" placeholder="End Date"
      [readonly]="!this.ledgerForm.value.startDate" [disabled]="!this.ledgerForm.value.startDate" [min]="this.ledgerForm.value.startDate" />
    <mat-datepicker-toggle matSuffix [for]="picker2">
      <mat-icon matDatepickerToggleIcon>
        <img src="./../../assets/calender.png" />
      </mat-icon>
    </mat-datepicker-toggle>
    <mat-datepicker #picker2></mat-datepicker>
  </mat-form-field>

</form>

  <button mat-stroked-button id="save-btn"
    class="button3 findAndClearBtn" (click)="filterByDate()">Find</button>

  <button mat-stroked-button id="save-btn"
    class="button3 findAndClearBtn" (click)="clearDate()">Clear</button>
</div>

<div class="datacontainer">
  <mat-card class="mt-n2">
    <mat-card-content>
      <div class="search-table-input">
        <mat-form-field appearance="none">
          <label>Search</label>
          <input matInput type="text" class="outline" placeholder="Search" [(ngModel)]='searchText'
            (keyup)="doFilter($event.target.value)" />
          <!-- <mat-icon matSuffix (click)="resetForm()" class="closebtn">close</mat-icon> -->
        </mat-form-field>

        <button mat-button class="buttonForRefresh refreshBtn" (click)="refreshData()">Refresh</button>

      </div>
      <table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort>
        <ng-container matColumnDef="id">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> id </b>
          </th>
          <td mat-cell *matCellDef="let element" class="links" (click)="goToDetails(element)">
            {{element.id}}
          </td>
        </ng-container>


        <ng-container matColumnDef="user" >
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>user</b>
          </th>
          <td mat-cell *matCellDef="let element">
           {{element.email.split("@")[0]}} 
          </td>
        </ng-container>

        <ng-container matColumnDef="eta">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> createTs</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ this.utils.formatDateToUTC(element.createTs) }}
          </td>
        </ng-container>

        <ng-container matColumnDef="workArea">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>workArea</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.indentArea }} 
          </td>
        </ng-container>

        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Status</b>
          </th>
          <td mat-cell *matCellDef="let element"> {{element.status}}</td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>
      <!-- <div class="dataMessage" *ngIf="dataSource?.data?.length == 0"> No Data Available </div> -->
      <mat-paginator [showTotalPages]="5" [pageSize]="10" [pageSizeOptions]="pageSizes"></mat-paginator>
    </mat-card-content>
  </mat-card>

</div>