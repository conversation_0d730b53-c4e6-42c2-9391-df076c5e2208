@import "../../styles-variables";

fieldset {
  border: 1px solid $primary;
  margin-left: 5%;
  // margin: 0 auto;;
  // legend {
  //   background-color: $primary;
  //   color: #fff;
  //   font-size: 1.0rem;
  //   position: relative;
  //   left: 1%;
  //   width: inherit;
  //   padding: 0.5rem;
  // }
}

.date-field {
  z-index: 1;
  position: relative;
   
  ::ng-deep .mat-form-field-suffix {
    position: absolute;
    bottom: 8px;
    right: -35px;
    
  }
}

.title{
  // align-items: end;
  margin-left: 2%;
  margin-right: 2%;
}

 .check_circle{
  color:green;
  font-size: 17px;
}

.topInput{
  display: flow-root;
  margin-left: 2%;
  margin-right: 2%;
}