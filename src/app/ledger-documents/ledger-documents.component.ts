import { Component, OnInit, ViewChild } from '@angular/core';
import { BranchTransferService } from '../_services/branch-transfer.service';
import { AuthService, ShareDataService } from '../_services';
import { FormControl, FormGroup, FormBuilder, Validators  } from '@angular/forms';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog } from '@angular/material';
export const MY_FORMATS = {
  parse: {
      dateInput: 'LL'
  },
  display: {
      dateInput: 'DD-MM-YYYY',
      monthYearLabel: 'YYYY',
      dateA11yLabel: 'LL',
      monthYearA11yLabel: 'YYYY'
  }
};
import { MomentDateModule, MomentDateAdapter } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material';
import { UtilsService } from '../_utils/utils.service';
import { SharedFilterService } from '../_services/shared-filter.service';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { SimpleDialogComponent } from '../_dialogs/simple-dialog/simple-dialog.component';

@Component({
  selector: 'app-ledger-documents',
  templateUrl: './ledger-documents.component.html',
  styleUrls: ['./ledger-documents.component.scss', "./../../common-dark.scss"],
  providers:[{ provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
            { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }
          ]
})
export class LedgerDocumentsComponent implements OnInit {
  user: any;
  ledgerForm: FormGroup;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  dataSource = new MatTableDataSource();
  @ViewChild(MatSort) sort: MatSort;
  displayedColumns: string[];
  pageSizes = []
  searchText: string;
  sharedFilterData: any = {};
  private unsubscribe$ = new Subject<void>();
  getBranchData: any[]
  branchesData: any[]
  branches: any[];
  deleteAccess: boolean;
  closeAccess: boolean;
  spoilageData: any;
    
  constructor(
    private branchTransfer: BranchTransferService,
    private auth: AuthService,
    private utils: UtilsService,
    private sharedData: ShareDataService,
    private fb: FormBuilder,
    private sharedFilterService: SharedFilterService,
    private router: Router,
    public dialog: MatDialog,
    private activateRoute: ActivatedRoute,
    ) {

    this.user = this.auth.getCurrentUser();

    this.ledgerForm = this.fb.group({
      branchSelection: [null, Validators.required],
      startDate: [null, Validators.required],
      endDate: [null, Validators.required]
    });

    this.sharedFilterService.getLedgerFilter.subscribe(obj => 
      this.sharedFilterData = obj
      
    ); 

    this.sharedData.sharedBranchData.pipe(takeUntil(this.unsubscribe$)).subscribe((val) => {
      this.getBranchData = val;
      if(this.getBranchData.length == 0 ){
        this.branches = this.user.restaurantAccess;
      }else if(this.getBranchData.length == 1){        
        const toSelect = this.getBranchData.find(data => data.branchName == this.getBranchData[0].branchName);
        this.ledgerForm.get('branchSelection').setValue(toSelect);
        this.branches = this.getBranchData
        this.getSpoilageData();
      }else{
        this.branches = this.getBranchData
        this.getSpoilageData();
      }
    });
  }

  ngOnInit() {
    this.refreshData();
  }

  getSpoilageData(){
    if (this.sharedFilterData != '') {
      this.ledgerForm.get('branchSelection').setValue(this.sharedFilterData.restaurantId);
      this.branches = this.getBranchData
      if(this.sharedFilterData.selectedStartDate && this.sharedFilterData.selectedEndDate){
        this.ledgerForm.get('startDate').setValue(this.sharedFilterData.selectedStartDate)
        this.ledgerForm.get('endDate').setValue(this.sharedFilterData.selectedEndDate)
      }
    }  

    let params = {
      tenantId: this.user.tenantId,
      restaurantId: this.ledgerForm.value.branchSelection.restaurantIdOld,
    }
    if(this.ledgerForm.value.startDate && this.ledgerForm.value.endDate){
      params['startDate'] = this.ledgerForm.value.startDate,
      params['endDate'] = this.ledgerForm.value.endDate
    }

    this.branchTransfer.getSpoilageList(params).subscribe(res => {
      this.displayedColumns = ['id', 'user' , 'eta' , 'workArea', 'status', 'action']
      this.spoilageData = res.data

      this.activateRoute.params.subscribe((params: Params) => {
        if (params.spoilageId && this.spoilageData.length > 0){   
          const data = this.spoilageData.find(data => data.id == params.spoilageId);
          if (data){
            this.sharedData.changeLedger(data);
            this.router.navigate(['/home/<USER>']);
          }
        }    
      });

      this.dataSource.data = res.data
      this.dataSource.sort = this.sort;
      this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
      this.dataSource.paginator = this.paginator;
    })
  }

  filterByDate() {
    this.getSpoilageData();
  }

  clearDate(){
    this.ledgerForm.get('startDate').setValue(null)
    this.ledgerForm.get('endDate').setValue(null)
    this.getSpoilageData();
  }

  refreshData(){
    this.getSpoilageData();
    this.tenantDetail();
  }

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }

  clearSearchText() {
    this.searchText = ''
    this.doFilter(this.searchText)
  }

  goToDetails(element){
    let inputObj = {
      restaurantId: this.ledgerForm.value.branchSelection,
      selectedStartDate: this.ledgerForm.value.startDate,
      selectedEndDate: this.ledgerForm.value.endDate,
      branchFlag: true
    }
    this.sharedFilterService.getLedgerFilter.next(inputObj);
    this.sharedData.changeLedger(element);
    this.router.navigate(['/home/<USER>']);
  }

  deleteSpoilage(element) {  
    let obj = {}
    obj['tenantId'] = element.tenantId
    obj['restaurantId'] = element.restaurantId
    obj['spoilageId'] = element.id
    obj['user'] = this.user.mId
    obj['status'] = element.status
    obj['workArea'] = element.indentArea

    this.dialog.open(SimpleDialogComponent, {
      data: {
        title: 'Delete Spoilage',
        msg: 'Are you sure you want to delete?',
        ok: function () {
          this.branchTransfer.deleteSpoilage(obj).subscribe(res => {
            if (res['result'] === true) {
              this.utils.snackBarShowSuccess(res['message']);
            } else {
              this.utils.snackBarShowError(res['message']);
            }
            this.refreshData();
          })
        }.bind(this)
      }
    });
  }

  updateStatus(element, status){    
    let obj = {}
    obj['tenantId'] = element.tenantId
    obj['restaurantId'] = element.restaurantId
    obj['spoilageId'] = element.id
    obj['user'] = this.user.mId
    obj['status'] = status
    this.dialog.open(SimpleDialogComponent, {
      data: {
        title: 'Close Spoilage',
        msg: 'Are you sure you want to Close?',
        ok: function () {
          this.branchTransfer.updateSpoilageStatus(obj).subscribe(res => {
            if (res['result'] === true) {
              this.utils.snackBarShowSuccess(res['message']);
            } else {
              this.utils.snackBarShowError(res['message']);
            }
            this.refreshData();
          })
        }.bind(this)
      }
    });
  }

  tenantDetail() {
    let obj = {
      tenantId: this.user.tenantId
    }
    this.branchTransfer.tenantDetails(obj).subscribe(res => {
      if (res.result == 'success') {
        let deleteAccess = res.data[0] && res.data[0].permission && res.data[0].permission.spoilageAccess ? res.data[0].permission.spoilageAccess.delete : false;
        let closeAccess = res.data[0] && res.data[0].permission && res.data[0].permission.spoilageAccess ? res.data[0].permission.spoilageAccess.close : false;

        if (deleteAccess) {
          let rolesList = (res.data[0] && res.data[0].permission && res.data[0].permission.spoilageAccess) ? res.data[0].permission.spoilageAccess.deleteAccess : [];
          this.deleteAccess = (rolesList.some(role => role.toLowerCase() === this.user.role.toLowerCase())) ? true : false ;
        } else {
          this.deleteAccess = false ;
        }
        
        if (closeAccess) {
          let rolesList = (res.data[0] && res.data[0].permission && res.data[0].permission.spoilageAccess) ? res.data[0].permission.spoilageAccess.closeAccess : [];
          this.closeAccess = (rolesList.some(role => role.toLowerCase() === this.user.role.toLowerCase())) ? true : false ;
        } else {
          this.closeAccess = false ;
        }

        if (!this.deleteAccess && !this.closeAccess) {
          if (this.displayedColumns.includes('action')) {
            this.displayedColumns = this.displayedColumns.filter(col => col !== 'action');
          }
        }
      } else {
        this.deleteAccess = false ;
        this.closeAccess = false ;
      }
    })
  }
  
}
