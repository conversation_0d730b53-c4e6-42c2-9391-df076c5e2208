.loading-screen-wrapper {
  z-index: 999;
  position: absolute;
  background-color: rgba(255, 255, 255, 0.6);
  width: 100%;
  height: 100%;
  display: block;
}

.mat-progress-spinner circle, .mat-spinner circle {
  stroke: #3586ca;
}


.loading-screen-icon {
  position: absolute;
  top: 40%;
  left: 55%;
  transform: translate(-50%, -50%);
}

// .message{
//   position: absolute;
//   top: 125%;
//   left: 50%;
//   white-space: nowrap;
//   color: #000;
//   font-size: larger;
//   font-weight: bolder;
//   transform: translate(-50%, -50%);
// }
