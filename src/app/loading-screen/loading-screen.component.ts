import { Component, OnInit, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { LoadingScreenService } from '../_services/';
import { Subscription } from "rxjs";
import { debounceTime} from 'rxjs/operators';

@Component({
  selector: 'app-loading-screen',
  templateUrl: './loading-screen.component.html',
  styleUrls: ['./loading-screen.component.scss']
})
export class LoadingScreenComponent implements OnInit, <PERSON><PERSON><PERSON>roy {
  // message = "Please wait, while we prepare your data.."
  message = "Loading..."
  loading : boolean = false;
  loadingSubscription: Subscription;
  constructor(private loadingScreenService : LoadingScreenService) { }

  ngOnInit() {
    this.loadingSubscription = this.loadingScreenService.loadingStatus.pipe(
      debounceTime(200)
    ).subscribe((value) => {
      this.loading = value;
    });
  }

  ngOnDestroy() {
    this.loadingSubscription.unsubscribe();
  }


}
