$primary: #3586ca;
* {
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
}

body,
html {
  height: 100%;
  font-family: "Public Sans", sans-serif;
  background: #000;
}

/*//////////////////////////////////////////////////////////////////
[ login ]*/
.limiter {
  width: 100%;
  margin: 0 auto;
  padding: 0 !important;
  box-shadow: none;
}

.container-login {
  width: 100%;
  min-height: 100vh;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.wrap-login {
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  flex-direction: row-reverse;
}

/*==================================================================
[ login more ]*/
.login-more {
  width: 56%;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  position: relative;
  z-index: 1;
  height: 100vh;
  //background-image: url("./../../assets/left.png");
}

.login_inner {
  width: 59% !important;
  margin: 0 auto;
  // margin-top: -webkit-calc(100vh - 600px);
  // margin-top: -moz-calc(100vh - 600px);
  // margin-top: calc(100vh - 600px);
  // margin-bottom: -webkit-calc(100vh - 600px);
  // margin-bottom: -moz-calc(100vh - 600px);
  // margin-bottom: calc(100vh - 600px);
}

.login-more img {
  width: 100%;
  height: 100vh;
}

.login-more::before {
  content: "";
  display: block;
  position: absolute;
  z-index: -1;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}
.overlay {
  position: absolute;
  top: 0;
  z-index: 9999;
  //background: #08080a;
  left: 0;
  width: 100%;
  opacity: 0.8;
  //background-image: linear-gradient(from left, #222222 100%);
  background-image: linear-gradient(to top, var(--black) 0%, #222222 100%);
}

/*==================================================================
[ Form ]*/

.login-form {
  width: 44%;
  height: 100vh;
  display: block;
  background-color: #191919;

  /*  padding: 173px 55px 55px 55px;*/
}

.login-form-title {
  width: 100%;
  display: block;
  font-family: Poppins-Regular;
  font-size: 17px;
  line-height: 1.2;
  text-align: center;
  margin: 10% auto 40px auto;
}

.login-fields {
  width: 300px;
  margin: 0px auto;
}

.copyright {
  width: 370px;
  font-size: 12px;
  position: absolute;
  left: 26%;
  bottom: 1%;
}
.copyright label {
  color: $primary  !important;
}

@media (max-width: 1440px) {
  .login-more img {
    width: 100%;
  }
}
@media (max-height: 600px) {
  .left-image {
    width: 100%;
    height: 150% !important;
  }
  .centered {
    top: 75% !important;
    left: 52% !important;
  }
}
@media (max-width: 992px) {
  .login-form {
    width: 44%;
    padding-left: 43px;
  }

  .login-more {
    width: 50%;
  }
}

@media (max-width: 1024px) {
  .login-form {
    width: 44% !important;
  }

  .login-more {
    width: 56% !important;
  }
  .login-more img {
    width: 100%;
    height: 100vh;
  }
  .centered {
    top: 60% !important;
    left: 52% !important;
  }
  .login_inner {
    width: 77% !important;
    margin: 0 auto;
  }
  .login-form-title {
    margin: 26% auto 40px auto !important;
  }
  .Rectangle-44 {
    width: 300px !important;
  }
  .copyright {
    left: 20% !important;
  }
}

@media (max-width: 768px) {
  .login-form {
    width: 44%;
  }
  .login-more {
    width: 56%;
    height: 100vh;
  }
  .login-more img {
    height: 100vh;
  }
  .centered {
    top: 50% !important;
  }
  .Rectangle-44 {
    width: 250px !important;
  }

  .heading {
    font-size: 25px !important;
    margin-left: 0px !important;
  }
  .para {
    font-size: 14px !important;
    margin-left: 0px !important;
  }
  .forecast {
    font-size: 12px !important;
  }
  .login_inner {
    margin: 0px auto !important;
  }
}
@media (max-width: 893px) {
  .login_inner {
    width: 100% !important;
    margin: 0 auto;
  }
}

@media (max-width: 576px) {
  .login-form {
    padding-left: 15px;
    padding-right: 15px;
    padding-top: 70px;
    width: 100% !important;
  }
  .login-more {
    display: none;
  }
  .logo-img {
    margin-top: 0px !important;
    height: 100px !important;
  }

}


/*------------------------------------------------------------------*/
@media (max-width: 992px) {
  .alert-validate::before {
    visibility: visible;
    opacity: 1;
  }
}

// .logo-img {
//   height: 160px;
// }
.Rectangle-44 {
  width: 350px;
  height: 50px;
  border-radius: 4px;
  border: solid 1px #2f2f2f;
  background-color: #2f2f2f;
  display: block;
  color: white !important;
  font-size: 16px;
  font-stretch: normal;
  font-style: normal;
  padding: 0.5rem 0.75rem;
}
.Tenant-ID {
  width: 100px;
  height: 16px;
  opacity: 0.4;
  font-family: "Public Sans", sans-serif;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 2.79;
  letter-spacing: normal;
  text-align: left;
  color: white;
  margin-top: 20px;
  margin-bottom: 18px;
  /* margin-bottom: 30px;
    margin-left: 155px;*/
}
input::placeholder {
  color: white !important;
  font-family: "Public Sans", sans-serif;
  font-size: 14px;
  font-weight: 400;
  font-stretch: normal;
  font-style: normal;
  line-height: 2.44;
  letter-spacing: normal;
  text-align: left;
}
.Rectangle-9 {
  width: 120px;
  height: 42px;
  border-radius: 50px;
  font-family: "Public Sans", sans-serif;
  text-align: center;
  display: block;
  margin: 40px auto;
  background: $primary  !important;
}
.form-control:focus {
  color: #495057;
  border-color: $primary  !important;
  background: black !important;
  outline: 0;
}
.m-b-10 {
  margin-bottom: 45px;
}
.centered {
  width: 75%;
  position: absolute;
  top: 56%;
  z-index: 999999;
  left: 46%;
  transform: translate(-50%, -50%);
}
.para {
  font-family: "Public Sans", sans-serif;
  font-size: 16px;
  font-weight: 300;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.5;
  letter-spacing: normal;
  text-align: left;
  color: white;
  margin-top: 33px;
  margin-bottom: 50px;
  margin-left: 30px;
}
.heading {
  font-family: "Public Sans", sans-serif;
  font-size: 32px;
  font-weight: 300;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.2;
  letter-spacing: normal;
  text-align: left;
  color: $primary !important;
  margin-left: 30px;
}
.img10 {
  text-align: center;
  display: block;
  margin: 0px auto;
}
.forecast {
  color: white;
  margin-top: 15px;
  text-align: center;
  font-size: 16px;
  font-weight: 300;
}
.icon-box {
  margin-bottom: 50px;
}
