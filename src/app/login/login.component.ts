import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from '../_services/auth.service';
import { GlobalsService } from '../_services'
@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})
export class LoginComponent implements OnInit {
  loginForm: FormGroup;
  vendorLoginForm: FormGroup;
  title: string = 'Digitory';
  submitAttempted: boolean = false;
  isVendor = false;
  constructor(private fb: FormBuilder,
    private router: Router,
    private auth: AuthService) { }

  ngOnInit() {
    localStorage.clear();
    sessionStorage.clear();
    this.loginForm = this.fb.group({
      tenantId: ['', Validators.required],
      email: ['', Validators.required],
      password: ['', Validators.required],
    });

    this.vendorLoginForm = this.fb.group({
      email: ['', Validators.required],
      password: ['', Validators.required],
    });
  }

  login() {
    let loginReqObj: any = {};

    if (this.isVendor) {
      if (this.vendorLoginForm.valid) {
        loginReqObj = this.vendorLoginForm.value;
        loginReqObj.uType = GlobalsService.vendor;
        this.auth.login(loginReqObj).subscribe(res => {
          console.log('res from api', res);

          if (res)
            this.router.navigate(['home']);
          else {
          }
        }, err => {
          console.log(err);
        });
      }

    }
    else {
      if (this.loginForm.valid) {
        loginReqObj = this.loginForm.value;
        loginReqObj.uType = GlobalsService.restaurant;
        this.auth.login(loginReqObj).subscribe(res => {
          if (res)
            this.router.navigate(['home']);
          else {
          }
        }, err => {
          console.log(err);
        });

      }

    }


    this.submitAttempted = true;

  }

  isFieldInvalid(field: any) {

    let isInvalid: boolean = (
      !this.loginForm.get(field).valid && this.loginForm.get(field).touched
    ) || (this.loginForm.get(field).untouched && this.submitAttempted);
    return isInvalid;
  }


}
