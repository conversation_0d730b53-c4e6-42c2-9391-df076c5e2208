<div class="app-container">
  <mat-toolbar class="app-toolbar">
    <button mat-icon-button (click)="sideNav.toggle()">
      <mat-icon>menu</mat-icon>
    </button>
    <span>Digitory</span>
    <span class="example-spacer"> </span>
    <div class="mr-2" style=" background: #242424 !important;">
      <mat-form-field appearance="fill"
        style=" background: #242424 !important;font-size: 13px !important; width: 250px;">
        <mat-select placeholder="Select Branches" [formControl]="userType" multiple>
          <mat-option>
            <ngx-mat-select-search placeholderLabel="search branch" noEntriesFoundLabel="'no branch found'"
              [formControl]="vendorFilterCtrl">
            </ngx-mat-select-search>
          </mat-option>
          <mat-option *ngIf="this.user.restaurantAccess && this.user.restaurantAccess.length > 1" #allSelected
            (click)="toggleAllSelection()" [value]="1">All</mat-option>
          <mat-option *ngFor="let rest of vendorsBanks | async" [value]="rest.branchName" (click)="filterByBranches()">
            {{rest.branchName}}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>

    <p class="formal-text">{{ versionNumber }}</p>

    <mat-menu #menu="matMenu">
      <div mat-menu-item *ngFor="let item of notifications" style="cursor: default;">
        <span (click)="download(item)" style="cursor: pointer;" matTooltip="click to download">{{item?.message}}</span>
        <span (click)="deleteNotification(item?._id.$oid)"
          style="cursor: pointer;float:right; padding: 12px 0px 0px 20px;">
          <mat-icon>close</mat-icon>
        </span>
      </div>
    </mat-menu>

    <button mat-button [matMenuTriggerFor]="beforeMenu" class="menu_buttons menu_buttons2">
      <mat-icon style="margin-top: 5px !important;">account_circle</mat-icon>
      <span class="inlspn inlspn2">{{ user.name }}</span>
      <span class="inlspn inlspn3">{{ cardDesc }}</span>
    </button>
    <mat-menu #beforeMenu="matMenu" xPosition="after">
        <!-- <button mat-menu-item (click)="openSetting()" *ngIf="checkUserRole()">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-gear" viewBox="0 0 16 16">
            <path d="M8 4.754a3.246 3.246 0 1 0 0 6.492 3.246 3.246 0 0 0 0-6.492M5.754 8a2.246 2.246 0 1 1 4.492 0 2.246 2.246 0 0 1-4.492 0"/>
            <path d="M9.796 1.343c-.527-1.79-3.065-1.79-3.592 0l-.094.319a.873.873 0 0 1-1.255.52l-.292-.16c-1.64-.892-3.433.902-2.54 2.541l.159.292a.873.873 0 0 1-.52 1.255l-.319.094c-1.79.527-1.79 3.065 0 3.592l.319.094a.873.873 0 0 1 .52 1.255l-.16.292c-.892 1.64.901 3.434 2.541 2.54l.292-.159a.873.873 0 0 1 1.255.52l.094.319c.527 1.79 3.065 1.79 3.592 0l.094-.319a.873.873 0 0 1 1.255-.52l.292.16c1.64.893 3.434-.902 2.54-2.541l-.159-.292a.873.873 0 0 1 .52-1.255l.319-.094c1.79-.527 1.79-3.065 0-3.592l-.319-.094a.873.873 0 0 1-.52-1.255l.16-.292c.893-1.64-.902-3.433-2.541-2.54l-.292.159a.873.873 0 0 1-1.255-.52zm-2.633.283c.246-.835 1.428-.835 1.674 0l.094.319a1.873 1.873 0 0 0 2.693 1.115l.291-.16c.764-.415 1.6.42 1.184 1.185l-.159.292a1.873 1.873 0 0 0 1.116 2.692l.318.094c.835.246.835 1.428 0 1.674l-.319.094a1.873 1.873 0 0 0-1.115 2.693l.16.291c.415.764-.42 1.6-1.185 1.184l-.291-.159a1.873 1.873 0 0 0-2.693 1.116l-.094.318c-.246.835-1.428.835-1.674 0l-.094-.319a1.873 1.873 0 0 0-2.692-1.115l-.292.16c-.764.415-1.6-.42-1.184-1.185l.159-.291A1.873 1.873 0 0 0 1.945 8.93l-.319-.094c-.835-.246-.835-1.428 0-1.674l.319-.094A1.873 1.873 0 0 0 3.06 4.377l-.16-.292c-.415-.764.42-1.6 1.185-1.184l.292.159a1.873 1.873 0 0 0 2.692-1.115z"/>
          </svg>
          Setting</button> -->
          <button mat-menu-item (click)="logout()">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
              class="bi bi-box-arrow-left mr-1" viewBox="0 0 16 16">
              <path fill-rule="evenodd"
                d="M6 12.5a.5.5 0 0 0 .5.5h8a.5.5 0 0 0 .5-.5v-9a.5.5 0 0 0-.5-.5h-8a.5.5 0 0 0-.5.5v2a.5.5 0 0 1-1 0v-2A1.5 1.5 0 0 1 6.5 2h8A1.5 1.5 0 0 1 16 3.5v9a1.5 1.5 0 0 1-1.5 1.5h-8A1.5 1.5 0 0 1 5 12.5v-2a.5.5 0 0 1 1 0v2z" />
              <path fill-rule="evenodd"
                d="M.146 8.354a.5.5 0 0 1 0-.708l3-3a.5.5 0 1 1 .708.708L1.707 7.5H10.5a.5.5 0 0 1 0 1H1.707l2.147 2.146a.5.5 0 0 1-.708.708l-3-3z" />
            </svg>
            Logout</button>
    </mat-menu>
  </mat-toolbar>

  <mat-sidenav-container class="app-sidenav-container sb-sidenav">
    <mat-sidenav class="sb-sidenav-menu" #sideNav opened="true" class="app-sideNav"
      [mode]="mobileQuery.matches ? 'over' : 'side'">
      <div class="nav">
        <div class="sb-sidenav-menu-heading">
          <img class="image image-contain" [src]="logoUrl" alt="Existing Logo" >
          <div class="userinfo">
            <span class="inlspn inlspn4">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                class="bi bi-person-fill" viewBox="0 0 16 16">
                <path d="M3 14s-1 0-1-1 1-4 6-4 6 3 6 4-1 1-1 1H3Zm5-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z" />
              </svg>
              {{ cardDesc | titlecase }} </span>
          </div>
        </div>
      </div>

      <mat-accordion multi="true" *ngIf="!showBanner">
        <section class="example-container-1 mat-elevation-z8">
          <mat-expansion-panel *ngFor="let link of navLinks; let i = index" hideToggle="true"
            class="containerRoutingdata">
            <mat-expansion-panel-header *ngIf="!link.path" (click)="toggleMenu(i)">
              <div class="mr-2">
                {{ link.name | titlecase }}
              </div>
              <div>
                <mat-icon *ngIf="!link.panelOpenState">add_box</mat-icon>
                <mat-icon *ngIf="link.panelOpenState">indeterminate_check_box</mat-icon>
              </div>
            </mat-expansion-panel-header>
            <mat-nav-list>
              <ng-container *ngFor="let child of link.children">
                <a mat-list-item routerLink="{{ child.path }}" routerLinkActive="active-link"
                  matTooltip="{{ child.tooltip }}">

                  <div class="routingDatas">
                    <div class="row">
                      <div *ngIf="child.name?.split(' ')[1]" class="ml-0 Thirdletter">
                        <div class="ml-0">
                          {{ child.name.split(" ")[0][0] | uppercase }}
                        </div>
                        <div class="ml-0">
                          {{ child.name?.split(" ")[1][0] | uppercase }}
                        </div>
                        <div *ngIf="child.name?.split(' ')[2]" class="ml-0">
                          {{ child.name?.split(" ")[2][0] | uppercase }}
                        </div>
                      </div>
                      <div *ngIf="!child.name?.split(' ')[1]">
                        {{ child.name | uppercase }}
                      </div>
                    </div>
                    <div class="row mainName">
                      {{ child.name | titlecase }}
                    </div>
                  </div>
                </a>
              </ng-container>
            </mat-nav-list>
          </mat-expansion-panel>
        </section>
        <div class="bottomText">
          <span>
            Powered By Digitory</span>
        </div>
      </mat-accordion>
    </mat-sidenav>
    <mat-sidenav-content>
      <router-outlet></router-outlet>

      <div class="closingContainer" *ngIf="showBanner">
        <div class="closingContainerDatas">
            <mat-card >
                <div class="closeMsg">
                    {{message}}
                </div>
                <!-- <div class="closeMsgBtn mt-2">
                    <img src="../../assets/cache.png" alt="cache">
                </div> -->
                <div class="text-align-center text-center m-3">
                    <button mat-button mat-raised-button (click)="refreshPage()">
                     click to update </button>
                </div>
            </mat-card>
        </div>
      </div>
    </mat-sidenav-content>
  </mat-sidenav-container>
</div>

<div class="notification-banner" *ngIf="showMaintanance">
  <span class="message">{{ messageMaintanance }}</span>
  <button class="close-icon" (click)="closeBanner()" matTooltip="close">
    <span>&times;</span>
  </button>
</div>