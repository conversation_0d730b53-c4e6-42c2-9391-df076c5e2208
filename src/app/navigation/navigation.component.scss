@import "../../styles-variables";

.app-container {
  display: flex;
  flex-direction: column;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.inlspn {
  display: block;
  line-height: 10px;
  text-align: left;
  margin-left: 5px;
  margin-bottom: 5px;
  margin-top: 4px;
}
.inlspn1 {
  font-size: 16px !important;
}
.inlspn2 {
  font-size: 10px !important;
}
.inlspn3 {
  font-size: 10px !important;
  opacity: 0.55;
  font-weight: normal;
}
.inlspn4 {
  font-size: 14px !important;
  opacity: 0.55;
  font-weight: normal;
}
.mat-icon {
  // background-color: #2f2f2f;
  background-color: transparent;
  float: left !important;
  margin-right: 10px;
  color: #868e96;
}

.mat-icon2 {
  background-color: #464646 !important;
  height: 18px !important;
  width: 18px !important;
  font-size: 18px !important;
  margin-top: 4px;
}

.mat-menu {
  margin-right: 15px !important;
}

.menu_buttons {
  min-width: auto !important;
  line-height: 36px;
  padding: 0 !important;
  margin: 0 10px !important;
}

.menu_buttons2 {
  margin: 0 24px !important;
}

.mat-nav-list .mat-list-item:hover {
  background: #ffffff;
  color: black;
}
.app-is-mobile .app-toolbar {
  position: fixed;
  /* Make sure the toolbar will stay on top of the content as it scrolls past. */
  z-index: 2;
}

.mat-expansion-panel-header {
  height: 45px !important;
  // font-size: 14px !important;
  font-size: small !important;
  font-weight: 300 !important;
  padding: 0px 15px;
}


.mat-list-base .mat-list-item {
  font-size: 14px !important;
  font-weight: 300 !important;
  padding: 1px 0px !important;
  margin-bottom: 8px !important;
}

.app-toolbar {
  background-color: #2f2f2f !important;
  height: 44px;
}

div.mat-drawer-inner-container::-webkit-scrollbar {
  width: 10px;
}

/* Track */
div.mat-drawer-inner-container::-webkit-scrollbar-track {
  background: #f1f1f1;
}

/* Handle */
div.mat-drawer-inner-container::-webkit-scrollbar-thumb {
  background: #888;
}

.userinfo {
  margin-top: 15px;
}

/* Handle on hover */
div.mat-drawer-inner-container::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.sb-sidenav-menu-heading {
  margin: 0 auto;
  padding: 1rem 0;
  font-size: 0.75rem !important;
  font-weight: 500 !important;
  text-align: center;
}

.image-100002 {
  border-radius: 3px;
}

.image-100003 {
  border-radius: 3px;
  // width: 160px;
  // height: 120px;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

h1.app-name {
  margin-left: 8px;
}

.app-sidenav-container {
  flex: 1;
}
.mat-list-item {
  height: 36px !important;
  font-size: 14px !important;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.14;
  letter-spacing: normal;
  text-align: left;
}

::ng-deep .mat-expansion-panel-body {
  padding: 0px !important;
}

.app-is-mobile .app-sidenav-container {
  flex: 1 0 auto;
}

mat-sidenav.app-sideNav {
  width: 225px;
  background-color: #272727 !important;
}

.example-spacer {
  flex: 1 1 auto;
}

.mat-expansion-panel {
  background-color: #272727 !important;
  border-top: 1px solid #464646 !important;
  box-shadow: none !important;
}

// ::ng-deep .app-container .mat-sidenav-content {
//   overflow: hidden;
// }

mat-expansion-panel-header div {
  // padding-left: 4%;
  padding-top: 1.4%;
  opacity: 0.6;
}
.mat-list-item-content {
  padding: 0px !important;
  margin-left: 15px !important;
  margin-right: 15px !important;
}
.mat-list-item {
  padding: 5px !important;
}

.active-panel {
  color: $primary  !important;
}

mat-card-subtitle {
  text-transform: capitalize;
}
.notification {
  position: relative;
  display: inline-block;
  border-radius: 2px;
  font-size: 21px;
  color: #c7c7c7 !important;
  margin-top: 5px;
}

.notification .badge {
  position: absolute;
  top: 2px;
  right: -6px;
  padding: 3px 0px;
  border-radius: 50%;
  background-color: $primary !important;
  color: white !important;
  height: 14px;
  width: 14px;
  color: white !important;
  font-size: 9px !important;
}

@media (max-width: 720px) {
  mat-sidenav.app-sideNav {
    min-width: 360px;
  }
}
.cdk-overlay-connected-position-bounding-box {
  ::ng-deep .cdk-overlay-pane {
    margin-right: -20px !important;
  }
}
.cdk-focused,
.cdk-mouse-focused,
.cdk-keyboard-focused,
.mat-button {
  border: 0px !important;
}
@media (max-width: 1024px) {
  mat-sidenav.app-sideNav {
    width: 20% !important;
    background-color: #272727 !important;
  }
  .mat-expansion-panel-header {
    height: 60px !important;
    font-size: small !important;
    font-weight: 300 !important;
    padding: 0px 15px;
}
}

.image {
  width: 8rem;
  height: 7rem;
}

.image-contain {
  object-fit: contain;
  object-position: center;
}

// ::ng-deep .ng-tns-c9-1.ng-trigger.ng-trigger-transformPanel.mat-select-panel.mat-primary.mat-select-search-panel {
//   font-size: 13px !important;
// }

// ::ng-deep .mat-select-value {
//   font-size: 13px !important;
// }


::ng-deep .mat-content{
  justify-content: space-between !important;
}

.Thirdletter{
  display: flex;
}

.mainName{
  font-size: smaller;
}

.routingDatas{
  margin-left: 22px !important;
  margin-right: 10px !important;
  display: table;
}

.example-container-1{
  height: 515px;
  overflow: auto;
  direction: rtl;
}

.containerRoutingdata{
  direction: ltr !important;
}

::-webkit-scrollbar {
  width: 5px;
  overflow-y: scroll;
  background: grey;
  box-shadow: inset 0 0 4px #707070;
}

::-webkit-scrollbar-thumb {
  background: #3586ca;
  border-radius: 10px;
}

.example-container {
  width: 500px;
  height: 300px;
  border: 1px solid rgba(0, 0, 0, 0.5);
}

.example-sidenav-content {
  display: flex;
  height: 100%;
  align-items: center;
  justify-content: center;
}

.example-sidenav {
  padding: 20px;
}

::ng-deep .mat-drawer-inner-container {
  overflow: hidden !important;
}

.bottomText{
  text-align: center;
  font-size: smaller;
  // margin-top: 7px;
  margin: 10px;
}

.digiImage{
  height: 18px;
}

.formal-text {
  font-family: Arial, sans-serif;
  font-size: 16px;
  text-align: justify;
  line-height: 1.5;
  margin: 20px 0;
  padding: 10px;
  color: white;
}


.notification-banner {
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: #ffcc00;
  color: #333;
  padding: 2px 20px;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);
}

.message {
  flex-grow: 1;
  text-align: center;
  font-weight: bold;
}

.close-icon {
  background: none;
  border: none;
  color: #333;
  font-size: 30px;
  cursor: pointer;
}

.closingContainer{
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 92vh;
}

.closingContainerDatas{
  max-width: 80vw;
  pointer-events: auto;
  width: 550px;
  position: static;
}

.closeMsg{
  text-align: center;
  font-weight: bold;
  padding-top: 2rem;
  padding-bottom: 1rem;
}

.closeMsgBtn{
  text-align: center;
}
