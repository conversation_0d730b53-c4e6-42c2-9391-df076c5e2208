<h3>
  {{title}}
</h3>

<div fxLayout fxLayoutAlign="space-between center" class="search-table-input">
  <mat-form-field fxFlex="10%">
    <input matInput type="text" autocomplete="false" (keyup)="doFilter($event.target.value)" [(ngModel)]='searchText' placeholder="Search">
    <!-- <mat-icon matSuffix>search</mat-icon> -->
    <mat-icon matSuffix (click)="resetForm()" class="closebtn">close</mat-icon>
  </mat-form-field>

  <div style="font-size : 20px" *ngIf="!isVendor || false">
    <b>Total(Rs.in Lakhs) : {{this.utils.truncateNew(totalOrderValue)}}</b>
  </div>
  <div fxLayout fxLayoutAlign="space-between center">
    <mat-slide-toggle (input)="sliderDetailsChange()" [(ngModel)]="isVendor">More Details</mat-slide-toggle>

    <button fxLayout fxLayoutAlign="end end" mat-button mat-raised-button id="save-btn" color="primary"
      *ngIf="!isVendor" (click)="createPrs()">Order</button>
    <button mat-button mat-raised-button id="save-btn" color="primary" (click)="saveOrders()">Save</button>
  </div>


</div>

<table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort>



  <ng-container matColumnDef="select">
    <th mat-header-cell *matHeaderCellDef>
      <mat-checkbox #selectAll (change)="$event ? masterToggle() : null"
        [checked]="selection.hasValue() && isAllSelected()" [indeterminate]="selection.hasValue() && !isAllSelected()"
        [aria-label]="checkboxLabel()">
      </mat-checkbox>
    </th>
    <td mat-cell *matCellDef="let row">
      <mat-checkbox (change)="$event ? selection.toggle(row) : null; getTotal($event)"
        [checked]="selection.isSelected(row)" [aria-label]="checkboxLabel(row)">
      </mat-checkbox>
    </td>
  </ng-container>


  <ng-container matColumnDef="index">
    <th mat-header-cell *matHeaderCellDef class="tableId"><b>#</b></th>
    <td mat-cell *matCellDef="let element; let i = index;" class="name-cell tableId">{{i + 1}}</td>
  </ng-container>

  <ng-container matColumnDef="itemName" sticky>
    <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell"><b> Item Name</b> </th>
    <td mat-cell *matCellDef="let element" class="name-cell"> {{element.itemName | titlecase}} </td>
    <mat-divider></mat-divider>
  </ng-container>

  <ng-container matColumnDef="onHand">

    <th mat-header-cell *matHeaderCellDef mat-sort-header><b> On Hand </b></th>
    <td mat-cell *matCellDef="let element"> {{ this.utils.truncateNew(element.onHand) }} </td>
  </ng-container>

  <ng-container matColumnDef="optStock">
    <th mat-header-cell *matHeaderCellDef mat-sort-header> <b>Optimum Stock </b></th>
    <td mat-cell *matCellDef="let element"> {{element.optimumStock}} </td>
  </ng-container>


  <ng-container matColumnDef="openOrders">
    <th mat-header-cell *matHeaderCellDef mat-sort-header> <b>Open Orders</b> </th>
    <td mat-cell *matCellDef="let element" class="estimated-cell">
      {{element.onOrder}}
    </td>
  </ng-container>

  <ng-container matColumnDef="reqQty">
    <th mat-header-cell *matHeaderCellDef mat-sort-header> <b>Open to Buy</b> </th>
    <td mat-cell *matCellDef="let element"> {{this.utils.truncateNew(element.otb )}} </td>
  </ng-container>

  <ng-container matColumnDef="orderQty">
    <th mat-header-cell *matHeaderCellDef mat-sort-header> <b>Order Quantity</b> </th>
    <td mat-cell *matCellDef="let element">
      <input (input)="getTotal()" style="text-align: center;width:100%" type="number" [(ngModel)]="element.orderQty"
        *ngIf="!isVendor">
      <div *ngIf="isVendor">
        {{this.utils.truncateNew(element.orderQty)}}
      </div>
    </td>
  </ng-container>

  <ng-container matColumnDef="unit">
    <th mat-header-cell *matHeaderCellDef mat-sort-header> <b>Unit</b> </th>
    <td mat-cell *matCellDef="let element">{{element.uom}} </td>
  </ng-container>

  <ng-container matColumnDef="leadTime">
    <th mat-header-cell *matHeaderCellDef mat-sort-header> <b>Lead Time(days)</b></th>
    <td mat-cell *matCellDef="let element"> {{this.utils.truncateNew(element.leadTime)}} </td>
  </ng-container>

  <ng-container matColumnDef="openToBuy">
    <th mat-header-cell *matHeaderCellDef mat-sort-header> <b>Open To Buy</b> </th>
    <td mat-cell *matCellDef="let element"> {{this.utils.truncateNew(element.openToBuy)}} </td>
  </ng-container>

  <ng-container matColumnDef="totalValue">
    <th mat-header-cell *matHeaderCellDef mat-sort-header><b> Total Value</b></th>
    <td mat-cell *matCellDef="let element">
      <div *ngIf="!isVendor">
        {{this.utils.truncateNew(element.unitPrice * element.otb)}}
      </div>
      <div *ngIf="isVendor">
        {{this.utils.truncateNew(element.unitPrice * element.deliverableQty)}}
      </div>
    </td>
  </ng-container>

  <ng-container matColumnDef="unitPrice">
    <th mat-header-cell *matHeaderCellDef mat-sort-header><b> Unit Cost</b></th>
    <td mat-cell *matCellDef="let element">
      <input *ngIf="!(inputObj.tableType == 1)" (input)="getTotal()" style="text-align: center; width:75%" type="number"
        [(ngModel)]="element.unitPrice">
      <div *ngIf="inputObj.tableType == 1">
        {{this.utils.truncateNew(element.unitPrice)}}
      </div>
    </td>
  </ng-container>

  <ng-container matColumnDef="quotedUnitPrice">
    <th mat-header-cell *matHeaderCellDef mat-sort-header><b> Unit Cost</b></th>
    <td mat-cell *matCellDef="let element">
      <input *ngIf="!(inputObj.tableType == 1)" (input)="getTotal()" style="text-align: center;" type="number"
        [(ngModel)]="element.unitPrice">
      <div *ngIf="inputObj.tableType == 1" [style.color]="isGreater(element) ">
        {{this.utils.truncateNew(element.quotedUnitPrice)}}
      </div>
    </td>
  </ng-container>

  <ng-container matColumnDef="itemCode">
    <th mat-header-cell *matHeaderCellDef mat-sort-header><b> Item Code</b></th>
    <td mat-cell *matCellDef="let element"> {{element.itemCode}} </td>
  </ng-container>

  <ng-container matColumnDef="customerName">
    <th mat-header-cell *matHeaderCellDef mat-sort-header><b> Customer Name</b></th>
    <td mat-cell *matCellDef="let element"> {{element.customerName}} </td>
  </ng-container>
  <ng-container matColumnDef="supplyDate">
    <th mat-header-cell *matHeaderCellDef mat-sort-header><b> Suppply Date</b></th>
    <td mat-cell *matCellDef="let element"> {{element.supplyDate}} </td>
  </ng-container>

  <ng-container matColumnDef="deliverableQty">
    <th mat-header-cell *matHeaderCellDef mat-sort-header><b> Deliverable Quantity</b></th>
    <td mat-cell *matCellDef="let element">
      <input *ngIf="!(inputObj.tableType == 1)" (input)="getTotal()" style="text-align: center;" type="number"
        [(ngModel)]="element.deliverableQty">
      <div *ngIf="inputObj.tableType == 1">
        {{element.deliverableQty}}
      </div>
    </td>
  </ng-container>

  <ng-container matColumnDef="actionBtns">
    <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Actions</b></th>
    <td mat-cell *matCellDef="let element"> <button mat-icon-button (click)="acceptOrder(inputObj.tableType)">
        <mat-icon>done</mat-icon>
      </button>
    </td>
  </ng-container>

  <ng-container matColumnDef="vendorName">
    <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Vendor</b></th>
    <td mat-cell *matCellDef="let element"> {{element.vendor}} </td>
  </ng-container>

  <ng-container matColumnDef="poId">
    <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Purchase Id</b></th>
    <td mat-cell *matCellDef="let element"> {{element.id}} </td>
  </ng-container>

  <ng-container matColumnDef="status">
    <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Status</b></th>
    <td mat-cell *matCellDef="let element"> {{element.status}} </td>
  </ng-container>
  <ng-container matColumnDef="vendorType">
    <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Vendor Type</b></th>
    <td mat-cell *matCellDef="let element"> {{element.vendorType}} </td>
  </ng-container>

  <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
  <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>


</table>

<div fxLayout fxLayoutAlign="end center" class="search-table-input" style="margin-top : 1.5%;">
  <button mat-button mat-raised-button color="primary" style="margin-right : 1%" (click)="showSpecialOrder()"
    *ngIf="!isVendor">Special Order</button>
  <button mat-button mat-raised-button id="save-btn" color="primary" *ngIf="!isVendor"
    (click)="createPrs()">Order</button>
  <button mat-button mat-raised-button id="save-btn" color="primary" *ngIf="inputObj.tableType == 1"
    (click)="approveQuotes()">Approve Quotes</button>
  <button mat-button mat-raised-button id="save-btn" color="primary" (click)="saveOrders()">Save</button>

</div>