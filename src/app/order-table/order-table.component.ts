import { Component, OnInit, Input, ViewChild, OnChanges } from '@angular/core';
import { PurchaseItem } from '../_models/';
import { GlobalsService, AuthService, PurchasesService } from '../_services';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog } from '@angular/material';
import { SelectionModel } from '@angular/cdk/collections';
import { UtilsService } from '../_utils/utils.service';
import { SimpleDialogComponent } from '../_dialogs/simple-dialog/simple-dialog.component';
import { NotificationService } from '../_services/notification.service';
@Component({
  selector: 'app-order-table',
  templateUrl: './order-table.component.html',
  styleUrls: ['./order-table.component.scss']
})
export class OrderTableComponent implements OnInit, OnChanges {
  @Input() inputObj: any;
  @ViewChild(MatSort) sort: MatSort;
  moreDetails: boolean = true;
  dataSource: MatTableDataSource<any>;
  displayedColumns: string[];
  selection = new SelectionModel<any>(true, []);
  totalOrderValue = 0;
  isVendor: boolean = false;
  title = 'Title';
  hasError = true;
  categories: any[];
  inventoryItems: any[];
  invCategories: any[];
  menuGroups: any[];
  @ViewChild(MatPaginator) paginator: MatPaginator;
  searchText: string;
  searchValue: string;

  constructor(private utils: UtilsService, private auth: AuthService,
    private notifyService: NotificationService,
    private purchases: PurchasesService, private dialog: MatDialog
  ) {

  }
  ngOnChanges() {
    setTimeout(() => {
      this.intializeInputObj()
    }, 2000)
  }
  ngOnInit() {
    setTimeout(() => {
      this.intializeInputObj()
    }, 2000)
    this.masterToggle();

  }
  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }

  resetForm() {
    this.searchText = ''
    this.searchValue = ''
    this.doFilter(this.searchValue)
    this.dataSource.data = this.inventoryItems;
  }
  
  showSpecialOrder() {
    this.utils.snackBarShowSuccess('Special Order Dialog');
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  /** Selects all rows if they are not all selected; otherwise clear selection. */
  masterToggle() {
    this.isAllSelected() ?
      this.selection.clear() :
      this.dataSource.data.forEach(row => this.selection.select(row));
    this.getTotal();

  }

  checkboxLabel(row?: PurchaseItem): string {
    if (!row) {
      return `${this.isAllSelected() ? 'select' : 'deselect'} all`;
    }
    return `${this.selection.isSelected(row) ? 'deselect' : 'select'} row ${row.itemCode + 1}`;
  }

  getTotal(e?: Event) {
    this.selection.selected.forEach(item => {
      item.totalValue = item.otb * item.unitPrice;
    });
    this.totalOrderValue = this.utils.getTotal(this.selection.selected, 'totalValue') / 100000;
  }

  acceptOrder(num) {
    if (!num)
      num = 0;
    switch (num) {
      case 0:
        this.utils.snackBarShowSuccess(`accepting order`);
        break;
      case 1:
        this.utils.snackBarShowSuccess(`approving quote`);
        break

    }

  }

  saveOrders() {
    this.utils.snackBarShowSuccess(`saving orders no of orders : ${this.selection.selected.length}`);
  }

  isGreater(obj) {
    if (obj.unitPrice > obj.quotedUnitPrice)
      return 'green'
    else if (obj.unitPrice === obj.quotedUnitPrice)
      return 'black';
    else
      return 'red'
  }

  approveQuotes() {
    this.utils.snackBarShowSuccess(`number of quotes approved : ${this.selection.selected.length}`);
  }

  private intializeInputObj() {
    this.dataSource = new MatTableDataSource<PurchaseItem>();
    this.inventoryItems = this.inputObj.data.filter
      (item =>
        item.invCategory.name ||
        item.menuGroup.name === "LIQUOR" ||
        item.menuGroup.name === "TOBACCO" ||
        item.menuGroup.name === "MIXERS" ||
        item.menuGroup.name === "BEER" ||
        item.menuGroup.name === "BEVERAGES").map((item: any) => {
          item.issueQty = item.projectedSales
          return item
        }
        );
    this.dataSource.data = this.inventoryItems;
    if (this.inputObj.title)
      this.title = this.inputObj.title;

    if (this.inputObj.isVendor) {
      this.isVendor = this.inputObj.isVendor;
      this.dataSource.data.forEach(item => {
        item.deliverableQty = item.orderQty;
        item.totalValue = item.unitPrice * item.deliverableQty;
      });
    }
    else
      this.dataSource.data.forEach(item => {
        item.orderQty = item.otb;
        item.totalValue = item.unitPrice * item.otb;
      });
    this.displayedColumns = this.inputObj.displayedColumns;
    this.dataSource.sort = this.sort;

    if (this.inputObj.title === "Purchase List")
      this.sliderDetailsChange()
  }


  createPrs() {
    let items = this.selection.selected.filter(item => item.orderQty > 0)
    let obj = {
      items: items,
      restaurantId: this.auth.getCurrentUser().restaurantAccess['0'].restaurantIdOld,
      tenantId: this.auth.getCurrentUser().tenantId,
      uId: this.auth.getCurrentUser().mId
    }
    this.purchases.createPrs(obj).subscribe(data => {
      let prSet = new Set();
      data.prDetails.forEach(pr => prSet.add(pr.vendorName))
      this.dialog.open(SimpleDialogComponent, {
        data: {
          title: 'PRs generated',
          msg: `Total number PRs generated ${data.totalPrs} for ${prSet.size} vendors`,
          ok: function () {
            this.router.navigate(['/home/<USER>']);
          }.bind(this)
        }

      })
    }, err => console.error(err))
  }

  sliderDetailsChange() {
    this.moreDetails = !this.moreDetails;
    if (this.moreDetails)
      this.displayedColumns = this.inputObj.displayedColumns
    else
      this.displayedColumns = GlobalsService.purchaseListLessColumns
  }

  selectCategory(val) {
    let obj = {
      key1: 'invCategory',
      key2: 'name',
      arr: this.inventoryItems,
      val: val.value
    }
    this.dataSource.data = this.utils.filterArrByPropVal(obj)
  }

}
