<div class="title row">
  <div>
    <form [formGroup]="piApprovalForm">
    <mat-form-field *ngIf="multiBranchUser" appearance="none" class="topitem">
      <label>Select Branch</label>
      <mat-select placeholder="Select Branch" formControlName="branchSelection"
        (selectionChange)="filterByBranch($event.value)" class="outline">
        <mat-option *ngFor="let rest of branches" [value]="rest">
          {{ rest.branchName }}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </form>
  </div>

  <div>
    <mat-form-field *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" appearance="none"
      style="margin-left: 10px;" class="topitem">
      <label>Start Date</label>
      <input matInput class="outline" [matDatepicker]="picker1" [(ngModel)]="selectedStartDate" placeholder="Start Date"
        [formControl]="startDate" />
      <mat-datepicker-toggle matSuffix [for]="picker1">
        <mat-icon matDatepickerToggleIcon>
          <img src="./../../assets/calender.png" />
        </mat-icon>
      </mat-datepicker-toggle>
      <mat-datepicker #picker1></mat-datepicker>
    </mat-form-field>
  </div>

  <div>
    <mat-form-field *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" appearance="none"
      style="margin-left: 10px;" class="topitem">
      <label>End Date</label>
      <input matInput class="outline" [matDatepicker]="picker2" [(ngModel)]="selectedEndDate" [formControl]="endDate"
        placeholder="End Date" [disabled]=" !startDate.value" [min]="startDate.value" [readonly]="!startDate.value" />
      <mat-datepicker-toggle matSuffix [for]="picker2">
        <mat-icon matDatepickerToggleIcon>
          <img src="./../../assets/calender.png" />
        </mat-icon>
      </mat-datepicker-toggle>
      <mat-datepicker #picker2></mat-datepicker>
    </mat-form-field>
  </div>

  <div>
    <button *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" id="save-btn" mat-stroked-button
      class="button3" style="margin-top: 24px;padding-left: 0%;padding-right: 0% ;height: 39px;"
      (click)="filterByDate()">Find</button>

      <button *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" id="save-btn" mat-stroked-button
      class="button3" style="margin-top: 24px;padding-left: 0%;padding-right: 0% ;height: 39px;"
      (click)="clear()">Clear</button>
  </div>
</div>

<div *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" class="datacontainer">
  <mat-card>
    <mat-card-content>
      <div class="search-table-input">
        <mat-form-field appearance="none">
          <label>Search</label>
          <input matInput type="text" class="outline" (keyup)="doFilter($event.target.value)" [(ngModel)]='searchText'
            placeholder="Search" />
          <mat-icon matSuffix (click)="resetForm()" class="closebtn">close</mat-icon>
        </mat-form-field>

        <mat-form-field appearance="none">
          <label>Approval Status</label>
          <mat-select placeholder="Approval Status" [(ngModel)]="selectedApprovalStatus" class="outline"
            (selectionChange)="filterByStatus($event.value)">
            <mat-option value="All">All</mat-option>
            <mat-option value="approved">Approved</mat-option>
            <mat-option value="rejected">Rejected</mat-option>
            <mat-option value="pending">Pending</mat-option>
          </mat-select>
        </mat-form-field>

        <button *ngIf="selectedApprovalStatus === 'pending'" mat-stroked-button class="button3"
        (click)="approvePis()" [disabled] = "this.dataSource?.data?.length === 0" style="margin-top: 36px;">
        Approve All</button>

       <button *ngIf="selectedApprovalStatus === 'pending'" mat-stroked-button class="button3"
        (click)="rejectPis()" [disabled] = "this.dataSource?.data?.length === 0" style="margin-top: 36px; margin-left: 15px;">
         Reject All</button>
      </div>

      <table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort>
        <ng-container matColumnDef="select">
          <th mat-header-cell *matHeaderCellDef>
            <mat-checkbox (change)="$event ? masterToggle() : null"
                          [checked]="selection.hasValue() && isAllSelected()"
                          [indeterminate]="selection.hasValue() && !isAllSelected()"  [(ngModel)] = "checkSelection">
            </mat-checkbox>
          </th>
          <td mat-cell *matCellDef="let row">
            <mat-checkbox (click)="$event.stopPropagation()"
                          (change)="$event ? selection.toggle(row) : null"
                          [checked]="selection.isSelected(row)">
            </mat-checkbox>
          </td>
        </ng-container>

        <ng-container matColumnDef="grnId">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>GRN Id</b>
          </th>
          <td mat-cell *matCellDef="let element" class="links" (click)="receiveIndent(element)">
            {{ element.grnId }}
          </td>
        </ng-container>

        <ng-container matColumnDef="vendorName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Vendor Name</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.vendorName }}
          </td>
        </ng-container>

        <ng-container matColumnDef="total">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Total Amount</b>
          </th>
          <td mat-cell *matCellDef="let element">{{ element.grandTotal }}</td>
        </ng-container>

        <ng-container matColumnDef="date">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Requested Date</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.createTs | date: "EEEE, MMMM d, y" }}
          </td>
        </ng-container>

        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Status</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.approvalStatus }}
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>
      <div class="dataMessage" *ngIf="dataSource?.data.length == 0"> No Data Available </div>
      <mat-paginator [showTotalPages]="5" [pageSize]='10' [pageSizeOptions]="pageSizes"></mat-paginator>
    </mat-card-content>
  </mat-card>

</div>