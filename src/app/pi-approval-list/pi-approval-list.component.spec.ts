import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { PiApprovalListComponent } from './pi-approval-list.component';

describe('PiApprovalListComponent', () => {
  let component: PiApprovalListComponent;
  let fixture: ComponentFixture<PiApprovalListComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ PiApprovalListComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PiApprovalListComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
