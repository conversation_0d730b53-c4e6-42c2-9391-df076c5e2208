
import { Component, OnInit, Input, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { GlobalsService } from '../_services';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog } from '@angular/material';
import { SelectionModel } from '@angular/cdk/collections';
import { UtilsService } from '../_utils/utils.service';
import { ShareDataService } from '../_services/share-data.service';
import { PurchasesService, AuthService } from '../_services/';
import { NotificationService } from '../_services/notification.service';
import { FormBuilder, FormControl, FormGroup, Validators } from "@angular/forms";
import { ReplaySubject, Subject } from 'rxjs';
import {  BranchTransferService } from '../_services/';
import { SimpleDialogComponent } from '../_dialogs/simple-dialog/simple-dialog.component';
import { takeUntil } from 'rxjs/operators';
import { SharedFilterService } from '../_services/shared-filter.service';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material';
import {  MomentDateAdapter } from '@angular/material-moment-adapter';
export const MY_FORMATS = {
  parse: {
      dateInput: 'LL'
  },
  display: {
      dateInput: 'DD-MM-YYYY',
      monthYearLabel: 'YYYY',
      dateA11yLabel: 'LL',
      monthYearA11yLabel: 'YYYY'
  }
};

@Component({
  selector: 'app-pi-approval-list',
  templateUrl: './pi-approval-list.component.html',
  styleUrls: ['./pi-approval-list.component.scss',"./../../common-dark.scss"],
  providers:[{ provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
  { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }]
})

export class PiApprovalListComponent implements OnInit {
  indentApprovalList: any = []
  @ViewChild(MatSort) sort: MatSort;
  searchText: string;
  searchValue: string;
  indentArea: any;
  indentAreas: any;
  allData: any = [];
  tempData: any;
  @ViewChild(MatPaginator) set paginator(value: MatPaginator) {
    if (this.dataSource) {
      this.dataSource.paginator = value;
    }
  };
  restaurantId: any;
  selectedBranch: any;
  selectedApprovalStatus: any  = 'pending';
  selectedOrderStatus: any;
  selectedStartDate: any;
  selectedEndDate: any;
  multiBranchUser; branchSelected: boolean;
  dataSource = new MatTableDataSource();
  displayedColumns: string[];
  selection = new SelectionModel<any>(true, []);
  purchaseStatus: string[] = ['All','pending','complete'];
  vendorsList: any[] = ['All'];
  filteredByDateList: any[];
  filterKeys = { status: { orderStatus: 'All' } }
  date: any = { startDate: '', endDate: '' };
  pageSizes: any[];
  user :any;
  purchaseStat = new FormControl();
  vendors = new FormControl();
  startDate = new FormControl();
  endDate = new FormControl();
  branches: any[];
  getBranchData: any[]
  piApprovalForm: FormGroup;
  checkSelection : boolean ;
  dialogRef: any;
  IndentApproval = encodeURI(GlobalsService.csiApproval)
  csiApprovalDetailUrl = encodeURI(GlobalsService.csiApprovalDetail)
  indentReqUrl = encodeURI(GlobalsService.indentRequests)
  indentReqFlag: boolean
  private unsubscribe$ = new Subject<void>();
  sharedFilterData: any = {};

  constructor(
    private utils: UtilsService, 
    private router: Router,
    private auth: AuthService,
    private purchases: PurchasesService,
    private notifyService: NotificationService,
    private sharedData: ShareDataService,
    private fb: FormBuilder,
    private branchTransfer: BranchTransferService,
    private dialog: MatDialog,
    private sharedFilterService: SharedFilterService

    ) {
    this.displayedColumns = ['select','grnId','vendorName','total','date','status'];
    this.user = this.auth.getCurrentUser();
    this.multiBranchUser = this.user.multiBranchUser;
    this.piApprovalForm = this.fb.group({
      branchSelection: [null, Validators.required]
    });
    this.sharedFilterService.getFilteredCsi.pipe(
      takeUntil(this.unsubscribe$)
    ).subscribe(obj => 
      this.sharedFilterData = obj
    );    
      this.sharedData.sharedBranchData.pipe(takeUntil(this.unsubscribe$)).subscribe((val) => {
        this.getBranchData = val;
        if(this.getBranchData.length == 0 ){
          this.branches = this.user.restaurantAccess;
        }else if(this.getBranchData.length == 1){        
          const toSelect = this.getBranchData.find(data => data.branchName == this.getBranchData[0].branchName);
          if(toSelect != this.sharedFilterData.restaurantId){
            this.sharedFilterData = '';
            this.vendors.setValue(['All']);
            this.selectedApprovalStatus = 'pending';
            this.startDate.setValue(null);
            this.endDate.setValue(null);
          }
          this.piApprovalForm.get('branchSelection').setValue(toSelect);
          this.branches = this.getBranchData
          this.filterByBranch(this.piApprovalForm.value.branchSelection);
        }else{
          this.branches = this.getBranchData
        }
    });

  if(this.router.url.includes(this.indentReqUrl)){
    this.indentReqFlag = true
  }else{
    this.indentReqFlag = false
  }
  }
  ngOnInit() {
    // this.displayedColumns = ['select','grnId','vendorName','total','date','status'];
  }
  
  ngOnDestroy() {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
    if(!this.router.url.includes(this.csiApprovalDetailUrl)){
      this.sharedFilterService.getFilteredCsi['_value'] = ''
    }
  }

  selectWorkArea(workarea){    
    this.dataSource.filter = workarea.trim().toLocaleUpperCase();
  }
  
  doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleUpperCase();
    this.selectedApprovalStatus = undefined ;
    this.selectedOrderStatus = undefined ;
  }

  selectPurchaseStatus(status) {
    if(this.selectedApprovalStatus != undefined){
      if(this.sharedFilterData.approvalStatus){
        this.sharedFilterData.approvalStatus = null;
      }
    }
    this.setUpCall();
  }
    
  filterByStatus(status) {
    this.selectedApprovalStatus = status;
    if (status === 'pending' || status === 'approved' || status === 'rejected') {
      let filteredData = this.allData.filter(element => element['approvalStatus'] === status);
      this.dataSource = new MatTableDataSource<any> ();
      this.dataSource.data = filteredData;
      this.pageSizes = this.utils.getPageSizes(this.dataSource.data) ;
    } else if (status === 'All') {
      this.dataSource = new MatTableDataSource<any> ();
      this.dataSource.data = this.allData;
      this.pageSizes = this.utils.getPageSizes(this.dataSource.data) ;
    }
    this.displayedColumns = status === 'pending' ? ['select','grnId','vendorName','total','date','status'] : ['grnId','vendorName','total','date','status'];
  }
  
  filterByDate() {
    this.setUpCall();
  }


  setUpCall(){
      let obj = {
        tenantId: this.user.tenantId,
        uType: this.user.uType,
        restaurantId: this.restaurantId,
        startDate: this.startDate.value,
        endDate: this.endDate.value,
        userEmail:this.auth.getCurrentUser().email
      }

      if(this.startDate.value && this.endDate.value){
        obj['startDate'] = this.utils.dateCorrection(this.startDate.value);
        obj['endDate'] = this.utils.dateCorrection(this.endDate.value);
      }else{
        obj['startDate'] = null;
        obj['endDate'] = null;
      }

      if(this.purchaseStat.value){
        obj['orderStatus'] = this.purchaseStat.value;
      }else{
        obj['orderStatus'] = 'All';
      }
      this.branchTransfer.getPIList(obj).subscribe(data => {
        data.forEach((el)=>{
          let result = el.approvalDetail.find(obj => obj.role === this.user.role );
          let status = result ? result.status : null;
          el.approvalStatus = status;
        })
        this.allData = data ;
        this.dataSource = new MatTableDataSource<any> ();
        this.dataSource.data =  data ;
        if(this.tempData == undefined){
          this.tempData = data;
        }
        this.pageSizes = this.utils.getPageSizes(this.dataSource.data) ;

        if(this.selectedApprovalStatus == undefined){
          this.selectedApprovalStatus = 'pending'
        }else{
          if(this.sharedFilterData.approvalStatus){
            this.selectedApprovalStatus = this.sharedFilterData.approvalStatus;
          }
        }
        this.filterByStatus(this.selectedApprovalStatus);
      });
  }

  resetForm() {
    this.searchText = undefined ;
    this.purchaseStat.setValue('All')
    this.selectedApprovalStatus = undefined ;
    this.selectedStartDate = undefined ;
    this.selectedEndDate = undefined ;
    this.dataSource.data = this.allData ;
  }

  filterByBranch(restId) {
    this.selectedApprovalStatus = null ;
    this.restaurantId = restId.hasOwnProperty('restaurantIdOld')  ?  restId.restaurantIdOld : restId;
    this.branchSelected = true
    if(this.sharedFilterData != ''){
      if(this.sharedFilterData.restaurantId == restId){
        this.piApprovalForm.get('branchSelection').setValue(this.sharedFilterData.restaurantId);
      }else{
        this.piApprovalForm.get('branchSelection').setValue(restId);
      }
      this.branches = this.getBranchData;
      this.selectedStartDate = this.sharedFilterData.selectedStartDate;
      this.selectedEndDate = this.sharedFilterData.selectedEndDate;
      this.startDate.setValue(this.sharedFilterData.selectedStartDate);
      this.endDate.setValue(this.sharedFilterData.selectedEndDate);
      this.selectedApprovalStatus = this.sharedFilterData.approvalStatus;
      this.tempData = this.sharedFilterData.data;
    }

    if(this.sharedFilterData.orderStatus){
      this.purchaseStat.setValue(this.sharedFilterData.orderStatus);
    }else{
      this.purchaseStat.setValue('All');
    }
    this.setUpCall();
  }

  clear(){
    this.purchaseStat.setValue('All')
    this.selectedApprovalStatus = 'pending';
    this.startDate.setValue(null);
    this.endDate.setValue(null);
    this.setUpCall();
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  masterToggle() {
    this.isAllSelected() ?
        this.selection.clear() :
        this.dataSource.data.forEach(row => this.selection.select(row));
  }

  approveBulkIndents() {
  	let obj = {
  		tenantId: this.user.tenantId,
  		restaurantId: this.restaurantId,
  		approvalData: this.selection.selected,
  		role: this.user.role
  	}
  	this.purchases.indentBulkApproval(obj).subscribe(data => {
  		data.result === 'success' ? this.utils.snackBarShowSuccess(data.message) : this.utils.snackBarShowError(data.message);
  		this.filterByBranch(obj.restaurantId);
      this.selectedApprovalStatus = null ;
  	})
  }

  rejectBulkIndent() {
  	let obj = {
  		tenantId: this.user.tenantId,
  		restaurantId: this.restaurantId,
  		approvalData: this.selection.selected,
  		role: this.user.role
  	}
    this.dialogRef = this.dialog.open(SimpleDialogComponent, {
      data: {
        title: 'Reject Reason',
        msg: 'Enter Reason(min 10 and max 40 characters allowed)',
        inputFromUser: { 'Reason': '' },
        ok: function () {
          this.dialogRef.afterClosed().subscribe(res => {
            this.reason = res['Reason'];
            if(res['Reason'] == ''){
              this.utils.snackBarShowInfo('please provide the vaild reason here..');
            }else{
              obj['reason'] = this.reason;
              this.purchases.indentBulkRejection(obj).subscribe(data => {
                data.result === 'success' ? this.utils.snackBarShowSuccess(data.message) : this.utils.snackBarShowError(data.message);
                this.filterByBranch(obj.restaurantId);
                this.selectedApprovalStatus = null ;
              }, err => console.error(err))
            }
          })
        }.bind(this)
      }
    });
  }


  receiveIndent(obj: any){
    obj['piApprovalScreen'] = true ;
    let inputObj = {
      restaurantId :this.piApprovalForm.value.branchSelection,
      selectedStartDate : this.startDate.value,
      selectedEndDate : this.endDate.value,
      // vendorName : [this.vendorName.value],
      vendorName : [],
      data : this.tempData,
      branchFlag : true
    }    
    this.sharedFilterService.getFilteredInvoice.next(inputObj);
    this.sharedData.changePi(obj)
    this.router.navigate(['/home/<USER>'])
  }

  
  approvePis() {
  	let obj = {
  		tenantId: this.user.tenantId,
  		restaurantId: this.restaurantId,
  		approvalData: this.selection.selected,
  		role: this.user.role
  	}
  	this.purchases.piGroupApproval(obj).subscribe(data => {
  		data.result === 'success' ? this.utils.snackBarShowSuccess(data.message) : this.utils.snackBarShowError(data.message);
  		this.filterByBranch(obj['restaurantId']) ;
      this.selectedApprovalStatus = 'All' ;
      this.filterByStatus(this.selectedApprovalStatus) ;
  	})
  }

  rejectPis() {
  	let obj = {
  		tenantId: this.user.tenantId,
  		restaurantId: this.restaurantId,
  		approvalData: this.selection.selected,
  		role: this.user.role
  	}
    this.dialogRef = this.dialog.open(SimpleDialogComponent, {
      data: {
        title: 'Reject Reason',
        msg: 'Enter Reason(min 10 and max 40 characters allowed)',
        inputFromUser: { 'Reason': '' },
        ok: function () {
          this.dialogRef.afterClosed().subscribe(res => {
            this.reason = res['Reason'];
            if(res['Reason'] == ''){
              this.utils.snackBarShowInfo('Please provide valid reason here..');
            }else{
              obj['reason'] = this.reason;
              this.purchases.piGroupReject(obj).subscribe(data => {
                data.result === 'success' ? this.utils.snackBarShowSuccess(data.message) : this.utils.snackBarShowError(data.message);
                this.filterByBranch(obj['restaurantId']) ;
                this.selectedApprovalStatus = 'All' ;
                this.filterByStatus(this.selectedApprovalStatus) ;
              }, err => console.error(err))
            }
          })
        }.bind(this)
  
      }
    });
  }

}
