<div class="title">
  <button mat-raised-button class="button" style="float: left; margin-left: 0px;" (click)="goBack()">
    <mat-icon>keyboard_backspace</mat-icon> Back To List
  </button>

  <div>
    <button mat-button mat-raised-button id="save-btn" class="button3" style="float: right;" [matTooltip]="'Reject PR'"
      matTooltipPosition="below" (click)="rejectPr()" [disabled]="!showButton || isDone">
      Reject
    </button>
    <button mat-button mat-raised-button id="save-btn" class="button3" style="float: right;" [matTooltip]="'Approve PR'"
      matTooltipPosition="below" (click)="checkApproval()" [disabled]="!showButton || isDone">
      Approve
    </button>
    <div *ngIf="editAccess?.editAccess == 'true'">
      <button mat-button mat-raised-button id="save-btn" class="button3" style="float: right;"
        [matTooltip]="'Modify PR'" matTooltipPosition="below" [disabled]="!showButton || isDone"
        (click)="redirectSpecialOrder()">
        Edit PR
      </button>
    </div>
  </div>
</div>

<div class="search-table-input fieldcontainer">
  <div class="row">
    <div class="col">
      <table class="table">
        <tbody>
          <tr>
            <th class="topItemkey" scope="row">Vendor Name</th>
            <td>{{ purReq.vendorDetails.vendorName }}</td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="col">
      <table class="table">
        <tbody>
          <tr>
            <th class="topItemkey" scope="row">Order Number</th>
            <td>{{ purReq.prId }}</td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="col">
      <table class="table">
        <tbody>
          <tr>
            <th class="topItemkey" scope="row">Receiving Branch</th>
            <td>{{ restaurantBranch | titlecase }}</td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="col">
      <table class="table">
        <tbody>
          <tr>
            <th class="topItemkey" scope="row">Created By</th>
            <td>{{ purReq.creator }}</td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="col">
      <table class="table">
        <tbody>
          <tr>
            <th class="topItemkey" scope="row">System Entry Date</th>
            <td>{{ this.utils.formatDateToUTC(purReq.createTs) }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="col">
      <table class="table">
        <tbody>
          <tr>
            <th class="topItemkey" scope="row">Department</th>
            <td>{{ purReq.selectedWorkArea | titlecase }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
    <div style="margin-top: 15px; padding-right: 10px;">
      <span>Select Item Price History :</span>
      <mat-checkbox [(ngModel)] = "allVendorsChecked" (change)="onCheckboxChange('allVendors')">All Vendors</mat-checkbox>
      <mat-checkbox [(ngModel)] = "currentVendorChecked" (change)="onCheckboxChange('currentVendor')">Current Vendor</mat-checkbox>
    </div>
</div>
<mat-card class="matcontent">
  <mat-card-content>
    <mat-slide-toggle style="float: right !important;margin-top: 25px !important;" class="ml-2"
      [(ngModel)]="allExtraFieldFlag" (change)="addExtraFields()">
      <span>Show Cess</span>
    </mat-slide-toggle>

    <table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort>
      <ng-container matColumnDef="index">
        <th mat-header-cell *matHeaderCellDef class="tableId"><b>#</b></th>
        <td mat-cell *matCellDef="let element; let i = index" class="name-cell tableId">
          {{ i + 1 }}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="actionBtns">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Actions</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <button mat-icon-button (click)="r(element)">
            <mat-icon>done</mat-icon>
          </button>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="itemName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Inventory Item</b>
        </th>
        <td mat-cell *matCellDef="let element">
          {{ element.itemName | titlecase }}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="pkgName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Pkg Name</b></th>
        <td mat-cell *matCellDef="let element"> {{element.packages[0].packageName | titlecase}} </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="orderQty">
        <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell"><b> Order Qty</b> </th>
        <td mat-cell *matCellDef="let element" class="name-cell">
          <span *ngIf="editPrFlag == 'false'">
            {{element.quantity}}
          </span>
          <div *ngIf="editPrFlag == 'true'">
            <input class="input1" type="number" step="0.01" min="0" (keyup)="getTotalPrCost($event , element)"
            (input)="checkNumericInput($event , element)" [(ngModel)]="element.quantity" (focus)="focusFunctionWithOutForm(element, 'quantity')" (focusout)="focusOutFunctionWithOutForm(element, 'quantity')" 
            onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)"/>
          </div>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="pendingQty">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Pending Quantity</b>
        </th>
        <td mat-cell *matCellDef="let element">
          {{ element.pendingQty }}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="cessAmt">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Cess Amt</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <span *ngIf="!editPrFlag">
            {{ element.cessAmt ? element.cessAmt : 0 }}
          </span>
          <div *ngIf='editPrFlag'>
            <input (keyup)="validateTotalPrice($event , element)" class="input1" type="number" step="any" min="0"
              [(ngModel)]="element.cessAmt" (focus)="focusFunctionWithOutForm(element, 'cessAmt')" (focusout)="focusOutFunctionWithOutForm(element, 'cessAmt')"
              onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)" />
          </div>
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getOtherTotal('cessAmt'))}}</td>
      </ng-container>

      <ng-container matColumnDef="extraAmt">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Extra Charge</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <span *ngIf="!editPrFlag">
            {{ element.extraAmt ? element.extraAmt : 0 }}
          </span>
          <div *ngIf='editPrFlag'>
            <input (keyup)="validateTotalPrice($event , element)" class="input1" type="number" step="any" min="0"
              [(ngModel)]="element.extraAmt" (focus)="focusFunctionWithOutForm(element, 'extraAmt')" (focusout)="focusOutFunctionWithOutForm(element, 'extraAmt')"
              onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)" />
          </div>
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getOtherTotal('extraAmt'))}}</td>
      </ng-container>

      <ng-container matColumnDef="discnt">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Discount</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <span *ngIf="!editPrFlag">
            {{ element.discAmt ? element.discAmt : 0 }}
          </span>
          <div *ngIf='editPrFlag'>
            <input (keyup)="validateTotalPrice($event , element)" class="input1" type="number" step="any" min="0"
              [(ngModel)]="element.discAmt" (focus)="focusFunctionWithOutForm(element, 'discAmt')" (focusout)="focusOutFunctionWithOutForm(element, 'discAmt')"
              onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)" />
          </div>
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getOtherTotal('discAmt'))}}</td>
      </ng-container>

      <ng-container matColumnDef="taxableAmt">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Taxable Amt</b></th>
        <td mat-cell *matCellDef="let element"> {{ this.utils.truncateNew(element.orderQty * element.unitPrice) -
          (element.discAmt ? element.discAmt : 0) }} </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>


      <ng-container matColumnDef="itemCode">
        <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell"><b> Item Code</b> </th>
        <td mat-cell *matCellDef="let element" class="name-cell"> {{element.itemCode}} </td>
        <td mat-footer-cell *matFooterCellDef>Total</td>
      </ng-container>

      <ng-container matColumnDef="vendor">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Brand</b></th>
        <td mat-cell *matCellDef="let element">{{ element.brand.name }}</td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="unitPrice">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Unit Cost</b>
        </th>
        <td mat-cell *matCellDef="let element">{{ this.utils.truncateNew(element.packages[0].packagePrice)}}</td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="totalValue">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Total (incl.tax,etc)</b></th>
        <td mat-cell *matCellDef="let element">
          {{this.utils.truncateNew(
          ((element.unitPriceExclTax ) * element.quantity) + element.taxAmount + element.cessAmt + element.extraAmt -
          element.discAmt)
          }}
        </td>
        <td mat-footer-cell *matFooterCellDef>{{ getTotalPrice() }}</td>
      </ng-container>

      <ng-container matColumnDef="subTotal">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Total (excl.tax)</b></th>
        <td mat-cell *matCellDef="let element">
          {{this.utils.truncateNew(
          ((element.packages[0].packagePrice ) *
          element.quantity) )
          }}
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getSubTotal())}}</td>
      </ng-container>

      <ng-container matColumnDef="rate">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Tax Rate(%)</b></th>
        <td mat-cell *matCellDef="let element">
          {{element.taxRate}}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="taxAmt">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Tax Amt</b></th>
        <td mat-cell *matCellDef="let element">
          <!-- {{
          this.utils.truncateNew(((element.taxRate / 100 ) * element.unitPriceExclTax * element.quantity ) )
          }} -->
          {{ this.utils.truncateNew(element.taxAmount)}}
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getOtherTotal('taxAmount'))}}</td>
      </ng-container>

      <ng-container matColumnDef="priceHistory">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b> Price History </b>
        </th>
        <td mat-cell *matCellDef="let element">
          <button class="mat-icon-button" (click)="togglePriceList(element, $event)">
            <mat-icon>info</mat-icon>
          </button>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="Action">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b> Action</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <button *ngIf="
              element.itemStatus != 'complete' && user.uType === 'restaurant'
            " class="mat-icon-button" matTooltip="Add or Edit Packaging Sizes" (click)="displayPackages(element)">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
              class="bi bi-pencil-square svgEditIcon" viewBox="0 0 16 16">
              <path
                d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z" />
              <path fill-rule="evenodd"
                d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5v11z" />
            </svg>
          </button>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      <tr mat-footer-row *matFooterRowDef="displayedColumns"></tr>
    </table>
    <div class="dataMessage" *ngIf="dataSource?.data.length == 0"> No Data Available </div>
  </mat-card-content>
  <div *ngIf="isPriceListVisible" class="autoComplete" style="float: right;">
    <mat-grid-list cols="12" rowHeight="30px">
      <mat-grid-tile colspan="2">GRN ID</mat-grid-tile>
      <mat-grid-tile colspan="2">Date</mat-grid-tile>
      <mat-grid-tile colspan="4">Vendor Name</mat-grid-tile>
      <mat-grid-tile colspan="2">Pkg Name</mat-grid-tile>
      <mat-grid-tile colspan="0.5">Rate</mat-grid-tile>
      <mat-grid-tile colspan="0.5">Qty</mat-grid-tile>
    </mat-grid-list>
    <mat-divider [inset]="true"></mat-divider>
    <mat-option *ngFor="let val of filteredUnitPrice" class="matOption">
      <mat-grid-list cols="12" rowHeight="20px">
        <mat-grid-tile colspan="2">{{ val.grnId || '-' }}</mat-grid-tile>
        <mat-grid-tile colspan="2">{{ val.createTs | date:'dd-MM-yyyy' || '-' }}</mat-grid-tile>
        <mat-grid-tile colspan="4">{{ val.vendorName || '-' }}</mat-grid-tile>
        <mat-grid-tile colspan="2">{{ val.packageName || '-' }}</mat-grid-tile>
        <mat-grid-tile colspan="0.5">{{ val.unitPrice || '-' }}</mat-grid-tile>
        <mat-grid-tile colspan="0.5">{{ val.quantity || '-' }}</mat-grid-tile>
      </mat-grid-list>
    </mat-option>
  </div>

  <div class="mt-3 mb-3">
    <div *ngIf="purReq.vendorDetails && purReq.vendorDetails.vendorId">
      <mat-form-field appearance="none" style="float: right !important; margin-right: 0px !important;">
        <mat-select placeholder="Select Tax" [formControl]="taxCtrl"  
        class="outline" (selectionChange)="addTax($event.value)" multiple>
          <mat-option>      
            <ngx-mat-select-search [formControl]="taxFilterCtrl" placeholderLabel="Select Tax..."></ngx-mat-select-search>  
          </mat-option>    
          <mat-option class="hide-checkbox" (click)="toggleSelectAll()">
            Select All / Deselect All
          </mat-option>           
          <mat-option *ngFor="let tax of filteredTaxArray" [value]="tax">
            {{ tax }}
          </mat-option>
        </mat-select>
      </mat-form-field> 
    </div>
    <br>
      <span *ngIf="purReq.vendorDetails && purReq.vendorDetails.vendorId" 
       class="otherchargeHeading topItemkey">ADD TAX</span>
    <br><br><br>
    
    <div *ngFor="let tax of otherTaxes; let i = index" style="padding-bottom: 22px !important;">
      <span class="otherchargeHeading topItemkey">{{ tax.taxName }}</span>
      <input matInput class="outline otherTax" [(ngModel)]="tax.value" type="number" step="0.01" min="0"
      (focus)="focusFn(tax)" (focusout)="focusOutFn(tax)" (ngModelChange)="adjustGrandTotal(tax, i)" [disabled]="!showButton || isDone"
      onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)"/>
    </div>

    <div *ngIf="purReq.vendorDetails && purReq.vendorDetails.vendorId">
      <span class="otherchargeHeading topItemkey">Grand total ₹</span>
      <input matInput class="outline otherTax" [value]="getGrandTotal()" placeholder="Total ₹" disabled />
    </div>
  </div>

</mat-card>