import { Component, OnInit, HostListener, ChangeDetectorRef } from "@angular/core";
import { ShareDataService, PurchasesService, AuthService } from "../_services/";
import { MatTableDataSource, MatDialog } from "@angular/material";
import { SelectionModel } from "@angular/cdk/collections";
import { Location } from "@angular/common";
import { NotificationService } from "../_services/notification.service";
import { Router } from "@angular/router";
import { SimpleDialogComponent } from "../_dialogs/simple-dialog/simple-dialog.component";
import { environment } from "src/environments/environment";
import { UtilsService } from "../_utils/utils.service";
import { FormControl } from "@angular/forms";
import { takeUntil } from "rxjs/operators";
import { Subject } from "rxjs";

@Component({
  selector: "app-poapproval-detail",
  templateUrl: "./poapproval-detail.component.html",
  styleUrls: ["./poapproval-detail.component.scss", "./../../common-dark.scss"],
})
export class PoapprovalDetailComponent implements OnInit {
  purReq: any;
  dataSource: MatTableDataSource<any>;
  displayedColumns: String[];
  restaurantBranch: any;
  dialogRef: any;
  selection = new SelectionModel<any>(true, []);
  user: any;
  showButton: boolean = true;
  isDone: boolean = false;
  approveBtnText: String;
  reason: String;
  editPrFlag: boolean = false;
  allExtraFieldFlag: boolean;
  allVendorsChecked: boolean = false ;
  currentVendorChecked: boolean = true ;
  filteredUnitPrice: any;
  isPriceListVisible = false;
  addItem: number;
  addItems: any[] = [];
  grandTotal: number;
  otherTaxes: any;
  taxArray = ["AROED", "EXCISE DUTY", "MISCELLANEOUS", "TCS", "VAT"];
  taxCtrl = new FormControl();
  taxFilterCtrl = new FormControl();
  filteredTaxArray: any[] = [];
  allSelected: boolean = false;
  protected _onDestroy = new Subject<void>();
  isModified: boolean;
  editAccess: any;

  constructor(
    private shareData: ShareDataService,
    private router: Router,
    private location: Location,
    private notifyService: NotificationService,
    private purchases: PurchasesService,
    private auth: AuthService,
    private dialog: MatDialog,
    public utils: UtilsService,
    private cdRef: ChangeDetectorRef
  ) {
    this.user = this.auth.getCurrentUser();
    this.dataSource = new MatTableDataSource<any>();
    this.shareData.currPurOrder.subscribe((order) => {
      this.purReq = order;
      this.otherTaxes = this.purReq.hasOwnProperty('otherTax') && this.purReq.otherTax ? this.purReq.otherTax : [];    
      if (this.purReq && Object.keys(this.purReq).length != 0) {
        this.checkApprovalProcess();
        this.processInputData();
      } else {
        this.location.back();
      }
    });
  }

  ngOnInit() {
    if ("name" in this.user) {
      this.approveBtnText = this.user.name + ": Approve";
    } else {
      this.approveBtnText = "Approve";
    }
    this.displayedColumns = [
      "index",
      "itemCode",
      "itemName",
      "pkgName",
      "orderQty",
      "unitPrice",
      "subTotal",
      "rate",
      "taxAmt",
      "totalValue",
      "priceHistory"
    ];
    this.filteredTaxArray = this.taxArray.slice(); 
    this.taxCtrl.setValue(this.otherTaxes.map(tax => tax.taxName));
    this.taxFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
      this.filterTaxArray();
    });
  }

  processInputData() {
    this.dataSource.data = this.purReq.prDetails;
    this.restaurantBranch = this.purReq.restaurantId.split("@")[1];
    this.dataSource.data.forEach((item) => {
      item["originalQty"] = item["quantity"];
      if (!item.hasOwnProperty("cessAmt")) {
        item.cessAmt = 0;
      }
      if (!item.hasOwnProperty("discAmt")) {
        item.discAmt = 0;
      }
      if (!item.hasOwnProperty("extraAmt")) {
        item.extraAmt = 0;
      }
    });
    this.dataSource.data = [...this.dataSource.data];
  }

  checkApprovalProcess() {
    let status = this.purReq.approvalDetail.filter(
      (el) => el.role === this.user.role && el.status === "pending"
    );
    this.showButton = status.length == 0 ? false : true;
    this.editAccess = this.purReq.approvalDetail.find(
      (el) => el.role === this.user.role
    );
    this.editPrFlag = this.editAccess ? this.editAccess.editAccess : false;    
  }

  goBack() {
    this.location.back();
  }

  getTotal() {
    const formattedTotalSubTotal = this.purReq.prDetails
      .reduce((total, item) => {
        const subTotal = item.quantity * item.unitPriceExclTax;
        const taxTotal = subTotal * (item.taxRate / 100);
        const otherCharges = (item.hasOwnProperty('cessAmt') ? item.cessAmt : 0) + (item.hasOwnProperty('extraAmt') ? item.extraAmt : 0);
        return this.utils.truncateNew(total + subTotal + taxTotal + otherCharges);
      }, 0);
    return formattedTotalSubTotal;
  }

  getSubTotal() {
    let subTotalSum = 0;
    this.dataSource.data.forEach((element) => {
      subTotalSum += element.quantity * element.packages[0].packagePrice;
    });
    return subTotalSum;
  }

  getTaxTotal() {
    let taxTotal = 0;
    this.dataSource.data.forEach((element) => {
      taxTotal += element.taxAmount;
    });
    return taxTotal;
  }

  approvePr() {
    let obj = {};
    obj["tenantId"] = this.user.tenantId;
    obj["restaurantId"] = this.purReq.restaurantId;
    obj["role"] = this.user.role;
    obj["prId"] = this.purReq.prId;
    obj["appCat"] = this.purReq.approvalCategory;
    this.purchases.approvePo(obj).subscribe((data) => {
      if (data.success) {
        this.purReq.approveDetail = data.approveDetail;
        this.utils.snackBarShowSuccess(`${data.message}`);
        this.isDone = true;
      } else {
        this.utils.snackBarShowError(
          "Something went wrong.Please try again later"
        );
      }
    });
  }

  rejectPr() {
    let obj = {};
    obj["tenantId"] = this.user.tenantId;
    obj["restaurantId"] = this.purReq.restaurantId;
    obj["role"] = this.user.role;
    obj["prId"] = this.purReq.prId;
    obj["appCat"] = this.purReq.approvalCategory;
    this.dialogRef = this.dialog.open(SimpleDialogComponent, {
      data: {
        title: "Reject Reason",
        msg: "Enter Reason(min 10 and max 40 characters allowed)",
        inputFromUser: { Reason: "" },
        ok: function () {
          this.dialogRef.afterClosed().subscribe((res) => {
            this.reason = res["Reason"];
            if (res["Reason"] == "") {
              this.utils.snackBarShowInfo("Please provide valid reason here..");
            } else {
              obj["reason"] = this.reason;
              this.purchases.rejectPo(obj).subscribe(
                (data) => {
                  if (data.success) {
                    this.purReq.approveDetail = data.approveDetail;
                    this.utils.snackBarShowSuccess(`${data.message}`);
                    this.isDone = true;
                  } else {
                    this.utils.snackBarShowError(
                      "Something went wrong.Please try again later"
                    );
                  }
                },
                (err) => console.error(err)
              );
            }
          });
        }.bind(this),
      },
    });
  }

  modifyPr() {
    let obj: any = {};
    obj["tenantId"] = this.user.tenantId;
    obj["restaurantId"] = this.purReq.restaurantId;
    obj["prObj"] = this.purReq;
    obj["baseUrl"] = environment.baseUrl;
    obj["role"] = this.user.role;
    obj["userEmail"] = this.auth.getCurrentUser().email;

    if (this.purReq.hasOwnProperty('grandTotal')){
      this.purReq.grandTotal = this.grandTotal;
    }
    if (this.otherTaxes != null && this.otherTaxes != undefined){   
      const otherTaxNames = [...this.otherTaxes];
      this.purReq.otherTax =  otherTaxNames
    }
    this.purchases.modifyPr(obj).subscribe((data) => {
      if (data.status) {
        this.isDone = true;
        this.utils.snackBarShowSuccess(`Approved Successfully`);
      }
    });
  }

  getTotalPrCost(event, element) {
    element.subTotal = element.quantity * element.unitPrice;
    element.totalExcTax = element.quantity * element.unitPriceExclTax
    element.taxAmount =
      (element.taxRate / 100) * element.unitPriceExclTax * element.quantity;
    element.totalPrice = element.taxAmount + element.subTotal;
  }

  getOtherTotal(key: string) {
    return this.utils.getTotal(this.dataSource.data, key); 
}

  checkApproval() {
    let filteredItem = this.purReq.prDetails.filter((el) => {
      return el["originalQty"] !== el["quantity"];
    });  
    const areOtherTaxesEqual = (arr1, arr2) => {
      if (arr1.length !== arr2.length) return false;
      return arr1.every((obj1) =>
        arr2.some(
          (obj2) =>
            obj1.taxName === obj2.taxName && obj1.value === obj2.value
        )
      );
    };  
    const otherTax = this.purReq.hasOwnProperty('otherTax') && this.purReq.otherTax ? this.purReq.otherTax : [];    
    const otherTaxesMatch = areOtherTaxesEqual(otherTax, this.otherTaxes);    
    filteredItem.length == 0 && otherTaxesMatch && !this.isModified ? this.approvePr() : this.modifyPr();
    this.isModified = false;
  }

  focusFunctionWithOutForm(element , value){
    if(Number(element[value]) === 0){
      element[value] = null;
    }
  }
  
  focusOutFunctionWithOutForm(element , value){
    if(element[value] === null){
      element[value] = 0
    }
  }

  addExtraFields() {
    if (this.allExtraFieldFlag === true) {
      this.displayedColumns = [
        "index",
        "itemCode",
        "itemName",
        "pkgName",
        "orderQty",
        "unitPrice",
        "subTotal",
        "rate",
        "taxableAmt",
        "taxAmt",
        "extraAmt",
        "discnt",
        "cessAmt",
        "totalValue",
        "priceHistory"
      ];
    } else {
      this.displayedColumns = [
        "index",
        "itemCode",
        "itemName",
        "pkgName",
        "orderQty",
        "unitPrice",
        "subTotal",
        "rate",
        "taxAmt",
        "totalValue",
        "priceHistory"
      ];
    }
  }

  validateTotalPrice(event, element) {
    if (event.keyCode == 190) {
      return;
    }
    element["extraAmt"] === null ? (element["extraAmt"] = 0) : undefined;
    element["cessAmt"] === null ? (element["cessAmt"] = 0) : undefined;
    element["discAmt"] === null ? (element["discAmt"] = 0) : undefined;
    let afterDiscount = 0;
    let tax = 0;
    afterDiscount = element.orderQty * element.unitPrice - element.discAmt;
    tax = afterDiscount * (element.taxRate / 100);
    element.subTotal = afterDiscount;
    element.taxAmount = tax;
    element.totalPrice =
      afterDiscount + element.cessAmt + element.extraAmt + tax;
  }

  checkNumericInput(event: any , element) {
    if (element.packages[0].packageNam === 'NOS') {
      const input = event.target.value;
      event.target.value = input.replace(/[^0-9]/g, ''); 
      element.quantity = event.target.value;
    }
  }

  onCheckboxChange(checkbox: string) {
    if (checkbox === 'allVendors') {
      this.currentVendorChecked = false;
      this.isPriceListVisible = false;
    } else if (checkbox === 'currentVendor') {
      this.allVendorsChecked = false;
      this.isPriceListVisible = false;
    }
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    this.isPriceListVisible = false;
  }

  togglePriceList(element: any, event: MouseEvent): void {
    event.stopPropagation(); 
    this.getLastGrnPrice(element);
  }

  getLastGrnPrice(element){
    this.isPriceListVisible = true;
    let params={
      'restaurantId':this.purReq.restaurantId,
      'vendorId':this.purReq.vendorDetails.vendorId,
      'itemCode':element.itemCode,
      'packageName':element.packages[0].packageName,
    }
    if (this.allVendorsChecked) {
      params['allVendors'] = true;
    }
    this.purchases.getLastGrnPrice(params).subscribe(data => {
      if (data['success']){        
        this.filteredUnitPrice = data.priceList
      }
    }) 
  }

  focusFn(tax: any){
    if(Number(tax.value) === 0){
      tax.value = null;
    }
  }
  
  focusOutFn(tax: any){
    if (tax.value < 0) {
      tax.value = 0;
    }
    if(tax.value === null){
      tax.value = 0
    }
  }

  getTotalPrice() {
    let totalPrice = 0    
    this.dataSource.data.forEach(element => {      
      let afterDiscount = 0;
      let tax = 0;
      afterDiscount = (element.quantity * element.packages[0].packagePrice) - element.discAmt
      tax = afterDiscount * (element.taxRate / 100)
      element.subTotal = afterDiscount;
      element.taxAmount = tax;
      totalPrice += afterDiscount + element.cessAmt + element.extraAmt + tax;
    });        
    return this.utils.truncateNew(totalPrice)
  }

  getGrandTotal() {
    let itemTotal = this.getTotalPrice()
    let taxTotal =  this.otherTaxes.reduce((acc, item) => acc + item.value, 0);  
    this.grandTotal =  (this.utils.truncateNew(itemTotal) + this.utils.truncateNew(taxTotal));    
    return this.utils.truncateNew(this.grandTotal)
  }

  adjustGrandTotal(tax: any, index: number) {
    const previousTaxValue = parseFloat(this.otherTaxes[index].oldValue) || 0;
    const newTaxValue = parseFloat(tax.value) || 0;  
    this.grandTotal += (newTaxValue - previousTaxValue);
    this.otherTaxes[index].oldValue = newTaxValue;    
    this.isModified = true;
    this.getGrandTotal();
  }
  
  private filterTaxArray() {
    const search = this.taxFilterCtrl.value ? this.taxFilterCtrl.value.toLowerCase() : '';
    this.filteredTaxArray = this.taxArray.filter(tax => tax.toLowerCase().includes(search));
  }

  toggleSelectAll() {
    this.allSelected = !this.allSelected;
    if (this.allSelected) {
      this.taxCtrl.setValue(this.taxArray);
    } else {
      this.taxCtrl.setValue([]);
    }
    this.addTax(this.taxCtrl.value || []);  
    this.cdRef.detectChanges();
  }

  addTax(selectedTaxes: string[]) {
    const updatedTaxes = [];  
    selectedTaxes.forEach((tax) => {
      const existingTax = this.otherTaxes.find(item => item.taxName === tax);
      if (existingTax) {
        updatedTaxes.push(existingTax);  
      } else {
        updatedTaxes.push({ taxName: tax, value: 0 });
      }
    });  
    this.otherTaxes = updatedTaxes;
  }

  redirectSpecialOrder(){
    const data = this.purReq.prDetails;   
    const totalPrice = this.getTotalPrice(); 
    data.forEach((element) => {
      element.totalPrice = totalPrice;
    });
    let obj = {
      tenantId: this.user.tenantId,
      pr: this.purReq,
      userMId: this.user.mId,
      rId: this.purReq.restaurantId,
      vendorId: this.purReq.vendorDetails.vendorId
    } 
    if (this.otherTaxes != null && this.otherTaxes != undefined){   
      const otherTaxNames = [...this.otherTaxes];
      this.purReq.otherTax =  otherTaxNames
    }
    this.purchases.editDraft(obj).subscribe(res => {      
      if (res.result == true){
        this.shareData.editPr(this.purReq);
        this.router.navigate(['/home/<USER>']);
      } else {
        this.utils.snackBarShowInfo('Failed to edit PR');
      }
    })
  }
  
}
