import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { PostGrnApprovalDetailComponent } from './post-grn-approval-detail.component';

describe('PostGrnApprovalDetailComponent', () => {
  let component: PostGrnApprovalDetailComponent;
  let fixture: ComponentFixture<PostGrnApprovalDetailComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ PostGrnApprovalDetailComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PostGrnApprovalDetailComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
