import { Component, OnInit } from "@angular/core";
import { ShareDataService, PurchasesService, AuthService } from "../_services/";
import {
  MatTableDataSource,
  MatDialog,
} from "@angular/material";
import { SelectionModel } from "@angular/cdk/collections";
import { GlobalsService } from "../_services/globals.service";
import { Location } from "@angular/common";
import { UtilsService } from "../_utils/utils.service";
import { NotificationService } from "../_services/notification.service";
import { Router } from "@angular/router";
import { SimpleDialogComponent } from "../_dialogs/simple-dialog/simple-dialog.component";

@Component({
  selector: "app-post-grn-approval-detail",
  templateUrl: "./post-grn-approval-detail.component.html",
  styleUrls: [
    "./post-grn-approval-detail.component.sass",
    "./../../common-dark.scss",
  ],
})
export class PostGrnApprovalDetailComponent implements OnInit {
  purReq: any;
  dataSource: MatTableDataSource<any>;
  displayedColumns: String[];
  InvNo: any;
  restaurantBranch: any;
  dialogRefr: any;
  dialogRef: any;
  selection = new SelectionModel<any>(true, []);
  user: any;
  approveFlag: boolean = true;
  approveBtnText: String;
  reason: String;
  allExtraFieldFlag: any;
  showButton: boolean = true;
  isDone: boolean = false;
  constructor(
    private shareData: ShareDataService,
    private location: Location,
    private notifyService: NotificationService,
    private utils: UtilsService,
    private purchases: PurchasesService,
    private router: Router,
    private auth: AuthService,
    private dialog: MatDialog
  ) {
    this.user = this.auth.getCurrentUser();
  }

  ngOnInit() {
    if ("name" in this.user) {
      this.approveBtnText = this.user.name + ": Approve";
    } else {
      this.approveBtnText = "Approve";
    }
    this.displayedColumns = Object.create(GlobalsService.approvePoDtlColumns);
    this.shareData.currPurOrder.subscribe((order) => {
      this.purReq = order;
      this.checkApprovalProcess();
      if (!this.purReq) this.location.back();
    });
    this.dataSource = new MatTableDataSource<any>();
    if (this.purReq.hasOwnProperty("poItems"))
      this.processInputData(this.purReq);
    else this.router.navigate(["/home"]);
  }

  processInputData(purReq) {
    this.dataSource.data = purReq.poItems;
    this.restaurantBranch = this.purReq.restaurantId.split("@")[1];
    this.dataSource.data.forEach((item) => {
      if (!item.hasOwnProperty("cessAmt")) {
        item.cessAmt = 0;
      }
      if (!item.hasOwnProperty("discAmt")) {
        item.discAmt = 0;
      }
      if (!item.hasOwnProperty("extraAmt")) {
        item.extraAmt = 0;
      }
    });
    this.dataSource.data = [...this.dataSource.data];
  }

  cancel() {
    this.location.back();
  }

  checkApprovalProcess() {
    let status = this.purReq.approvalDetail.filter(
      (el) => el.role === this.user.role && el.status === "pending"
    );
    this.showButton = status.length == 0 ? false : true;
  }

  setEachItemStatus() {
    this.selection.selected.forEach((selection) => {
      selection.pendingQty = selection.quantity - selection.receivedQty;
      if (selection.receivedQty > 0)
        selection.pendingQty === 0
          ? (selection.itemStatus = "complete")
          : (selection.itemStatus = "partial");
    });
  }

  printpdf() {
    this.purchases.printpdfs(this.purReq, "Po").subscribe((data) => {
      this.purchases.globalPrintPdf(data.eFile);
    });
  }
  exportToExcel() {
    this.purchases.exportToExcel(this.purReq, "Po").subscribe((data) => {
      window.open("data:text/csv;base64," + data.eFile);
    });
  }

  goBack() {
    this.location.back();
  }

  getTotal() {
    let totalPrice = 0;
    this.dataSource.data.forEach((element) => {
      totalPrice += element.quantity * element.unitPrice;
    });
    return totalPrice;
  }

  getSubTotal() {
    let subTotalSum = 0;
    this.dataSource.data.forEach((element) => {
      subTotalSum += element.quantity * element.packages[0].packagePrice;
    });
    return subTotalSum;
  }

  getTaxTotal() {
    let taxTotal = 0;
    this.dataSource.data.forEach((element) => {
      taxTotal += element.taxAmount;
    });
    return taxTotal;
  }

  getFieldTotal(key: string) {
    return this.utils.getTotal(this.dataSource.data, key);
  }

  approveGrn() {
    let obj = {};
    obj["tenantId"] = this.user.tenantId;
    obj["restaurantId"] = this.purReq.restaurantId;
    obj["role"] = this.user.role;
    obj["grnId"] = this.purReq.grnId;
    this.purchases.approveGrnStatus(obj).subscribe((data) => {
      if (data.success == true) {
        this.utils.snackBarShowSuccess(`${data.message}`);
        this.approveFlag = true;
        this.isDone = true;
      } else {
        this.utils.snackBarShowError(`${data.message}`);
      }
    });
  }

  rejectGrn() {
    let obj = {};
    obj["tenantId"] = this.user.tenantId;
    obj["restaurantId"] = this.purReq.restaurantId;
    obj["role"] = this.user.role;
    obj["grnId"] = this.purReq.grnId;
    this.dialogRef = this.dialog.open(SimpleDialogComponent, {
      data: {
        title: "Reject Reason",
        msg: "Enter Reason(min 10 and max 40 characters allowed)",
        inputFromUser: { Reason: "" },
        ok: function () {
          this.dialogRef.afterClosed().subscribe((res) => {
            this.reason = res["Reason"];
            if (res["Reason"] == "") {
              this.utils.snackBarShowInfo("Please enter Reason");
            } else {
              obj["reason"] = this.reason;
              this.purchases.rejectGrnStatus(obj).subscribe(
                (data) => {
                  if (data.success == true) {
                    this.utils.snackBarShowInfo(`Rejected Successfully`);
                    this.approveFlag = true;
                    this.isDone = true;
                  } else {
                    this.utils.snackBarShowError("Something went wrong.Please try again later");
                  }
                },
                (err) => console.error(err)
              );
            }
          });
        }.bind(this),
      },
    });
  }
}
