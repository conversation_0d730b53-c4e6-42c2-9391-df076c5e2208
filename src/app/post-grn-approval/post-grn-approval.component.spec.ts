import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { PostGrnApprovalComponent } from './post-grn-approval.component';

describe('PostGrnApprovalComponent', () => {
  let component: PostGrnApprovalComponent;
  let fixture: ComponentFixture<PostGrnApprovalComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ PostGrnApprovalComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PostGrnApprovalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
