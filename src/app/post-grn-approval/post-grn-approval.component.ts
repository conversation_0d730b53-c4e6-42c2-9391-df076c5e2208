import { Component, OnInit, Input, ViewChild } from "@angular/core";
import { Router } from "@angular/router";
import { GlobalsService } from "../_services";
import {
  MatTableDataSource,
  MatPaginator,
  MatSort,
  MatDialog,
} from "@angular/material";
import { SelectionModel } from "@angular/cdk/collections";
import { UtilsService } from "../_utils/utils.service";
import { ShareDataService } from "../_services/share-data.service";
import { PurchasesService, AuthService } from "../_services/";
import { NotificationService } from "../_services/notification.service";
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from "@angular/forms";
import { ReplaySubject, Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import {
  DateAdapter,
  MAT_DATE_FORMATS,
  MAT_DATE_LOCALE,
} from "@angular/material";
import {
  MomentDateModule,
  MomentDateAdapter,
} from "@angular/material-moment-adapter";
import { SimpleDialogComponent } from "../_dialogs/simple-dialog/simple-dialog.component";
import { SharedFilterService } from "../_services/shared-filter.service";

export const MY_FORMATS = {
  parse: {
    dateInput: "LL",
  },
  display: {
    dateInput: "DD-MM-YYYY",
    monthYearLabel: "YYYY",
    dateA11yLabel: "LL",
    monthYearA11yLabel: "YYYY",
  },
};

@Component({
  selector: "app-post-grn-approval",
  templateUrl: "./post-grn-approval.component.html",
  styleUrls: ["./post-grn-approval.component.scss", "./../../common-dark.scss"],
})
export class PostGrnApprovalComponent implements OnInit {
  purchaseOrderReqList: any = [];
  @ViewChild(MatSort) sort: MatSort;
  searchText: string;
  searchValue: string;
  tempData: any;
  @ViewChild(MatPaginator) set paginator(value: MatPaginator) {
    if (this.dataSource) {
      this.dataSource.paginator = value;
    }
  }
  restaurantId: any;
  selectedBranch: any;
  selectedVendorName: any;
  selectedApprovalStatus: any;
  selectedOrderStatus: any;
  selectedStartDate: any;
  selectedEndDate: any;
  multiBranchUser;
  branchSelected: boolean;
  // dataSource = new MatTableDataSource();
  dataSource: MatTableDataSource<any> = new MatTableDataSource<any> ();
  displayedColumns: string[];
  selection = new SelectionModel<any>(true, []);
  purchaseStatus: string[] = ["All", "Complete", "Partial", "Open"];
  vendorsList: any[] = ["All"];
  filteredByDateList: any[];
  filterKeys = {
    status: { orderStatus: "All" },
    vendorDetails: { vendorName: "All" },
  };
  date: any = { startDate: "", endDate: "" };
  pageSizes: any[];
  user: any;
  vendors = new FormControl();
  startDate = new FormControl();
  endDate = new FormControl();
  branches: any[];
  getBranchData: any[];
  purchaseOrderAprovalForm: FormGroup;
  checkSelection: boolean;

  public purOrderApprovalvendors: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public VendorData: any[] = [];
  public vendorFilterCtrl: FormControl = new FormControl();
  protected _onDestroy = new Subject<void>();
  dialogRef: any;
  postGrnApproval = encodeURI(GlobalsService.postGrnApproval);
  postGrnApprovalDetailUrl = encodeURI(GlobalsService.postGrnApprovalDetail);
  
  private unsubscribe$ = new Subject<void>();
  sharedFilterData: any = {};

  constructor(
    private utils: UtilsService,
    private router: Router,
    private auth: AuthService,
    private purchases: PurchasesService,
    private notifyService: NotificationService,
    private sharedData: ShareDataService,
    private fb: FormBuilder,
    private dialog: MatDialog,
    private sharedFilterService: SharedFilterService
  ) {
    this.user = this.auth.getCurrentUser();
    this.multiBranchUser = this.user.multiBranchUser;
    this.purchaseOrderAprovalForm = this.fb.group({
      branchSelection: [null, Validators.required],
    });


    this.sharedFilterService.getFilteredPGrnApproval.pipe(
      takeUntil(this.unsubscribe$)
    ).subscribe(obj => 
      this.sharedFilterData = obj
    ); 
    this.sharedData.sharedBranchData
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((val) => {
        this.getBranchData = val;
        if (this.getBranchData.length == 0) {
          this.branches = this.user.restaurantAccess;
        } else if (this.getBranchData.length == 1) {
          const toSelect = this.getBranchData.find(
            (data) => data.branchName == this.getBranchData[0].branchName
          );
        if(toSelect != this.sharedFilterData.restaurantId){
          this.sharedFilterData = '';
          this.vendors.setValue(['All']);
          this.selectedApprovalStatus = 'pending';
          this.startDate.setValue(null);
          this.endDate.setValue(null);
        }
          this.purchaseOrderAprovalForm.get("branchSelection").setValue(toSelect);
          this.branches = this.getBranchData;
          this.filterByBranch(
            this.purchaseOrderAprovalForm.value.branchSelection
          );

        } else {
          this.branches = this.getBranchData;
        }
      });
  }

  ngOnInit() {
    if (this.branchSelected) {
      // this.filterByBranch(window.sessionStorage.getItem('restaurantId'))
    } else {
      if (!this.user.multiBranchUser) {
        this.restaurantId = this.user.restaurantAccess[0].restaurantIdOld;
        this.branchSelected = true;
        this.setupCall();
      }
    }
  }

  ngOnDestroy() {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
    if(!this.router.url.includes(this.postGrnApprovalDetailUrl)){
      this.sharedFilterService.getFilteredPGrnApproval['_value'] = ''
    }
  }

  setupCall() {
    this.user = this.auth.getCurrentUser();
    this.multiBranchUser = this.user.multiBranchUser;
      let obj = {
        tenantId: this.user.tenantId,
        uType: this.user.uType,
        restaurantId: this.restaurantId,
        userEmail: this.auth.getCurrentUser().email,
      };

      if(this.vendors.value && this.tempData){
        let array = []
        array.push(this.vendors.value)
        array = array[0].filter(item => item !== "All");
        // "filter data using vendorname"
        const filteredData = this.tempData.filter(item => array.includes(item.vendorDetails.vendorName));
        // "remove Duplicate data"
        const uniqueData = filteredData.filter((obj, index, self) => {
          return index === self.findIndex(item => item.vendorDetails.vendorName === obj.vendorDetails.vendorName);
        });
  
        const vendorIds = [];
        uniqueData.forEach((obj: any) => {
          vendorIds.push(obj.vendorDetails.vendorId);
        });
  
        if(vendorIds.length === 0){
          obj['vendorId'] = ['All'];
          this.vendors.setValue(['All']);
        }else{
          obj['vendorId'] = vendorIds
        }
      }else{
        obj['vendorId'] = ['All'];
      }
    
      if(this.startDate.value && this.endDate.value){
        obj['startDate'] = this.utils.dateCorrection(this.startDate.value);
        obj['endDate'] = this.utils.dateCorrection(this.endDate.value);
      }else{
        obj['startDate'] = null;
        obj['endDate'] = null;
      }

      this.purchases.getFilteredGrns(obj).subscribe((data) => {
        this.purchaseOrderReqList = data["grns"];
        this.dataSource = new MatTableDataSource<any> ();
        this.pageSizes = this.utils.getPageSizes(this.purchaseOrderReqList);
        if(this.tempData == undefined){
          this.tempData = data["grns"];
        }
        this.getPRList();
      });
  }

  getPRList() {
    this.purchaseOrderReqList.forEach(async (element, index) => {
      this.vendorsList.push(element.vendorDetails.vendorName);
      if (element.isPoCreated) {
        element.progress = "Processed";
      } else {
        element.progress = "pending";
      }
      await this.getApprovalStatus(element);
      if (this.purchaseOrderReqList.length - 1 == index) {
        this.vendorsList = this.vendorsList.filter(
          (j, l, arr) => arr.indexOf(j) === l
        );

        this.VendorData = this.vendorsList;
        this.purOrderApprovalvendors.next(this.VendorData.slice());
        this.vendorFilterCtrl.valueChanges
          .pipe(takeUntil(this._onDestroy))
          .subscribe(() => {
            this.vendorfilterBanks();
          });
        this.filteredByDateList = this.purchaseOrderReqList;
        this.dataSource.sort = this.sort;
      }
    });

    if(this.selectedApprovalStatus == undefined){
      this.selectedApprovalStatus = 'pending'
    }else{
      if(this.sharedFilterData.approvalStatus){
        this.selectedApprovalStatus = this.sharedFilterData.approvalStatus;
      }
    }
    this.filterByStatus(this.selectedApprovalStatus , false);
  }

  protected vendorfilterBanks() {
    if (!this.VendorData) {
      return;
    }
    let search = this.vendorFilterCtrl.value;
    if (!search) {
      this.purOrderApprovalvendors.next(this.VendorData.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.purOrderApprovalvendors.next(
      this.VendorData.filter(
        (VendorData) => VendorData.toLowerCase().indexOf(search) > -1
      )
    );
  }

  async getApprovalStatus(element: any) {
    element.approvalDetail.forEach((el) => {
      if (el.role === this.user.role) {
        element["approvalStatus"] = `${el.status}`;
      }
    });
  }

  doFilter = (value: string) => {
    this.filterByStatus("All", true);
    this.selectedApprovalStatus = "All";
    this.dataSource.filter = value.trim().toLocaleUpperCase();
  };

  receiveOrder(obj) {
    let inputObj = {
      restaurantId : this.purchaseOrderAprovalForm.value.branchSelection,
      selectedStartDate : this.startDate.value,
      selectedEndDate : this.endDate.value,
      vendorName : [this.vendors.value],
      approvalStatus : this.selectedApprovalStatus,
      data : this.tempData
    }    
    this.sharedFilterService.getFilteredPGrnApproval.next(inputObj);
    this.sharedData.changeOrder(obj);
    this.router.navigate(["/home/<USER>"]);
  }

  allFilter() {
    this.filterByStatus(this.selectedApprovalStatus, false);
    let tmp = this.dataSource.data;
    let prev = this.dataSource.data;
    Object.keys(this.filterKeys).forEach((element) => {
      Object.keys(this.filterKeys[element]).forEach((nestElement) => {
        if (this.filterKeys[element][nestElement] != "All") {
          tmp = prev.filter((purOrder) => {
            if (purOrder[element][nestElement] !== undefined) {
              return (
                purOrder[element][nestElement].toLowerCase() ===
                this.filterKeys[element][nestElement].toLowerCase()
              );
            }
          });
          prev = tmp;
        }
      });
    });
    this.dataSource.data = tmp;
  }

  selectVendorFilter(vendor) {
    if(this.selectedApprovalStatus != undefined){
      if(this.sharedFilterData.approvalStatus){
        this.sharedFilterData.approvalStatus = null;
      }
    }
    this.setupCall();
  }

  filterByDate() {
    this.setupCall();
  }
  
  clear(){
    this.vendors.setValue(['All']);
    this.startDate.setValue(null);
    this.endDate.setValue(null);
    this.selectedApprovalStatus = null;
    this.setupCall();

  }
  resetForm() {
    this.searchText = "";
    this.date = "";
    this.filteredByDateList = this.dataSource.data = this.purchaseOrderReqList;
    this.vendors.setValue("");
    this.filterKeys.status.orderStatus = "All";
    this.filterKeys.vendorDetails.vendorName = "All";
    this.searchValue = "";
    this.doFilter(this.searchValue);
    this.dataSource.data = this.purchaseOrderReqList;
  }

  filterByBranch(restId) {
    this.restaurantId = restId.restaurantIdOld;
    this.branchSelected = true;
    if(this.sharedFilterData != ''){
      if(this.sharedFilterData.restaurantId == restId){
        this.purchaseOrderAprovalForm.get('branchSelection').setValue(this.sharedFilterData.restaurantId);
      }else{
        this.purchaseOrderAprovalForm.get('branchSelection').setValue(restId);
      }
      this.branches = this.getBranchData;
      this.selectedStartDate = this.sharedFilterData.selectedStartDate;
      this.selectedEndDate = this.sharedFilterData.selectedEndDate;
      this.startDate.setValue(this.sharedFilterData.selectedStartDate);
      this.endDate.setValue(this.sharedFilterData.selectedEndDate); 
      this.selectedApprovalStatus = this.sharedFilterData.approvalStatus;
      this.tempData = this.sharedFilterData.data;
      this.tempData.forEach(element => {
        this.vendorsList.push(element.vendorDetails.vendorName);
      });
      this.vendorsList = this.vendorsList.filter((j, l, arr) => arr.indexOf(j) === l);
      this.VendorData = this.vendorsList
      this.purOrderApprovalvendors.next(this.VendorData.slice());
      this.vendorFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
          this.vendorfilterBanks();
      });
    }else{
      this.vendors.setValue(['All']);
      this.selectedApprovalStatus = 'pending';
      this.startDate.setValue(null);
      this.endDate.setValue(null);
    }

    if(this.sharedFilterData == ''){
      this.vendors.setValue(['All']);
    }else{
      if(this.sharedFilterData.vendorName){
        this.vendors.setValue(this.sharedFilterData.vendorName[0]);
      }
    }
    this.setupCall();
  }

  filterByStatus(event,reset){
    this.selectedApprovalStatus = event;
    if(event == 'All'){
      this.displayedColumns = ['poId', 'vendorName','total', 'eta', 'status', 'approvalStatus'];
      this.dataSource.data = this.purchaseOrderReqList ;
      this.pageSizes = this.utils.getPageSizes(this.dataSource.data) ;
      this.selection = new SelectionModel<any>(true, this.dataSource.data) ;
      this.masterToggle() ;
    }else{
        this.dataSource.data = []
        this.dataSource.data = (event === 'pending') ? this.purchaseOrderReqList.filter(request => (request.approvalStatus != "rejected") && (request.approvalStatus != "approved")) : this.purchaseOrderReqList.filter(request => request.approvalStatus === event) ;
        this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
        this.displayedColumns = (event === 'pending') ? ['select','poId', 'vendorName','total', 'eta', 'status', 'approvalStatus'] : ['poId', 'vendorName','total', 'eta', 'status', 'approvalStatus'];
        this.checkSelection = this.dataSource.data.length > 0  ? true : false ;
        if (this.checkSelection && event == 'pending'){
          this.isAllSelected() ;
          this.masterToggle() ;
        } else {
          !this.isAllSelected() ? (  this.selection = new SelectionModel<any>(true, this.dataSource.data),this.masterToggle() ) : null ;
        }
    }
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  masterToggle() {
    this.isAllSelected()
      ? this.selection.clear()
      : this.dataSource.data.forEach((row) => this.selection.select(row));
  }

  approvePrs() {
    let obj = {
      tenantId: this.user.tenantId,
      restaurantId: this.restaurantId,
      approvalData: this.selection.selected,
      role: this.user.role,
    };
    this.purchases.grnStatusGroupApproval(obj).subscribe((data) => {
      data.result === "success"
        ? this.utils.snackBarShowSuccess(data.message)
        : this.utils.snackBarShowError(data.message);
      this.setupCall();
    });
  }

  rejectPrs() {
    let obj = {
      tenantId: this.user.tenantId,
      restaurantId: this.restaurantId,
      approvalData: this.selection.selected,
      role: this.user.role,
    };
    this.dialogRef = this.dialog.open(SimpleDialogComponent, {
      data: {
        title: "Reject Reason",
        msg: "Enter Reason(min 10 and max 40 characters allowed)",
        inputFromUser: { Reason: "" },
        ok: function () {
          this.dialogRef.afterClosed().subscribe((res) => {
            this.reason = res["Reason"];
            if (res["Reason"] == "") {
              this.utils.snackBarShowInfo("Please provide valid reason here..");
            } else {
              obj["reason"] = this.reason;
              this.purchases.grnStatusGroupRejection(obj).subscribe(
                (data) => {
                  data.result === "success"
                    ? this.utils.snackBarShowSuccess(data.message)
                    : this.utils.snackBarShowError(data.message);
                  this.setupCall();
                },
                (err) => console.error(err)
              );
            }
          });
        }.bind(this),
      },
    });
  }
}
