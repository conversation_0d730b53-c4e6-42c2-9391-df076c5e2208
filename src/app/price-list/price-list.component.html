<!-- <div class="parent container d-flex justify-content-center align-items-center h-100  w-600" *ngIf="priceListDialog">
  <div class="child border"> 
    <div class="mt-4">
      <span>
        <b>Select Price List</b>
      </span>
    </div>
    <br>
    <div>
      <button mat-raised-button (click)="locationPriceListfunc()" class="mr-2 locationBtn button3">
        Location Wise
      </button>
    
      <button mat-raised-button (click)="vendorPriceListfunc()" class="ml-2 vendorBtn button3">
        Vendor Wise
      </button>
    </div>
  </div>
</div> -->

<!-- <div *ngIf="!priceListDialog">
  <div *ngIf="locationPriceList">

    <div class="title title2">
      <span mat-card-title class="headTag">Location Wise Price List</span>
      <button *ngIf="!createNewEvent" mat-button class="button2 "
        style="border: 1px solid white; margin: -47px 1px 0.5px 0px; float: left;" (click)="goToLocatonPl()">
        GO BACK
      </button>
      <button *ngIf="!createNewEvent" mat-button class="button2 "
        style="border: 1px solid white; margin: -47px 1px 0.5px 0px; float: right;" (click)="showInputs()">
        ADD NEW
      </button>
    </div>


    <div *ngIf=" !createNewEvent" class="datacontainer">
      <mat-card>
        <mat-card-content>
          <div class="search-table-input">
            <div style="margin-top: 5px;">
              <label>Filter</label><br>
              <mat-form-field appearance="fill">
                <input matInput (keyup)="applyFilter($event)" placeholder="search here.." #input>
              </mat-form-field>
            </div>
          </div>
          <div class="table-responsive">
            <table #table mat-table [dataSource]="dataSource" matSort>
              <ng-container matColumnDef="priceListId">
                <th mat-header-cell *matHeaderCellDef> Id </th>
                <td mat-cell *matCellDef="let element" class="links" (click)="detailedLocationPl(element)">
                  {{element.priceListId}}
                </td>
              </ng-container>
              <ng-container matColumnDef="priceListName">
                <th mat-header-cell *matHeaderCellDef> Name </th>
                <td mat-cell *matCellDef="let element"> {{element.priceListName}} </td>
              </ng-container>
              <ng-container matColumnDef="priceListrestaurantId">
                <th mat-header-cell *matHeaderCellDef> Location </th>
                <td mat-cell *matCellDef="let element"> {{element.restaurantId.split("@")[1]}} </td>
              </ng-container>
              <ng-container matColumnDef="createTs">
                <th mat-header-cell *matHeaderCellDef> CreatedAt </th>
                <td mat-cell *matCellDef="let element"> {{element.createTs | date: 'MMM d, y, h:mm:ss a' }} </td>
              </ng-container>
              <ng-container matColumnDef="startDate">
                <th mat-header-cell *matHeaderCellDef> StartDate</th>
                <td mat-cell *matCellDef="let element"> {{element.startDate | date: 'MMM d, y' }} </td>
              </ng-container>
              <ng-container matColumnDef="endDate">
                <th mat-header-cell *matHeaderCellDef> EndDate </th>
                <td mat-cell *matCellDef="let element"> {{element.endDate | date: 'MMM d, y'}} </td>
              </ng-container>

              <ng-container matColumnDef="download">
                <th mat-header-cell *matHeaderCellDef> Export </th>
                <td mat-cell *matCellDef="let element">
                  <button mat-icon-button [disabled]="element?.status?.export === false">
                    <mat-icon matTooltip="click to download" (click)="downloadPriceList(element)">download</mat-icon>
                  </button>
                  <mat-icon *ngIf="element.status.export === true" class="check_circle">check_circle</mat-icon>
                  <div class="spinner-border" role="status" *ngIf="element.status.export === false">
                    <span class="sr-only">Loading...</span>
                  </div>
                </td>
              </ng-container>

              <ng-container matColumnDef="upload">
                <th mat-header-cell *matHeaderCellDef> Import </th>
                <td mat-cell *matCellDef="let element">
                  <button mat-icon-button matTooltip="Upload & Update Template"
                    (click)="fileInput.value=''; checkUpload(element)">
                    <mat-icon> upload </mat-icon>
                  </button>
                  <input #fileInput (change)="processPriceList($event , element)"
                    accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                    type='file' id="getFile" style="display:none">
                  <mat-icon *ngIf="element.status.import === true" class="check_circle">check_circle</mat-icon>
                  <div class="spinner-border" role="status" *ngIf="element.status.import === false">
                    <span class="sr-only">Loading...</span>
                  </div>
                </td>
              </ng-container>

              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef> Status </th>
                <td mat-cell *matCellDef="let element">
                  <div *ngIf="element.active === true">
                    <mat-icon style="color: rgb(4, 255, 4); margin-top : 7px;">check_circle</mat-icon>
                  </div>
                  <div *ngIf="element.active === false">
                    <mat-icon style="color: #ff2929 ;margin-top : 7px;">cancel</mat-icon>
                  </div>

                </td>
              </ng-container>
              <ng-container matColumnDef="refresh">
                <th mat-header-cell *matHeaderCellDef> Refresh </th>
                <td mat-cell *matCellDef="let element">
                  <button mat-icon-button matTooltip="Refresh" (click)="checkExportAndImportStatus(element)">
                    <mat-icon>refresh</mat-icon> </button>
                </td>
              </ng-container>

              <ng-container matColumnDef="action">
                <th mat-header-cell *matHeaderCellDef> Action </th>
                <td mat-cell *matCellDef="let element">
                  <button mat-icon-button matTooltip="Delete" (click)="deletePl(element)"> <mat-icon>delete</mat-icon>
                  </button>
                  <button mat-icon-button matTooltip="Click to edit" (click)="updatePriceList(element)"> <mat-icon> edit
                    </mat-icon></button>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
            </table>
          </div>
        </mat-card-content>
      </mat-card>
      <mat-paginator digitory-paginator [showTotalPages]="5" [pageSize]="10"
        [pageSizeOptions]="pageSizes"></mat-paginator>
    </div>

    <mat-card *ngIf="createNewEvent">
      <button *ngIf=" createNewEvent" mat-icon-button style=" float: right;" (click)="goBack()">
        <mat-icon>close</mat-icon>
      </button>
      <br><br>

      <div class="container">

        <form [formGroup]="priceListForm" style="display: grid;">
          <mat-form-field appearance="outline">
            <mat-label>Select Location</mat-label>
            <mat-select placeholder="Select Location" [multiple]="true" #multiSelect
              [(ngModel)]="selectedRestaurantBranch" formControlName="selectedBranch" >
              <mat-option *ngFor="let rest of this.user.restaurantAccess" [value]="rest.restaurantIdOld">
                {{ rest.branchName }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>PriceList Name</mat-label>
            <input matInput placeholder="Price List Name" [(ngModel)]="priceListName" formControlName="eventName">
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>Start Date</mat-label>
            <input matInput [matDatepicker]="picker1" [(ngModel)]="selectedStartDateTime"
              formControlName="startDate" placeholder="Start Date" />
            <mat-datepicker-toggle matSuffix [for]="picker1">
              <mat-icon matDatepickerToggleIcon>
                <img src="./../../assets/calender.png" />
              </mat-icon>
            </mat-datepicker-toggle>
            <mat-datepicker #picker1></mat-datepicker>
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>End Date</mat-label>
            <input matInput [matDatepicker]="picker2" [min]="selectedStartDateTime"
              [(ngModel)]="selectedEndDateTime" formControlName="endDate" placeholder="End Date"
              [disabled]="!selectedStartDateTime" />
            <mat-datepicker-toggle matSuffix [for]="picker2">
              <mat-icon matDatepickerToggleIcon>
                <img src="./../../assets/calender.png" />
              </mat-icon>
            </mat-datepicker-toggle>
            <mat-datepicker #picker2></mat-datepicker>
          </mat-form-field>
        </form>

        <div class="creatBtn mt-2" *ngIf="!updateNewEvent">
          <button mat-raised-button class="createBtn button3" [disabled]="!priceListForm.valid"
            (click)="createPriceList(); goBack()">
            Create
          </button>
        </div>
        <div class="creatBtn mt-2" *ngIf="updateNewEvent">
          <button mat-raised-button class="createBtn button3" [disabled]="!priceListForm.valid" (click)="editData(); goBack()">
            Update
          </button>
        </div>

      </div>
      <br><br>
    </mat-card>
  </div>
</div> -->

<!--------------------------------------------- vendor price list -------------------------------------------->
<!-- <div>
  <button mat-button (click)="setkeysInPriceList()"> set data </button>
</div> -->

<!-- <div *ngIf="!priceListDialog"> -->
  <!-- <div *ngIf="vendorPriceList"> -->
    <div class="title title2">

      <!-- <span mat-card-title class="headTag">Vendor Contract</span> -->
      <!-- <button *ngIf="!createNewEvent " mat-button (click)="goToVendorPl()"
        style="border: 1px solid white; margin: -47px 1px 0.5px 0px; float: left;" class="button2 ">
        GO BACK
      </button> -->
      <!-- <button *ngIf="!createNewEvent" mat-raised-button class="button3"
        style="border: 1px solid white; margin: -47px 1px 0.5px 0px; float: right;" (click)="showInputs()">
        ADD NEW
      </button> -->

      <!-- <button *ngIf=" createNewEvent" mat-raised-button
      class="button2 " style="border: 1px solid white; margin: -47px 5px 0.5px 0px; float: right;" (click)="goBack()">
      <mat-icon>arrow_back</mat-icon>
      BACK
    </button> -->
    </div>


    <div *ngIf=" !createNewEvent" class="datacontainer">
      <mat-card>
        <mat-card-content>
          <div class="row search-table-input"> 
              <!-- <label>Filter</label><br> -->
              <mat-form-field appearance="fill" class="mt-3">
                <input matInput (keyup)="applyFilter($event)" placeholder="search here.." #input>
              </mat-form-field>

            <mat-form-field appearance="fill" class="mt-3">
              <!-- <mat-label>Vendor</mat-label> -->
              <mat-select placeholder="Select Vendor" [(ngModel)]="selectedvendorDropDown" (selectionChange)="filterByVendor($event.value)">
                <mat-option>
                  <ngx-mat-select-search placeholderLabel="Vendor..." noEntriesFoundLabel="'no Vendor found'"
                    [formControl]="vendorFilterCtrl"></ngx-mat-select-search>
                </mat-option>
                <mat-option >
                  All
                </mat-option>
                <mat-option *ngFor="let vendor of vendorsBanks | async" [value]="vendor">
                  {{ vendor.name }}
                </mat-option>
              </mat-select>
            </mat-form-field>
        </div>

        <button mat-button class="addNewBtn button3" (click)="showInputs()" matTooltip="Add contract">
          ADD NEW
        </button>
          <!-- <div class="table-responsive"> -->
            <table #table mat-table matSort [dataSource]="dataSourceVendor">
              <ng-container matColumnDef="priceListId">
                <th mat-header-cell *matHeaderCellDef> Id </th>
                <td mat-cell *matCellDef="let element" class="links" matTooltip="Show Details" (click)="detailedLocationPl(element)">
                  {{element.priceListId }} </td>
              </ng-container>
              <ng-container matColumnDef="vendorName">
                <th mat-header-cell *matHeaderCellDef> Vendor Name </th>
                <td mat-cell *matCellDef="let element">
                  {{element.vendorName }} </td>
              </ng-container>
              <ng-container matColumnDef="priceListName">
                <th mat-header-cell *matHeaderCellDef> Name </th>
                <td mat-cell *matCellDef="let element"> {{element.vendorPriceListName }} </td>
              </ng-container>
              <ng-container matColumnDef="priceListrestaurantId">
                <th mat-header-cell *matHeaderCellDef> Location </th>
                <td mat-cell *matCellDef="let element"> 
                    <mat-form-field appearance="none" >
                      <mat-select placeholder="selected Locations" [(ngModel)]="element.selectedvendorBranch[0]" class="outline">
                        <mat-option>
                          <ngx-mat-select-search placeholderLabel="Location..." noEntriesFoundLabel="'no Location found'" (keyup)="locSearch($event.target.value ,element)"></ngx-mat-select-search>
                        </mat-option>
                        <mat-option *ngFor="let array of element.selectedvendorBranch" [value]="array">
                          {{array.split("@")[1]}}
                        </mat-option>
                      </mat-select>
                    </mat-form-field>
                </td>
              </ng-container>
              <ng-container matColumnDef="createTs">
                <th mat-header-cell *matHeaderCellDef> CreatedAt </th>
                <td mat-cell *matCellDef="let element"> {{ this.utils.formatDateToUTC(element.createTs) }} </td>

              </ng-container>
              <ng-container matColumnDef="startDate">
                <th mat-header-cell *matHeaderCellDef> StartDate</th>
                <td mat-cell *matCellDef="let element"> {{element.startvendordate | date: 'MMM d, y' }} </td>
              </ng-container>
              <ng-container matColumnDef="endDate">
                <th mat-header-cell *matHeaderCellDef> ExpireDate </th>
                <!-- <td mat-cell *matCellDef="let element"> {{element.expireDate  | date: 'yyyy-MM-ddTHH:mm:ss.sssZ' : '-5.30'}} </td> -->
                <td mat-cell *matCellDef="let element"> {{element.expireDate  | date: 'MMM d, y' : '-5.30'}} </td>

              </ng-container>

              <!-- <ng-container matColumnDef="download">
              <th mat-header-cell *matHeaderCellDef> Export </th>
              <td mat-cell *matCellDef="let element">
                  <button mat-icon-button [disabled]="element?.status?.export === false" > 
                    <mat-icon matTooltip="click to download" (click)="downloadPriceList(element)">download</mat-icon>
                  </button>
                  <mat-icon *ngIf="element.status.export === true" class="check_circle" >check_circle</mat-icon>
              <div class="spinner-border" role="status" *ngIf="element.status.export === false" >
                <span class="sr-only">Loading...</span>
              </div>

              </td>
            </ng-container> -->

              <!-- <ng-container matColumnDef="upload">
              <th mat-header-cell *matHeaderCellDef> Import </th>
              <td mat-cell *matCellDef="let element">
                    <button mat-icon-button matTooltip="Upload & Update Template" (click)="fileInput.value=''; checkUpload(element)" >
                    <mat-icon> upload </mat-icon> 
                  </button>
                  <input #fileInput (change)="processPriceList($event , element)"
                    accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                    type='file' id="getFile" style="display:none">
                    <mat-icon *ngIf="element.status.import === true" class="check_circle" >check_circle</mat-icon>
              <div class="spinner-border" role="status" *ngIf="element.status.import === false" >
                <span class="sr-only">Loading...</span>
              </div>
              </td>
            </ng-container> -->

              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef> Status </th>
                <td mat-cell *matCellDef="let element">
                  <div *ngIf="element.active === true">
                    <mat-icon style="color: rgb(4, 255, 4); margin-top : 7px;">check_circle</mat-icon>
                  </div>
                  <div *ngIf="element.active === false">
                    <mat-icon style="color: #ff2929 ;margin-top : 7px;">cancel</mat-icon>
                  </div>
                </td>
              </ng-container>

              <ng-container matColumnDef="refresh">
                <th mat-header-cell *matHeaderCellDef> Refresh </th>
                <td mat-cell *matCellDef="let element">
                  <button mat-icon-button matTooltip="Refresh" (click)="refreshVendor()"> <mat-icon>refresh</mat-icon>
                  </button>
                </td>
              </ng-container>
              <!-- (click)="checkExportAndImportStatus(element)" -->

              <ng-container matColumnDef="action">
                <th mat-header-cell *matHeaderCellDef> Action </th>
                <td mat-cell *matCellDef="let element" style="width: 160px;">
                    <!-- <button class="editBtn mr-1" matTooltip="Edit" (click)="updatePriceList(element)"><mat-icon style="font-size:15px;">edit_outline</mat-icon></button> -->
                    <button class="editBtn mr-1" matTooltip="Show Details" (click)="detailedLocationPl(element)"><mat-icon style="font-size:15px;" >edit_outline</mat-icon></button>
                    <button class="deleteForeverBtn" matTooltip="Delete" (click)="deleteItemsShowDialog(element)"><mat-icon style="font-size:15px;">delete_forever</mat-icon></button>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumnsVendor"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumnsVendor"></tr>
            </table>
          <!-- </div> -->
        </mat-card-content>
        <div class="dataMessage" *ngIf="dataSourceVendor?.data.length == 0"> No Data Available </div>
      </mat-card>
      <mat-paginator [showTotalPages]="5" [pageSize]="10"[pageSizeOptions]="pageSizes"></mat-paginator>
    </div>

    <mat-card *ngIf="createNewEvent">
      <button *ngIf=" createNewEvent" mat-icon-button class="plCloseBtn" matTooltip="go back"
      style=" float: right; margin-right: -10px; margin-top: 5px;" (click)="goBack()">
        <mat-icon>close</mat-icon>
      </button>
      <br><br>
      <div class="container">
        <form [formGroup]="priceListFormvendor" style="display: grid;">

          <!-- <mat-form-field appearance="outline">
            <mat-label>Vendor PriceList Name</mat-label>
            <input matInput placeholder="PriceList Name" [(ngModel)]="vendorPriceListName"
              formControlName="vendorPriceListName">
          </mat-form-field> -->

          <mat-form-field appearance="outline" style="width: 100% !important;">
            <mat-label>Contract Name</mat-label>
            <input matInput placeholder="Contract Name" [(ngModel)]="contractName" 
            formControlName="contractName" (keyup)="contractNameFunc()">
          </mat-form-field>

          <mat-form-field appearance="outline" style="width: 100% !important;">
            <mat-label>Contract Referance</mat-label>
            <input matInput placeholder="Contract Referance" [(ngModel)]="contractReferance"
              formControlName="contractReferance" (keyup)="contractReferanceFunc()">
          </mat-form-field>

          <mat-form-field appearance="outline" style="width: 100% !important;">
            <mat-label>Vendor</mat-label>
            <mat-select placeholder="Select Vendor" [(ngModel)]="selectedvendor" formControlName="vendor">
              <mat-option>
                <ngx-mat-select-search placeholderLabel="Vendor..." noEntriesFoundLabel="'no Vendor found'"
                  [formControl]="vendorFilterCtrl"></ngx-mat-select-search>
              </mat-option>
              <mat-option *ngFor="let vendor of vendorsBanks | async" [value]="vendor" (click)="selectingVendor(); selectedvendorFunc()">
                {{ vendor.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <!-- <mat-form-field appearance="none">
        <label>Vendor</label>
        <mat-select placeholder="Vendor" formControlName="vendor"
          (selectionChange)="selectVendor($event)">
            <mat-option>
              <ngx-mat-select-search placeholderLabel="Vendor Item..."
                noEntriesFoundLabel="'no Vendor Item found'"
                [formControl]="vendorFilterCtrl"></ngx-mat-select-search>
            </mat-option>
          <mat-option *ngFor="let vendor of vendorsBanks | async" [value]="vendor">
            {{ vendor.name }}
          </mat-option>
        </mat-select>

      </mat-form-field> -->

          <!-- <mat-form-field appearance="outline" style="width: 100% !important;">
            <mat-label>Select Location</mat-label>
            <mat-select placeholder="Select Location" [(ngModel)]="selectedvendorBranch"
              formControlName="selectedvendorBranch">
              <mat-option *ngFor="let rest of this.user.restaurantAccess" [value]="rest.restaurantIdOld" (click)="selectedvendorBranchFunc()">
                {{ rest.branchName }}
              </mat-option>
            </mat-select>
          </mat-form-field> -->

          <mat-form-field appearance="outline" style="width: 100% !important;">
            <mat-label>Select Location</mat-label>
            <mat-select placeholder="Select Location" [(ngModel)]="selectedvendorBranch" formControlName="selectedvendorBranch"
            [multiple]="true" #multiSelect>
              <mat-option>
                <ngx-mat-select-search placeholderLabel="Location..." noEntriesFoundLabel="'no Location found'"
                  [formControl]="locationFilterCtrl"></ngx-mat-select-search>
              </mat-option>
              <mat-option *ngFor="let rest of LocationBanks | async" [value]="rest.restaurantIdOld" (click)="selectedvendorBranchFunc()">
                {{ rest.branchName }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="outline" style="width: 100% !important;">
            <mat-label>Start Date</mat-label>
            <input matInput [matDatepicker]="picker3" [(ngModel)]="startvendordate" [min]="startDate"
              formControlName="startvendordate" placeholder="Start Date" (dateChange)="startDateFunc($event)"/>
            <mat-datepicker-toggle matSuffix [for]="picker3"  >
              <mat-icon matDatepickerToggleIcon>
                <img src="./../../assets/calender.png"/>
              </mat-icon>  
            </mat-datepicker-toggle>
            <mat-datepicker #picker3 ></mat-datepicker>
          </mat-form-field>

          <div *ngIf="isValidDates" class="commomForIcons">
            <mat-icon *ngIf="this.startDateResult.length == 0" class="check_circle" >check_circle
            </mat-icon>
          </div>

          <div *ngIf="isValidDates" class="commomForIcons">
            <mat-icon *ngIf="this.startDateResult.length > 0" class="check_close " >close
            </mat-icon>
          </div>

          <!-- <div *ngIf="!this.startDateResult" class="commomForIcons">
            <div class="spinner-border" role="status" *ngIf="startvendordate">
              <span class="sr-only">Loading...</span>
            </div>
          </div> -->

          <mat-form-field appearance="outline" style="width: 100% !important;">
            <mat-label>Expiry Date</mat-label>
            <input matInput [matDatepicker]="picker4" [min]="startvendordate" [(ngModel)]="expiryDate"
              formControlName="expiryDate" placeholder="Expiry Date" (dateChange)="endDateFunc($event)" />
            <mat-datepicker-toggle matSuffix [for]="picker4">
              <mat-icon matDatepickerToggleIcon>
                <img src="./../../assets/calender.png" />
              </mat-icon>
            </mat-datepicker-toggle>
            <mat-datepicker #picker4></mat-datepicker>
          </mat-form-field>

          <div *ngIf="expiryDate" class="commomForIcons">
            <mat-icon *ngIf="this.endDateResult.length == 0" class="check_circle " >check_circle
            </mat-icon>
          </div>

          <div *ngIf="expiryDate" class="commomForIcons">
            <mat-icon *ngIf="this.endDateResult.length > 0 " class="check_close " >close
            </mat-icon>
          </div>

        </form>

        <div class="creatBtn mt-2" *ngIf="!updateNewEvent">
          <button mat-raised-button class="createBtn button3" [disabled]="!priceListFormvendor.valid || this.startDateResult.length > 0"
            (click)="createVendorPriceList()">
            Create
          </button>
        </div>

        <div class="creatBtn mt-2" *ngIf="updateNewEvent">
          <button mat-raised-button class="createBtn button3" [disabled]="!priceListFormvendor.valid  || this.startDateResult.length > 0"
            (click)="editData()">
            Update
          </button>
        </div>

      </div>
      <br><br>
    </mat-card>
  <!-- </div> -->
<!-- </div> -->