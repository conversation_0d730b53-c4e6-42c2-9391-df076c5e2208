.container {
    max-width: 500px;
}

.creatBtn{
    text-align: center;
}

  .check_circle{
    color:green;
    font-size: 15px;
  }

.findButton{
	margin: 0px 20px 0px 20px!important;
	padding: 2px;
	border-color: white;
	float: right;
  }

  .tableIcons{
    margin-top: -7px;
  }

  .child{
	height: 20%;
	width: 80%;
	border-radius: 10px;
    text-align: center;
    background: black;
  }

.container {
  max-width: 650px;
}
  
.child{
  height: 160px;
}

.plBtn{
  border: 1px solid currentColor !important;
}

.check_circle{
  color:green;
  font-size: 27px;
}

.check_close{
  color:rgb(255, 8, 8);
  font-size: 27px;
}

.commomForIcons{
  margin-top: -41px;
  margin-left: 101%;
}

.deleteForeverBtn{
  width: 30px;
  border-radius: 4px;
  border: none;
  padding-top: 7px;
}

.editBtn{
  width: 30px;
  border-radius: 4px;
  border: none;
  padding-left: 7px;
  padding-top: 7px;
}


.plCloseBtn:hover{
  background-color: #464646 ;
}

.tableSelectedvendorBranchData{
  margin-bottom: 3px;
}

.addNewBtn{
  margin: -47px 1px 0.5px 0px;
  float: right;
}

::ng-deep .mat-select-search-inside-mat-option .mat-select-search-clear {
  top: 3px;
  display: none !important;
}
