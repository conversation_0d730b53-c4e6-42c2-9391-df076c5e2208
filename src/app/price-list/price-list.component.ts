
import { Component, OnInit, ViewChild } from '@angular/core';
import { AuthService, ShareDataService, VendorsService } from '../_services';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NotificationService } from '../_services/notification.service';
import { MasterdataupdateService } from '../_services/masterdataupdate.service';
import { MatDatepickerInputEvent, MatDialog, MatPaginator, MatTableDataSource } from '@angular/material';
import { UtilsService } from "../_utils/utils.service";
import { Vendor } from '../_models';
import { takeUntil } from 'rxjs/operators';
import { ReplaySubject, Subject } from 'rxjs';
import { FormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { SimpleDialogComponent } from '../_dialogs/simple-dialog/simple-dialog.component';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material';
import { MomentDateModule, MomentDateAdapter } from '@angular/material-moment-adapter';

export const MY_FORMATS = {
  parse: {
      dateInput: 'LL'
  },
  display: {
      dateInput: 'DD-MM-YYYY',
      monthYearLabel: 'YYYY',
      dateA11yLabel: 'LL',
      monthYearA11yLabel: 'YYYY'
  }
};
@Component({
  selector: 'app-price-list',
  templateUrl: './price-list.component.html',
  styleUrls: ['./price-list.component.scss', './../../common-dark.scss'],
  providers:[{ provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
            { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }
          ]
})
export class PriceListComponent implements OnInit {
  @ViewChild('fileInput') fileInput: any;
  user: any;
  multiBranchUser; branchSelected: boolean;
  restaurantId: any;
  createNewEvent = false;
  updateNewEvent = false;
  locationPriceList = false;
  vendorPriceList = false;
  priceListDialog = true;
  backToPl = false;
  public priceListForm: FormGroup;
  public priceListFormvendor: FormGroup;
  displayedColumns: string[];
  displayedColumnsVendor: any;
  dataSource = new MatTableDataSource();
  dataSourceVendor = new MatTableDataSource();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  restaurantIdBranch: any;
  public fileObj: any;
  public isValidFormat: boolean = true;
  public fileContent: any = '';
  public baseUrl: string;
  selectedRestaurantBranch: string;
  priceListName: string;
  selectedStartDateTime: string;
  selectedEndDateTime: string;
  event: string;
  selectedPriceListId: number;
  minDateToFinish = new Subject<string>();
  minDate;
  selectedRestaurantId: any;
  splitRestaurantId: any;
  dataSource1: any;
  priceListIdData: any;
  pageSizes = [];
  interval: any;
  vendors: Vendor[];
  public VendorBank: any[] = [];
  public vendorsBanks: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public vendorFilterCtrl: FormControl = new FormControl();
  public LocationBank: any[] = [];
  public LocationBanks: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public locationFilterCtrl: FormControl = new FormControl();
  protected _onDestroy = new Subject<void>();
  getPriceListId: any;
  plType: any;
  // vendorPriceListName: any;
  goToLocatonPlFlg = true;
  goToVendorPlFlg = true;
  contractName: any;
  contractReferance: any;
  vendorId: string;
  vendor: any;
  startAndEndDate = [];
  startDateResult = [];
  endDateResult = [];
  gettedDates: any;
  selectedvendorDropDown: any;
  searchValue: string;
  isValidDates: boolean = false;
  startvendordate: string;
  startDate: Date;
  selectedvendor: any;
  selectedvendorBranch: any;
  expiryDate: any;
  filteredLocations: string[] = [];
  constructor(
    private utils: UtilsService,
    private auth: AuthService,
    public fb: FormBuilder,
    private notifyService: NotificationService,
    private masterDataService: MasterdataupdateService,
    private vendorService: VendorsService,
    private sharedData: ShareDataService,
    private router: Router,
    private dialog: MatDialog
  ) {
    this.user = this.auth.getCurrentUser();
    this.priceListForm = fb.group({
      selectedBranch: ['', Validators.required],
      eventName: ['', Validators.required],
      startDate: ['', Validators.required],
      endDate: ['', Validators.required],
    });
    this.priceListFormvendor = fb.group({
      // vendorPriceListName : ['', Validators.required],
      contractName: ['', Validators.required],
      contractReferance: ['', Validators.required],
      vendor: ['', Validators.required],
      selectedvendorBranch: ['', Validators.required],
      startvendordate: ['', Validators.required],
      expiryDate: ['', Validators.required],
    });

    

    this.minDateToFinish.subscribe(r => {
      this.minDate = new Date(r);
    })
  }

  ngOnInit() {
    this.selectedRestaurantId =this.restaurantIdBranch;
    this.multiBranchUser = this.user.multiBranchUser
    if (!this.user.multiBranchUser) {
      this.restaurantId = this.user.restaurantAccess[0].restaurantIdOld
    }

    this.LocationBank = this.user.restaurantAccess
    this.LocationBanks.next(this.LocationBank.slice());
    this.locationFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
        this.locationfilterBanks();
    });


    this.restaurantId = this.user.restaurantId
    this.getVendors();
    this.getVendorList();
    // this.getDates();
  }

  ngOnDestroy() {
    clearInterval(this.interval);
    this._onDestroy.next();
    this._onDestroy.complete();
  }


  getVendors() {
    this.vendorService.getVendors(this.auth.getCurrentUser()).subscribe((data: Vendor[]) => {
      this.vendors = data;
      this.vendors.forEach(element => {
       this.vendorId = element.tenantId  
      });
      this.VendorBank = this.vendors
      this.vendorsBanks.next(this.VendorBank.slice());
      this.vendorFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
          this.vendorfilterBanks();
      });
      
    })
  }

  protected vendorfilterBanks() {
    if (!this.VendorBank) {
      return;
    }
    let search = this.vendorFilterCtrl.value;
    if (!search) {
      this.vendorsBanks.next(this.VendorBank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.vendorsBanks.next(
      this.VendorBank.filter(VendorBank => VendorBank.name.toLowerCase().indexOf(search) > -1)
    );
  }

  protected locationfilterBanks() {
    if (!this.LocationBank) {
      return;
    }
    let search = this.locationFilterCtrl.value;
    if (!search) {
      this.LocationBanks.next(this.LocationBank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.LocationBanks.next(
      this.LocationBank.filter(LocationBank => LocationBank.branchName.toLowerCase().indexOf(search) > -1)
    );
  }

  vendorPriceListfunc(){
    this.getVendorList();
    this.vendorPriceList = true;
    this.locationPriceList = false;
    this.priceListDialog = false;
  }
  locationPriceListfunc(){
    this.fetchPriceList();
    this.locationPriceList = true;
    this.vendorPriceList = false;
    this.priceListDialog = false;
  }

  dateChange(e) {
    this.minDateToFinish.next(e.value.toString());
  }

  showInputs() {
    this.createNewEvent = true;
    this.startDate = new Date();
      this.priceListFormvendor.get('contractReferance').disable();
      this.priceListFormvendor.get('vendor').disable();
      this.priceListFormvendor.get('selectedvendorBranch').disable();
      this.priceListFormvendor.get('startvendordate').disable();
      this.priceListFormvendor.get('expiryDate').disable();
  }

  contractNameFunc(){
    if(this.contractName){
      this.priceListFormvendor.get('contractReferance').enable();
    }else{
      this.priceListFormvendor.get('contractReferance').disable();
    }
  }

  contractReferanceFunc(){
    if(this.contractReferance){
      this.priceListFormvendor.get('vendor').enable();
    }else{
      this.priceListFormvendor.get('vendor').disable();
    }

  }

  selectedvendorFunc(){
    if(this.selectedvendor){
      this.priceListFormvendor.get('selectedvendorBranch').enable();
    }else{
      this.priceListFormvendor.get('selectedvendorBranch').disable();
    }
  }

  selectedvendorBranchFunc(){
    if(this.selectedvendorBranch){
      this.priceListFormvendor.get('startvendordate').enable();
    }else{
      this.priceListFormvendor.get('startvendordate').disable();
    }
  }

  goBack() {
    this.createNewEvent = false
    this.updateNewEvent = false
    this.fetchPriceList();
    this.getVendorList();
    this.priceListForm.reset();
    this.priceListFormvendor.reset();
    this.isValidDates = false
  }

  showInputsEnable(){
    this.priceListFormvendor.get('contractReferance').enable();
    this.priceListFormvendor.get('vendor').enable();
    this.priceListFormvendor.get('selectedvendorBranch').enable();
    this.priceListFormvendor.get('startvendordate').enable();
    this.priceListFormvendor.get('expiryDate').enable();
  }

  updatePriceList(element){
    this.updateNewEvent = true;
    this.createNewEvent = true;
    // this.showInputs(); 
    this.showInputsEnable(); 
    this.selectedPriceListId = element.priceListId
    this.plType = element.type
    this.selectedRestaurantBranch = element.selectedBranch;
    this.priceListName = element.priceListName;
    this.selectedStartDateTime = element.startDate;
    this.selectedEndDateTime = element.endDate;

    // this.vendorPriceListName = element.vendorPriceListName;
      this.contractName = element.contract;
      this.contractReferance = element.contractRef;
      this.selectedvendorBranch = element.selectedvendorBranch;
      this.startvendordate = element.startvendordate;
      this.expiryDate = element.expireDate;
  }

  createPriceList() {  
    let inputObj = {}
    inputObj['tenantId'] = this.user.tenantId;
    inputObj['restaurantId'] = this.user.restaurantId;
    inputObj['selectedBranch'] = this.priceListForm.value.selectedBranch;
    inputObj["priceListName"] = this.priceListForm.value.eventName;
    inputObj['startDate'] = this.priceListForm.value.startDate;
    inputObj['endDate'] = this.priceListForm.value.endDate;
    inputObj["type"] = "location"
    this.masterDataService.createPriceList(inputObj).subscribe((response: any) => {
      if (response.success === true ) {
        this.utils.snackBarShowSuccess("Contract Updated successfully")
        this.fetchPriceList();
      }
      else {
        this.utils.snackBarShowError("Something Worng, please try again later")
      }
    });
  }


  createVendorPriceList() {
    let inputObj = {}
    inputObj['tenantId'] = this.user.tenantId;
    inputObj['restaurantId'] = this.priceListFormvendor.value.selectedvendorBranch;
    // inputObj['vendorPriceListName'] = this.priceListFormvendor.value.vendorPriceListName
    inputObj['contract'] = this.priceListFormvendor.value.contractName;
    inputObj['contractRef'] = this.priceListFormvendor.value.contractReferance;
    inputObj["name"] = this.priceListFormvendor.value.vendor.name;
    inputObj['selectedvendorBranch'] = this.priceListFormvendor.value.selectedvendorBranch;
    inputObj['startvendordate'] = this.utils.dateCorrection(this.priceListFormvendor.value.startvendordate);
    inputObj['expDate'] = this.utils.dateCorrection(this.priceListFormvendor.value.expiryDate);
    inputObj["type"] = "vendor";
    inputObj["vendorId"] = this.priceListFormvendor.value.vendor.tenantId;
    // inputObj["vendorId"] = this.vendors;
    this.masterDataService.createVendor(inputObj).subscribe((response: any) => {
      if (response.success === true ) {
        this.utils.snackBarShowSuccess("Contract created successfully")
        this.getVendorList();
      }
      else {
        this.utils.snackBarShowError("Something Worng, please try again later")
      }
    });

    this.isValidDates = false
    this.priceListFormvendor.reset();
    this.goBack();
  }

  // setkeysInPriceList(){
  //   let inputObj = {}
  //   inputObj['tenantId'] = this.user.tenantId;
  //   this.masterDataService.setkeysInPriceList(inputObj).subscribe((response: any) => {
  //     if (response.success === true ) {
  //       this.utils.snackBarShowSuccess("Contract created successfully")
  //       console.log(response);
        
  //     }
  //     else {
  //       this.utils.snackBarShowError("Something Worng, please try again later")
  //     }
  //   });
  // }


  fetchPriceList() {
    let obj = {}
    obj['tenantId'] = this.user.tenantId;
    obj['restaurantId'] = this.restaurantId;
    obj['type'] = "vendor"
    // obj['type'] = "location"
    this.masterDataService.fetchPriceList(obj).subscribe((response: any) => {
      if (response.success === true) {
        this.dataSource.data = response.data
        this.displayedColumns = ['priceListId','priceListName','priceListrestaurantId', 'createTs', 'startDate', 'endDate','status','action'];
        this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
        this.dataSource.paginator = this.paginator;
      }
    });    
  }

  refreshVendor(){
    this.getVendorList();
  }

  getVendorList(){
    let obj = {}
    obj['tenantId'] = this.user.tenantId;
    // obj['restaurantId'] = this.user.restaurantId;
    obj['type'] = "vendor"
    this.masterDataService.getVendorList(obj).subscribe((response: any) => {
      if (response.success === true) {
        this.displayedColumnsVendor = ['priceListId','vendorName','priceListrestaurantId', 'createTs', 'startDate', 'endDate','status','action'];
        this.dataSourceVendor.data = response.result
        this.pageSizes = this.utils.getPageSizes(this.dataSourceVendor.data)
        this.dataSourceVendor.paginator = this.paginator;
      }
    }); 
  }

  filterByVendor(value){
    if(this.selectedvendorDropDown == undefined){
      this.getVendorList();
    }else{
      let obj = {}
      obj['tenantId'] = this.user.tenantId;
      // obj['restaurantId'] = this.restaurantId;
      obj['vendorId'] = value.tenantId
      this.masterDataService.filterByVendor(obj).subscribe((response: any) => {
        if (response.success === true) {
          this.displayedColumnsVendor = ['priceListId','vendorName','priceListrestaurantId', 'createTs', 'startDate', 'endDate','status','action'];
          this.dataSourceVendor.data = response.data
          this.pageSizes = this.utils.getPageSizes(this.dataSourceVendor.data)
          this.dataSourceVendor.paginator = this.paginator;
        }
      });   
    }
  }

  checkExportAndImportStatus(element){
    let obj = {}
    obj['tenantId'] = this.user.tenantId;
    obj['priceListId'] = element.templateNo;
    this.masterDataService.checkExportAndImportStatus(obj).subscribe((response: any) => {
      if (response.success === true) {
        this.dataSource.data = response.data

      }
    });
    this.fetchPriceList();
  }


  editData(){
  if(this.plType == "location"){
    let obj = {}
    obj['tenantId'] = this.user.tenantId;
    obj['restaurantId'] = this.priceListFormvendor.value.selectedvendorBranch;
    obj['priceListId'] = this.selectedPriceListId;
    obj['type'] = this.plType;
    obj['selectedBranch'] = this.priceListForm.value.selectedBranch;
    obj["priceListName"] = this.priceListForm.value.eventName;
    obj['startDate'] = this.priceListForm.value.startDate;
    obj['endDate'] = this.priceListForm.value.endDate;
    this.masterDataService.updatePriceList(obj).subscribe((response: any) => {
      if (response.success) {
        this.utils.snackBarShowSuccess(response.message)
      }
    });
    this.fetchPriceList();
  }else{
    let obj = {}
        obj['tenantId'] = this.user.tenantId;
        obj['restaurantId'] = this.priceListFormvendor.value.selectedvendorBranch;
        obj['priceListId'] = this.selectedPriceListId;
        obj['type'] = this.plType;
        // obj['vendorPriceListName'] = this.priceListFormvendor.value.vendorPriceListName
        obj['contract'] = this.priceListFormvendor.value.contractName;
        obj['contractRef'] = this.priceListFormvendor.value.contractReferance;
        obj["name"] = this.priceListFormvendor.value.vendor.name;
        obj['selectedvendorBranch'] = this.priceListFormvendor.value.selectedvendorBranch;
        obj['startvendordate'] = this.priceListFormvendor.value.startvendordate;
        obj['expDate'] = this.priceListFormvendor.value.expiryDate;
        this.masterDataService.updatePriceList(obj).subscribe((response: any) => {
          if (response.success) {
            this.utils.snackBarShowSuccess("Contract updated Succesfully")
          }
        });
        this.getVendorList();
    }
    this.goBack();
  }

  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
    this.dataSourceVendor.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
    if (this.dataSourceVendor.paginator) {
      this.dataSourceVendor.paginator.firstPage();
    }
  }

  downloadPriceList(element) {
    let obj = {}
    obj['tenantId'] = this.user.tenantId;
    obj['restaurantId'] = this.restaurantId;
    obj['priceListName'] = element.priceListName;
    obj['priceListId'] = element.priceListId;
    obj['selectedBranch'] = element.selectedBranch;
    // obj['vendorId'] = element.vendorId
    obj['fullName'] = obj["priceListId"] + '.xlsx'
    this.masterDataService.downloadPriceList(obj).subscribe(res => {
      if (res['success'] == true) {
        this.utils.snackBarShowSuccess("Downloaded successfully")
        var downloadLink = document.createElement("a");
        downloadLink.href = 'data:application/vnd.ms-excel;base64,' + res.eFile;
        downloadLink.download = res.fileName;
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
      } else {
        this.utils.snackBarShowError("Something went Wrong")
      }
    });
  }

  checkUpload(element) {
    document.getElementById("getFile").click()
  }

  processPriceList(event, element) {
    let files = event.target.files;
    let file = files[0];
    this.fileObj = file;
    let possibleFormat = ['XLSX', 'XLS']
    let fileExtension = this.fileObj.name.split(".")[1];
    if (!possibleFormat.includes(fileExtension.toUpperCase())) {
      this.isValidFormat = false;
      this.utils.snackBarShowWarning("Please upload excel format only")
    } else {
      this.isValidFormat = true;
      var reader = new FileReader();
      reader.onloadend = (e) => {
        this.fileContent = reader.result;
        this.uploadFile(this.fileContent, this.fileObj.name, this.fileObj.type, element);
      };
      reader.readAsDataURL(file);
    }

  }


  uploadFile(file, name, type, element) {
    let obj = this.user;
    let inputObj = {
      fileObj: file,
      name: name,
      type: type,
      tenantId: this.user.tenantId,
      priceListName: element.priceListName,
      priceListId: element.priceListId,
      restaurantId: this.restaurantId,
      selectedBranch: element.selectedRestaurantIds,
      templateNo: element.templateNo,
      date: element.createTs,
      token: obj["token"],
      email: obj["email"],
    }
    this.masterDataService.processPriceList(inputObj).subscribe((response: any) => {
      if (response.success) {
        this.utils.snackBarShowSuccess(response['message'])
      }
    });
  }

  detailedLocationPl(obj){
      this.sharedData.pricelist(obj);
      this.router.navigate(['/home/<USER>']);
    }

    deleteItemsShowDialog(element){
      this.dialog.open(SimpleDialogComponent, {
        data: {
          title: 'Delete Contract',
          msg: 'Are you sure you want to delete?',
          ok: function () {
            this.deletePl(element);
          }.bind(this)
        }
      });
  }

    deletePl(element){
      let obj = {}
      obj['tenantId'] = element.tenantId;
      obj['rId'] = element.restaurantId;
      obj['vendorId'] = element.vendorId;
      obj['priceListId'] = element.priceListId;
      this.getPriceListId = element.priceListId;
      this.masterDataService.deletePl(obj).subscribe(res => {
        if (res['success'] == true) {
          this.utils.snackBarShowSuccess("Contract Deleted Successfully")
          this.fetchPriceList();
          this.getVendorList();
        } else {
          this.utils.snackBarShowError("Something went Wrong")
        }
      });

    }
    // editPl(element){
    //   this.updatePl(element);
    // }

    // updatePl(element){
    //   if(element.type == "location"){
    //     console.log ("location................")
    //     var InitDate = new Date(); 
    //     let inputObj = {}
    //     inputObj['tenantId'] = this.user.tenantId;
    //     inputObj['restaurantId'] = this.user.restaurantId;
    //     inputObj['priceListId'] = this.getPriceListId
    //     inputObj['selectedBranch'] = this.priceListForm.value.selectedBranch;
    //     inputObj['selectedBranch'] = this.priceListForm.value.selectedBranch;
    //     inputObj["priceListName"] = this.priceListForm.value.eventName;
    //     inputObj['startDate'] = this.priceListForm.value.startDate.toLocaleString();
    //     inputObj['endDate'] = this.priceListForm.value.endDate.toLocaleString();
    //     inputObj["type"] = "location"
    //     this.masterDataService.updatePl(inputObj).subscribe((response: any) => {
    //       if (response.success === true ) {
    //         this.utils.showSuccess("Record updated successfully",'',  3000)
    //         this.fetchPriceList();
    //       }
    //       else {
    //         this.utils.showError("Something Worng, please try again later",'', 3000)
    //       }
    //     });

    //   }else{
    //     console.log ("vendor................")
    //     var InitDate = new Date();    
    //     let inputObj = {}
    //     inputObj['tenantId'] = this.user.tenantId;
    //     inputObj['restaurantId'] = this.user.restaurantId;
    //     inputObj['priceListId'] = this.getPriceListId
    //     inputObj['vendorPriceListName'] = this.priceListFormvendor.value.vendorPriceListName
    //     inputObj['contract'] = this.priceListFormvendor.value.contractName;
    //     inputObj['contractRef'] = this.priceListFormvendor.value.contractReferance;
    //     inputObj["name"] = this.priceListFormvendor.value.vendor.name;
    //     inputObj['selectedvendorBranch'] = this.priceListFormvendor.value.selectedvendorBranch;
    //     inputObj['startvendordate'] = this.priceListFormvendor.value.startvendordate.toLocaleString();
    //     inputObj['expDate'] = this.priceListFormvendor.value.expiryDate.toLocaleString();
    //     inputObj["type"] = "vendor"
    //     this.masterDataService.updatePl(inputObj).subscribe((response: any) => {
    //       if (response.success === true ) {
    //         this.utils.showSuccess("Record updated successfully",'',  3000)
    //         this.getVendorList();
    //       }
    //       else {
    //         this.utils.showError("Something Worng, please try again later",'', 3000)
    //       }
    //     });

    //   }

    // }

    goToVendorPl(){
      this.priceListDialog = true
    }
    
    goToLocatonPl(){
      this.priceListDialog = true
    }


    // goToPl(){
    //   if(this.priceListDialog = true){
    //     this.priceListDialog = false;
    //   }
    //   this.priceListDialog = true
    // }

    startDateFunc(event: MatDatepickerInputEvent<Date>) {

      if(this.startvendordate){
        this.priceListFormvendor.get('expiryDate').enable();
      }else{
        this.priceListFormvendor.get('expiryDate').disable();
      }

      let inputObj = {}
      inputObj['tenantId'] = this.user.tenantId;
      inputObj['restaurantId'] = this.priceListFormvendor.value.selectedvendorBranch;
      inputObj['vendorId'] = this.priceListFormvendor.value.vendor.tenantId;
      inputObj['startvendordate'] = this.priceListFormvendor.value.startvendordate;
      // inputObj['expDate'] = this.priceListFormvendor.value.expiryDate;
      this.masterDataService.getContractStartDateByVendor(inputObj).subscribe((response: any) => {
        if (response.success === true) {
          this.startDateResult = response.result
          this.isValidDates = true
          if(this.startDateResult.length > 0){
            // this.notifyService.showWarning(`Contract Already Date Exist for ${this.startDateResult[0].priceListId}`,'')
            this.utils.snackBarShowWarning("Overlapping contract date is not allowed")

          }
        }
      });

    }

    endDateFunc(event: MatDatepickerInputEvent<Date>){
      let inputObj = {}
      inputObj['tenantId'] = this.user.tenantId;
      inputObj['restaurantId'] = this.priceListFormvendor.value.selectedvendorBranch;
      inputObj['vendorId'] = this.priceListFormvendor.value.vendor.tenantId;
      // inputObj['startvendordate'] = this.priceListFormvendor.value.startvendordate;
      inputObj['expDate'] = this.priceListFormvendor.value.expiryDate;
      this.masterDataService.getContractEndDateByVendor(inputObj).subscribe((response: any) => {
        if (response.success === true) {
          this.endDateResult = response.result
          if(this.endDateResult.length > 0){
            // this.notifyService.showWarning(`Contract Already Date Exist for ${this.endDateResult[0].priceListId}`,'')
            this.utils.snackBarShowWarning("Overlapping contract date is not allowed")
          }
        }
      });
    }

    // getDates() {
    //   let inputObj = {}
    //   inputObj['tenantId'] = this.user.tenantId;
    //   inputObj['restaurantId'] = this.user.restaurantId;
    //   inputObj['vendorId'] = this.priceListFormvendor.value.vendor.tenantId;
    //   inputObj['startvendordate'] = this.priceListFormvendor.value.startvendordate;
    //   inputObj['expDate'] = this.priceListFormvendor.value.expiryDate;
    //   this.masterDataService.getContractDateByVendor(inputObj).subscribe((response: any) => {
    //     if (response.success === true) {
    //       this.startAndEndDate = response.result
    //       // this.startAndEndDate.forEach(element => {
    //         // this.gettedDates = element;
    //       //  });
    //       if(this.startAndEndDate.length > 0){
    //         this.notifyService.showWarning(`Price List Already Date Exist for ${this.startAndEndDate[0].priceListId}`,'')
    //       }
    
    //     }
    
    //   });
    // }

    clearFilter(){
      this.searchValue = ''
      this.getVendorList();
    }

    selectingVendor(){
      this.startvendordate  = ''
    }

  locSearch(text,element){
    let location = element.selectedvendorBranch;
    if(text != ''){
      if(location.length == 0){
        element.selectedvendorBranch = element.restaurantId.filter(item => item.toLowerCase().includes(text.toLowerCase()));
      }else{
        element.selectedvendorBranch = location.filter(item => item.toLowerCase().includes(text.toLowerCase()));
      }
    }else{
      element.selectedvendorBranch = element.restaurantId
    }
  }
}