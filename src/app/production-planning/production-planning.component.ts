import { Component, OnInit, ViewChild } from '@angular/core';
import { MenuItemService, GlobalsService, ShareDataService } from '../_services';
import { AuthService } from '../_services';
import { UtilsService } from '../_utils/utils.service';
import { DateUtilsService } from '../_services/';

@Component({
  selector: 'app-production-planning',
  templateUrl: './production-planning.component.html',
  styleUrls: ['./production-planning.component.scss']
})
export class ProductionPlanningComponent implements OnInit {
  private menuItemObj: any;
  allowEdit: boolean;
  allowActions: boolean;
  title = "Production Planning";
  tableData: any;
  productionPlanningArr: any[] = [];
  @ViewChild('digiTable') digiTable: any;
  restaurant: any;
  rId: any;
  ngOnInit() {
  }


  constructor(private auth: AuthService, private menuItems: MenuItemService,
    private utils: UtilsService, private dateUtils: DateUtilsService, private sharedData: ShareDataService) {
    this.allowEdit = false;
    this.allowActions = false;
  }

  getItems(reqObj: any) {
    this.allowActions = false;
    let obj = this.utils.getUserReqObj(this.auth.getCurrentUser());
    let date = this.dateUtils.getDateParam(reqObj.date);
    obj.myObj = reqObj;
    obj.myObj.date = date;
    this.menuItems.getItems(obj).subscribe(res => {
      this.menuItemObj = res;
      this.allowEdit = !this.menuItemObj.newObj.disable;
      this.tableData = {};
      this.tableData.columns = GlobalsService.prodPlanColumns;
      this.productionPlanningArr = this.utils.objToArr(this.menuItemObj.newObj.obj);
      this.tableData.data = this.productionPlanningArr;
      this.tableData.data.forEach((item: any) => {
        item.diff = item.predicted - item.estimated;
        if (item.predicted === 0) {
          item.diffP = 0;
        }
        else {
          if (item)
            item.diffP = (item.predicted - item.estimated) / item.predicted;
        }
      });
      if (this.tableData.data.length > 0) {
        this.allowActions = true;
      }
      setTimeout(() =>  document.getElementById('table').scrollIntoView(),500)
      if(this.digiTable){
        this.digiTable.category.setValue('')
        this.digiTable.searchText.setValue('')
      }


    }, err => {
      console.log(err)
    });
  }

  saveItems() {
    this.sharedData.ForcastRes.subscribe(ForcastRes => { 
      this.rId  = ForcastRes.branchArray;
    });

    let obj = this.utils.getUserReqObj(this.auth.getCurrentUser());
    obj["rId"] = this.rId
    obj.items = JSON.stringify(
      this.utils.arrtoObj(
        this.tableData.data
      )
    );
    obj.ids = this.menuItemObj.newObj.ids;
    this.menuItems.updateItems(obj).subscribe(res => {
      if (res.result === GlobalsService.success) {
        this.utils.openSnackBar(res.desc, null, 3000);
      }
    }, err => console.log(err));

  }

  exportToExcel(reqObj) {
    let obj = this.utils.getUserReqObj(this.auth.getCurrentUser());
    obj.productionPlanningItems = this.digiTable.sortedData.data;
    obj.objDetails = reqObj;
    this.menuItems.exportProductionPlanning(obj).subscribe(res => {
      window.open('data:text/csv;base64,' + res.eFile);
    }, err => {
      console.log(
        err
      )
    });
  }

  printItems(reqObj) {
    let obj = this.utils.getUserReqObj(this.auth.getCurrentUser());
    obj.productionPlanningItems = JSON.stringify(this.digiTable.sortedData.data);
    obj.objDetails = JSON.stringify(reqObj);
    this.menuItems.printProductionPlanning(obj).subscribe(res => {
      window.open('data:application/pdf;base64,' + res.eFile);
    }, err => {
      console.log(
        err
      )
    });
  }


}
