import { Component, OnInit, Input, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { PurchaseItem } from '../_models/';
import { GlobalsService } from '../_services';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog } from '@angular/material';
import { SelectionModel } from '@angular/cdk/collections';
import { UtilsService } from '../_utils/utils.service';
import { ShareDataService } from '../_services/share-data.service';
import { PurchasesService, AuthService } from '../_services/';
import { SimpleDialogComponent } from '../_dialogs/simple-dialog/simple-dialog.component';
import { NotificationService } from '../_services/notification.service';
import { FormBuilder, FormControl, FormGroup, Validators } from "@angular/forms";
import { ReplaySubject, Subject } from 'rxjs';
import {takeUntil } from 'rxjs/operators';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material';
import { MomentDateModule, MomentDateAdapter } from '@angular/material-moment-adapter';
import { SharedFilterService } from '../_services/shared-filter.service';

export const MY_FORMATS = {
  parse: {
      dateInput: 'LL'
  },
  display: {
      dateInput: 'DD-MM-YYYY',
      monthYearLabel: 'YYYY',
      dateA11yLabel: 'LL',
      monthYearA11yLabel: 'YYYY'
  }
};
@Component({
  selector: 'app-purchase-approval',
  templateUrl: './purchase-approval.component.html',
  styleUrls: ['./purchase-approval.component.scss', './../../common-dark.scss'],
  providers:[{ provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
            { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }
          ]
})
export class PurchaseApprovalComponent implements OnInit {
  purReqList: any = []
  @ViewChild(MatSort) sort: MatSort;
  searchText: string;
  searchValue: string;
  windowCurrentUrl: string;
  tempData: any;
  vendorId: any;
  @ViewChild(MatPaginator) set paginator(value: MatPaginator) {
    if (this.dataSource) {
      this.dataSource.paginator = value;
    }
  };
  restaurantId: any;
  selectedBranch: any;
  selectedVendorName: any;
  selectedApprovalStatus: any ;
  selectedOrderStatus: any;
  selectedStartDate: any;
  selectedEndDate: any;
  multiBranchUser; branchSelected: boolean;
  selected: any;
  dataSource: MatTableDataSource<PurchaseItem> = new MatTableDataSource<any> ();
  displayedColumns: string[];
  selection = new SelectionModel<PurchaseItem>(true, []);
  totalOrderValue = 90000;
  isVendor: boolean = false;
  purchaseStatus: string[] = ['All', 'Complete', 'Partial', 'Open'];
  vendorsList: any[] = ['All'];
  filteredByDateList: any[];
  filterKeys = { status: { orderStatus: 'All' }, vendorDetails: { vendorName: 'All' } }
  date: any = { startDate: '', endDate: '' };
  pageSizes: any[];
  title;
  user;
  pickerDateRange: any = {};
  dateRange: any = null;
  purchaseStat = new FormControl();
  vendors = new FormControl();
  private startDate = new FormControl();
  private endDate = new FormControl();
  branches: any[];
  getBranchData: any[]
  purchaseRequestApprovalForm: FormGroup;
  checkSelection : boolean ;

  public purReqApprovalvendors: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public VendorData: any[] = [];
  public vendorFilterCtrl: FormControl = new FormControl();
  protected _onDestroy = new Subject<void>();
  dialogRef: any;
  purchaseApproval = encodeURI(GlobalsService.purchaseApproval)
  private unsubscribe$ = new Subject<void>();
  sharedFilterData: any = {};
  purchaseApprovalDtlUrl = encodeURI(GlobalsService.purchaseApprovalDtl)

  constructor(private utils: UtilsService, private router: Router,
    private auth: AuthService,
    private purchases: PurchasesService,
    private notifyService: NotificationService,
    private dialog: MatDialog,
    private sharedData: ShareDataService,
    private fb: FormBuilder,
    private sharedFilterService: SharedFilterService) {
      this.user = this.auth.getCurrentUser();
      this.multiBranchUser = this.user.multiBranchUser;
    this.purchaseRequestApprovalForm = this.fb.group({
      branchSelection: [null, Validators.required]
    });

    this.sharedFilterService.getFilteredPurReqApproval.pipe(takeUntil(this.unsubscribe$)).subscribe(obj => 
      this.sharedFilterData = obj
    );    

      this.sharedData.sharedBranchData.pipe(takeUntil(this.unsubscribe$)).subscribe((val) => {
        this.getBranchData = val;
        if(this.getBranchData.length == 0){
          this.branches = this.user.restaurantAccess;
        }else if(this.getBranchData.length == 1 ){  
            const toSelect = this.getBranchData.find(data => data.branchName == this.getBranchData[0].branchName);
            if(toSelect != this.sharedFilterData.restaurantId){
              this.sharedFilterData = '';
              this.vendors.setValue(['All']);
              this.selectedApprovalStatus = 'pending';
              this.startDate.setValue(null);
              this.endDate.setValue(null);
            }
            this.purchaseRequestApprovalForm.get('branchSelection').setValue(toSelect);
            this.branches = this.getBranchData
            this.filterByBranch(this.purchaseRequestApprovalForm.value.branchSelection);
        }else{
          this.branches = this.getBranchData
        }
    });

  }

  ngOnInit() {
    if (!this.branchSelected) {
      if (!this.user.multiBranchUser) {
        this.restaurantId = this.user.restaurantAccess[0].restaurantIdOld;
        this.branchSelected = true;
        let reqObj: any = {
          tenantId: this.user.tenantId,
          restaurantId: this.restaurantId
        }
        this.setupCall();
      }
    }
  this.displayedColumns = ['poId', 'vendorName','total', 'approvalStatus'];
  }

  ngOnDestroy() {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
    if(!this.router.url.includes(this.purchaseApprovalDtlUrl)){
      this.sharedFilterService.getFilteredPurReqApproval['_value'] = ''
    }
  }

  setupCall() {
    this.user = this.auth.getCurrentUser();
    this.multiBranchUser = this.user.multiBranchUser
      let obj = {
        tenantId: this.user.tenantId,
        uType: this.user.uType,
        restaurantId: this.restaurantId,
        userEmail:this.auth.getCurrentUser().email,
      }

      if(this.vendors.value && this.tempData){
        let array = []
        array.push(this.vendors.value)
        array = array[0].filter(item => item !== "All");
        // "filter data using vendorname"
        const filteredData = this.tempData.filter(item => array.includes(item.vendorDetails.vendorName));
        // "remove Duplicate data"
        const uniqueData = filteredData.filter((obj, index, self) => {
          return index === self.findIndex(item => item.vendorDetails.vendorName === obj.vendorDetails.vendorName);
        });
  
        const vendorIds = [];
        uniqueData.forEach((obj: any) => {
          vendorIds.push(obj.vendorDetails.vendorId);
        });
  
        if(vendorIds.length === 0){
          obj['vendorId'] = ['All'];
          this.vendors.setValue(['All']);
        }else{
          obj['vendorId'] = vendorIds
        }
      }else{
        obj['vendorId'] = ['All'];
      }
    
      if(this.startDate.value && this.endDate.value){
        obj['startDate'] = this.utils.dateCorrection(this.startDate.value);
        obj['endDate'] = this.utils.dateCorrection(this.endDate.value);
      }else{
        obj['startDate'] = null;
        obj['endDate'] = null;
      }

      this.purchases.getPrs(obj).subscribe(data => {        
        this.purReqList = data;
        this.pageSizes = this.utils.getPageSizes(this.purReqList);
        if(this.tempData == undefined){
          this.tempData = data;
        }
        if(this.sharedFilterData == ''){
          this.purReqList.forEach(element => { 
          this.vendorsList.push(element.vendorDetails.vendorName);
          this.vendorsList = this.vendorsList.filter((j, l, arr) => arr.indexOf(j) === l);
          this.VendorData = this.vendorsList
          this.purReqApprovalvendors.next(this.VendorData.slice());
          this.vendorFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
              this.vendorfilterBanks();
          });
        });
      }
        this.getPRList();
      });
  }

  getPRList() {
    this.initializeInputObj()
    this.vendorsList = ['All']
    this.purReqList.forEach(element => {

      if (element.isPoCreated) {
        element.progress = 'Completed'
      }
      else {
        element.progress = 'Pending'
      }
      if (element.hasOwnProperty('validityDate')) {
        let currDate = new Date('June 04, 2021 01:24:00')
        let dateStr = element.validityDate
        let validityDate = new Date(dateStr)
        if (validityDate.getTime() < currDate.getTime()) {
          element.progress = 'Expired'
        }
      }
      this.getApprovalStatus(element)
    });
    if(this.selectedApprovalStatus == null){
      this.selectedApprovalStatus = 'pending'
    }else{
      if(this.sharedFilterData.approvalStatus){
        this.selectedApprovalStatus = this.sharedFilterData.approvalStatus;
      }
    }
    this.filterByStatus(this.selectedApprovalStatus)

  }

   getApprovalStatus(element: any) {
    element.approvalDetail.forEach((el) => {
      if (el.role === this.user.role) {
        element["approvalStatus"] = `${el.status}`;
      }
    });
  }

  protected vendorfilterBanks() {
    if (!this.VendorData) {
      return;
    }
    let search = this.vendorFilterCtrl.value;
    if (!search) {
      this.purReqApprovalvendors.next(this.VendorData.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.purReqApprovalvendors.next(
      this.VendorData.filter(VendorData => VendorData.toLowerCase().indexOf(search) > -1)
    );
  }

  private initializeInputObj() {
    this.dataSource = new MatTableDataSource<PurchaseItem>(this.purReqList);
    this.title = "Purchase request Approval";
    this.dataSource.sort = this.sort;
    this.filteredByDateList = this.purReqList;
    this.dataSource.sort = this.sort;
    this.dataSource.filterPredicate = (data, filter) => {
      let filt = JSON.stringify(data).includes(filter)
      return filt
    }
  }
  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleUpperCase();
  }

  showSpecialOrder() {
    this.utils.snackBarShowError('Special Order Dialog');
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  masterToggle() {
    this.isAllSelected() ?
        this.selection.clear() :
        this.dataSource.data.forEach(row => this.selection.select(row));
  }

  getTotal(e?: Event) {
    this.selection.selected.forEach(item => {
      item.totalValue = item.orderQty * item.unitPrice;
    });
    this.totalOrderValue = this.utils.getTotal(this.selection.selected, 'totalValue') / 100000;
  }

  acceptOrder(obj) {
   this.utils.snackBarShowSuccess(`printing order ${obj.id}`);
  }

  saveOrders() {
   this.utils.snackBarShowSuccess(`saving orders no of orders : ${this.selection.selected.length}`);
  }

  receiveOrder(obj) {

    let uniqueArray = [];
    this.tempData.forEach(obj => {
      // Check if an object with the same id already exists in uniqueArray
      if (!uniqueArray.some(item => item.prId === obj.prId)) {
        uniqueArray.push(obj); // Push the object to uniqueArray if it's not a duplicate
      }
    });

    let inputObj = {
      restaurantId : this.purchaseRequestApprovalForm.value.branchSelection,
      selectedStartDate : this.startDate.value,
      selectedEndDate : this.endDate.value,
      vendorName : [this.vendors.value],
      data : uniqueArray,
      approvalStatus : this.selectedApprovalStatus,
    }    
    this.sharedFilterService.getFilteredPurReqApproval.next(inputObj);
    this.sharedData.changeOrder(obj);
    this.router.navigate(['/home/<USER>']);
  }

  allFilter() {
    let tmp = this.filteredByDateList
    let prev = this.filteredByDateList
    Object.keys(this.filterKeys).forEach(element => {
      Object.keys(this.filterKeys[element]).forEach(nestElement => {
        if (this.filterKeys[element][nestElement] != 'All') {
          tmp = prev.filter(purOrder => {
            if (purOrder[element][nestElement] !== undefined) {
              return purOrder[element][nestElement].toLowerCase() === this.filterKeys[element][nestElement].toLowerCase()
            }
          }
          )
          prev = tmp
        }
      });
    });
    this.dataSource.data = tmp
  }

  selectPurchaseStatus(status) {
    this.filterKeys.status = { orderStatus: status }
    this.allFilter()
  }

  selectVendorFilter(vendor) {
    this.vendorId = vendor
    if(this.selectedApprovalStatus != undefined){
      if(this.sharedFilterData.approvalStatus){
        this.sharedFilterData.approvalStatus = null;
      }
    }
    this.setupCall();
  }

  filterByDate() {
    this.setupCall();
  }

  resetForm() {
    this.searchText = ''
    this.date = '';
    this.filteredByDateList = this.dataSource.data = this.purReqList;
    this.purchaseStat.setValue('')
    this.vendors.setValue('')
    this.filterKeys.status.orderStatus = 'All'
    this.filterKeys.vendorDetails.vendorName = 'All'
    this.searchValue = ''
    this.doFilter(this.searchValue)
    this.dataSource.data = this.purReqList;
  }

  filterByBranch(restId) {
    this.restaurantId = restId.restaurantIdOld ?  restId.restaurantIdOld : restId ;
    this.branchSelected = true
    if(this.sharedFilterData != ''){
      if(this.sharedFilterData.restaurantId == restId){
        this.purchaseRequestApprovalForm.get('branchSelection').setValue(this.sharedFilterData.restaurantId);
      }else{
        this.purchaseRequestApprovalForm.get('branchSelection').setValue(restId);
      }
      this.branches = this.getBranchData;
      this.selectedStartDate = this.sharedFilterData.selectedStartDate;
      this.selectedEndDate = this.sharedFilterData.selectedEndDate;
      this.startDate.setValue(this.sharedFilterData.selectedStartDate);
      this.endDate.setValue(this.sharedFilterData.selectedEndDate);
      this.selectedApprovalStatus = this.sharedFilterData.approvalStatus;
      this.tempData = this.sharedFilterData.data;
      this.tempData.forEach(element => {
        this.vendorsList.push(element.vendorDetails.vendorName);
        this.vendorsList = this.vendorsList.filter((j, l, arr) => arr.indexOf(j) === l);
        this.VendorData = this.vendorsList
        this.purReqApprovalvendors.next(this.VendorData.slice());
        this.vendorFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
            this.vendorfilterBanks();
        });
      });
    }
    this.setupCall();
  }

  deletePo(element, ind) {
    this.dialog.open(SimpleDialogComponent, {
      data: {
        title: 'Delete Alert',
        msg: 'Are you sure you want to delete this PO?',
        ok: function () {
          this.purchases.deletePO(
            {
              tenantId: this.user.tenantId,
              restaurantId: this.restaurantId,
              poId: element.poId
            }
          ).subscribe(data => {
            if (data.result == 'success') {
              this.utils.openSnackBar(element.poId + ' is deleted successfully', null, 3000);
              this.dataSource.data.splice(ind, 1)
              this.dataSource._updateChangeSubscription()
            }
            else {
              this.utils.snackBarShowError(data.exception);
            }
          },
            (err) => {
              console.error(err);
            });
        }.bind(this)
      }
    });
  }
  filterByStatus(event){
    this.selectedApprovalStatus = event;
    if(event == 'All'){
      this.displayedColumns = ['poId', 'vendorName','total', 'approvalStatus'];
      this.dataSource.data = this.purReqList ;
      this.pageSizes = this.utils.getPageSizes(this.dataSource.data) ;
      this.selection = new SelectionModel<any>(true, this.dataSource.data) ;
      this.masterToggle() ;
    }else{
        this.dataSource.data = []
        this.dataSource.data = (event === 'pending') ? this.purReqList.filter(request => (request.approvalStatus != "rejected") && (request.approvalStatus != "approved")) : this.purReqList.filter(request => request.approvalStatus === event) ;
        this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
        this.displayedColumns = (event === 'pending') ? ['select','poId', 'vendorName','total', 'approvalStatus'] : ['poId', 'vendorName','total', 'approvalStatus'];
        this.checkSelection = this.dataSource.data.length > 0  ? true : false ;
        if (this.checkSelection && event == 'pending'){
          this.isAllSelected() ;
          this.masterToggle() ;
        } else {
          !this.isAllSelected() ? (  this.selection = new SelectionModel<any>(true, this.dataSource.data),this.masterToggle() ) : null ;
        }
    }
  }

  approvePrs() {
  	let obj = {
  		tenantId: this.user.tenantId,
  		restaurantId: this.restaurantId,
  		approvalData: this.selection.selected,
  		role: this.user.role
  	}
  	this.purchases.prGroupApproval(obj).subscribe(data => {
  		data.result === 'success' ? this.utils.snackBarShowSuccess(data.message) : this.utils.snackBarShowError(data.message);
  		this.filterByBranch(obj['restaurantId']) ;
      this.selectedApprovalStatus = 'All' ;
      this.filterByStatus(this.selectedApprovalStatus) ;
  	})
  }

  rejectPrs() {
  	let obj = {
  		tenantId: this.user.tenantId,
  		restaurantId: this.restaurantId,
  		approvalData: this.selection.selected,
  		role: this.user.role
  	}
    this.dialogRef = this.dialog.open(SimpleDialogComponent, {
      data: {
        title: 'Reject Reason',
        msg: 'Enter Reason(min 10 and max 40 characters allowed)',
        inputFromUser: { 'Reason': '' },
        ok: function () {
          this.dialogRef.afterClosed().subscribe(res => {
            this.reason = res['Reason'];
            if(res['Reason'] == ''){
              this.utils.snackBarShowInfo('Please provide valid reason here..');
            }else{
              obj['reason'] = this.reason;
              this.purchases.prGroupReject(obj).subscribe(data => {
                data.result === 'success' ? this.utils.snackBarShowSuccess(data.message) : this.utils.snackBarShowError(data.message);
                this.filterByBranch(obj['restaurantId']) ;
                this.selectedApprovalStatus = 'All' ;
                this.filterByStatus(this.selectedApprovalStatus) ;
              }, err => console.error(err))
            }
          })
        }.bind(this)
  
      }
    });
  }

  refreshdata(){
    this.filterByBranch(this.purchaseRequestApprovalForm.value.branchSelection);
  }

  clear(){
    this.startDate.setValue(null);
    this.endDate.setValue(null);
    this.selectedApprovalStatus = null;
    this.vendors.setValue(['All'])
    this.setupCall();
  }

  checkVendorData() {
    if (this.purReqList.length > 0){
      let vendorCheck = this.purReqList.find((el) => Object.keys(el.vendorDetails).length === 0)
      return vendorCheck ? false : true ;
    } else {
      return false  ;
    }

  }

}
