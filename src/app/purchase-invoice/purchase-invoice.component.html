<div class="title row">

    <div>
      <form [formGroup]="purchaseInvoiceForm">
      <mat-form-field *ngIf="multiBranchUser" appearance="none" class="topitem">
        <label class="title-palce">Select Branch</label>
        <mat-select placeholder="Select Branch" class="outline" formControlName="branchSelection" (selectionChange)="filterByBranch($event.value)">
          <mat-option *ngFor="let rest of branches" [value]="rest">
            {{ rest.branchName }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </form>
    </div>

    <div>
      <mat-form-field *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" appearance="none"
      style="margin-left: 10px;" class="topitem">
      <label>Start Date</label>
      <input matInput class="outline" [(ngModel)]="selectedStartDate" [matDatepicker]="picker1" placeholder="Start Date" [formControl]="startDate" />
      <mat-datepicker-toggle matSuffix [for]="picker1">
        <mat-icon matDatepickerToggleIcon>
          <img src="./../../assets/calender.png" />
        </mat-icon>
      </mat-datepicker-toggle>
      <mat-datepicker #picker1></mat-datepicker>
    </mat-form-field>
    </div>
  
    <div>
      <mat-form-field *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" appearance="none"
      style="margin-left: 10px;" class="topitem">
      <label>End Date</label>
      <input matInput class="outline" [(ngModel)]="selectedEndDate" [matDatepicker]="picker2" [formControl]="endDate" placeholder="End Date" [readonly] = "!startDate.value" [disabled]=" !startDate.value" [min]="startDate.value"/>
      <mat-datepicker-toggle matSuffix [for]="picker2">
        <mat-icon matDatepickerToggleIcon>
          <img src="./../../assets/calender.png" />
        </mat-icon>
      </mat-datepicker-toggle>
      <mat-datepicker #picker2></mat-datepicker>
    </mat-form-field>
    </div>
  
<div>
  <button *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser"  mat-stroked-button class="btn-block findButton button3"
  (click)="filterByDate()">
  Find
</button>
</div>

<div class="ml-2">
  <button *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser"  mat-stroked-button class="btn-block findButton button3 ml-2"
  (click)="clear()">
  Clear
  </button>
</div>

</div>

<div *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" class="datacontainer">
  <mat-card>
    <mat-card-content>
      <div class="search-table-input">
        <mat-form-field appearance="none">
          <label>Search</label>
          <input matInput type="text" (keyup)="doFilter($event.target.value)" [(ngModel)]='searchText' placeholder="Search" autocomplete="off"
            class="outline" />
            <mat-icon matSuffix (click)="resetForm()" class="closebtn">close</mat-icon>
          <!-- <mat-icon matSuffix class="closebtn">close</mat-icon>
          <mat-icon matSuffix class="searchbtn">search</mat-icon> -->
        </mat-form-field>

        <!-- <mat-form-field appearance="none">
          <label>Vendor Name</label>
          <mat-select placeholder="Vendor Name" [(ngModel)]="selectedvendorName" [formControl]="vendorName" class="outline">
            <mat-option *ngFor="let vendor of vendorList" [value]="vendor" (click)="selectVendor(vendor)">
              {{ vendor | titlecase }}
            </mat-option>
          </mat-select>
        </mat-form-field> -->

        <mat-form-field id="vendor-select" appearance="none">
          <label>Vendor Name</label>
          <!-- [(ngModel)]="selectedvendorName"  -->
          <mat-select placeholder="Vendor Name" [formControl]="vendorName" class="outline" [multiple]="true" #multiSelect>
            <mat-option>
              <ngx-mat-select-search placeholderLabel="Vendor Item..." noEntriesFoundLabel="'no Vendor Item found'"
                [formControl]="vendorFilterCtrl"></ngx-mat-select-search>
            </mat-option>
            <mat-option #allSelected (click)="toggleAllSelection()" [value]="1">All</mat-option>
            <mat-option *ngFor="let vendor of purInvVendors | async" [value]="vendor" (click)="selectVendor(vendor)">
              {{ vendor | titlecase }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <!-- <mat-form-field appearance="none">
          <label>Status</label>
          <mat-select placeholder="Status" [formControl]="piStatusType" class="outline">
            <mat-option *ngFor="let status of statusTypeList" [value]="status" (click)="selectStatus(status)">
              {{ status | titlecase }}
            </mat-option>
          </mat-select>
        </mat-form-field> -->
        <button mat-button class="buttonForRefresh refreshBtn" (click)="refreshdata()">Refresh</button>
      </div>

      <table #table mat-table [dataSource]="dataSource" matSortDirection="desc" matSort>
        <ng-container matColumnDef="grnId">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> GRN Id</b>
          </th>
          <td mat-cell *matCellDef="let element" class="links" (click)="detailedPi(element)">
            {{ element.grnId }}
          </td>
        </ng-container>

        <ng-container matColumnDef="poId">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> PO Id </b>
          </th>
          <td mat-cell *matCellDef="let element">
            <span *ngIf="element.details.ibtId">{{
              element.details.ibtId
              }}</span>
            <span *ngIf="element.details.poId">{{ element.details.poId }}</span>
          </td>
        </ng-container>

        <ng-container matColumnDef="vendorName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Vendor Name </b>
          </th>
          <td mat-cell *matCellDef="let element">{{ (element.vendorName) || "-" }}</td>
        </ng-container>
        <ng-container matColumnDef="total">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Total Amount</b>
          </th>
          <td mat-cell *matCellDef="let element">{{ element.grandTotal }}</td>
        </ng-container>
        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Status </b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ getPIStatus(element) }}
          </td>
        </ng-container>

        <ng-container matColumnDef="date">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>GRN Date(System Entry Date)</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.createTs | date: "EEEE, MMMM d, y" }}
          </td>
        </ng-container>

        <ng-container matColumnDef="invId">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Invoice Id </b>
          </th>
          <td mat-cell *matCellDef="let element">
            <span *ngIf="element.details.ibtId">N/A</span>
            <span *ngIf="element.details.poId">{{ element.invoiceId }}</span>
          </td>
        </ng-container>

        <ng-container matColumnDef="approvalStatus">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Approval Status</b>
          </th>
          <td mat-cell *matCellDef="let element">
            <div
              *ngIf="element?.approvalDetail && element?.approvalDetail.length != 0 ">
              <span class="links" (click)="mouseEnter(element)">{{ getDynamicText(element) }}</span>
            </div>
            <div
              *ngIf="!element?.approvalDetail || element?.approvalDetail.length == 0">
              -
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="action">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Edit</b>
          </th>
          <td mat-cell *matCellDef="let element">
            <button *ngIf="element.piId==undefined" mat-icon-button (click)="detailedPi(element)" class="action-print" matTooltip="Edit Grn"
              matTooltipPosition="right">
              <!-- <mat-icon class="action-print-icon">edit</mat-icon> -->
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-pencil-square svgEditIcon" viewBox="0 0 16 16">
                <path d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z"/>
                <path fill-rule="evenodd" d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5v11z"/>
              </svg>
            </button>
            <button *ngIf="element.piId!=undefined" mat-icon-button class="action-print" matTooltip="PI done"
            matTooltipPosition="right">
            <mat-icon class="action-print-icon">check</mat-icon>
          </button>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>
      <mat-paginator [showTotalPages]="5" [pageSize]="10" [pageSizeOptions]="pageSizes"></mat-paginator>
    </mat-card-content>
  </mat-card>
</div>