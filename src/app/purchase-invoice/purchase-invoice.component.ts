import { Component, ViewChild,OnInit } from '@angular/core';
import { PurchasesService, AuthService, ShareDataService } from '../_services';
import { MatSort, MatTableDataSource, MatPaginator, MatOption, MatDialog } from '@angular/material';
import { GlobalsService } from '../_services/globals.service';
import { Router } from '@angular/router';
import { UtilsService } from '../_utils/utils.service';
import { NotificationService } from '../_services/notification.service';
import { FormBuilder, FormControl, FormGroup, Validators } from "@angular/forms";
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material';
import { MomentDateModule, MomentDateAdapter } from '@angular/material-moment-adapter';
import { ReplaySubject , Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { SharedFilterService } from '../_services/shared-filter.service';
import { PreviewIbtComponent } from '../_dialogs/preview-ibt/preview-ibt.component';

export const MY_FORMATS = {
  parse: {
      dateInput: 'LL'
  },
  display: {
      dateInput: 'DD-MM-YYYY',
      monthYearLabel: 'YYYY',
      dateA11yLabel: 'LL',
      monthYearA11yLabel: 'YYYY'
  }
};
@Component({
  selector: 'app-purchase-invoice',
  templateUrl: './purchase-invoice.component.html',
  styleUrls: ['./purchase-invoice.component.scss', "./../../common-dark.scss"],
  providers:[{ provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
            { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }
          ]
})
export class PurchaseInvoiceComponent implements OnInit {
  searchText: string;
  searchValue: string;
  vendors: any;
  vendorId: any;
  tempData: any;
  elementData: any;
  @ViewChild(MatPaginator) set paginator(value: MatPaginator) {
    if (this.dataSource){
      this.dataSource.paginator = value;
    }
  };
  pageSizes=[];
  selectedBranch: any;
  selectedvendorName: any;
  selectedStartDate: any;
  selectedEndDate: any;
  private startDate = new FormControl();
  private endDate = new FormControl();
  filterKeys = {vendorName:'All',status:'All'}
  restaurantId: any;
  multiBranchUser:any;
  branchSelected: boolean;
  piStatusType = new FormControl();
  vendorName = new FormControl();
  grnList = [];
  // vendorList = ['All'];
  vendorList = [];
  statusTypeList = ['All','Pending','Completed'];
  private user : any;
  private dataSource: MatTableDataSource<any>;
  private displayedColumns: string[];
  @ViewChild(MatSort) sort: MatSort;
  branches: any[];
  getBranchData: any[]
  purchaseInvoiceForm: FormGroup;
  purchaseInvoiceUrl = encodeURI(GlobalsService.purchaseInvoice)
  private unsubscribe$ = new Subject<void>();
  sharedFilterData: any = {};
  detailedPiUrl = encodeURI(GlobalsService.detailedPi)
  public purInvVendors: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public VendorData: any[] = [];
  public vendorFilterCtrl: FormControl = new FormControl();
  protected _onDestroy = new Subject<void>();
  selectVendorAll : boolean =  true;
  stopSecondApiCall : boolean =  false;
  @ViewChild('allSelected') private allSelected: MatOption;
  clearedData : boolean = false;
  constructor(private purchases : PurchasesService, private auth : AuthService,
    private notifyService: NotificationService,          
    private router : Router,
    private utils: UtilsService,
    private sharedData : ShareDataService,
    private fb: FormBuilder,
    public dialog: MatDialog,
    private sharedFilterService: SharedFilterService) {
      this.user = this.auth.getCurrentUser();
      this.multiBranchUser = this.user.multiBranchUser;
      this.purchaseInvoiceForm = this.fb.group({
      branchSelection: [null, Validators.required]
      });

  var windowLocation = window.location.href;
  let windowCurrentUrl = windowLocation.split('/')[4]

  this.sharedFilterService.getFilteredInvoice.pipe(
    takeUntil(this.unsubscribe$)
  ).subscribe(obj => 
    this.sharedFilterData = obj
  );    


      this.sharedData.sharedBranchData.pipe(takeUntil(this.unsubscribe$)).subscribe((val) => {
      this.getBranchData = val;
      if(this.getBranchData.length == 0 ){
        this.branches = this.user.restaurantAccess;
      }else if(this.getBranchData.length == 1){        
        const toSelect = this.getBranchData.find(data => data.branchName == this.getBranchData[0].branchName);
        if(toSelect != this.sharedFilterData.restaurantId){
          this.sharedFilterData = '';
        }
        this.purchaseInvoiceForm.get('branchSelection').setValue(toSelect);
        this.branches = this.getBranchData
        this.filterByBranch(this.purchaseInvoiceForm.value.branchSelection);
        if(this.sharedFilterData){
          this.sharedFilterData.branchFlag = false;
        }
      }else{
        if(this.sharedFilterData.branchFlag == true){
          this.filterByBranch(this.sharedFilterData.restaurantId);
          this.sharedFilterData.branchFlag = false;
        }
        this.branches = this.getBranchData
      }
  });
  }

  ngOnInit() {
    this.displayedColumns = GlobalsService.piColumns;
  }

  ngOnDestroy() {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
    if(!this.router.url.includes(this.detailedPiUrl)){
      this.sharedFilterService.getFilteredInvoice['_value'] = ''
    }
  }

  getGrns(){

    let reqObj : any = {
      tenantId : this.user.tenantId,
      restaurantId : this.restaurantId,
      piScreen :true
    }

    if(this.vendorName.value && this.tempData){
      let array = []
      array.push(this.vendorName.value)
      array = array[0].filter(item => item !== "All");

      // "filter data using vendorname"
      const filteredData = this.tempData.filter(item => array.includes(item.vendorName));

      // "remove Duplicate data"
      const uniqueData = filteredData.filter((obj, index, self) => {
        return index === self.findIndex(item => item.vendorName === obj.vendorName);
      });

      const vendorIds = [];
      uniqueData.forEach((obj: any) => {
        vendorIds.push(obj.vendorId);
      });

      if(vendorIds.length === 0){
        reqObj['vendorIdData'] = ['All'];
      }else{
        reqObj['vendorIdData'] = vendorIds
      }
    }else{
      reqObj['vendorIdData'] = ['All'];
    }

    reqObj['grnDate'] = null;
  
    if(this.startDate.value && this.endDate.value){
      reqObj['startDate'] = this.utils.dateCorrection(this.startDate.value);
      reqObj['endDate'] = this.utils.dateCorrection(this.endDate.value);
    }else{
      reqObj['startDate'] = null;
      reqObj['endDate'] = null;
    }

    this.purchases.getGrn(reqObj).subscribe(data => {
      if(!this.dataSource)
      this.dataSource = new MatTableDataSource<any>();
      this.dataSource.data = data;
      this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
      this.dataSource.paginator = this.paginator;
      this.grnList = data;
      if(this.tempData == undefined){
        this.tempData = data;
      }
      if(this.clearedData || (this.sharedFilterData.restaurantId != this.purchaseInvoiceForm.value.branchSelection)){
        this.vendorList = []
        this.tempData.forEach(element => {
          this.vendorList.push(element.vendorName);
        });
        this.vendorList = this.vendorList.filter((k, i, ar) => ar.indexOf(k) === i);
        this.VendorData = this.vendorList
        this.purInvVendors.next(this.VendorData.slice());
        this.vendorFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
            this.vendorfilterBanks();
        });
        if(this.selectVendorAll && !this.sharedFilterData.vendorName){
          this.toggleAllSelection(true);
        }
      }
      this.clearedData = false;
      this.dataSource.sort = this.sort;
    }, err => {})
    
  }

  detailedPi(obj: any){
    obj['piApprovalScreen'] = false ;
    let inputObj = {
      restaurantId : this.purchaseInvoiceForm.value.branchSelection,
      selectedStartDate : this.startDate.value,
      selectedEndDate : this.endDate.value,
      vendorName : [this.vendorName.value],
      data : this.tempData,
      branchFlag : true
    }    
    this.sharedFilterService.getFilteredInvoice.next(inputObj);
    this.sharedData.changePi(obj)
    this.router.navigate(['/home/<USER>'])
  }

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }

  resetForm() {
    this.searchText = ''
    this.searchValue = ''
    this.selectedvendorName = ''
    this.doFilter(this.searchValue)
    this.dataSource.data = this.grnList;
  }

  allFilter(){
    let tmp = this.grnList
    let prev = this.grnList
    Object.keys(this.filterKeys).forEach(element => {
      if(this.filterKeys[element] != 'All'){
        tmp = prev.filter( item =>
          item[element].toLowerCase() === this.filterKeys[element].toLowerCase())
        prev = tmp
      }
    });
    this.dataSource.data = tmp
  }

  selectVendor(vendor){
    this.stopSecondApiCall = true;
    this.vendorId = vendor
    this.getGrns()
  }
 
  filterByBranch(restId){
    this.restaurantId = restId.restaurantIdOld;
    this.branchSelected = true
    if(this.sharedFilterData != '' && this.sharedFilterData.restaurantId == restId){  
        this.purchaseInvoiceForm.get('branchSelection').setValue(this.sharedFilterData.restaurantId);
        this.branches = this.getBranchData
        this.selectedStartDate = this.sharedFilterData.selectedStartDate
        this.selectedEndDate = this.sharedFilterData.selectedEndDate
        this.startDate.setValue(this.sharedFilterData.selectedStartDate) 
        this.endDate.setValue(this.sharedFilterData.selectedEndDate) 
        this.vendorName.setValue(this.sharedFilterData.vendorName[0]);
        this.tempData = this.sharedFilterData.data;
        this.tempData.forEach(element => {
          this.vendorList.push(element.vendorName)
        });
        this.vendorList = this.vendorList.filter((k, i, ar) => ar.indexOf(k) === i);
        this.VendorData = this.vendorList
        this.purInvVendors.next(this.VendorData.slice());
        this.vendorFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
            this.vendorfilterBanks();
        });
        if(this.selectVendorAll && !this.sharedFilterData.vendorName){
          this.toggleAllSelection(true);
        }
    }
    this.getGrns()
  }

  filterByDate(){
    if(this.startDate.value && this.endDate.value){
      this.tempData = undefined;
      this.selectVendorAll = true;
      this.getGrns()
    }
    else{
      this.utils.snackBarShowError('Please select start date and end date')
    }
  } 

  clear(){
    this.clearedData = true;
    this.tempData = undefined;
    if(this.sharedFilterData.vendorName){
      this.sharedFilterData.vendorName = ''
    }
    this.selectedStartDate = ''
    this.selectedEndDate = ''
    this.startDate.setValue(null);
    this.endDate.setValue(null);
    this.vendorName.setValue(null);
    this.stopSecondApiCall = true;
    // this.toggleAllSelection(true)
    this.getGrns();
  }

  refreshdata(){
    this.getGrns();
  }

  protected vendorfilterBanks() {
    if (!this.VendorData) {
      return;
    }
    let search = this.vendorFilterCtrl.value;
    if (!search) {
      this.purInvVendors.next(this.VendorData.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.purInvVendors.next(
      this.VendorData.filter(VendorData => VendorData.toLowerCase().indexOf(search) > -1)
    );
  }

  toggleAllSelection(manual = false) {
    this.selectVendorAll = false
    if (this.allSelected && this.allSelected.selected || manual) {
      this.vendorName.patchValue([]);
      this.vendorName.patchValue([...this.tempData.map(item => item.vendorName), 1]);
    }else {
      this.vendorName.patchValue([]);
    }
    if(this.stopSecondApiCall){
      this.getGrns()
    }
  }

  getDynamicText(element: any): string {
    if (element.approvalDetail && element.approvalDetail.length > 0) {
      return this.getStatus(element.approvalDetail);
    } else {
      return 'Click here';
    }
  }


  getStatus(data) {
    if (Object.keys(data).length !== 0) {
      const levelOrder = data.map(item => item.level);
      let statusWithRole = "";
      for (const currentLevel of levelOrder) {
        const matchingData = data.find(item => item.level === currentLevel);
        
        if (matchingData) {
          const { level, status, role } = matchingData;
          
          if (status === "rejected") {
            statusWithRole = `${status.charAt(0).toUpperCase() + status.slice(1)} (${role})`;
            break;
          } else if (status === "pending" && !statusWithRole.includes("rejected")) {
            statusWithRole = `${status.charAt(0).toUpperCase() + status.slice(1)} (${role})`;
          } else if (status === "approved" && !statusWithRole.includes("rejected") && !statusWithRole.includes("pending")) {
            statusWithRole = `${status.charAt(0).toUpperCase() + status.slice(1)} (${role})`;
          }
        }
      }
      return statusWithRole;
    }
  }

  mouseEnter(element){
    this.elementData = element
    this.dialog.open(PreviewIbtComponent, {
      width: "600px",
      data: {
        title: "PI",
        component: "Purchase Status",
        items: this.elementData,
        ok: function () {
          this.location.back();
        }.bind(this),
      },
    });
  }

  getPIStatus(element) {
    let status ; 
    if (element.piId) {
      if (element.hasOwnProperty('approvalDetail') && element.approvalDetail) {
        let approvedItems = element.approvalDetail.filter((el)=> el.status == 'approved')
        status = (approvedItems.length == element.approvalDetail.length ) ? 'Completed' : 'Pending'
      }
    } else {
      status = element.piId ? 'Completed' : 'Pending'
    }
    return status
  }

}