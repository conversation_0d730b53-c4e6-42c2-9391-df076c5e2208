<div *ngIf="multiBranchUser" class="title row" style="display: flex; align-items: center; justify-content: space-between;">
  <!-- Left-aligned branch selection form -->
  <form [formGroup]="purchaseListForm" style="flex: 1;">
    <mat-form-field appearance="none">
      <mat-select formControlName="branchSelection" (selectionChange)="filterByBranch($event.value)" class="outline">
        <mat-option *ngFor="let branch of branches" [value]="branch">
          {{ branch.branchName }}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </form>

  <!-- Right-aligned buttons -->
  <div style="display: flex; gap: 8px;">
    <mat-slide-toggle color="accent" [(ngModel)]="indentPreview" (change)="preview()">
      Preview Mode
    </mat-slide-toggle>

    <button mat-flat-button color="primary"(click)="saveOrders()" [disabled]="this.selection.selected.length == 0 ">
      <span>Save</span>
    </button>

    <button mat-flat-button color="accent" *ngIf="!isVendor" (click)="createPrs()" [disabled]="this.selection.selected.length == 0 ">
      <span>Order Now</span>
    </button>

    <button mat-flat-button color="warn" (click)="generatePL()" [disabled]="dataSource?.data?.length === 0">
      <mat-icon class="text-sm">download</mat-icon>
      <span>Export</span>
      <mat-icon *ngIf="exportStatusDataCheck" class="ml-1 text-sm">check_circle</mat-icon>
      <mat-progress-spinner *ngIf="exportStatusDataLoader" mode="indeterminate" diameter="16"></mat-progress-spinner>
    </button>
  </div>
</div>


<div class="datacontainer" *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser">
  <mat-card>
    <mat-card-content>
      <div class="search-table-input">
        <mat-form-field appearance="none">
          <label>Search</label>
          <input matInput type="text" class="outline" [(ngModel)]='searchValue' (keyup)="doFilter($event.target.value)"
            placeholder="Search" />
          <mat-icon matSuffix class="closebtn" (click)="clearFilter()">close</mat-icon>
        </mat-form-field>

        <mat-form-field appearance="none" >
          <label>Category</label>
          <mat-select [formControl]="Category" class="outline" (selectionChange)="selectCategory($event.value)">
            <mat-option *ngFor="let cat of categoryList" [value]="cat">
              {{cat | titlecase}}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="none">
          <label>Subcategory</label>
          <mat-select [formControl]="Subcategory" class="outline" (selectionChange)="selectSubCat($event.value)">
            <mat-option *ngFor="let subCat of subCategoryList" [value]="subCat">
              {{subCat | titlecase}}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="none">
          <label>Vendor</label>
          <mat-select [formControl]="vendorForm" class="outline">
            <mat-option>
              <ngx-mat-select-search placeholderLabel="Search vendor..." noEntriesFoundLabel="No vendors found"
                [formControl]="vendorFilterCtrl">
              </ngx-mat-select-search>
            </mat-option>
            <!-- <mat-option #allSelected (click)="selectedVendor({'tenantId': null , 'name' : 'All'})" [value]="All">All</mat-option> -->
            <mat-option *ngFor="let vendor of purListvendors | async" [value]="vendor.tenantId"
              (click)="selectedVendor(vendor)">
              {{vendor.name}}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      
      <div style="overflow: auto; overflow-y: hidden;">
        <table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort>
        <!-- class="mat-elevation-z8" -->
          <ng-container matColumnDef="select">
            <th mat-header-cell *matHeaderCellDef>
              <mat-checkbox #selectAll (change)="$event ? masterToggle() : null"
                [checked]="selection.hasValue() && isAllSelected()"
                [indeterminate]="selection.hasValue() && !isAllSelected()" [aria-label]="checkboxLabel()">
              </mat-checkbox>
            </th>
            <td mat-cell *matCellDef="let row">
              <mat-checkbox (change)="$event ? selection.toggle(row) : null; getTotal($event)"
                [checked]="selection.isSelected(row)" [aria-label]="checkboxLabel(row)">
              </mat-checkbox>
            </td>
          </ng-container>
  
          <ng-container matColumnDef="index">
            <th mat-header-cell *matHeaderCellDef class="tableId"><b>#</b></th>
            <td mat-cell *matCellDef="let element; let i = index" class="name-cell tableId">
              {{ i + 1 }}
            </td>
          </ng-container>
  
          <ng-container matColumnDef="ledger">
            <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Ledger</b></th>
            <td mat-cell *matCellDef="let element">{{ element.ledger }}</td>
          </ng-container>
  
          <ng-container matColumnDef="category">
            <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Category</b></th>
            <td mat-cell *matCellDef="let element">{{ element.category }}</td>
          </ng-container>
  
          <ng-container matColumnDef="subCategory">
            <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Sub Category</b></th>
            <td mat-cell *matCellDef="let element">{{ element.subCategory }}</td>
          </ng-container>
  
          <ng-container matColumnDef="itemCode">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Item Code</b>
            </th>
            <td mat-cell *matCellDef="let element">{{ element.itemCode }}</td>
          </ng-container>
  
          <ng-container matColumnDef="itemName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header style="min-width: 200px !important;">
              <b> Item Name</b>
            </th>
            <td mat-cell *matCellDef="let element" style="min-width: 200px !important;">
              {{ element.itemName | titlecase }}
            </td>
            <mat-divider></mat-divider>
          </ng-container>
  
          <ng-container matColumnDef="unit">
            <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Unit</b></th>
            <td mat-cell *matCellDef="let element">{{ element.uom }}</td>
          </ng-container>
  
          <ng-container matColumnDef="pkgName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Pkg</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.packageName | titlecase }}
            </td>
            <mat-divider></mat-divider>
          </ng-container>
  
          <ng-container matColumnDef="pkgQty">
            <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Pkg Qty</b></th>
            <td mat-cell *matCellDef="let element">{{ element.pkgQty }}</td>
          </ng-container>
  
          <ng-container matColumnDef="taxRate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Tax Rate</b></th>
            <td mat-cell *matCellDef="let element">{{ element.taxRate }}</td>
          </ng-container>
  
          <ng-container matColumnDef="unitPrice">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> WAC(incl.tax,etc) </b>
            </th>
            <td mat-cell *matCellDef="let element">
              <input *ngIf="!(inputObj.tableType == 1)" step="0.1" class="input1" (input)="getTotal(element.withTaxPrice)"
                style="text-align: center;" type="number" [(ngModel)]="element.withTaxPrice" />
              <div *ngIf="inputObj.tableType == 1">
                {{ this.utils.truncateNew(element.withTaxPrice,2) }}
              </div>
            </td>
          </ng-container>
  
          <ng-container matColumnDef="leadTime">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Lead Time(days)</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ this.utils.truncateNew(element.leadTime) }}
            </td>
          </ng-container>
  
          <ng-container matColumnDef="onHand">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> On Hand </b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ this.utils.truncateNew(element.onHand) }}
            </td>
          </ng-container>
  
          <ng-container matColumnDef="openOrders">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Open Orders</b>
            </th>
            <td mat-cell *matCellDef="let element" class="estimated-cell">
              {{ element.onOrder }}
            </td>
          </ng-container>

          <ng-container matColumnDef="vendorList">
            <th mat-header-cell *matHeaderCellDef mat-sort-header class="ven-cell" style="min-width: 200px !important;">
              <b>Vendors</b>
            </th>
            <td mat-cell *matCellDef="let element" class="ven-cell" style="min-width: 200px !important;">
              <mat-select [(ngModel)]="element.defVendor" class="ven-light" (selectionChange)="selectVendor($event.value,element)">
                <mat-option *ngFor="let vendor of element.vendorList" [value]="vendor.vendorName">
                  {{ vendor.vendorName }}
                </mat-option>
              </mat-select>
            </td>
            <mat-divider></mat-divider>
          </ng-container>
  
          <ng-container matColumnDef="openToBuy">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>OTB</b>
            </th>
            <td mat-cell *matCellDef="let element" style="text-align: center;">
              {{ this.utils.truncateNew(element.otb) }}
            </td>
          </ng-container>
  
          <ng-container matColumnDef="orderQty">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Order Quantity</b>
            </th>
            <td mat-cell *matCellDef="let element">
              <input (input)="getTotal()" style="text-align: center;" class="input1" type="number" step="0.01" min="0"
                [(ngModel)]="element.orderQty" *ngIf="!isVendor" (focus)="focusFunctionWithOutForm(element,'orderQty')"
                (focusout)="focusOutFunctionWithOutForm(element,'orderQty')" />
              <div *ngIf="isVendor">
                {{ this.utils.truncateNew(element.orderQty)}}
              </div>
            </td>
          </ng-container>
  
          <ng-container matColumnDef="optStock">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Optimum Stock </b>
            </th>
            <td mat-cell *matCellDef="let element">{{ element.optimumStock }}</td>
          </ng-container>
  
          <ng-container matColumnDef="reqQty">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Open to Buy</b>
            </th>
            <td mat-cell *matCellDef="let element" style="text-align: center;">
              {{this.utils.truncateNew(element.otb) }}
            </td>
          </ng-container>
  
          <ng-container matColumnDef="totalValue">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Total Value</b>
            </th>
            <td mat-cell *matCellDef="let element" style="text-align: center;">
              <div *ngIf="!isVendor">
                {{ this.utils.truncateNew((element.withTaxPrice * element.orderQty),2)}}
              </div>
              <div *ngIf="isVendor">
                {{ this.utils.truncateNew((element.withTaxPrice * element.deliverableQty),2) }}
              </div>
            </td>
          </ng-container>
  
          <ng-container matColumnDef="quotedUnitPrice">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Unit Cost</b>
            </th>
            <td mat-cell *matCellDef="let element">
              <input class="input1" *ngIf="!(inputObj.tableType == 1)" (input)="getTotal()" style="text-align: center;"
                 [(ngModel)]="element.withTaxPrice" />
              <div *ngIf="inputObj.tableType == 1" [style.color]="isGreater(element)">
                {{ this.utils.truncateNew(element.quotedUnitPrice) }}
              </div>
            </td>
          </ng-container>
  
          <ng-container matColumnDef="supplyDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Suppply Date</b>
            </th>
            <td mat-cell *matCellDef="let element">{{ element.supplyDate }}</td>
          </ng-container>
  
          <ng-container matColumnDef="deliverableQty">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Deliverable Quantity</b>
            </th>
            <td mat-cell *matCellDef="let element">
              <input *ngIf="!(inputObj.tableType == 1)" (input)="getTotal()" style="text-align: center;" type="number"
                [(ngModel)]="element.deliverableQty" />
              <div *ngIf="inputObj.tableType == 1">
                {{ element.deliverableQty }}
              </div>
            </td>
          </ng-container>
  
          <ng-container matColumnDef="actionBtns">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Actions</b>
            </th>
            <td mat-cell *matCellDef="let element">
              <button mat-icon-button (click)="acceptOrder(inputObj.tableType)">
                <mat-icon>done</mat-icon>
              </button>
            </td>
          </ng-container>
  
          <ng-container matColumnDef="vendorName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Vendor</b>
            </th>
            <td mat-cell *matCellDef="let element">{{ element.vendor }}</td>
          </ng-container>
  
          <ng-container matColumnDef="poId">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Purchase Id</b>
            </th>
            <td mat-cell *matCellDef="let element">{{ element.id }}</td>
          </ng-container>
  
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Status</b>
            </th>
            <td mat-cell *matCellDef="let element">{{ element.status }}</td>
          </ng-container>
  
          <ng-container matColumnDef="vendorType">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Vendor Type</b>
            </th>
            <td mat-cell *matCellDef="let element">{{ element.vendorType }}</td>
          </ng-container>
  
          <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;">
          </tr>
        </table>
      </div>

      <mat-paginator [showTotalPages]="5" [pageSize]="10" [pageSizeOptions]="pageSizes"></mat-paginator>
    </mat-card-content>
  </mat-card>
</div>