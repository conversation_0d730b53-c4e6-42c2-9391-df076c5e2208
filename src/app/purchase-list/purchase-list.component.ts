import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { GlobalsService } from '../_services/globals.service';
import { AuthService, PurchasesService, ShareDataService, VendorsService } from '../_services';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog } from '@angular/material';
import { SelectionModel } from '@angular/cdk/collections';
import { UtilsService } from '../_utils/utils.service';
import { SimpleDialogComponent } from '../_dialogs/simple-dialog/simple-dialog.component';
import { Vendor } from '../_models';
import { FormControl, FormGroup, FormBuilder, Validators } from '@angular/forms';
import { ReplaySubject, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { MasterdataupdateService } from '../_services/masterdataupdate.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-purchase-list',
  templateUrl: './purchase-list.component.html',
  styleUrls: ['./purchase-list.component.scss', './../../common-dark.scss']
})
export class PurchaseListComponent implements OnInit {
  @ViewChild(MatSort) sort: MatSort;
  dataSource: MatTableDataSource<any>;
  indentPreview: boolean = false;
  curDataSource: any[];
  displayedColumns: string[];
  selection = new SelectionModel<any>(true, []);
  totalOrderValue = 0;
  isVendor: boolean = false;
  title = 'Title';
  hasError = true;
  pageSizes = [];
  categories: any[];
  inventoryItems: any[];
  invCategories: any[];
  menuGroups: any[];
  inputObj: any;
  multiBranchUser: any; branchSelected: boolean;
  restaurantId: any;
  restaurantName: any = '';
  categoryList = ['All'];
  subCategoryList = ['All'];
  initCategoryList = [];
  initSubCategoryList = [];
  filterKeys = { category: 'All', subCategory: 'All', vendorId: 'All' }
  Category = new FormControl();
  Subcategory = new FormControl();
  vendorForm = new FormControl();
  searchValue: any;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  dataObj: any = {};
  tableType = 0;
  user: any;
  vendors: any;
  vendor: any;
  totalVendorOrderValue: number;
  totalSubCatOrderValue: number;
  totalCatOrderValue: number;
  searchText: any
  getBranchData: any[]
  purchaseListForm: FormGroup;
  branches: any[];
  public purListvendors: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public VendorData: any[] = [];
  public vendorFilterCtrl: FormControl = new FormControl();
  protected _onDestroy = new Subject<void>();
  listPurchasesUrl = encodeURI(GlobalsService.listPurchases)
  private unsubscribe$ = new Subject<void>();
  sub: any;
  curdate: Date;
  exportStatusDataLoader: boolean = false
  exportStatusDataCheck: boolean = false
  constructor(
    private utils: UtilsService, private auth: AuthService, private vendorService: VendorsService,private cd: ChangeDetectorRef,
    private purchases: PurchasesService, private dialog: MatDialog,
    private sharedData: ShareDataService, private fb: FormBuilder,
    public router: Router,
    private masterDataService: MasterdataupdateService,
  ) {
    this.user = this.auth.getCurrentUser();
    this.getVendors();
    this.purchaseListForm = this.fb.group({
      branchSelection: [null, Validators.required]
    });


    this.sharedData.sharedBranchData.pipe(takeUntil(this.unsubscribe$)).subscribe((val) => {
      this.getBranchData = val;
      if (this.getBranchData.length == 0) {
        this.branches = this.user.restaurantAccess;
      } else if (this.getBranchData.length == 1) {
        const toSelect = this.getBranchData.find(data => data.branchName == this.getBranchData[0].branchName);
        this.purchaseListForm.get('branchSelection').setValue(toSelect);
        this.branches = this.getBranchData
        this.filterByBranch(this.purchaseListForm.value.branchSelection);
      } else {
        this.branches = this.getBranchData
      }
    });
  }

  preview() {
    if (this.indentPreview == true) {
      this.curDataSource = this.dataSource.data
      this.dataSource.data = this.selection.selected
    }
    else {
      this.dataSource.data = this.curDataSource
    }
  }

  ngOnInit() {
    this.multiBranchUser = this.user.multiBranchUser
    this.restaurantName = this.user.restaurantAccess[0].branchName
    if (!this.user.multiBranchUser) {
      this.restaurantId = this.user.restaurantAccess[0].restaurantIdOld
      this.inputObj = {}
      this.getSsi();
    }
  }

  ngOnDestroy() {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  getVendors() {
    this.vendorService.getVendors(this.auth.getCurrentUser()).subscribe((data: Vendor[]) => {
      this.vendors = data;
      this.VendorData = this.vendors
      this.VendorData.unshift({'tenantId': 'All' , 'name' : 'All'})
      this.purListvendors.next(this.VendorData.slice());
      this.vendorFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
        this.vendorfilterBanks();
      });
    })
  }

  protected vendorfilterBanks() {
    if (!this.VendorData) {
      return;
    }
    let search = this.vendorFilterCtrl.value;
    if (!search) {
      this.purListvendors.next(this.VendorData.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.purListvendors.next(
      this.VendorData.filter(VendorData => VendorData.name.toLowerCase().indexOf(search) > -1)
    );
  }

  increaseValue(element: { orderQty: number; }) {
    element.orderQty = element.orderQty + 1
  }

  decreaseValue(element: { orderQty: number; }) {
    element.orderQty = element.orderQty - 1
    if (element.orderQty < 0)
      element.orderQty = 0
    this.getTotal(null)
  }

  increasePrice(element: { withTaxPrice: number; }) {
    element.withTaxPrice = element.withTaxPrice + 1
  }

  decreasePrice(element: { withTaxPrice: number; }) {
    element.withTaxPrice = element.withTaxPrice - 1
    if (element.withTaxPrice < 0)
      element.withTaxPrice = 0
    this.getTotal(null)
  }

  getSsi() {
    let obj = {
      tenantId: this.user.tenantId,
      restaurantId: this.restaurantId,
      event: 'purchaseList'
    }
    this.dataObj.displayedColumns = GlobalsService.purchaseListColumns;
    this.dataObj.title = "Purchase List";
    this.purchases.getSsi(obj).subscribe(data => {
      this.inputObj.data = data ? data : [];
      this.intializeInputObj()
    }, err => { console.error(err) })
  }

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }

  showSpecialOrder() {
    this.utils.snackBarShowSuccess("Special Order Dialog")
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  masterToggle() {
    if (this.dataSource.data)
      this.isAllSelected() ?
        this.selection.clear() :
        this.dataSource.data.forEach(row => this.selection.select(row));
    this.getTotal();
    this.indentPreview = false
  }

  checkboxLabel(row?: any): string {
    if (!row) {
      return `${this.isAllSelected() ? 'select' : 'deselect'} all`;
    }
    return `${this.selection.isSelected(row) ? 'deselect' : 'select'} row ${row.itemCode + 1}`;
  }

  getTotal(_e?: Event) {
    this.selection.selected.forEach(item => {
      item.totalValue = item.otb * item.withTaxPrice;
    });
    this.totalOrderValue = this.utils.getTotal(this.selection.selected, 'totalValue') / 100000;
  }

  getVendorTotal() {
    this.dataSource.data.forEach(item => {
      item.totalValue = item.otb * item.withTaxPrice;
    });
    this.totalVendorOrderValue = this.utils.getTotal(this.dataSource.data, 'totalValue') / 100000;
  }

  getSubCatTotal() {
    this.dataSource.data.forEach(item => {
      item.totalValue = item.otb * item.withTaxPrice;
    });
    this.totalSubCatOrderValue = this.utils.getTotal(this.dataSource.data, 'totalValue') / 100000;
  }

  getCatTotal() {
    this.dataSource.data.forEach(item => {
      item.totalValue = item.otb * item.withTaxPrice;
    });
    this.totalCatOrderValue = this.utils.getTotal(this.dataSource.data, 'totalValue') / 100000;
  }

  acceptOrder(num: number) {
    if (!num)
      num = 0;
    switch (num) {
      case 0:
        this.utils.snackBarShowSuccess("accepting order")
        break;
      case 1:
        this.utils.snackBarShowSuccess("approving quote")
        break
    }
  }

  saveOrders() {
    let items = this.selection.selected.filter(item => item.orderQty > 0)
    let obj = {
      restaurantId: this.restaurantId,
      tenantId: this.auth.getCurrentUser().tenantId,
      items: items,
    }
    this.purchases.savePrsDraft(obj).subscribe(data => {
    // this.utils.snackBarShowSuccess(`saving orders no of orders : ${this.selection.selected.length}`)
      this.utils.snackBarShowSuccess('order saved successfully')
    }, err => console.error(err))  
  }

  getPrsDraft() {
    let obj = {
      restaurantId: this.restaurantId,
      tenantId: this.auth.getCurrentUser().tenantId,
    }
    this.purchases.getPrsDraft(obj).subscribe(res => {
      if(res.data.length > 0 && res.data[0].items.length > 0){
        let tempData = this.dataSource.data
        this.dataSource = new MatTableDataSource<any>();
        res.data[0].items.forEach(draftItem => {
            const originalIndex = tempData.findIndex(
              (item) => item.itemCode.toLowerCase() === draftItem.itemCode.toLowerCase()
            );
            if (originalIndex > -1) {
              tempData.splice(originalIndex, 1);
            }
            tempData.unshift(draftItem);
          });
          this.dataSource.data = tempData
          this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
          this.dataSource.paginator = this.paginator;
          this.cd.detectChanges(); 
      }
    })
  }

  deleteDraft(){
    let obj = {
      restaurantId: this.restaurantId,
      tenantId: this.auth.getCurrentUser().tenantId,
    }
    this.purchases.deletePrsDraft(obj).subscribe(res => {
      // this.utils.snackBarShowSuccess('order cleared successfully')
    })
  }

  isGreater(obj: { withTaxPrice: number; quotedUnitPrice: number; }) {
    if (obj.withTaxPrice > obj.quotedUnitPrice)
      return 'green'
    else if (obj.withTaxPrice === obj.quotedUnitPrice)
      return 'black';
    else
      return 'red'
  }

  approveQuotes() {
    this.utils.snackBarShowSuccess(`number of quotes approved : ${this.selection.selected.length}`)
  }

  private intializeInputObj() {
    this.dataSource = new MatTableDataSource<any>();
    this.inventoryItems = this.inputObj.data
    this.inventoryItems.map((item: any) => {
      if (!item.vendorType)
        item.vendorType = 'Fixed'
      item.otb = this.utils.truncateNew(item.otb / item.moq) * item.moq      
      item.orderQty = item.otb
      const defVendor = item.defVendor;
      // let vendor = item.vendorList.find((vendor: any) => vendor.vendorName === defVendor);
      this.selectVendor(item.vendorList[0].vendorName , item);
      if (item.moq && item.moq > item.otb)
        item.orderQty = item.moq
      item.withTaxPrice = item.vendorList[0].withTaxPrice
      item.totalValue = item.withTaxPrice * item.otb;
      item.defVendor = item.vendorList[0].vendorName;
      item.vendorId = item.vendorList[0].vendorId;
      item.withTaxPrice = item.withTaxPrice ? item.withTaxPrice : 0;
      this.selectVendor(item.vendorList[0].vendorName , item);
      return item
    });
    this.dataSource.data = this.inventoryItems;
    console.log("🚀 ~ PurchaseListComponent ~ intializeInputObj ~ this.dataSource.data:", this.dataSource.data)
    this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
    this.dataSource.paginator = this.paginator;

    this.inventoryItems.forEach(item => {
      if (item.category == null) {
        item.category = 'N/A'
      }
      if (item.subCategory == null) {
        item.subCategory = 'N/A'
      }
      this.categoryList.push(item.category)
      this.subCategoryList.push(item.subCategory)
      if (!item.uom)
        item.uom = "units"
    })
    this.categoryList = this.categoryList.filter((k, i, ar) => ar.indexOf(k) === i);
    this.subCategoryList = this.subCategoryList.filter((j, l, arr) => arr.indexOf(j) === l);
    this.initCategoryList = this.categoryList;
    this.initSubCategoryList = this.subCategoryList;
    this.title = this.dataObj.title;
    this.displayedColumns = this.dataObj.displayedColumns
    this.dataSource.sort = this.sort;

    this.Category.setValue('All');
    this.selectCategory('All');
    this.Subcategory.setValue('All');
    this.selectSubCat('All');
    this.vendorForm.setValue('All');
    this.selectedVendor({'tenantId': 'All' , 'name': 'All' })
    this.getPrsDraft();
  }


  createPrs() {
    let items = this.selection.selected.filter(item => item.orderQty > 0)
    let obj = {
      items: items,
      restaurantId: this.restaurantId,
      tenantId: this.auth.getCurrentUser().tenantId,
      uId: this.auth.getCurrentUser().mId,
      type: 'purchaseList'
    }
    this.purchases.createPrs(obj).subscribe(data => {
      let prSet = new Set();
      data.prDetails.forEach((pr: { vendorName: unknown; }) => prSet.add(pr.vendorName))
      this.dialog.open(SimpleDialogComponent, {
        data: {
          title: 'PRs generated',
          msg: `Total number PRs generated ${data.totalPrs} for ${prSet.size} vendors`,
          ok: function () {
            this.deleteDraft();
            this.router.navigate(['/home/<USER>']);
          }.bind(this)
        }
      })
    }, err => console.error(err))
  }

  filterByBranch(restId: { restaurantIdOld: any; }) {
    this.restaurantId = restId.restaurantIdOld;
    this.branchSelected = true
    this.inputObj = {}
    this.getSsi()
  }

  selectVendor(vendor: { unitPrice : any; withTaxPrice: any; taxRate: any; vendorId: any; }, element: { withTaxPrice: any; taxRate: any; vendorId: any; vendorList :any; }) {    
    const vendorList = element.vendorList.find(v => v.vendorName === vendor);
    element['withTaxPrice'] =  vendorList.withTaxPrice ? vendorList.withTaxPrice : vendorList.unitPrice
    element.taxRate = vendorList.taxRate
    element.vendorId = vendorList.vendorId
    this.cd.detectChanges();
  }

  selectCategory(cat: string) {
    let filteredSubCategoryList = []
    let filteredItem = []
    if (cat != 'All') {
      filteredItem = this.inventoryItems.filter(item => item['category'].toUpperCase() === cat.toUpperCase());
      filteredItem.forEach(element => {
        filteredSubCategoryList.push(element.subCategory)
      });
      this.subCategoryList = filteredSubCategoryList.filter((k, i, ar) => ar.indexOf(k) === i);
      this.subCategoryList.splice(0, 0, 'All')
    }
    else {
      this.subCategoryList = this.initSubCategoryList
    }
    this.filterKeys.category = cat;
    this.filterKeys.subCategory = 'All'
    this.allFilter().then(() => {
      this.getCatTotal();
    });
  }

  selectSubCat(subCat: string) {
    this.filterKeys.subCategory = subCat;
    this.allFilter().then(() => {
      this.getSubCatTotal();
    });
  }

  selectedVendor(vendor: { tenantId: string; name: any; }) {
    this.filterKeys.vendorId = vendor.tenantId;
    this.vendor = vendor.name;
    this.allFilter().then(() => {
      this.getVendorTotal();
    });
  }

  async allFilter() {
    let tmp = this.inventoryItems
    let prev = this.inventoryItems
    Object.keys(this.filterKeys).forEach(element => {
      if (this.filterKeys[element] != 'All') {
        tmp = prev.filter(item => item[element].toLowerCase() === this.filterKeys[element].toLowerCase())
        prev = tmp
      }
    });
    this.dataSource.data = tmp
    this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
    this.dataSource.paginator = this.paginator;
  }

  clearValue() {
    this.searchValue = ''
    this.indentPreview = false
  }

  clearFilter() {
    this.filterKeys = { category: 'All', subCategory: 'All', vendorId: 'All' }
    this.vendor = ''
    this.searchValue = ''
    this.totalCatOrderValue = 0;
    this.totalSubCatOrderValue = 0;
    this.totalVendorOrderValue = 0;
    this.doFilter('')
    this.dataSource.data = this.inventoryItems
    this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
    this.dataSource.paginator = this.paginator;
    this.indentPreview = false
  }

  clearAllFilter() {
    this.Category.setValue('')
    this.Subcategory.setValue('')
    this.vendorForm.setValue('')
    this.filterKeys = { category: 'All', subCategory: 'All', vendorId: 'All' }
    this.vendor = ''
    this.searchValue = ''
    this.totalCatOrderValue = 0;
    this.totalSubCatOrderValue = 0;
    this.totalVendorOrderValue = 0;
    this.doFilter('')
    this.dataSource.data = this.inventoryItems
    this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
    this.dataSource.paginator = this.paginator;
    this.indentPreview = false
  }

  generatePL() {
    this.exportStatusDataLoader = true;
    let obj = {}
    obj['tenantId'] = this.auth.getCurrentUser().tenantId
    obj['restaurantId'] = this.restaurantId
    obj['dataSource'] = this.dataSource.data
    this.masterDataService.export(obj).subscribe(res => {
      if (res['result'] == 'success') {
        var downloadLink = document.createElement("a");
        downloadLink.href = 'data:application/vnd.ms-excel;base64,' + res.eFile;
        downloadLink.download = res.fileName;
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
        this.exportStatusDataLoader = false;
        this.exportStatusDataCheck = true;
      } else {
        this.utils.snackBarShowError("Something went Wrong")
      }
    });
  }

  focusFunctionWithOutForm(element: { [x: string]: null; }, value: string | number) {
    if (Number(element[value]) === 0) {
      element[value] = null;
    }
  }

  focusOutFunctionWithOutForm(element: { [x: string]: number; }, value: string | number) {
    if (element[value] === null) {
      element[value] = 0
    }
  }
}
