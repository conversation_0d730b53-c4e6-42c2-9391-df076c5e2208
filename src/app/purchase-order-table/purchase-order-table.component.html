<div class="row title">
  <form [formGroup]="purchaseOrderForm">
    <mat-form-field *ngIf="multiBranchUser && this.user.tenantId != '100000'" appearance="none"
      class="matFormFieldTopItems">
      <mat-select placeholder="Select Branch" formControlName="branchSelection"
        (selectionChange)="filterByBranch($event.value)" class="outline">
        <mat-option *ngFor="let rest of branchesData" [value]="rest">
          {{ rest.branchName }}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </form>

  <mat-form-field appearance="none" class="ml-2 matFormFieldTopItems" *ngIf="this.user.tenantId == '100000'">
    <label>Select Client</label>
    <mat-select placeholder="Client" [(value)]="selectedClient" class="outline">
      <mat-option *ngFor="let client of clients" [value]="client.tenantId" (click)="getBranch()">
        {{client.full}}
      </mat-option>
    </mat-select>
  </mat-form-field>

  <mat-form-field appearance="none" *ngIf="this.user.tenantId == '100000'" class=" matFormFieldTopItems">
    <label>Select Branch</label>
    <mat-select placeholder="Restaurant" (selectionChange)="filterByBranch($event.value)" class="outline">
      <mat-option *ngFor="let branch of branches" [value]="branch">
        {{ branch }}
      </mat-option>
    </mat-select>
  </mat-form-field>

  <mat-form-field *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" appearance="none"
    class="ml-2 matFormFieldTopItems">
    <!-- <label>Start Date</label> -->
    <input matInput class="outline" [matDatepicker]="picker1" placeholder="Start Date" [formControl]="startDate" />
    <mat-datepicker-toggle matSuffix [for]="picker1">
      <mat-icon matDatepickerToggleIcon>
        <img src="./../../assets/calender.png" />
      </mat-icon>
    </mat-datepicker-toggle>
    <mat-datepicker #picker1></mat-datepicker>
  </mat-form-field>

  <mat-form-field *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" appearance="none"
    class="ml-2 matFormFieldTopItems">
    <!-- <label>End Date</label> -->
    <input matInput class="outline" [matDatepicker]="picker2" [formControl]="endDate" placeholder="End Date"
      [readonly]="!startDate.value" [disabled]="!startDate.value" [min]="startDate.value" />
    <mat-datepicker-toggle matSuffix [for]="picker2">
      <mat-icon matDatepickerToggleIcon>
        <img src="./../../assets/calender.png" />
      </mat-icon>
    </mat-datepicker-toggle>
    <mat-datepicker #picker2></mat-datepicker>
  </mat-form-field>

  <button *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" mat-stroked-button id="save-btn"
    class="button3 findAndClearBtn" (click)="filterByDate()">Find</button>

  <button *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" mat-stroked-button id="save-btn"
    class="button3 findAndClearBtn" (click)="clearDate()">Clear</button>
</div>

<div *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" class="datacontainer">
  <mat-card class="mt-n2">
    <mat-card-content>
      <div class="search-table-input">
        <mat-form-field appearance="none">
          <label>Search</label>
          <input matInput type="text" class="outline" placeholder="Search" [(ngModel)]='searchText'
            (keyup)="doFilter($event.target.value)" />
          <mat-icon matSuffix (click)="resetForm()" class="closebtn">close</mat-icon>
        </mat-form-field>

        <mat-form-field appearance="none">
          <label>PO Status</label>
          <mat-select placeholder="Order Status" [formControl]="purchaseStat" class="outline" multiple>
            <mat-option *ngFor="let status of purchaseStatus" [value]="status" (click)="selectPurchaseStatus(status)">
              {{ status | titlecase }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="none">
          <label>Vendor Name</label>
          <mat-select placeholder="Vendor Name" [formControl]="vendors" class="outline" [multiple]="true" #multiSelect>
            <mat-option>
              <ngx-mat-select-search placeholderLabel="Vendor Item..." noEntriesFoundLabel="'no Vendor Item found'"
                [formControl]="vendorFilterCtrl"></ngx-mat-select-search>
            </mat-option>
            <mat-option #allSelected (click)="toggleAllSelection()" [value]="1">All</mat-option>
            <mat-option *ngFor="let vendor of stockReceivevendors | async" [value]="vendor"
              (click)="selectVendorFilter(vendor)">
              {{ vendor }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="none">
          <label>Approval Status</label>
          <mat-select placeholder="Approval Status" [(ngModel)]="selectedApprovalStatus" class="outline"
            (selectionChange)="filterByStatus($event.value)">
            <mat-option value="All">All</mat-option>
            <mat-option value="approved">Approved</mat-option>
            <mat-option value="rejected">Rejected</mat-option>
            <mat-option value="pending">Pending</mat-option>
          </mat-select>
        </mat-form-field>

        <!-- <button mat-stroked-button class="clrButton" (click)="resetForm()">
          Clear
        </button> -->
        <button mat-button class="buttonForRefresh refreshBtn" (click)="refreshData()">Refresh</button>

      </div>
      <table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort>
        <ng-container matColumnDef="customerName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Customer Name</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.tenantDetails.tenantName }}
          </td>
        </ng-container>
        <ng-container matColumnDef="eta">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> PO Date</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ this.utils.formatDateToUTC(element.createTs) }}
          </td>
        </ng-container>

        <ng-container matColumnDef="deliveryDate">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Requested Delivery Date</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.validityDate | date: "EEEE, MMMM d, y" }}
          </td>
        </ng-container>

        <!-- <ng-container *ngIf="(this.user.tenantId == '100000' || (this.user.tenantId != '100000' && user.uType != 'vendor' && (closeAccess || deleteAccess)))"  matColumnDef="Action"> -->
        <ng-container matColumnDef="Action">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Actions</b>
          </th>
          <td mat-cell *matCellDef="let element , let i = index">
            <div *ngIf="this.user.tenantId == '100000'">
              <button mat-icon-button (click)="receiveOrder(element)" class="action-btn">
                <mat-icon class="action-print-icon">store</mat-icon>
              </button>
              <button mat-icon-button (click)="deletePo(element, i)" class="action-btn">
                <mat-icon class="action-print-icon" matTooltip="Delete" matTooltipPosition="right">delete</mat-icon>
              </button>
            </div>
            <div *ngIf="this.user.tenantId != '100000'">
              <button mat-icon-button (click)="receiveOrder(element)"
                *ngIf="user.uType != 'vendor' && element.status.orderStatus != 'complete'" class="action-btn">
                <mat-icon class="action-print-icon" matTooltip="View PO" matTooltipPosition="right">store</mat-icon>
              </button>
              <div *ngIf="user.uType != 'vendor' && element.status.orderStatus == 'complete'">
                <mat-icon class="check_circle">check_circle</mat-icon>
              </div>
              <button mat-icon-button (click)="updateStatus(element, 'closed')"
                [disabled]="element.status.orderStatus === 'closed' || element.status.orderStatus === 'complete'"
                class="action-btn" *ngIf="element.status.orderStatus != 'complete' && closeAccess">
                <mat-icon class="action-print-icon"
                  [matTooltip]="element.status.orderStatus === 'closed' ? '' : 'Close PO'" matTooltipPosition="right">
                  lock
                </mat-icon>
              </button>
              <button mat-icon-button (click)="deletePo(element, i)" class="action-btn" *ngIf="element.status.orderStatus === 'open' && deleteAccess">
                <mat-icon class="action-print-icon" matTooltip="Delete" matTooltipPosition="right">delete</mat-icon>
              </button>
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="restaurantName" *ngIf="user.uTpye == 'vendor'">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Restaurant</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.tenantDetails.tenantName }}
          </td>
        </ng-container>

        <ng-container matColumnDef="vendorName" *ngIf="user.uTpye != 'vendor'">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Vendor</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ (element.vendorDetails.vendorName) || '-' }}
          </td>
        </ng-container>

        <ng-container matColumnDef="approvalStatus">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Approval Status</b>
          </th>
          <td mat-cell *matCellDef="let element">{{ element.approvalStatus }}</td>
        </ng-container>

        <ng-container matColumnDef="poTerms">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Po Terms</b>
          </th>
          <td mat-cell *matCellDef="let element">
            <div *ngIf="element.poTerms" class="links" (click)="showPoTerms(element)" matTooltip="Click to view">
              poTerms
            </div>
            <div *ngIf="!element.poTerms">
              -
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="prId">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Purchase Id</b>
          </th>
          <td mat-cell *matCellDef="let element">{{ element.prId }}</td>
        </ng-container>

        <ng-container matColumnDef="poId">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Purchase Id</b>
          </th>
          <td mat-cell *matCellDef="let element" class="links" (click)="receiveOrder(element)">
            {{ element.poId }}
          </td>
        </ng-container>

        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>PO Status</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.status.orderStatus }}
          </td>
        </ng-container>

        <ng-container matColumnDef="totalAmount">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Total price</b>
          </th>
          <td mat-cell *matCellDef="let element">
            <!-- {{this.utils.truncateNew(sumTotalPrice(element.poItems))}} -->
            {{this.utils.truncateNew(element.grandTotal)}}
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>
      <div class="dataMessage" *ngIf="dataSource?.data?.length == 0"> No Data Available </div>
      <mat-paginator [showTotalPages]="5" [pageSize]="10" [pageSizeOptions]="pageSizes"></mat-paginator>
    </mat-card-content>
  </mat-card>

  <button routerLink="/home/<USER>" *ngIf="user.role === 'purchaseController'"
    matTooltip="Create a new purchase order" mat-button mat-raised-button color="primary">
    Special Order
  </button>
</div>