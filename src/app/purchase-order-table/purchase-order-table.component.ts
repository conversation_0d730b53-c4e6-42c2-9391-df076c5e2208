import { Component, OnInit, Input, ViewChild, ChangeDetectorRef } from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { PurchaseItem } from '../_models/';
import { BranchTransferService, GlobalsService } from '../_services';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog } from '@angular/material';
import { SelectionModel } from '@angular/cdk/collections';
import { UtilsService } from '../_utils/utils.service';
import { ShareDataService } from '../_services/share-data.service';
import { PurchasesService, AuthService } from '../_services/';
import { SimpleDialogComponent } from '../_dialogs/simple-dialog/simple-dialog.component';
import * as moment from 'moment';
import { MasterdataupdateService } from '../_services/masterdataupdate.service';
import { NotificationService } from '../_services/notification.service';
import { FormControl, FormGroup, FormBuilder, Validators  } from '@angular/forms';
import { ReplaySubject, Subject } from 'rxjs';
import {takeUntil } from 'rxjs/operators';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material';
import { MomentDateModule, MomentDateAdapter } from '@angular/material-moment-adapter';
import { SharedFilterService } from '../_services/shared-filter.service';
import { PreviewIbtComponent } from '../_dialogs/preview-ibt/preview-ibt.component';
import { logWarnings } from 'protractor/built/driverProviders';
import { MatOption } from '@angular/material';
import { InvoiceDialogComponent } from '../_dialogs/invoice-dialog/invoice-dialog.component';

export const MY_FORMATS = {
  parse: {
      dateInput: 'LL'
  },
  display: {
      dateInput: 'DD-MM-YYYY',
      monthYearLabel: 'YYYY',
      dateA11yLabel: 'LL',
      monthYearA11yLabel: 'YYYY'
  }
};


@Component({
  selector: 'app-purchase-order-table',
  templateUrl: './purchase-order-table.component.html',
  styleUrls: ['./purchase-order-table.component.scss', "./../../common-dark.scss"],
  providers:[{ provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
            { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }
          ]
})
export class PurchaseOrderTableComponent implements OnInit {
  @Input() inputObj: any;
  @ViewChild(MatSort) sort: MatSort;
  searchText: string;
  searchValue: any;
  windowCurrentUrl: string;
  tempData: any;
  selectedPostatus: any;
  purchaseOrderData: any[];
  selectedApprovalStatus: string;
  approvalStatus: any;
  setVendorsArray: any[];
  @ViewChild(MatPaginator) set paginator(value: MatPaginator) {
    if (this.dataSource) {
      this.dataSource.paginator = value;
    }
  };
  restaurantId: any;
  multiBranchUser; branchSelected: boolean;
  selectedvendorName: any;
  selected: any;
  dataSource = new MatTableDataSource();
  displayedColumns: string[];
  selection = new SelectionModel<PurchaseItem>(true, []);
  totalOrderValue = 90000;
  isVendor: boolean = false;
  purchaseStatus: string[] = ['All', 'complete', 'partial', 'open', 'closed'];
  vendorsList = [];
  paymentList: string[] = ['Success', 'pending', 'declain']
  filteredByDateList: any[];
  filterKeys = { status: { orderStatus: 'All' }, vendorDetails: { vendorName: 'All' } }
  date: any = { startDate: '', endDate: '' };
  pageSizes = []
  title: any;
  user: any;
  pickerDateRange: any = {};
  dateRange: any = null;
  purchaseStat = new FormControl();
  vendors = new FormControl();
  totalValue = new FormControl();
  paymentStatus = new FormControl();
  invoiceDateAndNumber = new FormControl();y
  startDate = new FormControl();
  endDate = new FormControl();
  poNumber: any;
  selectedClient = ""
  branches: any;
  getBranchData: any[]
  purchaseOrderForm: FormGroup;
  branchesData: any[]
  selectVendorAll : boolean =  true;
  stopSecondApiCall : boolean =  false;
  public stockReceivevendors: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public VendorData: any[] = [];
  public vendorFilterCtrl: FormControl = new FormControl();
  protected _onDestroy = new Subject<void>();
  purchaseOrdersUrl = encodeURI(GlobalsService.purchaseOrders)
  stockReceiveUrl = encodeURI(GlobalsService.stockReceive)
  purchaseOrdersList = encodeURI(GlobalsService.purchaseOrdersList)
  private unsubscribe$ = new Subject<void>();
  sharedFilterData: any = {};
  receivePurchaseOrderUrl = encodeURI(GlobalsService.receivePurchaseOrder)
  @ViewChild('allSelected') private allSelected: MatOption;
  clearedData : boolean = false;
  deleteAccess: boolean;
  editAccess: boolean;
  closeAccess: boolean;
  constructor(
    private notifyService: NotificationService,
    private masterDataService: MasterdataupdateService,
    private branchTransfer: BranchTransferService,
    private router: Router,
    private utils: UtilsService,
    private activateRoute: ActivatedRoute,
    private auth: AuthService,
    private purchases: PurchasesService,
    private dialog: MatDialog,
    private sharedData: ShareDataService,
    private fb: FormBuilder,
    private sharedFilterService: SharedFilterService) {
    this.user = this.auth.getCurrentUser();
    this.multiBranchUser = this.user.multiBranchUser;

    this.purchaseOrderForm = this.fb.group({
      branchSelection: [null, Validators.required]
    });

    this.activateRoute.params.subscribe((params: Params) => {
      if (params.poNumber) {
        this.poNumber = params.poNumber;
      }
    });
    var windowLocation = window.location.href;
    this.windowCurrentUrl = windowLocation.split('/')[4]
    if((this.windowCurrentUrl != this.purchaseOrdersUrl) || (this.windowCurrentUrl != this.stockReceiveUrl) || (this.windowCurrentUrl != this.purchaseOrdersList) ){
      this.windowCurrentUrl = windowLocation.split('/')[4].split(';')[0]
    }
    this.sharedFilterService.getFilteredPurOrder.subscribe(obj => 
      this.sharedFilterData = obj
    );    
      this.sharedData.sharedBranchData.pipe(takeUntil(this.unsubscribe$)).subscribe((val) => {
        this.getBranchData = val;
        if(this.getBranchData.length == 0){
          this.branchesData = this.user.restaurantAccess;
        }else if(this.getBranchData.length == 1){
          const toSelect = this.getBranchData.find(data => data.branchName == this.getBranchData[0].branchName);
          if(toSelect != this.sharedFilterData.restaurantId){
            this.sharedFilterData = '';
            this.purchaseStat.setValue(['All']);
          }
          this.purchaseOrderForm.get('branchSelection').setValue(toSelect);
          this.branchesData = this.getBranchData
          this.purchaseStat.setValue(['All'])
          this.filterByBranch(this.purchaseOrderForm.value.branchSelection);
          if(this.sharedFilterData){
            this.sharedFilterData.branchFlag = false;
          }
        }else{
          if(this.sharedFilterData.branchFlag == true){
            this.filterByBranch(this.sharedFilterData.restaurantId);
            this.sharedFilterData.branchFlag = false;
          }
          this.branchesData = this.getBranchData
        }
    });
  }

  private readonly dateRanges: any = {
    'Today': [moment(), moment()],
    'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
    'Last 7 Days': [moment().subtract(6, 'days'), moment()],
    'Last 30 Days': [moment().subtract(29, 'days'), moment()],
    'This Month': [moment().startOf('month'), moment().endOf('month')],
    'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
  }

  ngOnInit() {
        this.displayedColumns = this.inputObj.displayedColumns;
        this.tenantDetail();
  }

  ngAfterViewInit() {
    setTimeout(() => this.dataSource.paginator = this.paginator);
  }

  clearDate(){
    this.clearedData = true;
    this.tempData = undefined;
    if(this.sharedFilterData.vendorName){
      this.sharedFilterData.vendorName = ''
    }
    this.startDate.setValue(null);
    this.endDate.setValue(null);
    this.vendors.setValue(null);
    this.purchaseStat.setValue(['All']);
    this.stopSecondApiCall = true;
    this.setupCall();

  }

  filterByBranch(restId) {
    this.restaurantId = restId.restaurantIdOld;
    this.branchSelected = true
    this.setupCall();

    if(this.sharedFilterData != '' && this.sharedFilterData.restaurantId == restId){
            this.purchaseOrderForm.get('branchSelection').setValue(this.sharedFilterData.restaurantId);
        this.branches = this.getBranchData
        this.startDate.setValue(this.sharedFilterData.selectedStartDate);
        this.endDate.setValue(this.sharedFilterData.selectedEndDate);
        this.vendors.setValue(this.sharedFilterData.vendorName);
        this.purchaseStat.setValue(this.sharedFilterData.status);
        this.selectedApprovalStatus = this.sharedFilterData.approvalStatus
        this.inputObj =  this.sharedFilterData.inputObj
        this.tempData = this.sharedFilterData.data
        this.getPurOrderList();
    }
  }

  getPurOrderList() {
    this.intializeInputObj();
    this.tempData.forEach(element => {
      this.vendorsList.push(element['vendorDetails']['vendorName']);
    });
    this.vendorsList = this.vendorsList.filter((j, l, arr) => arr.indexOf(j) === l);
    this.VendorData = this.vendorsList
    this.setVendorsArray = this.VendorData;
    this.stockReceivevendors.next(this.VendorData.slice());
    this.vendorFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
        this.vendorfilterBanks();
    });
    if(this.selectVendorAll && !this.sharedFilterData.vendorName){
      this.toggleAllSelection(true);
    }
  }

  protected vendorfilterBanks() {
    if (!this.VendorData) {
      return;
    }
    let search = this.vendorFilterCtrl.value;
    if (!search) {
      this.stockReceivevendors.next(this.VendorData.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.stockReceivevendors.next(
      this.VendorData.filter(VendorData => VendorData.toLowerCase().indexOf(search) > -1)
    );
  }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
        if(!this.router.url.includes(this.receivePurchaseOrderUrl)){
            this.sharedFilterService.getFilteredPurOrder['_value'] = ''
    }
  }

  async intializeInputObj() {

    if (this.inputObj.title)
      this.title = this.inputObj.title;
    if (this.user.role.includes(GlobalsService.store)) {
      this.title = "Stock Receive";
    }
    
    this.displayedColumns = this.inputObj.displayedColumns;
    this.filteredByDateList = this.dataSource.data;
    this.dataSource.sort = this.sort;
    setTimeout(() => this.dataSource.paginator = this.paginator);
    this.dataSource.filterPredicate = (data, filter) => {
      let filt = JSON.stringify(data).includes(filter)
      return filt
    }
    if (this.poNumber) {
      this.findPrObjectById(this.poNumber);
    }
    this.masterToggle();
  }

  findPrObjectById(poId) {
    this.dataSource.data.find(obj => {
      if (obj['poId'] === poId) {
        this.receiveOrder(obj);
        return true
      }
    })
  }

  /** Selects all rows if they are not all selected; otherwise clear selection. */
  masterToggle() {
    this.isAllSelected() ?
      this.selection.clear() :
      this.dataSource.data.forEach(row => this.selection.select());
    this.getTotal();
  }

  receiveOrder(obj) {
    let inputObj = {
      restaurantId : this.purchaseOrderForm.value.branchSelection,
      selectedStartDate : this.startDate.value,
      selectedEndDate : this.endDate.value,
      vendorName : this.vendors.value,
      status : this.purchaseStat.value,
      data : this.tempData,
      inputObj : this.inputObj,
      approvalStatus : this.selectedApprovalStatus,
      branchFlag : true,
      setVendorsArray : this.setVendorsArray
    }
    this.sharedFilterService.getFilteredPurOrder.next(inputObj);
    this.sharedData.changeOrder(obj);
    this.router.navigate(['/home/<USER>']);
  }

  getTotal(e?: Event) {
    this.selection.selected.forEach(item => {
      item.totalValue = item.orderQty * item.unitPrice;
    });
    this.totalOrderValue = this.utils.getTotal(this.selection.selected, 'totalValue') / 100000;
  }

  sumTotalPrice(arr) {
    return arr.reduce((sum, element) => { 
      let afterDiscount = 0;
      let tax = 0;
      afterDiscount = (element.quantity * element.packages[0].packagePrice) - (element.discAmt ? element.discAmt : 0)
      tax = afterDiscount * (element.taxRate / 100)
      element.subTotal = afterDiscount;
      element.taxAmount = tax;
      return sum += afterDiscount + (element.cessAmt ? element.cessAmt : 0) + (element.extraAmt ? element.extraAmt : 0) + tax;
    }, 0 );
  }

  async setupCall(newF = false) {
            let obj = {
          tenantId: this.user.tenantId,
          uType: this.user.uType,
          restaurantId: this.restaurantId,
        }
        if(this.startDate.value && this.endDate.value){
          obj['startDate'] = this.utils.dateCorrection(this.startDate.value);
          obj['endDate'] = this.utils.dateCorrection(this.endDate.value);
        }else{
          obj['startDate'] = null;
          obj['endDate'] = null;
        }
        
        if(this.vendors.value && this.tempData){
          let array = []
          array.push(this.vendors.value)
          array = array[0].filter(item => item !== "All");
          // "filter data using vendorname"
          const filteredData = this.tempData.filter(item => array.includes(item.vendorDetails.vendorName));
          // "remove Duplicate data"
          const uniqueData = filteredData.filter((obj, index, self) => {
            return index === self.findIndex(item => item.vendorDetails.vendorName === obj.vendorDetails.vendorName);
          });
          const vendorIds = [];
          uniqueData.forEach((obj: any) => {
            vendorIds.push(obj.vendorDetails.vendorId);
          });
          if(vendorIds.length === 0){
            obj['vendorId'] = ['All'];
          }else{
            if (newF){
              obj['vendorId'] = ['All'];
            }else{
              obj['vendorId'] = vendorIds
            }
            
          }
        }else{
          obj['vendorId'] = ['All'];
        }

        if(this.purchaseStat.value){
          obj['status'] = this.purchaseStat.value;
        }else{
          obj['status'] = ['All']; 
        }
        
        this.purchases.getPo(obj).subscribe(data => {
          if (data == undefined) {
            data = []
          }
          if(!this.selectedApprovalStatus){
            this.selectedApprovalStatus = 'All'
          }
          this.dataSource.data = data;
          this.purchaseOrderData = data
          this.pageSizes = this.utils.getPageSizes(data)
          this.dataSource.paginator = this.paginator;
          this.dataSource.data.forEach(async (el)=>{
            let status
            el['poApprovalDetail'].length > 0 ? ( status = this.getPoStatus(el['poApprovalDetail'])) : status = 'approved' ;
            el['approvalStatus'] = status ;
          })
          if(this.tempData == undefined || this.tempData.length == 0){
            this.tempData = data
            this.getPurOrderList();
          }
          if(this.clearedData || (this.sharedFilterData.restaurantId != this.purchaseOrderForm.value.branchSelection)){
            this.vendorsList = [];
            this.getPurOrderList();
          }
          this.clearedData = false;
        });
  }

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }

  selectVendor(vendor, element) {
    element.unitPrice = vendor.unitPrice
    element.taxRate = vendor.taxRate
    element.vendorId = vendor.vendorId
  }

  showSpecialOrder() {
    this.utils.snackBarShowError('Special Order Dialog');
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  checkboxLabel(row?: PurchaseItem): string {
    if (!row) {
      return `${this.isAllSelected() ? 'select' : 'deselect'} all`;
    }
    return `${this.selection.isSelected(row) ? 'deselect' : 'select'} row ${row.itemCode + 1}`;
  }

  acceptOrder(obj) {
    this.utils.snackBarShowSuccess(`printing order ${obj.id}`);
  }

  saveOrders() {
    this.utils.snackBarShowSuccess(`saving orders no of orders : ${this.selection.selected.length}`);
  }

  allFilter() {
    let tmp = this.filteredByDateList
    let prev = this.filteredByDateList
    Object.keys(this.filterKeys).forEach(element => {
      Object.keys(this.filterKeys[element]).forEach(nestElement => {
        if (this.filterKeys[element][nestElement] != 'All') {
          tmp = prev.filter(purOrder => {
            if (purOrder[element][nestElement] !== undefined) {
              return purOrder[element][nestElement].toLowerCase() === this.filterKeys[element][nestElement].toLowerCase()
            }
          })
          prev = tmp
        }
      });
    });
    this.dataSource.data = tmp;
    this.dataSource.sort = this.sort;
    setTimeout(() => this.dataSource.paginator = this.paginator);
  }

  selectPurchaseStatus(status) {
    this.selectedPostatus = status
    this.setupCall();
  }

  selectVendorFilter(vendor) {
    this.stopSecondApiCall = true;
    this.selectedvendorName = vendor
    this.setupCall();
  }

  filterByDate() {
    this.tempData = undefined;
    this.selectVendorAll = true;
    this.setupCall(true);
  }

  resetForm() {
    this.date = '';
    this.purchaseStat.setValue('')
    this.vendors.setValue('')
    this.searchText = ''
    this.filterKeys.status.orderStatus = 'All'
    this.filterKeys.vendorDetails.vendorName = 'All'
    this.searchValue = ''
    this.doFilter(this.searchValue)
    this.dataSource.data = this.filteredByDateList;    
  }

  deletePo(element, ind) {
    this.dialog.open(SimpleDialogComponent, {
      data: {
        title: 'Delete Alert',
        msg: 'Are you sure you want to delete this PO?',
        ok: function () {
            this.purchases.deletePO(
              {
                tenantId: this.selectedClient,
                restaurantId: this.restaurantId,
                email: this.user.email,
                poId: element.poId
              }
            ).subscribe(data => {
              if (data.result == 'success') {
                this.utils.openSnackBar(element.poId + ' is deleted successfully', null, 3000);
                this.dataSource.data.splice(ind, 1)
                this.dataSource._updateChangeSubscription()
              }
              else {
                this.utils.snackBarShowError(data.exception);
              }
            },
              (err) => {
                console.error(err);
              });
            this.filterByBranch(this.purchaseOrderForm.value.branchSelection);
        }.bind(this)
      }
    });
  }


  getBranch() {
    let obj = {};
    obj['tenantId'] = this.selectedClient;
    this.branchTransfer.getBranch(obj).subscribe((response: any) => {
      if (response.success) {
        this.branches = response.data;
      }
    });
  }

  refreshData(){
    this.setupCall();
  }

  filterByStatus(event){
    if(event == 'All'){
      this.dataSource.data = this.purchaseOrderData
      this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
    }else{
      this.dataSource.data = []
      this.dataSource.data = (event === 'pending') ? this.purchaseOrderData.filter(request => (request.approvalStatus != "rejected") && (request.approvalStatus != "approved")) : this.purchaseOrderData.filter(request => request.approvalStatus === event) ;
      this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
    }
  }

  getPoStatus(data){
    if(Object.keys(data).length !== 0){
    const levelOrder = data.map(item => item.level);
    let levelWithStatus = "";
    for (const currentLevel of levelOrder) {
      const matchingData = data.find(item => item.level === currentLevel);
      
      if (matchingData) {
        const { level, status } = matchingData;
        
        if (status === "rejected") {
          levelWithStatus = `${level} ${status.charAt(0).toUpperCase() + status.slice(1)}`;
          break;
        } else if (status === "pending" && !levelWithStatus.includes("rejected")) {
          levelWithStatus = `${level} ${status.charAt(0).toUpperCase() + status.slice(1)}`;
        } else if (status === "approved" && !levelWithStatus.includes("rejected") && !levelWithStatus.includes("pending")) {
          levelWithStatus = status.charAt(0).toUpperCase() + status.slice(1);
        }
      }
    }
    return levelWithStatus
  }
  }

  showPoTerms(element){
    this.dialog.open(PreviewIbtComponent, {
      // height: "600px",
      width: "600px",
      data: {
        title: "Po Terms",
        items: element.poTerms,
        ok: function () {
          this.location.back();
        }.bind(this),
      },
    });
  }

  toggleAllSelection(manual = false) {
    this.selectVendorAll = false
    if (this.allSelected && this.allSelected.selected || manual) {
      this.vendors.patchValue([]);
      this.vendors.patchValue([...this.tempData.map(item => item.vendorDetails.vendorName), 1]);
    }else {
      this.vendors.patchValue([]);
    }
    if(this.stopSecondApiCall){
      this.setupCall();
    }
  }

  updateStatus(element,event){
      let obj = {}
      obj['tenantId'] = element.tenantId
      obj['poId'] = element.poId
      obj['uId'] = this.user.mId
      obj['orderStatus'] = event
      this.dialog.open(SimpleDialogComponent, {
        data: {
          title: 'Close PO',
          msg: 'Are you sure you want to Close?',
          ok: function () {
            this.purchases.updateStatus(obj).subscribe(res => {
              res.result ? (this.utils.snackBarShowSuccess('Po closed successfully'),this.refreshData()) : this.utils.snackBarShowError('Something went wrong')
            })
          }.bind(this)
        }
      });
  }

  tenantDetail() {
    let obj = {
      tenantId: this.user.tenantId
    }
    this.branchTransfer.tenantDetails(obj).subscribe(res => {
      if (res.result == 'success') {
        let tenantDetails = res.data[0].permission 
        let deleteAccess = res.data[0] && res.data[0].permission && res.data[0].permission.POAccess ? res.data[0].permission.POAccess.delete : false;
        let editAccess = res.data[0] && res.data[0].permission && res.data[0].permission.POAccess ? res.data[0].permission.POAccess.edit : false;
        let closeAccess = res.data[0] && res.data[0].permission && res.data[0].permission.POAccess ? res.data[0].permission.POAccess.close : false;

        if (deleteAccess) {
          let rolesList = (res.data[0] && res.data[0].permission && res.data[0].permission.POAccess) ? res.data[0].permission.POAccess.deleteAccess : [];
          this.deleteAccess = (rolesList.some(role => role.toLowerCase() === this.user.role.toLowerCase())) ? true : false ;
        } else {
          this.deleteAccess = false ;
        }
        if (editAccess) {
          let rolesList = (res.data[0] && res.data[0].permission && res.data[0].permission.POAccess) ? res.data[0].permission.POAccess.editAccess : [];
          this.editAccess = (rolesList.some(role => role.toLowerCase() === this.user.role.toLowerCase())) ? true : false ;
        } else {
          this.editAccess = false ;
        }
        if (closeAccess) {
          let rolesList = (res.data[0] && res.data[0].permission && res.data[0].permission.POAccess) ? res.data[0].permission.POAccess.closeAccess : [];
          this.closeAccess = (rolesList.some(role => role.toLowerCase() === this.user.role.toLowerCase())) ? true : false ;
        } else {
          this.closeAccess = false ;
        }
        if (this.user.tenantId == '100000' || (this.user.tenantId != '100000' && this.user.uType != 'vendor' && (this.closeAccess || this.deleteAccess))) {
          if (!this.displayedColumns.includes('Action')) {
            this.displayedColumns.push('Action');
          }
        }
      } else {
        this.deleteAccess = false ;
        this.editAccess = false ;
        this.closeAccess = false ;
      }
    })
  }

}
