import { Component, OnInit} from '@angular/core';
import { GlobalsService } from '../_services/globals.service';
import { AuthService } from '../_services/';

@Component({
  selector: 'app-purchase-orders',
  templateUrl: './purchase-orders.component.html',
  styleUrls: ['./purchase-orders.component.scss', './../../common-dark.scss']
})
export class PurchaseOrdersComponent implements OnInit {

  dataObj : any = {};
  user: any;

  constructor(private auth : AuthService) {
  }

  ngOnInit() {
    this.user =  this.auth.getCurrentUser();
    if(this.auth.getCurrentUser().uType === GlobalsService.vendor)
    this.dataObj.displayedColumns = GlobalsService.vendorPurchaseOrdersColumns;
    else
    this.dataObj.displayedColumns = GlobalsService.purchaseOrdersColumns;
    this.dataObj.title = "Purchase Orders";
  }


}
