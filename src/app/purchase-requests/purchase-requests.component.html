<div class="title row">
  <form [formGroup]="purchaseRequestForm">
    <mat-form-field appearance="none" class="matFormFieldTopItems" *ngIf="this.user.tenantId != '100000' && multiBranchUser">
      <mat-select placeholder="Restaurant" formControlName="branchSelection"
        (selectionChange)="filterByBranch($event.value)" class="outline">
        <mat-option *ngFor="let rest of branchesData" [value]="rest">
          {{ rest.branchName }}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </form>

  <mat-form-field appearance="none" class="matFormFieldTopItems ml-2" *ngIf="this.user.tenantId == '100000'">
    <mat-select placeholder="Client" [(value)]="selectedClient" class="outline">
      <mat-option *ngFor="let client of clients" [value]="client.tenantId" (click)="afterselectclient()">
        {{client.full}}
      </mat-option>
    </mat-select>
  </mat-form-field>

  <mat-form-field appearance="none" class="matFormFieldTopItems ml-2" *ngIf="this.user.tenantId == '100000'">
    <mat-select placeholder="Restaurant" [(ngModel)]="selectedBranch" (selectionChange)="filterByBranch($event.value)"
      class="outline">
      <mat-option *ngFor="let branch of branches" [value]="branch">
        {{ branch }}
      </mat-option>
    </mat-select>
  </mat-form-field>

  <mat-form-field *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" appearance="none"
    style="margin-left: 10px;" class="matFormFieldTopItems ml-2">
    <input matInput class="outline" [matDatepicker]="picker1" [(ngModel)]="selectedStartDate" placeholder="Start Date"
      [formControl]="startDate" />
    <mat-datepicker-toggle matSuffix [for]="picker1">
      <mat-icon matDatepickerToggleIcon>
        <img src="./../../assets/calender.png" />
      </mat-icon>
    </mat-datepicker-toggle>
    <mat-datepicker #picker1></mat-datepicker>
  </mat-form-field>

  <mat-form-field *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" appearance="none"
    style="margin-left: 10px;" class="matFormFieldTopItems ml-2">
    <input matInput class="outline" [matDatepicker]="picker2" [(ngModel)]="selectedEndDate" [formControl]="endDate"
      placeholder="End Date" [readonly]="!startDate.value" [disabled]=" !startDate.value" [min]="startDate.value" />
    <mat-datepicker-toggle matSuffix [for]="picker2">
      <mat-icon matDatepickerToggleIcon>
        <img src="./../../assets/calender.png" />
      </mat-icon>
    </mat-datepicker-toggle>
    <mat-datepicker #picker2></mat-datepicker>
  </mat-form-field>

  <button *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" id="save-btn" mat-stroked-button
    class="button3 findAndClearBtn" (click)="filterByDate()">Find</button>

  <button *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" id="save-btn" mat-stroked-button
    class="button3 findAndClearBtn" (click)="clearDate()">Clear</button>

  <!-- <div class="mt-2 grep">
  <button routerLink="/home/<USER>" *ngIf="((branchSelected && multiBranchUser) || !multiBranchUser) && (user.uType != 'vendor')" matTooltip="Create a new purchase order"
  mat-button mat-raised-button style="float: right;" class="button3">
  Special Order
  </button>
</div> -->

</div>

<div *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" class="datacontainer">
  <mat-card>
    <mat-card-content>
      <div class="search-table-input">
        <mat-form-field appearance="none">
          <label>Search</label>
          <input matInput type="text" class="outline" [(ngModel)]='searchText' (keyup)="doFilter($event.target.value)"
            placeholder="Search" />
          <mat-icon matSuffix class="closebtn" (click)="resetForm()">close</mat-icon>
        </mat-form-field>

        <mat-form-field id="vendor-select" appearance="none">
          <label>Vendor Name</label>
          <mat-select placeholder="Vendor Name" [formControl]="vendorForm" class="outline" [multiple]="true"
            #multiSelect>
            <mat-option>
              <ngx-mat-select-search placeholderLabel="Vendor Item..." noEntriesFoundLabel="'no Vendor Item found'"
                [formControl]="vendorFilterCtrl"></ngx-mat-select-search>
            </mat-option>
            <mat-option #allSelected (click)="toggleAllSelection()" [value]="1">All</mat-option>
            <mat-option *ngFor="let vendor of purReqvendors | async" [value]="vendor" (click)="selectVendor(vendor)">
              {{ vendor | titlecase }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <button mat-button class="buttonForRefresh refreshBtn" (click)="refreshdata()">Refresh</button>
      </div>

      <table #table mat-table [dataSource]="dataSource" matSortActive="prId" matSortDirection="asc" matSort>
        <ng-container matColumnDef="customerName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Customer Name</b>
          </th>
          <td mat-cell *matCellDef="let element">{{ element.customerName }}</td>
        </ng-container>
        <ng-container matColumnDef="supplyDate">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Suppply Date</b>
          </th>
          <td mat-cell *matCellDef="let element">{{ element.supplyDate }}</td>
        </ng-container>

        <ng-container matColumnDef="restaurantName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Restaurant</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.tenantDetails.tenantName }}
          </td>
        </ng-container>

        <ng-container matColumnDef="vendorName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Vendor</b>
          </th>
          <td mat-cell *matCellDef="let element">
            <div *ngIf="element?.vendorDetails?.vendorName">
              {{ element.vendorDetails.vendorName }}
            </div>
            <div *ngIf="!element?.vendorDetails?.vendorName">
              -
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="prId">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Purchase Request Id</b>
          </th>
          <td mat-cell *matCellDef="let element" (click)="detailedPr(element)" class="links">
            {{ element.prId }}
          </td>
        </ng-container>

        <ng-container matColumnDef="poTerms">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Po Terms</b>
          </th>
          <!-- <td mat-cell *matCellDef="let element" class="links" (click)="showPoTerms(element)">{{ element.poTerms ? element.poTerms : '-'}}</td> -->
          <td mat-cell *matCellDef="let element">
            <div *ngIf="element.poTerms" class="links" (click)="showPoTerms(element)" matTooltip="Click to view">
              poTerms
            </div>
            <div *ngIf="!element.poTerms">
              -
            </div>
          </td>
        </ng-container>

        <ng-container matColumnDef="eta">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Delivery Date</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.eta | date: "EEEE, MMMM d, y" }}
          </td>
        </ng-container>

        <ng-container matColumnDef="poId">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Purchase Id</b>
          </th>
          <td mat-cell *matCellDef="let element">{{ element.id }}</td>
        </ng-container>
        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>PO Status</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.progress }}
          </td>
        </ng-container>

        <ng-container matColumnDef="approvalStatus">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Approval Status</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.approvalStatus | titlecase}}
          </td>
        </ng-container>

        <ng-container matColumnDef="totalAmount">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Total price</b>
          </th>
          <td mat-cell *matCellDef="let element">
            <!-- {{ this.utils.truncateNew(sumTotalPrice(element.prDetails)) }} -->
            {{ this.utils.truncateNew(element.grandTotal) }}
          </td>
        </ng-container>

        <ng-container matColumnDef="actionBtns">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Actions</b>
          </th>
          <td mat-cell *matCellDef="let element , let i = index">
            <div *ngIf="this.user.tenantId == '100000'">
              <mat-icon matTooltip="Delete" matTooltipPosition="left" (click)="deletePrs(element)">delete
              </mat-icon>
            </div>

            <div *ngIf="this.user.tenantId != '100000'">
              <button mat-icon-button (click)="editPr(element)" matTooltip="Edit" matTooltipPosition="left"
                class="action-btn">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                  class="bi bi-pencil-square svgEditIcon" viewBox="0 0 16 16">
                  <path
                    d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z" />
                  <path fill-rule="evenodd"
                    d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5v11z" />
                </svg>
              </button>
            </div>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>
      <div class="dataMessage" *ngIf="dataSource?.data.length == 0"> No Data Available </div>
      <mat-paginator [showTotalPages]="5" [pageSize]="10" [pageSizeOptions]="pageSizes"></mat-paginator>
    </mat-card-content>
  </mat-card>
</div>