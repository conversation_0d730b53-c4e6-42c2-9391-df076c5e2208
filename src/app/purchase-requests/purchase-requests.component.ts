import { Component, OnInit, ViewChild } from '@angular/core';
import { GlobalsService } from '../_services/globals.service';
import { PurchasesService, AuthService, BranchTransferService } from '../_services/';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog } from '@angular/material';
import { SelectionModel } from '@angular/cdk/collections';
import { UtilsService } from '../_utils/utils.service';
import { ShareDataService } from '../_services/share-data.service';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { MasterdataupdateService } from '../_services/masterdataupdate.service';
import { NotificationService } from '../_services/notification.service';
import { FormControl, FormGroup, FormBuilder, Validators  } from '@angular/forms';
import { ReplaySubject, Subject } from 'rxjs';
import {takeUntil } from 'rxjs/operators';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material';
import { MomentDateModule, MomentDateAdapter } from '@angular/material-moment-adapter';
import { SharedFilterService } from '../_services/shared-filter.service';
import { PreviewIbtComponent } from '../_dialogs/preview-ibt/preview-ibt.component';
import { MatOption } from '@angular/material';

export const MY_FORMATS = {
  parse: {
      dateInput: 'LL'
  },
  display: {
      dateInput: 'DD-MM-YYYY',
      monthYearLabel: 'YYYY',
      dateA11yLabel: 'LL',
      monthYearA11yLabel: 'YYYY'
  }
};
@Component({
  selector: 'app-purchase-requests',
  templateUrl: './purchase-requests.component.html',
  styleUrls: ["./purchase-requests.component.scss", "./../../common-dark.scss"],
  providers:[{ provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
            { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }
          ]
})
export class PurchaseRequestsComponent implements OnInit {
  dataObj: any = {};
  selectedvendorName: any;
  vendorList: any = []
  user: any;
  selectedBranch: any;
  restaurantId: any;
  selectedStartDate: any;
  selectedEndDate: any;
  multiBranchUser; branchSelected: boolean;
  dataSource: MatTableDataSource<any> = new MatTableDataSource<any> ();
  displayedColumns: string[];
  vendorForm = new FormControl()
  selection = new SelectionModel<any>(true, []);
  public startDate = new FormControl();
  public endDate = new FormControl();
  pageSizes=[];
  prNumber: any;
  selectclients: string;
  searchValue: string;
  searchText: string;
  windowCurrentUrl: string;
  private unsubscribe$ = new Subject<void>();
  vendorId: any;
  tempData: any;
  @ViewChild(MatPaginator) set paginator(value: MatPaginator) {
    if (this.dataSource) {
      this.dataSource.paginator = value;
    }
  };
  clients: any = [];
  selectedClient = ""
  branches: any;
  getBranchData: any[]
  purchaseRequestForm: FormGroup;
  branchesData: any[]
  public purReqvendors: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public VendorData: any[] = [];
  public vendorFilterCtrl: FormControl = new FormControl();
  protected _onDestroy = new Subject<void>();
  purchasheRequestsUrl = encodeURI(GlobalsService.purchasheRequests)
  detailedPrUrl = encodeURI(GlobalsService.detailedPr)
  sharedFilterData: any = {};
  selectVendorAll : boolean =  true;
  stopSecondApiCall : boolean =  false;
  @ViewChild('allSelected') private allSelected: MatOption;
  clearedData : boolean = false;
  constructor(
    private notifyService: NotificationService,
    private masterDataService: MasterdataupdateService,
    private branchTransfer: BranchTransferService,
    private activateRoute : ActivatedRoute,
    private purchases: PurchasesService, private auth: AuthService,
    private utils: UtilsService, private router: Router,
    private sharedData: ShareDataService,
    private fb: FormBuilder,
    private sharedFilterService: SharedFilterService,
    public dialog: MatDialog,
    ) {
    this.user = this.auth.getCurrentUser();
    this.multiBranchUser = this.user.multiBranchUser;

    this.activateRoute.params.subscribe((params: Params)=>{
      if(params.prNumber){
        this.prNumber = params.prNumber;
      }
    });

    this.purchaseRequestForm = this.fb.group({
      branchSelection: [null, Validators.required]
    });

    var windowLocation = window.location.href;
    this.windowCurrentUrl = windowLocation.split('/')[4]
    if(this.windowCurrentUrl != this.purchasheRequestsUrl ){
      this.windowCurrentUrl = windowLocation.split('/')[4].split(';')[0]
    }

    this.sharedFilterService.getFilteredPurReq.pipe(takeUntil(this.unsubscribe$)).subscribe(obj => 
      this.sharedFilterData = obj
    );    

      this.sharedData.sharedBranchData.pipe(takeUntil(this.unsubscribe$)).subscribe((val) => {
        this.getBranchData = val;
        if(this.getBranchData.length == 0 ){
          this.branchesData = this.user.restaurantAccess;
        }else if(this.getBranchData.length == 1){
          const toSelect = this.getBranchData.find(data => data.branchName == this.getBranchData[0].branchName);
          if(toSelect != this.sharedFilterData.restaurantId){
            this.sharedFilterData = '';
          }
          this.purchaseRequestForm.get('branchSelection').setValue(toSelect);
          this.branchesData = this.getBranchData
          this.filterByBranch(this.purchaseRequestForm.value.branchSelection);
          if(this.sharedFilterData){
            this.sharedFilterData.branchFlag = false;
          }
        }else{
          if(this.sharedFilterData.branchFlag == true){
            this.filterByBranch(this.sharedFilterData.restaurantId);
            this.sharedFilterData.branchFlag = false;
          }
          this.branchesData = this.getBranchData
        }
    });
  }

  ngOnInit() {
    if (this.user.uType === 'vendor'){
      this.dataObj.displayedColumns = GlobalsService.vendorPrColumns;
    }else{
      this.dataObj.displayedColumns = GlobalsService.purchaseReqColumns;
    }
  }

  ngOnDestroy() {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
    if(!this.router.url.includes(this.detailedPrUrl)){
      this.sharedFilterService.getFilteredPurReq['_value'] = ''
    }
  }

  clearDate(){
    this.clearedData = true;
    this.tempData = undefined;
    if(this.sharedFilterData.vendorName){
      this.sharedFilterData.vendorName = ''
    }
    this.selectedStartDate = ''
    this.selectedEndDate = ''
    this.startDate.setValue(null),
    this.endDate.setValue(null),
    this.vendorForm.setValue(null);
    this.stopSecondApiCall = true;
    // this.toggleAllSelection(true)
    let obj = {
      tenantId: this.user.tenantId,
      uType: this.user.uType,
      restaurantId: this.restaurantId
    }
    this.getPrReqList(obj)
  }

  getPrReqList(obj , newF= false) {
    if(this.vendorForm.value && this.tempData){
      let array = []
      array.push(this.vendorForm.value)
      array = array[0].filter(item => item !== "All");

      // "filter data using vendorname"
      const filteredData = this.tempData.filter(item => array.includes(item.vendorDetails.vendorName));

      // "remove Duplicate data"
      const uniqueData = filteredData.filter((obj, index, self) => {
        return index === self.findIndex(item => item.vendorDetails.vendorName === obj.vendorDetails.vendorName);
      });

      const vendorIds = [];
      uniqueData.forEach((obj: any) => {
        vendorIds.push(obj.vendorDetails.vendorId);
      });

      if(vendorIds.length === 0){
        obj['vendorId'] = ['All'];
      }else{
        obj['vendorId'] = vendorIds
        if (newF){
          obj['vendorId'] = ['All'];
        }else{
          obj['vendorId'] = vendorIds
        }
      }
    }else{
      obj['vendorId'] = ['All'];
    }
  
    if(this.startDate.value && this.endDate.value){
      obj['startDate'] = this.utils.dateCorrection(this.startDate.value);
      obj['endDate'] = this.utils.dateCorrection(this.endDate.value);
    }else{
      obj['startDate'] = null;
      obj['endDate'] = null;
    }
    this.purchases.getPrs(obj).subscribe(async data => {
      if(data == undefined){
        data = []
      }
      data.forEach(async (element) => {
          let status = await  element.approvalDetail.length > 0 ? this.getPrStatus(element.approvalDetail) : '-' ;
          element['approvalStatus'] = status ;
        if (element.isPoCreated == true) {
          element.progress = 'Completed'
        }
        else {
          element.progress = 'Pending'
        }
        if (element.hasOwnProperty('validityDate')) {
          if (element.validityDate != null) {
            let currDate = new Date('June 04, 2021 01:24:00')
            let dateStr = element.validityDate.substr(0, 10) + 'T' + element.validityDate.substr(11, 8)
            let validityDate = new Date(dateStr)
            if (validityDate.getTime() < currDate.getTime()) {
              element.progress = 'Expired'
            }
          }
        }
      });
      this.dataObj.data = await data;
      this.dataObj.title = "Purchase Requests";
      if (this.user.uType === 'vendor')
        this.dataObj.displayedColumns = GlobalsService.vendorPrColumns;
      else
        this.dataObj.displayedColumns = GlobalsService.purchaseReqColumns;
        this.dataSource.data = await data;
        if(this.tempData == undefined){
          this.tempData = await data;
        }
      this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
      this.dataSource.paginator = this.paginator;
        if(this.clearedData || (this.sharedFilterData.restaurantId != this.purchaseRequestForm.value.branchSelection)){
          this.vendorList = [];
          this.tempData.forEach(element => {
          this.vendorList.push(element.vendorDetails.vendorName)
        });
        this.vendorList = this.vendorList.filter((k, i, ar) => ar.indexOf(k) === i);
        this.VendorData = this.vendorList
        this.purReqvendors.next(this.VendorData.slice());
        this.vendorFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
            this.vendorfilterBanks();
        });
        if(this.selectVendorAll && !this.sharedFilterData.vendorName){
          this.toggleAllSelection(true);
        }
      }
      this.clearedData = false;
      this.displayedColumns = this.dataObj.displayedColumns;
      if(this.prNumber){
        this.findPrObjectById(this.prNumber);
      }
    }, err => { console.error(err) });
  }

  protected vendorfilterBanks() {
    if (!this.VendorData) {
      return;
    }
    let search = this.vendorFilterCtrl.value;
    if (!search) {
      this.purReqvendors.next(this.VendorData.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.purReqvendors.next(
      this.VendorData.filter(VendorData => VendorData.toLowerCase().indexOf(search) > -1)
    );
  }

   findPrObjectById(prId) {
    this.dataSource.data.find(obj => {
      if (obj['prId'] === prId){
        this.detailedPr(obj);
        return true;
      }
    })
  }

  detailedPr(obj) {
    let editFlag = false
    let uniqueArray = [];
    this.tempData.forEach(obj => {
      // Check if an object with the same id already exists in uniqueArray
      if (!uniqueArray.some(item => item.prId === obj.prId)) {
        uniqueArray.push(obj); // Push the object to uniqueArray if it's not a duplicate
      }
    });
    let inputObj = {
      restaurantId : this.purchaseRequestForm.value.branchSelection,
      selectedStartDate : this.startDate.value,
      selectedEndDate : this.endDate.value,
      vendorName : [this.vendorForm.value],
      data : uniqueArray,
      branchFlag : true
    }    
    this.sharedFilterService.getFilteredPurReq.next(inputObj);
    this.sharedData.changePr([obj, editFlag]);
    this.router.navigate(['/home/<USER>']);
  }

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }

  resetForm() {
    this.searchText = ''
    this.selectedvendorName = ''
    this.searchValue = ''
    this.doFilter(this.searchValue)
    this.dataSource.data = this.dataObj.data
  }

  filterByBranch(restId) {
    this.restaurantId = restId.restaurantIdOld;
    this.branchSelected = true

    if(this.sharedFilterData != '' && this.sharedFilterData.restaurantId == restId){
        this.purchaseRequestForm.get('branchSelection').setValue(this.sharedFilterData.restaurantId);
        this.branches = this.getBranchData;
        this.selectedStartDate = this.sharedFilterData.selectedStartDate;
        this.selectedEndDate = this.sharedFilterData.selectedEndDate;
        this.startDate.setValue(this.sharedFilterData.selectedStartDate) ;
        this.endDate.setValue(this.sharedFilterData.selectedEndDate);
        this.vendorForm.setValue(this.sharedFilterData.vendorName[0]);
        this.tempData = this.sharedFilterData.data;
        this.tempData.forEach(element => {
          this.vendorList.push(element.vendorDetails.vendorName)
        });
        this.vendorList = this.vendorList.filter((k, i, ar) => ar.indexOf(k) === i);
        this.VendorData = this.vendorList
        this.purReqvendors.next(this.VendorData.slice());
        this.vendorFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
            this.vendorfilterBanks();
        });
        if(this.selectVendorAll && !this.sharedFilterData.vendorName){
          this.toggleAllSelection(true);
        }
    }

    if(this.user.tenantId == "100000"){
    this.vendorForm.setValue('')
      let obj = {
        tenantId: this.selectedClient,
        uType: this.user.uType,
        restaurantId: this.restaurantId
      }
      if (window.sessionStorage.getItem("startDate-PR") && window.sessionStorage.getItem("endDate-PR")) {
        obj['startDate-PR'] = this.selectedStartDate
        obj['endDate-PR'] = this.selectedEndDate
      }
      this.getPrReqList(obj)
    }else{
      let obj = {
        tenantId: this.user.tenantId,
        uType: this.user.uType,
        restaurantId: this.restaurantId
      }
      if (window.sessionStorage.getItem("startDate-PR") && window.sessionStorage.getItem("endDate-PR")) {
        obj['startDate-PR'] = this.selectedStartDate
        obj['endDate-PR'] = this.selectedEndDate
      }      
      this.getPrReqList(obj)
    }
  }

  selectVendor(vendor) {
    this.stopSecondApiCall = true;
    this.vendorId = vendor
    let obj = {
      tenantId: this.user.tenantId,
      uType: this.user.uType,
      restaurantId: this.restaurantId
    }
    this.getPrReqList(obj)
  }

  filterByDate() {
    if(this.user.tenantId == "100000"){
      if (this.startDate.value && this.endDate.value) {
        this.tempData = undefined;
        this.selectVendorAll = true;
        let obj = {
          tenantId: this.selectedClient,
          uType: this.user.uType,
          restaurantId: this.restaurantId
        }
        this.getPrReqList(obj, true)
      }
      else {
          this.utils.snackBarShowError('Please select start date and end date')
      }
    }else{
      if (this.startDate.value && this.endDate.value) {
        this.tempData = undefined;
        this.selectVendorAll = true;
        let obj = {
          tenantId: this.user.tenantId,
          uType: this.user.uType,
          restaurantId: this.restaurantId
        }
        this.getPrReqList(obj)
      }
      else {
          this.utils.snackBarShowError('Please select start date and end date')
      }
    }
  }

  editPr(prObj) {
    let editFlag: boolean = true
    if (prObj.hasOwnProperty('approvalDetail')) {
      if (Object.keys(prObj.approvalDetail).length != 0) {
        for (let level of Object.entries(prObj.approvalDetail)) {
          if (level[1] == 'approved') {
            editFlag = false
            break
          }
        }
      }
    }
    if (editFlag && !prObj.isPoCreated) {
      let inputObj = {
        restaurantId : this.purchaseRequestForm.value.branchSelection,
        selectedStartDate : this.startDate.value,
        selectedEndDate : this.endDate.value,
        vendorName : [this.vendorForm.value],
        data : this.dataSource.data,
        branchFlag : true

      }    
      this.sharedFilterService.getFilteredPurReq.next(inputObj);
      this.sharedData.changePr([prObj, editFlag]);
      this.router.navigate(['/home/<USER>']);
    }
    else {
      this.utils.snackBarShowError('You can\'t edit this PR since it is partially or fully approved.');
    }

  }
  deletePrs(obj) {
    let reqObj ={}
    reqObj['prId'] = obj['prId']
    this.purchases.deletePrs(obj).subscribe(res => {
          if (res['success']){
            let obj : any = {
              tenantId : this.selectedClient,
              restaurantId : this.restaurantId
            }
            this.getPrReqList(obj)
            this.utils.snackBarShowSuccess(res['message']);
          }else{
            this.utils.snackBarShowError(res['message']);
          }
        });
  }

  masterDataUpdateConfig() {
    let obj = this.user;
    this.masterDataService.masterDataUpdateConfig(obj).subscribe((response: any) => {
      if (response.success) {
        this.clients = response.data.clients
      }
    });
  }

  afterselectclient(){
    this.getBranch();
  }

  getBranch(){
    let obj = {};
    obj['tenantId'] = this.selectedClient;
    this.branchTransfer.getBranch(obj).subscribe((response: any) => {
      if (response.success){
        this.branches = response.data;
      }
    });
  }

  refreshdata(){
    let obj = {
      tenantId: this.user.tenantId,
      uType: this.user.uType,
      restaurantId: this.restaurantId
    }
    this.getPrReqList(obj);
    }

    getPrStatus(data){
      if(Object.keys(data).length !== 0){
      const levelOrder = data.map(item => item.level);
      let levelWithStatus = "";
      for (const currentLevel of levelOrder) {
        const matchingData = data.find(item => item.level === currentLevel);
        
        if (matchingData) {
          const { level, status } = matchingData;
          
          if (status === "rejected") {
            levelWithStatus = `${level} ${status.charAt(0).toUpperCase() + status.slice(1)}`;
            break;
          } else if (status === "pending" && !levelWithStatus.includes("rejected")) {
            levelWithStatus = `${level} ${status.charAt(0).toUpperCase() + status.slice(1)}`;
          } else if (status === "approved" && !levelWithStatus.includes("rejected") && !levelWithStatus.includes("pending")) {
            levelWithStatus = status.charAt(0).toUpperCase() + status.slice(1);
          }
        }
      }
      return levelWithStatus
    }
    }

    sumTotalPrice(arr) {
      return arr.reduce((sum, obj) => sum + obj.totalPrice, 0);
    }
    
    showPoTerms(element){
      this.dialog.open(PreviewIbtComponent, {
        // height: "600px",
        width: "600px",
        data: {
          title: "Po Terms",
          items: element.poTerms,
          ok: function () {
            this.location.back();
          }.bind(this),
        },
      });
    }

    toggleAllSelection(manual = false) {
      this.selectVendorAll = false
      if (this.allSelected && this.allSelected.selected || manual) {
        this.vendorForm.patchValue([]);
        this.vendorForm.patchValue([...this.tempData.map(item => item.vendorDetails.vendorName), 1]);
      }else {
        this.vendorForm.patchValue([]);
      }
      if(this.stopSecondApiCall){
        let obj = {
          tenantId: this.user.tenantId,
          uType: this.user.uType,
          restaurantId: this.restaurantId
        }
        this.getPrReqList(obj)
      }
    }
}