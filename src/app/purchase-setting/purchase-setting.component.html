<mat-card>
  <div class="infoMessage">
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
      class="bi bi-info-circle-fill infoSvgIcon" viewBox="0 0 16 16">
      <path
        d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm.93-9.412-1 4.705c-.07.34.029.533.304.533.194 0 .487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703 0-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381 2.29-.287zM8 5.5a1 1 0 1 1 0-2 1 1 0 0 1 0 2z" />
    </svg>
    <p class="ml-2 mb-0" *ngIf="this.selectedMechanism != 'Amount Based'">
      <strong>DEFAULT</strong> category is mandatory for role based approval
    </p>
    <p class="ml-2 mb-0" *ngIf="this.selectedMechanism == 'Amount Based'">
      The <strong>amount</strong> should follow the pattern where Level 1 is
      less than Level 2, Level 2 is less than Level 3, and so on, up to Level N
    </p>
  </div>
  <div>
    <div fxLayout="row">
      <form [formGroup]="approvalSettingForm">
        <mat-form-field appearance="none" style="margin: 5px">
          <label>Select Branch</label>
          <mat-select placeholder="Restaurant" (selectionChange)="filterByBranch($event.value)" class="outline"
            formControlName="branchSelection">
            <mat-option *ngFor="let rest of branches" [value]="rest">
              {{ rest.branchName }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </form>

      <mat-form-field appearance="none" style="margin: 5px">
        <label>Select Type</label>
        <mat-select placeholder="Type" [(ngModel)]="selectedMechanism" (selectionChange)="selectType($event.value)"
          class="outline" [disabled]="selectedType == 'grnApproval' || settingFound">
          <mat-option *ngFor="let type of this.types" [value]="type">
            {{ type }}
          </mat-option>
        </mat-select>
        <div *ngIf="!selectedMechanism" class="error-message">
          Please select a Type.
        </div>
      </mat-form-field>
    </div>
  </div>

  <mat-tab-group (selectedTabChange)="tabClick($event)">
    <mat-tab>
      <ng-template mat-tab-label>
        <strong>PR Setting</strong>
      </ng-template>
      <mat-expansion-panel hideToggle #panel="matExpansionPanel" (click)="click(PRForm, workArea, panelOpenState)" (opened)="panelOpenState = true" (closed)="panelOpenState = false"
       *ngFor="let workArea of workAreas">
        <button mat-button (click)="saveSetting(workArea)" class="button3 mt-2 mb-2 tableTopItemsBtns"
          matTooltip="click to save setting" [disabled]="!selectedMechanism">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-save" viewBox="0 0 16 16">
            <path d="M2 1a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1H9.5a1 1 0 0 0-1 1v7.293l2.646-2.647a.5.5 0 0 1 .708.708l-3.5 3.5a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L7.5 9.293V2a2 2 0 0 1 2-2H14a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V2a2 2 0 0 1 2-2h2.5a.5.5 0 0 1 0 1H2z"/>
          </svg>
        </button>
        <button mat-button class="button3 m-2 tableTopItemsBtns" matTooltip="click to add new level"
          (click)="AddNewRow(PRForm, workArea)" [disabled]="!selectedMechanism">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-plus-circle" viewBox="0 0 16 16">
            <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
            <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
          </svg>
        </button>
        <mat-expansion-panel-header>
          <mat-panel-title>
            <span><strong >{{workArea}}</strong> 
              <svg *ngIf="this.checkWorkSAreaDatas.includes(workArea)" xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-check-circle-fill ml-2 tickIcon" viewBox="0 0 16 16">
                <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/>
              </svg>
              <svg *ngIf="!this.checkWorkSAreaDatas.includes(workArea)" xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-x-circle-fill ml-2 wrongIcon" viewBox="0 0 16 16">
                <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM5.354 4.646a.5.5 0 1 0-.708.708L7.293 8l-2.647 2.646a.5.5 0 0 0 .708.708L8 8.707l2.646 2.647a.5.5 0 0 0 .708-.708L8.707 8l2.647-2.646a.5.5 0 0 0-.708-.708L8 7.293 5.354 4.646z"/>
              </svg>
          </span>
          </mat-panel-title>
            <!-- <mat-icon *ngIf="workArea != this.getWorkArea">add</mat-icon>
            <mat-icon *ngIf="workArea == this.getWorkArea">remove</mat-icon> -->
            <mat-icon *ngIf="!this.getWorkArea.includes(workArea)">add</mat-icon>
            <mat-icon *ngIf="this.getWorkArea.includes(workArea)">remove</mat-icon>
        </mat-expansion-panel-header>
        <form [formGroup]="PRForm" autocomplete="off" *ngIf="isPRFormReady">
          <ng-container formArrayName="VORows">
            <table #table mat-table [dataSource]="filterVoRowsByWorkArea(PRForm, workArea)">
              <ng-container matColumnDef="category">
                <th mat-header-cell *matHeaderCellDef class="tableCategory">
                  Category
                </th>
                <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" class="tableCategory">
                  <mat-form-field appearance="none">
                    <input matInput *ngIf="i == 0" type="text" class="outline" formControlName="category" readonly />
                    <mat-select *ngIf="i != 0" (selectionChange)="categorySelection(PRForm, workArea)"
                      placeholder="select category" class="outline" formControlName="category">
                      <mat-option *ngFor="let category of this.categoryList" [value]="category">
                        {{ category }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </td>
              </ng-container>
              <ng-container matColumnDef="level">
                <th mat-header-cell *matHeaderCellDef>Level</th>
                <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element">
                  <mat-form-field appearance="none">
                    <input matInput type="text" placeholder="select level" class="outline" formControlName="level"
                      readonly />
                  </mat-form-field>
                </td>
              </ng-container>
              <ng-container matColumnDef="editAccess">
                <th mat-header-cell *matHeaderCellDef class="tableRole">
                  Edit Access
                </th>
                <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" class="tableRole">
                  <mat-form-field appearance="none" style="width: 100px !important">
                    <mat-select placeholder="select" class="outline" formControlName="editAccess">
                      <mat-option value="true">Yes</mat-option>
                      <mat-option value="false">No</mat-option>
                    </mat-select>
                  </mat-form-field>
                </td>
              </ng-container>
              <ng-container matColumnDef="role">
                <th mat-header-cell *matHeaderCellDef class="tableRole">
                  Role
                </th>
                <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" class="tableRole">
                  <mat-form-field appearance="none">
                    <mat-select placeholder="select role" class="outline" formControlName="role">
                      <mat-option *ngFor="let role of this.rolesList" [value]="role">
                        {{ role }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </td>
              </ng-container>
              <ng-container matColumnDef="amount">
                <th mat-header-cell *matHeaderCellDef class="tableAmount">
                  Amount (INR)
                </th>
                <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" class="tableAmount">
                  <mat-form-field appearance="none" style="width: 110px !important">
                    <input matInput class="input1" style="width: 110px" type="number" formControlName="amount" />
                  </mat-form-field>
                </td>
              </ng-container>
              <ng-container matColumnDef="email">
                <th mat-header-cell *matHeaderCellDef>Email</th>
                <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element">
                  <mat-form-field appearance="none">
                    <textarea matInput type="text" formControlName="email"></textarea>
                  </mat-form-field>
                </td>
              </ng-container>
              <ng-container matColumnDef="test">
                <th mat-header-cell *matHeaderCellDef>test</th>
                <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element">
                  <mat-form-field>
                    <mat-chip-list #chipList formArrayName="emails">
                      <mat-chip [color]="(item.invalid)?'warn':''" selected *ngFor="let item of emailList; let i=index"
                        [selectable]="true" [removable]="true" (removed)="removeEmail(item)" required name="chips">
                        {{item}}
                        <mat-icon matChipRemove *ngIf="true">cancel</mat-icon>
                      </mat-chip>
                      <input placeholder="enter item " [matChipInputFor]="chipList"
                        [matChipInputSeparatorKeyCodes]="separatorKeysCodes " [matChipInputAddOnBlur]="true "
                        (matChipInputTokenEnd)="add($event) " />
                    </mat-chip-list>
                  </mat-form-field>
                </td>
              </ng-container>
              <ng-container matColumnDef="action">
                <th mat-header-cell *matHeaderCellDef>Action</th>
                <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element">
                  <button matTooltip="Delete" class="deleteForeverBtn ml-2" color="warn"
                    *ngIf="this.selectedMechanism == 'Amount Based'" (click)="RemoveVO(PRForm, i, workArea)"
                    [disabled]="i + 1 != (filterVoRowsByWorkArea(PRForm, workArea)).length">
                    <mat-icon>delete</mat-icon>
                  </button>
                  <button matTooltip="Delete" class="deleteForeverBtn ml-2" color="warn"
                    *ngIf="this.selectedMechanism != 'Amount Based'" (click)="RemoveVO(PRForm, i, workArea)" [disabled]="
                    (i === 0 && (filterVoRowsByWorkArea(PRForm, workArea)).length > 1) ||
                    !canDeleteRow(PRForm, i, workArea)
                  ">
                    <mat-icon>delete</mat-icon>
                  </button>
                </td>
              </ng-container>
              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
            </table>
          </ng-container>
        </form>
      </mat-expansion-panel>
    </mat-tab>

    <mat-tab>
      <ng-template mat-tab-label>
        <strong>PO Setting</strong>
      </ng-template>
      <mat-expansion-panel hideToggle #panel="matExpansionPanel" (click)="click(PRForm, workArea, panelOpenState)" (opened)="panelOpenState = true" (closed)="panelOpenState = false" *ngFor="let workArea of workAreas">
        <button mat-button (click)="saveSetting(workArea)" class="button3 mt-2 mb-2 tableTopItemsBtns"
          matTooltip="click to save setting" [disabled]="!selectedMechanism">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-save" viewBox="0 0 16 16">
            <path d="M2 1a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1H9.5a1 1 0 0 0-1 1v7.293l2.646-2.647a.5.5 0 0 1 .708.708l-3.5 3.5a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L7.5 9.293V2a2 2 0 0 1 2-2H14a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V2a2 2 0 0 1 2-2h2.5a.5.5 0 0 1 0 1H2z"/>
          </svg>
        </button>
        <button mat-button class="button3 m-2 tableTopItemsBtns" matTooltip="click to add new level"
          (click)="AddNewRow(POForm, workArea)" [disabled]="!selectedMechanism">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-plus-circle" viewBox="0 0 16 16">
            <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
            <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
          </svg>
        </button>
        <mat-expansion-panel-header >
          <mat-panel-title>
            <span><strong>{{workArea}}</strong>
              <svg *ngIf="this.checkWorkSAreaDatas.includes(workArea)" xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-check-circle-fill ml-2 tickIcon" viewBox="0 0 16 16">
                <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/>
              </svg>
              <svg *ngIf="!this.checkWorkSAreaDatas.includes(workArea)" xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-x-circle-fill ml-2 wrongIcon" viewBox="0 0 16 16">
                <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM5.354 4.646a.5.5 0 1 0-.708.708L7.293 8l-2.647 2.646a.5.5 0 0 0 .708.708L8 8.707l2.646 2.647a.5.5 0 0 0 .708-.708L8.707 8l2.647-2.646a.5.5 0 0 0-.708-.708L8 7.293 5.354 4.646z"/>
              </svg>
            </span>
          </mat-panel-title>
          <!-- <mat-icon *ngIf="!panelOpenState">add</mat-icon>
          <mat-icon *ngIf="panelOpenState">remove</mat-icon> -->
          <mat-icon *ngIf="!this.getWorkArea.includes(workArea)">add</mat-icon>
          <mat-icon *ngIf="this.getWorkArea.includes(workArea)">remove</mat-icon>
        </mat-expansion-panel-header>
        <form [formGroup]="POForm" autocomplete="off" *ngIf="isPOFormReady">
          <ng-container formArrayName="VORows">
            <table #table mat-table [dataSource]="filterVoRowsByWorkArea(POForm, workArea)">
              <ng-container matColumnDef="category">
                <th mat-header-cell *matHeaderCellDef class="tableCategory">
                  Category
                </th>
                <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" class="tableCategory">
                  <mat-form-field appearance="none">
                    <input matInput *ngIf="i == 0" type="text" class="outline" formControlName="category" readonly />
                    <mat-select *ngIf="i != 0" (selectionChange)="categorySelection(POForm, workArea)"
                      placeholder="select category" class="outline" formControlName="category">
                      <mat-option *ngFor="let category of this.categoryList" [value]="category">
                        {{ category }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </td>
              </ng-container>
              <ng-container matColumnDef="level">
                <th mat-header-cell *matHeaderCellDef>Level</th>
                <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element">
                  <mat-form-field appearance="none">
                    <input matInput type="text" placeholder="select level" class="outline" formControlName="level"
                      readonly />
                  </mat-form-field>
                </td>
              </ng-container>
              <ng-container matColumnDef="role">
                <th mat-header-cell *matHeaderCellDef class="tableRole">
                  Role
                </th>
                <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" class="tableRole">
                  <mat-form-field appearance="none">
                    <mat-select placeholder="select role" class="outline" formControlName="role">
                      <mat-option *ngFor="let role of this.rolesList" [value]="role">
                        {{ role }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </td>
              </ng-container>
              <ng-container matColumnDef="amount">
                <th mat-header-cell *matHeaderCellDef class="tableAmount">
                  Amount(INR)
                </th>
                <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" class="tableAmount">
                  <mat-form-field appearance="none" style="width: 110px !important">
                    <input matInput class="input1" style="width: 110px" type="number" formControlName="amount" />
                  </mat-form-field>
                </td>
              </ng-container>
              <ng-container matColumnDef="email">
                <th mat-header-cell *matHeaderCellDef>Email</th>
                <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element">
                  <mat-form-field appearance="none">
                    <textarea matInput type="text" formControlName="email"></textarea>
                  </mat-form-field>
                </td>
              </ng-container>
              <ng-container matColumnDef="action">
                <th mat-header-cell *matHeaderCellDef>Action</th>
                <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element">
                  <button matTooltip="Delete" class="deleteForeverBtn ml-2" color="warn"
                    *ngIf="this.selectedMechanism == 'Amount Based'" (click)="RemoveVO(POForm, i, workArea)"
                    [disabled]="i + 1 != (filterVoRowsByWorkArea(POForm, workArea)).length">
                    <mat-icon>delete</mat-icon>
                  </button>
                  <button matTooltip="Delete" class="deleteForeverBtn ml-2" color="warn"
                    *ngIf="this.selectedMechanism != 'Amount Based'" (click)="RemoveVO(POForm, i, workArea)" [disabled]="
                        (i === 0 && (filterVoRowsByWorkArea(POForm, workArea)).length > 1) || 
                        !canDeleteRow(POForm, i, workArea)
                      ">
                    <mat-icon>delete</mat-icon>
                  </button>
                </td>
              </ng-container>
              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
            </table>
          </ng-container>
        </form>
      </mat-expansion-panel>
    </mat-tab>

    <mat-tab>
      <ng-template mat-tab-label>
        <strong>GRN Setting</strong>
      </ng-template>
      <mat-expansion-panel hideToggle #panel="matExpansionPanel" (click)="click(PRForm, workArea, panelOpenState)" (opened)="panelOpenState = true" (closed)="panelOpenState = false" *ngFor="let workArea of workAreas">
        <button mat-button (click)="saveSetting(workArea)" class="button3 mt-2 mb-2 tableTopItemsBtns"
          matTooltip="click to save setting" [disabled]="!selectedMechanism">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-save" viewBox="0 0 16 16">
            <path d="M2 1a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1H9.5a1 1 0 0 0-1 1v7.293l2.646-2.647a.5.5 0 0 1 .708.708l-3.5 3.5a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L7.5 9.293V2a2 2 0 0 1 2-2H14a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V2a2 2 0 0 1 2-2h2.5a.5.5 0 0 1 0 1H2z"/>
          </svg>
        </button>
        <button mat-button class="button3 m-2 tableTopItemsBtns" matTooltip="click to add new level"
          (click)="AddNewRow(GRNForm, workArea)" [disabled]="!selectedMechanism">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-plus-circle" viewBox="0 0 16 16">
            <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
            <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
          </svg>
        </button>
        <mat-expansion-panel-header >
          <mat-panel-title>
            <span><strong>{{workArea}}</strong>
              <svg *ngIf="this.checkWorkSAreaDatas.includes(workArea)" xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-check-circle-fill ml-2 tickIcon" viewBox="0 0 16 16">
                <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/>
              </svg>
              <svg *ngIf="!this.checkWorkSAreaDatas.includes(workArea)" xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-x-circle-fill ml-2 wrongIcon" viewBox="0 0 16 16">
                <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM5.354 4.646a.5.5 0 1 0-.708.708L7.293 8l-2.647 2.646a.5.5 0 0 0 .708.708L8 8.707l2.646 2.647a.5.5 0 0 0 .708-.708L8.707 8l2.647-2.646a.5.5 0 0 0-.708-.708L8 7.293 5.354 4.646z"/>
              </svg>
            </span>
          </mat-panel-title>
          <!-- <mat-icon *ngIf="!panelOpenState">add</mat-icon>
          <mat-icon *ngIf="panelOpenState">remove</mat-icon> -->
          <mat-icon *ngIf="!this.getWorkArea.includes(workArea)">add</mat-icon>
          <mat-icon *ngIf="this.getWorkArea.includes(workArea)">remove</mat-icon>
        </mat-expansion-panel-header>
        <form [formGroup]="GRNForm" autocomplete="off" *ngIf="isGRNFormReady">
          <ng-container formArrayName="VORows">
            <table #table mat-table [dataSource]="filterVoRowsByWorkArea(GRNForm, workArea)">
              <ng-container matColumnDef="category">
                <th mat-header-cell *matHeaderCellDef class="tableCategory">
                  Category
                </th>
                <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" class="tableCategory">
                  <mat-form-field appearance="none">
                    <input matInput *ngIf="i == 0" type="text" class="outline" formControlName="category" readonly />
                    <mat-select *ngIf="i != 0" (selectionChange)="categorySelection(GRNForm, workArea)"
                      placeholder="select category" class="outline" formControlName="category">
                      <mat-option *ngFor="let category of this.categoryList" [value]="category">
                        {{ category }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </td>
              </ng-container>
              <ng-container matColumnDef="level">
                <th mat-header-cell *matHeaderCellDef>Level</th>
                <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element">
                  <mat-form-field appearance="none">
                    <input matInput type="text" placeholder="select level" class="outline" formControlName="level"
                      readonly />
                  </mat-form-field>
                </td>
              </ng-container>
              <ng-container matColumnDef="role">
                <th mat-header-cell *matHeaderCellDef class="tableRole">
                  Role
                </th>
                <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" class="tableRole">
                  <mat-form-field appearance="none">
                    <mat-select placeholder="select role" class="outline" formControlName="role">
                      <mat-option *ngFor="let role of this.rolesList" [value]="role">
                        {{ role }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </td>
              </ng-container>
              <ng-container matColumnDef="amount">
                <th mat-header-cell *matHeaderCellDef class="tableAmount">
                  Amount(INR)
                </th>
                <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" class="tableAmount">
                  <mat-form-field appearance="none" style="width: 110px !important">
                    <input matInput class="input1" style="width: 110px" type="number" formControlName="amount" />
                  </mat-form-field>
                </td>
              </ng-container>
              <ng-container matColumnDef="email">
                <th mat-header-cell *matHeaderCellDef>Email</th>
                <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element">
                  <mat-form-field appearance="none">
                    <textarea matInput type="text" formControlName="email"></textarea>
                  </mat-form-field>
                </td>
              </ng-container>
              <ng-container matColumnDef="action">
                <th mat-header-cell *matHeaderCellDef>Action</th>
                <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element">
                  <button matTooltip="Delete" class="deleteForeverBtn ml-2" color="warn"
                    *ngIf="this.selectedMechanism == 'Amount Based'" (click)="RemoveVO(GRNForm, i, workArea)"
                    [disabled]="i + 1 != (filterVoRowsByWorkArea(GRNForm, workArea)).length">
                    <mat-icon>delete</mat-icon>
                  </button>
                  <button matTooltip="Delete" class="deleteForeverBtn ml-2" color="warn"
                    *ngIf="this.selectedMechanism != 'Amount Based'" (click)="RemoveVO(GRNForm, i, workArea)"
                    [disabled]="
                      (i === 0 && (filterVoRowsByWorkArea(GRNForm, workArea)).length > 1) || 
                      !canDeleteRow(GRNForm, i, workArea)
                    ">
                    <mat-icon>delete</mat-icon>
                  </button>
                </td>
              </ng-container>
              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
            </table>
          </ng-container>
        </form>
      </mat-expansion-panel>
    </mat-tab>

    <mat-tab>
      <ng-template mat-tab-label>
        <strong>Indent Setting</strong>
      </ng-template>
      <mat-expansion-panel hideToggle #panel="matExpansionPanel" (click)="click(PRForm, workArea, panelOpenState)" (opened)="panelOpenState = true" (closed)="panelOpenState = false" *ngFor="let workArea of workAreas">
        <button mat-button (click)="saveSetting(workArea)" class="button3 mt-2 mb-2 tableTopItemsBtns"
          matTooltip="click to save setting" [disabled]="!selectedMechanism">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-save" viewBox="0 0 16 16">
            <path d="M2 1a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1H9.5a1 1 0 0 0-1 1v7.293l2.646-2.647a.5.5 0 0 1 .708.708l-3.5 3.5a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L7.5 9.293V2a2 2 0 0 1 2-2H14a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V2a2 2 0 0 1 2-2h2.5a.5.5 0 0 1 0 1H2z"/>
          </svg>
        </button>
        <button mat-button class="button3 m-2 tableTopItemsBtns" matTooltip="click to add new level"
          (click)="AddNewRow(indentForm, workArea)" [disabled]="!selectedMechanism">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-plus-circle" viewBox="0 0 16 16">
            <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
            <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
          </svg>
        </button>
        <mat-expansion-panel-header >
          <mat-panel-title>
            <span><strong>{{workArea}}</strong>
              <svg *ngIf="this.checkWorkSAreaDatas.includes(workArea)" xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-check-circle-fill ml-2 tickIcon" viewBox="0 0 16 16">
                <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/>
              </svg>
              <svg *ngIf="!this.checkWorkSAreaDatas.includes(workArea)" xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-x-circle-fill ml-2 wrongIcon" viewBox="0 0 16 16">
                <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM5.354 4.646a.5.5 0 1 0-.708.708L7.293 8l-2.647 2.646a.5.5 0 0 0 .708.708L8 8.707l2.646 2.647a.5.5 0 0 0 .708-.708L8.707 8l2.647-2.646a.5.5 0 0 0-.708-.708L8 7.293 5.354 4.646z"/>
              </svg>
            </span>
          </mat-panel-title>
          <!-- <mat-icon *ngIf="!panelOpenState">add</mat-icon>
          <mat-icon *ngIf="panelOpenState">remove</mat-icon> -->
          <mat-icon *ngIf="!this.getWorkArea.includes(workArea)">add</mat-icon>
          <mat-icon *ngIf="this.getWorkArea.includes(workArea)">remove</mat-icon>
        </mat-expansion-panel-header>
        <form [formGroup]="indentForm" autocomplete="off" *ngIf="isIndentFormReady">
          <ng-container formArrayName="VORows">
            <table #table mat-table [dataSource]="filterVoRowsByWorkArea(indentForm, workArea)">
              <ng-container matColumnDef="category">
                <th mat-header-cell *matHeaderCellDef class="tableCategory">
                  Category
                </th>
                <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" class="tableCategory">
                  <mat-form-field appearance="none">
                    <input matInput *ngIf="i == 0" type="text" class="outline" formControlName="category" readonly />
                    <mat-select *ngIf="i != 0" (selectionChange)="categorySelection(indentForm, workArea)"
                      placeholder="select category" class="outline" formControlName="category">
                      <mat-option *ngFor="let category of this.categoryList" [value]="category">
                        {{ category }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </td>
              </ng-container>
              <ng-container matColumnDef="level">
                <th mat-header-cell *matHeaderCellDef>Level</th>
                <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element">
                  <mat-form-field appearance="none">
                    <input matInput type="text" placeholder="select level" class="outline" formControlName="level"
                      readonly />
                  </mat-form-field>
                </td>
              </ng-container>
              <ng-container matColumnDef="role">
                <th mat-header-cell *matHeaderCellDef class="tableRole">
                  Role
                </th>
                <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" class="tableRole">
                  <mat-form-field appearance="none">
                    <mat-select placeholder="select role" class="outline" formControlName="role">
                      <mat-option *ngFor="let role of this.rolesList" [value]="role">
                        {{ role }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </td>
              </ng-container>
              <ng-container matColumnDef="amount">
                <th mat-header-cell *matHeaderCellDef class="tableAmount">
                  Amount(INR)
                </th>
                <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" class="tableAmount">
                  <mat-form-field appearance="none" style="width: 110px !important">
                    <input matInput class="input1" style="width: 110px" type="number" formControlName="amount" />
                  </mat-form-field>
                </td>
              </ng-container>
              <ng-container matColumnDef="email">
                <th mat-header-cell *matHeaderCellDef>Email</th>
                <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element">
                  <mat-form-field appearance="none">
                    <textarea matInput type="text" formControlName="email"></textarea>
                  </mat-form-field>
                </td>
              </ng-container>
              <ng-container matColumnDef="action">
                <th mat-header-cell *matHeaderCellDef>Action</th>
                <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element">
                  <button matTooltip="Delete" class="deleteForeverBtn ml-2" color="warn"
                    *ngIf="this.selectedMechanism == 'Amount Based'" (click)="RemoveVO(indentForm, i, workArea)"
                    [disabled]="i + 1 != (filterVoRowsByWorkArea(indentForm, workArea)).length">
                    <mat-icon>delete</mat-icon>
                  </button>
                  <button matTooltip="Delete" class="deleteForeverBtn ml-2" color="warn"
                    *ngIf="this.selectedMechanism != 'Amount Based'" (click)="RemoveVO(indentForm, i, workArea)"
                    [disabled]="(i === 0 && (filterVoRowsByWorkArea(indentForm, workArea)).length > 1) || 
                        !canDeleteRow(indentForm, i, workArea)">
                    <mat-icon>delete</mat-icon>
                  </button>
                </td>
              </ng-container>
              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
            </table>
          </ng-container>
        </form>
      </mat-expansion-panel>
    </mat-tab>

    <mat-tab>
      <ng-template mat-tab-label>
        <strong>PI Setting</strong>
      </ng-template>
      <mat-expansion-panel hideToggle #panel="matExpansionPanel" (click)="click(PRForm, workArea, panelOpenState)" (opened)="panelOpenState = true" (closed)="panelOpenState = false" *ngFor="let workArea of workAreas">
        <button mat-button (click)="saveSetting(workArea)" class="button3 mt-2 mb-2 tableTopItemsBtns"
          matTooltip="click to save setting" [disabled]="!selectedMechanism">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-save" viewBox="0 0 16 16">
            <path d="M2 1a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1H9.5a1 1 0 0 0-1 1v7.293l2.646-2.647a.5.5 0 0 1 .708.708l-3.5 3.5a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L7.5 9.293V2a2 2 0 0 1 2-2H14a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V2a2 2 0 0 1 2-2h2.5a.5.5 0 0 1 0 1H2z"/>
          </svg>
        </button>
        <button mat-button class="button3 m-2 tableTopItemsBtns" matTooltip="click to add new level"
          (click)="AddNewRow(piForm, workArea)" [disabled]="!selectedMechanism">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-plus-circle" viewBox="0 0 16 16">
            <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
            <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
          </svg>
        </button>
        <mat-expansion-panel-header >
          <mat-panel-title>
            <span><strong>{{workArea}}</strong>
              <svg *ngIf="this.checkWorkSAreaDatas.includes(workArea)" xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-check-circle-fill ml-2 tickIcon" viewBox="0 0 16 16">
                <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/>
              </svg>
              <svg *ngIf="!this.checkWorkSAreaDatas.includes(workArea)" xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" class="bi bi-x-circle-fill ml-2 wrongIcon" viewBox="0 0 16 16">
                <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM5.354 4.646a.5.5 0 1 0-.708.708L7.293 8l-2.647 2.646a.5.5 0 0 0 .708.708L8 8.707l2.646 2.647a.5.5 0 0 0 .708-.708L8.707 8l2.647-2.646a.5.5 0 0 0-.708-.708L8 7.293 5.354 4.646z"/>
              </svg>
            </span>
          </mat-panel-title>
          <mat-icon *ngIf="!this.getWorkArea.includes(workArea)">add</mat-icon>
          <mat-icon *ngIf="this.getWorkArea.includes(workArea)">remove</mat-icon>
        </mat-expansion-panel-header>
        <form [formGroup]="piForm" autocomplete="off" *ngIf="isPiFormReady">
          <ng-container formArrayName="VORows">
            <table #table mat-table [dataSource]="filterVoRowsByWorkArea(piForm, workArea)">
              <ng-container matColumnDef="category">
                <th mat-header-cell *matHeaderCellDef class="tableCategory">
                  Category
                </th>
                <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" class="tableCategory">
                  <mat-form-field appearance="none">
                    <input matInput *ngIf="i == 0" type="text" class="outline" formControlName="category" readonly />
                    <mat-select *ngIf="i != 0" (selectionChange)="categorySelection(piForm, workArea)"
                      placeholder="select category" class="outline" formControlName="category">
                      <mat-option *ngFor="let category of this.categoryList" [value]="category">
                        {{ category }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </td>
              </ng-container>
              <ng-container matColumnDef="level">
                <th mat-header-cell *matHeaderCellDef>Level</th>
                <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element">
                  <mat-form-field appearance="none">
                    <input matInput type="text" placeholder="select level" class="outline" formControlName="level"
                      readonly />
                  </mat-form-field>
                </td>
              </ng-container>
              <ng-container matColumnDef="role">
                <th mat-header-cell *matHeaderCellDef class="tableRole">
                  Role
                </th>
                <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" class="tableRole">
                  <mat-form-field appearance="none">
                    <mat-select placeholder="select role" class="outline" formControlName="role">
                      <mat-option *ngFor="let role of this.rolesList" [value]="role">
                        {{ role }}
                      </mat-option>
                    </mat-select>
                  </mat-form-field>
                </td>
              </ng-container>
              <ng-container matColumnDef="amount">
                <th mat-header-cell *matHeaderCellDef class="tableAmount">
                  Amount(INR)
                </th>
                <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element" class="tableAmount">
                  <mat-form-field appearance="none" style="width: 110px !important">
                    <input matInput class="input1" style="width: 110px" type="number" formControlName="amount" />
                  </mat-form-field>
                </td>
              </ng-container>
              <ng-container matColumnDef="email">
                <th mat-header-cell *matHeaderCellDef>Email</th>
                <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element">
                  <mat-form-field appearance="none">
                    <textarea matInput type="text" formControlName="email"></textarea>
                  </mat-form-field>
                </td>
              </ng-container>
              <ng-container matColumnDef="action">
                <th mat-header-cell *matHeaderCellDef>Action</th>
                <td mat-cell *matCellDef="let element; let i = index" [formGroup]="element">
                  <button matTooltip="Delete" class="deleteForeverBtn ml-2" color="warn"
                    *ngIf="this.selectedMechanism == 'Amount Based'" (click)="RemoveVO(piForm, i, workArea)"
                    [disabled]="i + 1 != (filterVoRowsByWorkArea(piForm, workArea)).length">
                    <mat-icon>delete</mat-icon>
                  </button>
                  <button matTooltip="Delete" class="deleteForeverBtn ml-2" color="warn"
                    *ngIf="this.selectedMechanism != 'Amount Based'" (click)="RemoveVO(piForm, i, workArea)"
                    [disabled]="(i === 0 && (filterVoRowsByWorkArea(piForm, workArea)).length > 1) || 
                        !canDeleteRow(piForm, i, workArea)">
                    <mat-icon>delete</mat-icon>
                  </button>
                </td>
              </ng-container>
              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
            </table>
          </ng-container>
        </form>
      </mat-expansion-panel>
    </mat-tab>


  </mat-tab-group>
</mat-card>