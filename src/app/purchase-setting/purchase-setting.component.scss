h5{
    font-weight: bold;
    margin: 10px 0px 15px;
}
label{
    font-weight:bold; 
}

  .deleteBtn{
    margin-top: 2.5rem;
  }

  .error{
    color: red;
    font-size: 15px;
  }

  ::ng-deep .mat-tab-label {
    width: 50% !important;
    height: 48px;
    padding: 0 24px;
    cursor: pointer;
    box-sizing: border-box;
    opacity: .6;
    min-width: 160px;
    text-align: center;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
    position: relative;
  }

  ::ng-deep .mat-tab-labels {
    display: flex;
    justify-content: space-evenly;
}

  .dataInputs{
  margin-top: 10px;
  margin-bottom: 25px;
  }


.editBtn{
  width: 30px;
  border-radius: 4px;
  border: none;
  padding-left: 0px;
  padding-top: 7px;
}

.deleteForeverBtn{
  width: 30px;
  border-radius: 4px;
  border: none;
  padding-top: 7px;
}

.tableTopItems{
  margin-top: 16px !important;
}

.tableTopItemsBtns{
  float: right !important;
}

#toggleStng{
  margin-top: 15px !important;
}


.eraserBtn {
  width: 30px;
  border-radius: 4px;
  border: none;
  background: #464646;
}

.mat-expansion-panel{
  margin-left: 2%;
  margin-right: 2%;
  margin-top: 10px;
}

// ::ng-deep td.mat-cell, td.mat-footer-cell, th.mat-header-cell {
//   width: 8% !important;
// }

.slsectCatInput{
  height: 30px;
  background: white;
  color: black;
  margin-bottom: 5px;
}



.CloseBtn{
  float: right;
  margin-bottom: -1px;
}

.msgDiv{
  // text-align: center;
  max-width: 450px !important;
}

.message{
  font-size: 12px;
}

.reqBtn{
  text-align: center !important;
}

// ::ng-deep mat-dialog-container#mat-dialog-0 {
//   width: 400px !important;
//   // padding-left: 90px;
//   // padding-right: 90px;
// }

// ::ng-deep mat-dialog-content.mat-typography.mat-dialog-content.ng-star-inserted {
//   max-width: 450px !important;
//   width: 450px !important;
// }


.catInput{
  text-align: center !important;
  display: inline-block;
}

.selected-email {
  background-color: #333333;
  color: #ffffff;
  padding: 4px;
  margin-bottom: 4px;
  display: inline-block;
}

.example-container{
  overflow-x: auto;
  max-width: 100%;
}


.mat-form-field + .mat-form-field {
  margin-left: 2px;
}

.mat-expansion-panel .mat-expansion-panel-header-title {
  color: white;
  align-items: center;
}

.mat-expansion-panel .mat-expansion-panel-header-description {
  font-weight: lighter;
  color: white;
}

.error-message {
  color: red;
  font-size: 10px;
  margin: 2px;
}

.tickIcon{
  color: #006a4e;
}

.wrongIcon{
  color: #940000;
}