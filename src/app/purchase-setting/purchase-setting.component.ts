import { ChangeDetectorRef, Component, ElementRef, Input, OnInit, SimpleChanges, ViewChild } from "@angular/core";
import { MenuItemService, PurchasesService, ShareDataService } from "../_services";
import { AuthService } from "../_services";
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from "@angular/forms";
import { NotificationService } from "../_services/notification.service";
import { MatDialog, MatPaginator, MatTableDataSource } from "@angular/material";
import { SimpleDialogComponent } from "../_dialogs/simple-dialog/simple-dialog.component";
import { COMMA, ENTER } from "@angular/cdk/keycodes";
import { takeUntil } from "rxjs/operators";
import { Subject } from "rxjs";
import { UtilsService } from "../_utils/utils.service";

export interface PeriodicElement {
  category: string;
  level: string;
  editAccess: string;
  role: string;
  amount: number;
  email: Array<string>;
}

@Component({
  selector: "app-purchase-setting",
  templateUrl: "./purchase-setting.component.html",
  styleUrls: ["./purchase-setting.component.scss", "./../../common-dark.scss"],
})
export class PurchaseSettingComponent implements OnInit {
  @Input() title: string;
  @Input() subtitle: string;
  @Input() theme: "primary" | "critical";
  color: string = "#000011";
  panelOpenState: boolean;
  
  @ViewChild(MatPaginator) paginator: MatPaginator;
  paginatorList: HTMLCollectionOf<Element>;
  dataSource = new MatTableDataSource<any>();
  filteredDataSource = new MatTableDataSource<any>([]);
  displayedColumns: string[];
  unsubscribe$ = new Subject<void>();
  user: any = {};
  restaurantId: string = "";
  rolesList: Array<string> = [];
  multiBranchUser: boolean = false;
  PRForm: FormGroup;
  POForm: FormGroup;
  GRNForm: FormGroup;
  indentForm: FormGroup;
  piForm: FormGroup;
  selectedType = "purchaseRequest";
  selectedMechanism: string;
  isPOFormReady = false;
  isPRFormReady = false;
  isGRNFormReady = false;
  isIndentFormReady = false;
  isPiFormReady = false;
  cat: any[];
  levels = [];
  editAccess = [];
  categoryList: any[];
  types: any[];
  emailList = [];
  settingFound: boolean;
  separatorKeysCodes = [ENTER, COMMA];
  getBranchData: any[];
  branches: any[];
  approvalSettingForm: FormGroup;
  workAreas = []
  getWorkArea = [];
  checkWorkSAreaDatas = [];
    constructor(
    private host: ElementRef<any>,
    private readonly changeDetectorRef: ChangeDetectorRef,
    private fb: FormBuilder,
    private menuItems: MenuItemService,
    private notifyService: NotificationService,
    private auth: AuthService,
    private purchasService: PurchasesService,
    private dialog: MatDialog,
    private sharedData: ShareDataService,
    private _formBuilder: FormBuilder,
    private utils: UtilsService,

  ) {
    this.user = this.auth.getCurrentUser();
    this.restaurantId = null;
    this.PRForm = this._formBuilder.group({ VORows: this._formBuilder.array([]) });
    this.POForm = this._formBuilder.group({ VORows: this._formBuilder.array([]) });
    this.GRNForm = this._formBuilder.group({ VORows: this._formBuilder.array([]) });
    this.indentForm = this._formBuilder.group({ VORows: this._formBuilder.array([]) });
    this.piForm = this._formBuilder.group({ VORows: this._formBuilder.array([]) });
    this.types = ["Role Based", "Amount Based"];
    this.levels = ["L1", "L2", "L3", "L4", "L5", "L6", "L7", "L8", "L9", "L10"];
    this.editAccess = [true, false];
    this.approvalSettingForm = this.fb.group({ branchSelection: [null, Validators.required] });
    this.sharedData.sharedBranchData.pipe(takeUntil(this.unsubscribe$)).subscribe((val) => {
      this.getBranchData = val;
      if (this.getBranchData.length === 0) {
        this.branches = this.user.restaurantAccess;
      } else if (this.getBranchData.length === 1) {
        const toSelect = this.getBranchData.find(
          (data) => data.branchName === this.getBranchData[0].branchName
        );
        this.restaurantId = toSelect;
        this.approvalSettingForm.get("branchSelection").setValue(toSelect);
        this.branches = this.getBranchData;
        this.selectedMechanism = "";
        this.restaurantId = this.approvalSettingForm.value.branchSelection.restaurantIdOld;
        this.filterByBranch(this.approvalSettingForm.value.branchSelection);
      } else {
        this.branches = this.getBranchData;
      }
    });
  }
  

  selectType(val: string) {
    this.selectedMechanism = val;
    this.retrieveSetting();
  }

  ngOnInit() {
    this.multiBranchUser = this.user.multiBranchUser;
    if (!this.user.multiBranchUser) {
      this.restaurantId = this.user.restaurantId;
    }
    this.auth.getRolesList({ tenantId: this.user.tenantId }).subscribe((data) => {
      data.rolesList.forEach((element: { role: string }) => {
        this.rolesList.push(element.role);
      });
    });
    if (this.approvalSettingForm.value.branchSelection.restaurantIdOld) {
      this.selectedMechanism = "Role Based";
    }
    this.getCategories();
  }
  

  ngAfterViewChecked(): void {
    this.changeDetectorRef.detectChanges();
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
  }

  changeColumnsDynamically() {
    this.displayedColumns = this.selectedMechanism === 'Amount Based'
      ? (this.selectedType === 'grnApproval' || ['purchaseOrder', 'indentApproval','piApproval'].includes(this.selectedType))
        ? ['level', 'role', 'email', 'amount', 'action']
        : ['level', 'role', 'editAccess', 'email', 'amount', 'action']
      : (this.selectedType === 'grnApproval' || ['purchaseOrder', 'indentApproval','piApproval'].includes(this.selectedType))
        ? ['category', 'level', 'role', 'email', 'action']
        : ['category', 'level', 'editAccess', 'role', 'email', 'action'];
  }

  tabClick(tab: any) {    
    this.selectedMechanism = "Role Based";
    this.isPOFormReady = false;
    this.isPRFormReady = false;
    this.isGRNFormReady = false;
    this.isIndentFormReady = false;
    this.isPiFormReady = false;
    switch (tab.index) {
      case 2:
        this.selectedType = "grnApproval";
        break;
      case 1:
        this.selectedType = "purchaseOrder";
        break;
      case 3:
        this.selectedType = "indentApproval";
        break;
      case 4:
        this.selectedType = "piApproval";
        break;
      default:
        this.selectedType = "purchaseRequest";
        break;
    }
    this.retrieveSetting();
  }
  

  filterByBranch(restId: { restaurantIdOld: string; }) {
      this.restaurantId = restId.restaurantIdOld;
    this.user.restaurantAccess.forEach(element => {
      if (element.restaurantIdOld == this.restaurantId) {
        this.workAreas = element.workAreas;
        // this.workAreas.unshift('store');
        !this.workAreas.includes('store') ? this.workAreas.unshift('store') : undefined ;
      }
    });
    this.selectedMechanism = "Role Based";
    this.retrieveSetting();
  }
  
  setFormAndDataSource(form: FormGroup, formReadyFlag: string) {
    this.dataSource = new MatTableDataSource((form.get('VORows') as FormArray).controls);
    this[formReadyFlag] = true;
  }
  
  ngOnDestroy() {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  ngOnChanges(changes: SimpleChanges) {
    if ("theme" in changes) {
      this.color = this.theme === "primary" ? "#000011" : "#110000";
      this.host.nativeElement.style.setProperty(`--color`, this.color);
    }
  }
  
  getCategories() {
    this.user.restaurantAccess.forEach(element => {
      if (element.restaurantIdOld == this.restaurantId) {
        this.workAreas = element.workAreas
        !this.workAreas.includes('store') ? this.workAreas.unshift('store') : undefined ;
        // this.workAreas.unshift('store');
      }
    });
    this.menuItems.getCategories(this.user).subscribe(
      (res) => {
        if (res.result === "success") {
          this.categoryList = res.categories;
        }
      },
      (err) => {
        console.error(err);
      }
    );
  }


  //####################### FORM CRUD #######################

  filterVoRowsByWorkArea(form: FormGroup, selectedWorkArea: string) {
    const control = form.get("VORows") as FormArray;
    return control.controls.filter((control) => {
      const workArea = control.get("workArea").value;
      this.checkWorkSAreaDatas.push(workArea)
      return workArea === selectedWorkArea;
    });
  }
  
  AddNewRow(form: FormGroup, selectedWorkArea: string) {
    const control = form.get("VORows") as FormArray;
    const level = control.controls.filter((control) => {
      const workArea = control.get("workArea").value;
      return workArea === selectedWorkArea;
    }).length + 1;
    control.push(this.initiateForm(level, selectedWorkArea));
    this.dataSource = new MatTableDataSource(control.controls);
  }

  canDeleteRow(form: FormGroup, rowIndex: number, selectedWorkArea: string): boolean {
    const control = form.get("VORows") as FormArray;
    const filteredRows = control.controls.filter((control) => {
      const workArea = control.get("workArea").value;
      return workArea === selectedWorkArea;
    });
    const currentRow = filteredRows[rowIndex] as FormGroup;
    const currentCategory = currentRow.get("category").value;
    const currentLevel = currentRow.get("level").value;
    for (let i = 0; i < filteredRows.length; i++) {
      if (i !== rowIndex) {
        const row = filteredRows[i] as FormGroup;
        const category = row.get("category").value;
        const level = row.get("level").value;
        if (category === currentCategory && level > currentLevel) {
          return false;
        }
      }
    }
    return true;
  }

  RemoveVO(form: FormGroup, i: number, selectedWorkArea: string) {
    const control = form.get("VORows") as FormArray;
    const filteredControlsByWorkArea = control.controls.filter((c) => {
      const workArea = c.get("workArea").value;
      return workArea === selectedWorkArea;
    });
    this.dialog.open(SimpleDialogComponent, {
      data: {
        title: "Delete Setting",
        msg: "Are you sure you want to delete?",
        ok: () => {
          if (i >= 0 && i < filteredControlsByWorkArea.length) {
            const indexToRemove = control.controls.indexOf(filteredControlsByWorkArea[i]);
            if (indexToRemove !== -1) {
              control.removeAt(indexToRemove);
              this.dataSource = new MatTableDataSource(control.controls);
            }
          }
        }
      }
    })
  }
  
  categorySelection(form: { get: (arg0: string) => FormArray; }, selectedWorkArea: any) {
    const control = form.get("VORows") as FormArray;
    const filteredControls = control.controls.filter((control) => {
      const workArea = control.get("workArea").value;
      return workArea === selectedWorkArea;
    });
    const sortedControls = filteredControls.slice(1).sort((a, b) => {
      const categoryA = a.get("category").value.toLowerCase();
      const categoryB = b.get("category").value.toLowerCase();
      return categoryA.localeCompare(categoryB);
    });
    const categoryLevelsMap = sortedControls.reduce((map, control) => {
      const category = control.get("category").value;
      const currentLevel = map.get(category) || 0;
      control.get("level").setValue(`L${currentLevel + 1}`);
      map.set(category, currentLevel + 1);
      return map;
    }, new Map<string, number>());
    this.dataSource = new MatTableDataSource([filteredControls[0], ...sortedControls]);
  }
  
  initiateForm(level: number, workArea:string): FormGroup {
    if (this.selectedMechanism == "Amount Based") {
      if (["purchaseOrder", "indentApproval", "grnApproval","piApproval"].includes(this.selectedType)) {
        return this.fb.group({
          workArea: new FormControl(workArea, Validators.required),
          level: new FormControl(`L${level}`, Validators.required),
          role: new FormControl("", Validators.required),
          email: new FormControl("", Validators.required),
          amount: new FormControl("", Validators.required),
          action: new FormControl("newRecord"),
          isNewRow: new FormControl(true),
        });
      } else {
        return this.fb.group({
          workArea: new FormControl(workArea, Validators.required),
          level: new FormControl(`L${level}`, Validators.required),
          editAccess: new FormControl("", Validators.required),
          role: new FormControl("", Validators.required),
          email: new FormControl("", Validators.required),
          amount: new FormControl("", Validators.required),
          action: new FormControl("newRecord"),
          isNewRow: new FormControl(true),
        });
      }
    }else if (this.selectedMechanism == "Role Based") {
      let defaultCategory = level == 1 ? "DEFAULT" : "";
      let defaultLevel = level == 1 ? "L1" : "";
      if (["purchaseOrder", "indentApproval", "grnApproval","piApproval"].includes(this.selectedType)) {
        return this.fb.group({
          workArea: new FormControl(workArea, Validators.required),
          category: new FormControl(defaultCategory, Validators.required),
          level: new FormControl(defaultLevel, Validators.required),
          role: new FormControl("", Validators.required),
          email: new FormControl("", Validators.required),
          action: new FormControl("newRecord"),
          isNewRow: new FormControl(true),
        });
      }else{
        return this.fb.group({
          workArea: new FormControl(workArea, Validators.required),
          category: new FormControl(defaultCategory, Validators.required),
          level: new FormControl(defaultLevel, Validators.required),
          editAccess: new FormControl("", Validators.required),
          role: new FormControl("", Validators.required),
          email: new FormControl("", Validators.required),
          action: new FormControl("newRecord"),
          isNewRow: new FormControl(true),
        });
      }
    }else {
      return this.fb.group({
        workArea: new FormControl(workArea, Validators.required),
        level: new FormControl(`L${level}`, Validators.required),
        role: new FormControl("", Validators.required),
        email: new FormControl("", Validators.required),
        action: new FormControl("newRecord"),
        isNewRow: new FormControl(true),
      });
    }
  }

  formatSetting(datas: any, workArea: string) {
    const levels = datas.reduce((result: { [x: string]: { [x: string]: { role: any; editAccess: any; email: any; amount: any; }; }; }, data: { [x: string]: any; }) => {
      const level = data["level"];
      const category = data["category"] === undefined
        ? this.selectedMechanism === "Role Based" ? "DEFAULT" : "all"
        : data["category"];
      if (data["workArea"] === workArea) {
        result[category] = result[category] || {};
        result[category][level] = {
          role: data['role'],
          editAccess: data['editAccess'],
          email: Array.isArray(data['email']) ? data['email'] : data['email'].split(','),
          amount: this.selectedMechanism !== 'Amount Based' ? 0 : data['amount'],
        };
      }
      return result;
    }, {});
    this.createSetting(workArea, { "levels": levels, "location": workArea });
  }
  
  saveSetting(workArea: string) {
    let form: FormGroup;
    switch (this.selectedType) {
      case "grnApproval":
        form = this.GRNForm;
        break;
      case "purchaseOrder":
        form = this.POForm;
        break;
      case "indentApproval":
        form = this.indentForm;
        break;
      case "piApproval":

        form = this.piForm;
        break;
      default:
        form = this.PRForm;
        break;
    }
    if (form.valid) {
      if (form.value.VORows.length === 0) {
        form.value.VORows = [];
      }
      this.formatSetting(form.value.VORows, workArea);
    }else {
      this.utils.snackBarShowError("All columns are mandatory and must be filled");
    }
  }
  
  createSetting(workArea:string, levels:object) {
    let obj = {};
    obj["tenantId"] = this.user.tenantId;
    obj["restaurantId"] = this.restaurantId;
    obj['workArea'] = workArea;
    obj['levels'] = levels;
    obj["type"] = this.selectedType;
    obj["isAmountBased"] = this.selectedMechanism;
    obj["role"] = this.user.role;
    this.purchasService.createSetting(obj).subscribe((data) => {
      if (data.result == false) {
        this.dialog.open(SimpleDialogComponent, {
          data: {
            from: "approvalSetting",
            title: "Alert!",
            msg: `Hey!, before performing any action, you have to complete the all the open request`,
            approve: function () {
              this.purchasService.approveSetting(obj).subscribe((data: { result: string; message: any; }) => {
                if (data.result == "success") {
                  this.utils.snackBarShowSuccess(`${data.message}`);
                } else {
                  this.utils.snackBarShowError("Something went wrong, please contact support!");
                }
              });
            }.bind(this),
            reject: function () {
              this.purchasService.rejectSetting(obj).subscribe((data: { result: string; message: any; }) => {
                if (data.result == "success") {
                  this.utils.snackBarShowSuccess(`${data.message}`);
                } else {
                  this.utils.snackBarShowError("Something went wrong, please contact support!");
                }
              });
            }.bind(this),
          },
        });
      } else {
        this.utils.snackBarShowSuccess("Setting saved successfully");
        this.retrieveSetting();
      }
    });
  }

  retrieveSetting() {
    this.getWorkArea = [];
    this.checkWorkSAreaDatas = [];
    const obj = {
      tenantId: this.user.tenantId,
      restaurantId: this.approvalSettingForm.value.branchSelection.restaurantIdOld,
      type: this.selectedType
    };
    const retrieveServiceMap = {
      grnApproval: this.purchasService.retrievePrSetting(obj),
      purchaseOrder: this.purchasService.retrievePrSetting(obj),
      indentApproval: this.purchasService.retrievePrSetting(obj),
      piApproval: this.purchasService.retrievePrSetting(obj),
      default: this.purchasService.retrievePrSetting(obj),
    };
    const retrieveService = retrieveServiceMap[this.selectedType] || retrieveServiceMap['default'];
    retrieveService.subscribe((res) => {
      if (res.hasOwnProperty("isAmountBased")) {
        this.selectedMechanism = res["isAmountBased"];
      }
      this.changeColumnsDynamically();
      if (res["data"].length > 0) {
        this.settingFound = true;
        this.processArray(res["data"]);
        this.cat = [];
        Object.entries(res["data"]).forEach(([key, value]) => {
          if (key == "0") {Object.entries(value).forEach(([k]) => {this.cat.push(k) });}
        });
      } else {
        this.settingFound = false;
        this.processArray([]);
      }
    });
  }
  
  processArray(arr: any) {
    const ELEMENT_DATA = [];
    arr.forEach((area) => {
      const location = area['location'];
      Object.keys(area['levels']).forEach((category) => {
        Object.entries(area['levels'][category]).forEach(([key, value]) => {
          const tmpHash = {
            workArea: location,
            category: category,
            level: key,
            role: value['role'],
            email: value['email'],
            amount: value['amount'],
            editAccess: value['editAccess']
          };
          ELEMENT_DATA.push(tmpHash);
        });
      });
    });
    const formGroupConfig = {
      workArea: new FormControl('', Validators.required),
      category: new FormControl('', Validators.required),
      level: new FormControl('', Validators.required),
      role: new FormControl('', Validators.required),
      email: new FormControl('', Validators.required),
      amount: new FormControl(),
      editAccess: new FormControl(),
      action: new FormControl('existingRecord'),
      isNewRow: new FormControl(false),
    };
    const formArrayConfig = ELEMENT_DATA.map((val) =>
      this.fb.group({ ...formGroupConfig, ...val })
    );
    if (this.selectedType === 'grnApproval') {
      this.GRNForm = this.fb.group({ VORows: this.fb.array(formArrayConfig) });
      this.setFormAndDataSource(this.GRNForm, 'isGRNFormReady');
    }else if (this.selectedType === 'purchaseOrder') {
      this.POForm = this.fb.group({ VORows: this.fb.array(formArrayConfig)})
      this.setFormAndDataSource(this.POForm, 'isPOFormReady');
    }
    else if (this.selectedType === 'indentApproval') {
      this.indentForm = this.fb.group({ VORows: this.fb.array(formArrayConfig)});
      this.setFormAndDataSource(this.indentForm, 'isIndentFormReady');
    }
    else if (this.selectedType === 'piApproval') {
      this.piForm = this.fb.group({ VORows: this.fb.array(formArrayConfig)});
      this.setFormAndDataSource(this.piForm, 'isPiFormReady');
    }
    else {
      this.PRForm = this.fb.group({ VORows: this.fb.array(formArrayConfig)});
      this.setFormAndDataSource(this.PRForm, 'isPRFormReady');
    }
  }

  click(PRForm, workArea, panelOpenState){    
    if(panelOpenState){
      this.getWorkArea.push(workArea)
    }else{
      const indexToRemove = this.getWorkArea.indexOf(workArea);
      if (indexToRemove !== -1) {
        this.getWorkArea.splice(indexToRemove, 1);
      }
    }
  }

  // ====================CHIP EMAIL CODE (don't remove) WORKING CODE ===========================================

  // add(event): void {
  //   if (event.value) {
  //     if (this.validateEmail(event.value)) {
  //       // this.emailList.push({ value: event.value, invalid: false });
  //       this.emailList.push([ event.value ]);
  //       const vorowsArray = this.PRForm.get('VORows') as FormArray;
  //       const rowIndex = 0; 
  //       const emailsArray = (vorowsArray.at(rowIndex) as FormGroup).get('emails') as FormArray;
  //       emailsArray.push(new FormControl(event.value, Validators.required));

  //     } else {
  //       this.emailList.push([ event.value ]);
  //       // this.emailList.push({ value: event.value, invalid: true });
  //       // this.PRForm.controls['emails'].setErrors({'incorrectEmail': true});
  //     }
  //     console.log(this.PRForm)
  //     console.log(this.PRForm.controls)
  //     console.log(this.PRForm.controls['emails'])
  //   }
  //   if (event.input) {
  //     event.input.value = '';
  //   }
  // }

  // removeEmail(data: any): void {
  //   console.log('Removing ' + data)
  //   if (this.emailList.indexOf(data) >= 0) {
  //     this.emailList.splice(this.emailList.indexOf(data), 1);
  //   }
  // }

  // private validateArrayNotEmpty(c: FormControl) {
  //   if (c.value && c.value.length === 0) {
  //     return {
  //       validateArrayNotEmpty: { valid: false }
  //     };
  //   }
  //   return null;
  // }

  // private validateEmail(email) {
  //   var re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  //   return re.test(String(email).toLowerCase());
  // }

  // ====================CHIP EMAIL CODE (don't remove) WORKING CODE ===========================================

}
