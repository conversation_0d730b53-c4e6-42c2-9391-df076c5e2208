<div class="formclass">
  <!-- d-flex  -->
  <form [formGroup]="purchaseStatusOrderForm">
    <mat-form-field appearance="none" class="mr-2">
      <label>Restaurant</label>
      <mat-select formControlName="rest" placeholder="Restaurant" class="outline"
        (selectionChange)="filterByBranch($event.value)">
        <mat-option *ngFor="let rest of branchesData" [value]="rest">
          {{ rest.branchName }}
        </mat-option>
      </mat-select>
    </mat-form-field>

    <mat-form-field appearance="none" class="mr-2">
      <label>Start Date</label>
      <!-- [(ngModel)]="selectedStartDate"  -->
      <input matInput class="outline" [matDatepicker]="picker1" placeholder="Start Date"
        formControlName="startDate" />
      <mat-datepicker-toggle matSuffix [for]="picker1">
        <mat-icon matDatepickerToggleIcon>
          <img src="./../../assets/calender.png" />
        </mat-icon>
      </mat-datepicker-toggle>
      <mat-datepicker #picker1></mat-datepicker>
    </mat-form-field>

    <mat-form-field appearance="none" class="mr-2">
      <label>End Date</label>
      <!-- [(ngModel)]="selectedEndDate" -->
      <input matInput class="outline" [matDatepicker]="picker2"  formControlName="endDate"
        placeholder="End Date" [readonly]="!this.purchaseStatusOrderForm.value.startDate"
        [disabled]="!this.purchaseStatusOrderForm.value.startDate"
        [min]="this.purchaseStatusOrderForm.value.startDate" />
      <mat-datepicker-toggle matSuffix [for]="picker2">
        <mat-icon matDatepickerToggleIcon>
          <img src="./../../assets/calender.png" />
        </mat-icon>
      </mat-datepicker-toggle>
      <mat-datepicker #picker2></mat-datepicker>
    </mat-form-field>
  </form>

  <button mat-button class="button3 findBtn mr-2" (click)="filterByDate()">Find</button>
  <button mat-button class="button3 clearBtn mr-2" (click)="clear()">Clear</button>
</div>

<!-- <mat-card  *ngIf="showTable">
  <section class="example-container-1 mat-elevation-z8">
    <table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort
      class="mat-elevation-z8">
      <ng-container matColumnDef="purchaseRequest">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Purchase Request</b></th>
        <th mat-header-row class="subheading">
          <mat-header-cell *matHeaderCell colspan="3">Subheading</mat-header-cell>
        </th>
        <td mat-cell *matCellDef="let element">
          <section class="example-container-pr mat-elevation-z8">
            <table #table mat-table [dataSource]="[element.prData]" matSortActive="prId" matSortDirection="asc" matSort
              class="prtable">
              <ng-container matColumnDef="prId">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="innerTableHead">
                  <b class="topItemkey">PR ID</b>
                </th>
                <td mat-cell *matCellDef="let ele" class="links innerTableHead">
                  <div *ngIf="ele?.prId">
                    <span matTooltip="view details" (click)="detailedPr(element)" class="links">{{ ele.prId}}</span>
                    <strong *ngIf="ele?.parentId !=''" class="parentIdClass"> {{ele?.parentId}}</strong>
                  </div>
                  <div *ngIf="!ele?.prId">
                    -
                  </div>
                </td>
              </ng-container>
              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="innerTableHead">
                  <b class="topItemkey">PR STATUS</b>
                </th>
                <td mat-cell *matCellDef="let ele" class="innerTableHead">
                  <div *ngIf="ele?.prStatus">
                    {{ (ele.prStatus === 'completed' || ele.prStatus === 'Completed') ? 'Created' : ele.prStatus | titlecase }}
                  </div>
                  <div *ngIf="!ele?.prStatus">
                    -
                  </div>
                </td>
              </ng-container>
              <ng-container matColumnDef="approvalStatus">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="innerTableHead">
                  <b class="topItemkey">APPROVAL STATUS</b>
                </th>
                <td mat-cell *matCellDef="let ele" class="innerTableHead">
                  <div *ngIf="ele?.parentId !=''">
                    Approved
                  </div>
                  <div *ngIf="ele?.parentId ==''">
                    <div *ngIf="ele?.approvalStatus" class="links">
                      <span (click)="mouseEnter(element)"> {{ ele?.approvalStatus | titlecase }}</span>
                    </div>
                    <div *ngIf="!ele?.approvalStatus">
                      -
                    </div>
                  </div>
                </td>
              </ng-container>
              <tr mat-header-row *matHeaderRowDef="displayedColumnsPrData"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumnsPrData"></tr>
            </table>
            <div class="dataMessage" *ngIf="element.prData?.length == 0"> - </div>
          </section>
        </td>
      </ng-container>
      <ng-container matColumnDef="purchaseOrder">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Purchase Order</b></th>
        <td mat-cell *matCellDef="let element">
          <section class="example-container-po mat-elevation-z8">
            <table #table mat-table [dataSource]="element.poData" matSortActive="prId" matSortDirection="asc" matSort
              class="potable">
              <ng-container matColumnDef="poId">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="innerTableHead">
                  <b class="topItemkey">PO ID</b>
                </th>
                <td mat-cell *matCellDef="let ele" (click)="receiveOrder(ele , element)" class="links innerTableHead"
                  matTooltip="view details">{{ ele.poId }}</td>
              </ng-container>
              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="innerTableHead">
                  <b class="topItemkey">PO STATUS</b>
                </th>
                <td mat-cell *matCellDef="let ele" class="innerTableHead">
                  {{ ele.status?.orderStatus | titlecase }}
                </td>
              </ng-container>
              <ng-container matColumnDef="approvalStatus">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="innerTableHead">
                  <b class="topItemkey">APPROVAL STATUS</b>
                </th>
                <td mat-cell *matCellDef="let ele" class="innerTableHead">
                  <div *ngIf="ele?.approvalStatus  =='Pending'">
                    <span class="links" (click)="mouseEnterPo(ele)">Pending</span>
                  </div>
                  <div *ngIf="ele?.approvalStatus !='Pending'">
                    <div *ngIf="ele?.approvalStatus">
                      <span class="links" (click)="mouseEnterPo(ele)">
                        {{ ele?.approvalStatus | titlecase }}
                      </span>
                    </div>
                  </div>
                  <div *ngIf="!ele?.approvalStatus">
                    <span class="links" (click)="mouseEnterPo(ele)" >{{ele?.poId ? 'Approved' : '-'}}</span>
                  </div>
                </td>
              </ng-container>
              <tr mat-header-row *matHeaderRowDef="displayedColumnsPoData"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumnsPoData"></tr>
            </table>
            <div class="purStsTableMsg" *ngIf="element.poData?.length == 0"> Not Yet Created</div>
          </section>
        </td>
      </ng-container>
      <ng-container matColumnDef="grn" sticky>
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>GRN</b></th>
        <td mat-cell *matCellDef="let element" class="grnTable">
          <section class="example-container-grn mat-elevation-z8">
            <table #table mat-table [dataSource]="element.grnData" matSortActive="prId" matSortDirection="asc" matSort
              class="grntable">
              <ng-container matColumnDef="grnId">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="innerTableHead">
                  <b class="topItemkey">GRN Id</b>
                </th>
                <td mat-cell *matCellDef="let ele" class="innerTableHead links" matTooltip="view details" (click)="detailedGrn(element)" >{{ ele.grnId }}</td>
              </ng-container>
              <ng-container matColumnDef="status">
                <th mat-header-cell *matHeaderCellDef mat-sort-header class="innerTableHead">
                  <b class="topItemkey">GRN STATUS</b>
                </th>
                <td mat-cell *matCellDef="let ele" class="innerTableHead">
                  <span>{{ ele.status?.inwardStatus | titlecase }}</span>
                </td>
              </ng-container>
              <tr mat-header-row *matHeaderRowDef="displayedColumnsGrnData"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumnsGrnData"></tr>
            </table>
            <div class="purStsTableMsg" *ngIf="element.grnData?.length == 0"> Not Yet Created</div>
          </section>
        </td>
      </ng-container>
      <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true;"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
    </table>
  </section>
  <div class="dataMessage" *ngIf="dataSource?.data?.length == 0"> No Data Available </div>
  <mat-paginator #paginator [pageSize]='10' [pageSizeOptions]="pageSizes"></mat-paginator>
</mat-card> -->

<ng-template #openPrDialog>
  <table class="table tablecenterData">
    <tbody *ngFor="let item of this.elementdata.prData.approvalDetail">
      <tr>
        <th class="topItemkey" scope="row">Level</th>
        <td>
          <div *ngIf="item.level">
            {{ item }}
          </div>
          <div *ngIf="!item.level">
            -
          </div>
        </td>
      </tr>
      <tr>
        <th class="topItemkey" scope="row">Role</th>
        <td>
          <div *ngIf="item.role">
            {{ item.role }}
          </div>
          <div *ngIf="!item.role">
            -
          </div>
        </td>
      </tr>
      <tr>
        <th class="topItemkey" scope="row">Status</th>
        <td>
          <div *ngIf="item.status">
            {{ item.status }}
          </div>
          <div *ngIf="!item.status">
            -
          </div>
        </td>
      </tr>
      <tr>
        <th class="topItemkey" scope="row">Reason</th>
        <td>
          <div *ngIf="item.rejectionReason">
            {{ item.rejectionReason }}
          </div>
          <div *ngIf="!item.rejectionReason">
            -
          </div>
        </td>
      </tr>
    </tbody>
  </table>
</ng-template>
<ng-template #openPoDialog>
  <table class="table tablecenterData">
    <tbody *ngFor="let item of this.poElementdata?.poApprovalDetail">
      <tr>
        <th class="topItemkeyPs" scope="row">Level</th>
        <td>
          <div *ngIf="item.level">
            {{ item?.level }}
          </div>
          <div *ngIf="!item.level">
            -
          </div>
        </td>
      </tr>
      <tr>
        <th class="topItemkeyPs" scope="row">Role</th>
        <td>
          <div *ngIf="item.role">
            {{ item?.role }}
          </div>
          <div *ngIf="!item.role">
            -
          </div>
        </td>
      </tr>
      <tr>
        <th class="topItemkeyPs" scope="row">Status</th>
        <td>
          <div *ngIf="item.status">
            {{ item?.status }}
          </div>
          <div *ngIf="!item.status">
            -
          </div>
        </td>
      </tr>
      <tr>
        <th class="topItemkeyPs" scope="row">Reason</th>
        <td>
          <div *ngIf="item.reason">
            {{ item?.rejectReason }}
          </div>
          <div *ngIf="!item.reason">
            -
          </div>
        </td>
      </tr>
    </tbody>
  </table>
</ng-template>
<ng-template #openGrnDialog>
  <table class="table tablecenterData">
    <tbody *ngFor="let item of this.elementdata.grnApprovalSetting">
      <tr>
        <th class="topItemkey" scope="row">Role</th>
        <td>
          <div *ngIf="item.role">
            {{ item.role }}
          </div>
          <div *ngIf="item.role">
            -
          </div>
        </td>
      </tr>
      <tr>
        <th class="topItemkey" scope="row">Level</th>
        <td>
          <div *ngIf="item.level">
            {{ item.level }}
          </div>
          <div *ngIf="item.level">
            -
          </div>
        </td>
      </tr>

      <tr>
        <th class="topItemkey" scope="row">Status</th>
        <td>
          <div *ngIf="item.status">
            {{ item.status }}
          </div>
          <div *ngIf="item.status">
            -
          </div>
        </td>
      </tr>
    </tbody>
  </table>
</ng-template>


<div *ngIf="showTable" class="datacontainer">
  <mat-card>
    <div class="search-table-input">
      <mat-form-field appearance="none">
        <label>Search</label>
        <input matInput type="text" class="outline" (keyup)="callApi($event.target.value)" placeholder="search by PR ID" [(ngModel)]="searchText">
        <mat-icon matSuffix (click)="searchData()" class="closebtn">search</mat-icon>
      </mat-form-field>

      <mat-form-field appearance="none">
        <label>Work Area</label>
        <mat-select placeholder="Work Area" [formControl]="workArea" (selectionChange)="workAreaChange($event.value)" 
        class="outline" [multiple]="true" #multiSelect>
          <mat-option>
            <ngx-mat-select-search placeholderLabel="WorkAreas..." noEntriesFoundLabel="'no WorkArea found'"
              [formControl]="workAreaFilterCtrl"></ngx-mat-select-search>
          </mat-option>
          <mat-option (click)="toggleSelectAllWorkAreas()" class="hide-checkbox">
            Select All / Deselect All
          </mat-option>
          <mat-option *ngFor="let area of workAreas | async" [value]="area">
            {{ area }}
          </mat-option>          
        </mat-select>
      </mat-form-field>

      <mat-form-field appearance="none">
        <label>Item Name</label>
        <mat-select placeholder="Item Name" [formControl]="itemName" (selectionChange)="itemNameChange($event.value)" 
        class="outline" [multiple]="true" #multiSelect>
          <mat-option>
            <ngx-mat-select-search placeholderLabel="Items..." noEntriesFoundLabel="'no Item found'"
              [formControl]="itemFilterCtrl"></ngx-mat-select-search>
          </mat-option>
          <mat-option (click)="toggleSelectAllItems()" class="hide-checkbox">
            Select All / Deselect All
          </mat-option>
          <mat-option *ngFor="let item of itemBanks | async" [value]="item">
            {{ item }}
          </mat-option>          
        </mat-select>
      </mat-form-field>

      <button mat-button class="buttonForRefresh refreshBtnclss mt-6" (click)="refresh()">Refresh</button>
    </div>

    <div>
      <section class="example-container-1 mat-elevation-z8">
        <table mat-table [dataSource]="dataSource" class="mat-elevation-z8 table-striped">
          <ng-container matColumnDef="index">
            <th mat-header-cell *matHeaderCellDef class="topItemkey tableId"><b>#</b></th>
            <td mat-cell *matCellDef="let element; let i = index">
              {{ i + 1 }}
            </td>
          </ng-container>

        <!-- PR Data -->
          <ng-container matColumnDef="prId">
            <th mat-header-cell *matHeaderCellDef>
              <b class="topItemkey">PR ID</b>
            </th>
            <td mat-cell *matCellDef="let element">
              <div *ngIf="element.prData?.prId">
                <span matTooltip="view details" (click)="detailedPr(element)" class="links">{{ element.prData.prId}}</span>
                <strong *ngIf="element.prData?.parentId !=''" class="parentIdClass"> {{element.prData.parentId}}</strong>
              </div>
              <div *ngIf="!element.prData?.prId">
                -
              </div>
            </td>
          </ng-container>

          <ng-container matColumnDef="prStatus">
            <th mat-header-cell *matHeaderCellDef>
              <b class="topItemkey">PR STATUS</b>
            </th>
            <td mat-cell *matCellDef="let element">
              <div *ngIf="element.prData?.prStatus">
                {{ (element.prData.prStatus === 'completed' || element.prData.prStatus === 'Completed') ? 'Created' : element.prData.prStatus | titlecase }}
              </div>
              <div *ngIf="!element.prData?.prStatus">
                -
              </div>
            </td>
          </ng-container>
          <ng-container matColumnDef="prApprovalStatus">
            <th mat-header-cell *matHeaderCellDef>
              <b class="topItemkey">APPROVAL STATUS</b>
            </th>
            <td mat-cell *matCellDef="let element">
              <div *ngIf="element?.prData?.parentId !=''">
                Approved
              </div>
              <div *ngIf="element?.prData?.parentId ==''">
                <div *ngIf="element?.prData?.approvalStatus">
                  <span> {{ element?.prData?.approvalStatus | titlecase }}</span>
                </div>
                <div *ngIf="!element?.prData?.approvalStatus">
                  -
                </div>
              </div>
            </td>
          </ng-container>

        <!-- PO Data -->

          <ng-container matColumnDef="po">
            <th mat-header-cell *matHeaderCellDef>
              <b class="topItemkey"> PO DETAILS </b>
            </th>
            <td mat-cell *matCellDef="let element">
              <button mat-icon-button class="delete-button" matTooltip="View Details" (click)="viewPO(element)" matTooltipPosition="right">
                <mat-icon>visibility</mat-icon>
              </button>
            </td>
          </ng-container>

          <ng-container matColumnDef="poId">
            <th mat-header-cell *matHeaderCellDef>
              <b class="topItemkey">PO ID</b>
            </th>
            <!-- <td mat-cell *matCellDef="let element" >

                <div *ngFor="let ele of element?.poData">
                  <div *ngIf="ele" (click)="receiveOrder(ele , element)" class="links" matTooltip="view details">
                    {{ ele.poId}}
                  </div>
                  <div *ngIf="!ele">
                    -
                  </div>
                </div>
                /{{ this.getDisplayValue(ele) }}
            </td> -->

            <td mat-cell *matCellDef="let element">
              <ng-container *ngIf="element?.poData?.length > 0; else emptyData">
                <div *ngFor="let ele of element.poData">
                  <div (click)="receiveOrder(element)" class="links m-2" matTooltip="view details">
                    {{ ele.poId || '-' }}
                  </div>
                </div>
              </ng-container>
              <ng-template #emptyData>
                -
              </ng-template>
            </td>
          </ng-container>

          <ng-container matColumnDef="poStatus">
            <th mat-header-cell *matHeaderCellDef>
              <b class="topItemkey">PO STATUS</b>
            </th>
            <td mat-cell *matCellDef="let element">
              <!-- <div *ngFor="let ele of element?.poData">
                <div *ngIf="ele?.status?.orderStatus">
                  {{ ele?.status?.orderStatus | titlecase}}
                </div>
              </div> -->

            <ng-container *ngIf="element?.poData?.length > 0; else emptyData">
              <div *ngFor="let ele of element.poData" class="m-2">
                  {{ ele.status?.orderStatus || '-' }}
              </div>
            </ng-container>
            <ng-template #emptyData>
              -
            </ng-template>
          </td>
        </ng-container>

          <ng-container matColumnDef="poApprovalStatus">
            <th mat-header-cell *matHeaderCellDef>
              <b class="topItemkey">APPROVAL STATUS</b>
            </th>
            <td mat-cell *matCellDef="let element">
              <ng-container *ngIf="element?.poData?.length > 0; else emptyData">

              <div *ngFor="let ele of element?.poData">
                <div *ngIf="ele.approvalStatus == 'Pending'" class="m-2">
                  <span>Pending</span>
                </div>
                <div *ngIf="ele.approvalStatus != 'Pending'"  class="m-2">
                  <div *ngIf="ele.approvalStatus">
                    <span>
                      {{ ele?.approvalStatus | titlecase }}
                    </span>
                  </div>
                </div>
                <div *ngIf="!ele?.approvalStatus"  class="m-2">
                  <!-- <span class="links" (click)="mouseEnterPo(ele)" >{{ele?.poId ? 'Approved' : '-'}}</span> -->
                  <span>{{ele?.poId ? 'Approved' : '-'}}</span>
                </div>
              </div>
            </ng-container>
            <ng-template #emptyData>
              -
            </ng-template>
            </td>
          </ng-container>

        <!-- GRN Data -->

          <ng-container matColumnDef="grn">
            <th mat-header-cell *matHeaderCellDef>
              <b class="topItemkey"> GRN DETAILS </b>
            </th>
            <td mat-cell *matCellDef="let element">
              <button mat-icon-button class="delete-button" matTooltip="View Details" (click)="viewGRN(element)" matTooltipPosition="right">
                <mat-icon>visibility</mat-icon>
              </button>
            </td>
          </ng-container>

          <ng-container matColumnDef="grnId">
            <th mat-header-cell *matHeaderCellDef>
              <b class="topItemkey">GRN ID</b>
            </th>
            <td mat-cell *matCellDef="let element" >
              <!-- <div *ngFor="let element of element?.grnData">
                <div *ngIf="element?.grnId" class="links" matTooltip="view details" (click)="detailedGrn(element)">
                  {{ element?.grnId }}
                </div>
                <div *ngIf="!element?.grnId">
                  -
                </div>
            </div> -->

            <ng-container *ngIf="element?.grnData?.length > 0; else emptyData">
              <div *ngFor="let ele of element.grnData" class="m-2">
                  {{ ele.grnId || '-' }}
              </div>
            </ng-container>
            <ng-template #emptyData>
              -
            </ng-template>
            </td>
          </ng-container>

          <ng-container matColumnDef="grnStatus">
            <th mat-header-cell *matHeaderCellDef>
              <b class="topItemkey">GRN STATUS</b>
            </th>
            <td mat-cell *matCellDef="let element">
              <!-- <div *ngFor="let element of element.grnData">
                <div *ngIf="element?.status?.inwardStatus">
                  {{ element?.status?.inwardStatus | titlecase }}
                </div>
                <div *ngIf="!element?.status?.inwardStatus">
                  -
                </div>
              </div> -->

              <ng-container *ngIf="element?.grnData?.length > 0; else emptyData">
                <div *ngFor="let ele of element.grnData" class="m-2">
                    {{ ele.status?.inwardStatus || '-' }}
                </div>
              </ng-container>
              <ng-template #emptyData>
                -
              </ng-template>

            </td>
          </ng-container>

          <ng-container matColumnDef="header-row-first-group">
            <th mat-header-cell *matHeaderCellDef [style.text-align]="center" [attr.colspan]="4" class="parentHeaders"> PURCHASE REQUEST </th>
          </ng-container>
          
          <ng-container matColumnDef="header-row-second-group">
            <th mat-header-cell *matHeaderCellDef [attr.colspan]="1" class="parentHeaders"> PURCHASE ORDER </th>
          </ng-container>

          <ng-container matColumnDef="header-row-third-group">
            <th mat-header-cell *matHeaderCellDef [attr.colspan]="1" class="parentHeaders"> GRN </th>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="['header-row-first-group', 'header-row-second-group','header-row-third-group']"></tr>
          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
      </section>
    </div>
    <div class="dataMessage" *ngIf="dataSource?.data?.length == 0"> No Data Available </div>
    <mat-paginator [showTotalPages]="10" [pageSize]="10" [pageSizeOptions]="pageSizes"></mat-paginator>

    <ng-template #poDialog>
      <button mat-icon-button matDialogClose="yes" matTooltip="Close" (click)="close()" class="CloseBtn">
        <mat-icon>close</mat-icon>
      </button>
      <h2 mat-dialog-title>
        <b>PO DATA</b>
      </h2>

      <div *ngIf="poLoader" class="loader-container">
        <div class="childLoader_conatiner">
          <mat-spinner diameter="80"></mat-spinner>
        </div>
      </div>

      <div  *ngIf="!poLoader" class="searchBox">
        <mat-form-field appearance="none">
          <input matInput type="text" class="outline" placeholder="Search by ID" [(ngModel)]='searchPO'
            (keyup)="poFilter($event.target.value)" />
          <mat-icon matSuffix (click)="resetPO()" class="closebtn">close</mat-icon>
        </mat-form-field>
      </div> 

      <mat-dialog-content *ngIf="!poLoader" class="mat-typography">
        <div class="col">

          <section class="example-container-2 mat-elevation-z8"> 
          <table #table mat-table [dataSource]="poDataSource" matSortDirection="desc" matSort>
            <ng-container matColumnDef="index">
              <th mat-header-cell *matHeaderCellDef><b class="topItemkey">S.No</b></th>
              <td mat-cell *matCellDef="let element; let i = index">
                {{ i + 1 }}
              </td>
              <td mat-footer-cell *matFooterCellDef></td>
            </ng-container>

            <ng-container matColumnDef="poId">
              <th mat-header-cell *matHeaderCellDef>
                <b class="topItemkey">PO ID</b>
              </th>
              <td mat-cell *matCellDef="let element">
                <div (click)="receiveOrder(element)" class="links m-2" matTooltip="view details">
                  {{ element.poId || '-' }}
                </div>
              </td>
              <td mat-footer-cell *matFooterCellDef></td>
            </ng-container>
            
            <ng-container matColumnDef="poStatus">
              <th mat-header-cell *matHeaderCellDef>
                <b class="topItemkey">PO STATUS</b>
              </th>
              <td mat-cell *matCellDef="let element">
                {{ element.status?.orderStatus || '-' }}
              </td>
              <td mat-footer-cell *matFooterCellDef></td>
            </ng-container>
            
            <ng-container matColumnDef="poApprovalStatus">
              <th mat-header-cell *matHeaderCellDef>
                <b class="topItemkey">APPROVAL STATUS</b>
              </th>
              <td mat-cell *matCellDef="let element">
                <span *ngIf="element.approvalStatus === 'Pending'">Pending</span>
                <span *ngIf="element.approvalStatus && element.approvalStatus !== 'Pending'">
                  {{ element.approvalStatus | titlecase }}
                </span>
                <span *ngIf="!element.approvalStatus">
                  {{ element.poId ? 'Approved' : '-' }}
                </span>
              </td>
              <td mat-footer-cell *matFooterCellDef></td>
            </ng-container>        
          
            <tr mat-header-row *matHeaderRowDef="poDataColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: poDataColumns"></tr>
            <tr mat-footer-row *matFooterRowDef="poDataColumns"></tr>
          </table>
          </section>
          <div class="dataMessage" *ngIf="poDataSource?.data.length == 0"> No PO Data Available </div>
          <mat-paginator [showTotalPages]="5" [pageSize]="5" [pageSizeOptions]="pageSizes"></mat-paginator>    
        </div>
      </mat-dialog-content>
    </ng-template>

    <ng-template #grnDialog>
      <button mat-icon-button matDialogClose="yes" matTooltip="Close" (click)="close()" class="CloseBtn">
        <mat-icon>close</mat-icon>
      </button>
      <h2 mat-dialog-title>
        <b>GRN DATA</b>
      </h2>

      <div *ngIf="grnLoader" class="loader-container">
        <div class="childLoader_conatiner">
          <mat-spinner diameter="80"></mat-spinner>
        </div>
      </div>

      <div *ngIf="!grnLoader" class="searchBox">
        <mat-form-field appearance="none">
          <input matInput type="text" class="outline" placeholder="Search by ID" [(ngModel)]='searchGRN'
            (keyup)="grnFilter($event.target.value)" />
          <mat-icon matSuffix (click)="resetGRN()" class="closebtn">close</mat-icon>
        </mat-form-field>
      </div> 

      <mat-dialog-content *ngIf="!grnLoader" class="mat-typography">
        <div class="col">

          <section class="example-container-2 mat-elevation-z8"> 
          <table #table mat-table [dataSource]="grnDataSource" matSortDirection="desc" matSort>
            <ng-container matColumnDef="index">
              <th mat-header-cell *matHeaderCellDef><b class="topItemkey">S.No</b></th>
              <td mat-cell *matCellDef="let element; let i = index">
                {{ i + 1 }}
              </td>
              <td mat-footer-cell *matFooterCellDef></td>
            </ng-container>

            <ng-container matColumnDef="grnId">
              <th mat-header-cell *matHeaderCellDef>
                <b class="topItemkey">GRN ID</b>
              </th>
              <td mat-cell *matCellDef="let element">
                <div (click)="detailedGrn(element)" class="links m-2" matTooltip="view details">
                  {{ element.grnId || '-' }}
                </div>
              </td>
              <td mat-footer-cell *matFooterCellDef></td>
            </ng-container>
            
            <ng-container matColumnDef="grnStatus">
              <th mat-header-cell *matHeaderCellDef>
                <b class="topItemkey">GRN STATUS</b>
              </th>
              <td mat-cell *matCellDef="let element">
                {{ element.status?.inwardStatus || '-' }}
              </td>
              <td mat-footer-cell *matFooterCellDef></td>
            </ng-container>
            
            <tr mat-header-row *matHeaderRowDef="grnDataColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: grnDataColumns"></tr>
            <tr mat-footer-row *matFooterRowDef="grnDataColumns"></tr>
          </table>
          </section>
          <div class="dataMessage" *ngIf="grnDataSource?.data.length == 0"> No GRN Data Available </div>
          <mat-paginator [showTotalPages]="5" [pageSize]="5" [pageSizeOptions]="pageSizes"></mat-paginator>    
        </div>
      </mat-dialog-content>
    </ng-template>
  </mat-card>
</div>