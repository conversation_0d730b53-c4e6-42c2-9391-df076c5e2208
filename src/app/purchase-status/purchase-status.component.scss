.myDIV{
    position: relative;
}

.findBtn{
    position: absolute;
    top: 41px;
    left: 590px
  }

  .clearBtn{
    top: 41px;
    position: absolute;
    left: 665px;
  }

.formclass{
    margin-left: 2%;
    margin-right: 2%;
    display: flow-root !important;
}

.tableCells{
    padding: 5px;
}

.tableCard{
    position: absolute;
    margin-right: 2% !important;
    margin-left: 2% !important;
}

.tablecenterData{
    // display: flex;
    // justify-content: center;
    // position: relative;
    // top: 170px;
    width: 365px;
    margin-top: 80px;
    position: relative;
    border: 1px solid;
    // transition-duration: 2s;
    // transition: 0.8s;
}

.example-container-1{
    // height: 525px;
    // overflow: auto;
    max-height: 525px;
    overflow-y: auto;
  }

  .topItemkeyPs{
    width: 175px;
  }

  .CloseBtn{
    float: right;
    margin-bottom: -1px;
  }

  .grnTable{
    position: initial !important;
  }

  // ::ng-deep .mat-dialog-container {
  //   display: contents !important;
  // }

//  .ngTemplateClass {
//     display: contents !important;
//   }

  .table th, .table td {
    padding: 0.25rem !important;
}

.refreshBtnDiv{
  display: flow-root;
}

.refreshBtnclss{
  // float: right;
  position: absolute;
  right: 2%;
  top: 41px;
}

.example-container-po{
  max-height: 130px;
  overflow-y: auto;
}

.example-container-pr{
  max-height: 130px;
  overflow-y: auto;
}

.example-container-grn{
  max-height: 130px;
  overflow-y: auto;
}

th.innerTableHead{
  background-color: #1919195e !important;
}

td.innerTableHead{
  // background-color: #262626 !important;
  background-color: #2f2f2f !important;
  
}

.prtable{
  width: 98%;
  margin: 4px 0px 4px 0px;
  // margin-left: 0px;
  // margin-top: 0px;
}

.potable{
    width: 98%;
    margin: 4px 0px 4px 0px;
    // margin-left: 0px;
    // margin-top: 0px;
}

.grntable{
  margin: 4px 0px 4px 0px;
}

// -------------------------------- NEW DILOG BOX STYLES------------------------------------------------

body {
  font-family: "Roboto", sans-serif;
  padding: 0;
  margin: 0;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.cookiesContent {
  width: 320px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #fff;
  color: #000;
  text-align: center;
  border-radius: 20px;
  padding: 30px 30px 70px;
  button.close {
    width: 30px;
    font-size: 20px;
    color: #c0c5cb;
    align-self: flex-end;
    background-color: transparent;
    border: none;
    margin-bottom: 10px;
  }
  img {
    width: 82px;
    margin-bottom: 15px;
  }
  p {
    margin-bottom: 40px;
    font-size: 18px;
  }
  button.accept {
    background-color: #ed6755;
    border: none;
    border-radius: 5px;
    width: 200px;
    padding: 14px;
    font-size: 16px;
    color: white;
    box-shadow: 0px 6px 18px -5px rgba(237, 103, 85, 1);
  }
}

.purStsTableMsg{
  text-align: center !important;
  font-size: 11px !important;
  margin: 10px;
}

table .mat-row:nth-child(even) {
    background-color: #262626 !important;
}

.parentHeaders{
  text-align: center;
}
// --------------------------------------------------------------
.page-item.disabled .page-link {
  color: #6c757d;
  pointer-events: none;
  cursor: auto;
  background-color: transparent;
  border-color: transparent;
}

.page-link {
  position: relative;
  display: block;
  padding: 0.25rem 0.25rem;
  margin-left: -1px;
  line-height: 1.25;
  color: #007bff;
  background-color: transparent;
  border: transparent;
}

.pagenatorTxt {
  font-size: 11px;
  margin-top: 9px;
  margin-right: 5px
}

.searchInput{
  display: flex;
}

::ng-deep td.mat-cell, td.mat-footer-cell, th.mat-header-cell {
  padding: 0;
  border-bottom-width: 1px;
  border-bottom-style: solid;
  text-align: center;
}

::ng-deep .mat-paginator {
  display: unset !important;
}

.searchBox{
  display: flex;
  margin-top: 25px !important;
  margin-left: 13px;
}

.childLoader_conatiner{
  margin: 70px;
  display: flex;
  justify-content: center;
}

.loader-container{
  min-width: 40vw;
}

::ng-deep .hide-checkbox .mat-pseudo-checkbox {
  display: none !important;
}