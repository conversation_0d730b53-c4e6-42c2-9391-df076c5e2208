import { ChangeDetectorRef, Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog, MatDialogRef } from '@angular/material';
import { AuthService, GlobalsService, PurchasesService, ShareDataService } from '../_services';
import { FormBuilder, FormGroup, Validators,FormControl } from '@angular/forms';
import { NavigationStart, Router } from '@angular/router';
import { UtilsService } from '../_utils/utils.service';
import { takeUntil } from 'rxjs/operators';
import { ReplaySubject, Subject } from 'rxjs';
import { SharedFilterService } from '../_services/shared-filter.service';
import { PreviewIbtComponent } from '../_dialogs/preview-ibt/preview-ibt.component';
import { logWarnings } from 'protractor/built/driverProviders';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material';
import { MomentDateModule, MomentDateAdapter } from '@angular/material-moment-adapter';
export const MY_FORMATS = {
  parse: {
      dateInput: 'LL'
  },
  display: {
      dateInput: 'DD-MM-YYYY',
      monthYearLabel: 'YYYY',
      dateA11yLabel: 'LL',
      monthYearA11yLabel: 'YYYY'
  }
};
@Component({
  selector: 'app-purchase-status',
  templateUrl: './purchase-status.component.html',
  styleUrls: ['./purchase-status.component.scss', './../../common-dark.scss'],
  providers:[{ provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
            { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }
          ]
})
export class PurchaseStatusComponent implements OnInit {
  dataSource: MatTableDataSource<any>;
  dataSource1 = new MatTableDataSource();
  dataSourcePoData: MatTableDataSource<any>;
  displayedColumns: string[];
  displayedColumns1: string[];
  displayedColumnsPrData: string[];
  displayedColumnsPoData: string[];
  displayedColumnsGrnData: string[];
  user: any;
  multiBranchUser; branchSelected: boolean;
  getBranchData: any[]
  branchesData: any[]
  purchaseStatusOrderForm: FormGroup;
  selectedStartDate: any;
  selectedEndDate: any;
  pageSizes: any[];
  @ViewChild(MatPaginator) paginator: MatPaginator;
  poElementdata: any;
  @ViewChild('openPrDialog') openPrDialog: TemplateRef<any>;
  @ViewChild('openPoDialog') openPoDialog: TemplateRef<any>;
  @ViewChild('openGrnDialog') openGrnDialog: TemplateRef<any>;
  @ViewChild('openD') openD: TemplateRef<any>;
  @ViewChild('poDialog') poDialog!: TemplateRef<any>;
  @ViewChild('grnDialog') grnDialog!: TemplateRef<any>;
  dialogRef: any;
  tempData: any;
  PurchaseStatusUrl = encodeURI(GlobalsService.PurchaseStatus)
  detailedGrnUrl = encodeURI(GlobalsService.detailedGrn)
  receivePOUrl = encodeURI(GlobalsService.receivePurchaseOrder)
  detailedPrUrl = encodeURI(GlobalsService.detailedPr)
  windowCurrentUrl: string;
  private unsubscribe$ = new Subject<void>();
  grnElementdata: any;
  sharedFilterData: any;
  showTable : boolean = false;
  dataForSearch: any;
  currentPage = 1;
  pageSize = 10;
  totalItems = 0; 
  roundTotalItems: any;
  searchText: any;
  searchFilter: any;
  poDataColumns: any
  poDataSource: MatTableDataSource<any>;
  grnDataColumns: any
  grnDataSource: MatTableDataSource<any>;
  poData: any;
  public searchPOValue: any = ''
  public searchGRNValue: any = ''
  searchPO: string;
  searchGRN: string;
  poLoader: boolean = false;
  grnLoader: boolean = false;
  itemName = new FormControl();
  workArea = new FormControl();
  itemNameList: any;
  workAreaList: any;
  allItemSelected: boolean = true;
  allWorkAreaSelected: boolean = true;
  selectedWorkAreas: string[] = [];
  selectedItemNames: string[] = [];
  public itemBank: any[] = [];
  public itemBanks: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public itemFilterCtrl: FormControl = new FormControl();
  public workAreaBank: any[] = [];
  public workAreas: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public workAreaFilterCtrl: FormControl = new FormControl();
  protected _onDestroy = new Subject<void>();
  resetDate: boolean = false;

  constructor(
    private sharedData: ShareDataService,
    private auth: AuthService,
    private fb: FormBuilder,
    private purchases: PurchasesService, 
    private router: Router,
    private utils: UtilsService,
    public dialog: MatDialog,
    private sharedFilterService: SharedFilterService,
    private cd: ChangeDetectorRef
  ) {
    this.user = this.auth.getCurrentUser();
    this.multiBranchUser = this.user.multiBranchUser;

    this.purchaseStatusOrderForm = this.fb.group({
      rest: ['', Validators.required],
      startDate: ['', Validators.required],
      endDate: ['', Validators.required],
    });
    this.sharedFilterService.getFilteredPurStatus.subscribe(obj => 
      this.sharedFilterData = obj
      
    );
    this.sharedData.sharedBranchData.pipe(takeUntil(this.unsubscribe$)).subscribe((val) => {
        this.getBranchData = val;
        if(this.getBranchData.length == 0 ){
          this.branchesData = this.user.restaurantAccess;
        }else if(this.getBranchData.length == 1){
          const toSelect = this.getBranchData.find(data => data.branchName == this.getBranchData[0].branchName);
            if(toSelect != this.sharedFilterData.restaurantId){
            this.sharedFilterData = '';
            this.purchaseStatusOrderForm.get('startDate').setValue(null);
            this.purchaseStatusOrderForm.get('endDate').setValue(null);
          }

          this.purchaseStatusOrderForm.get('rest').setValue(toSelect);
          this.branchesData = this.getBranchData
          this.filterByBranch(this.purchaseStatusOrderForm.value.rest);
        }else{
          if(this.sharedFilterData.branchFlag == true){
            this.filterByBranch(this.sharedFilterData.restaurantId);
            this.sharedFilterData.branchFlag = false;
          }
          this.branchesData = this.getBranchData
        }
    });    
    this.router.events.subscribe(event => {
      if (event instanceof NavigationStart) {
        if (
          event.url.includes('/home/<USER>') || 
          event.url.includes('/home/<USER>') || 
          event.url.includes('/home/<USER>')
        ) {
          if (this.resetDate) {
            localStorage.removeItem('savedStartDate');
            localStorage.removeItem('savedEndDate');
            localStorage.removeItem('savedItemNames');
            localStorage.removeItem('savedWorkAreas');          
            return;
          }          
          if (this.purchaseStatusOrderForm.value.startDate && this.purchaseStatusOrderForm.value.endDate) {            
            localStorage.setItem('savedStartDate', this.utils.dateCorrection(this.purchaseStatusOrderForm.value.startDate));      
            localStorage.setItem('savedEndDate', this.utils.dateCorrection(this.purchaseStatusOrderForm.value.endDate));  
          }
          if (this.itemName.value) {            
            localStorage.setItem('savedItemNames', JSON.stringify(this.itemName.value));
          }
          if (this.workArea.value) {            
            localStorage.setItem('savedWorkAreas', JSON.stringify(this.workArea.value));
          }
        }
      }
    });
  }

  ngOnInit() {
    this.displayedColumns = ['index','prId', 'prStatus', 'prApprovalStatus','po','grn'];
    this.poDataSource = new MatTableDataSource<any>();  
    this.poDataColumns = ['index','poId', 'poStatus','poApprovalStatus'];
    this.grnDataSource = new MatTableDataSource<any>();  
    this.grnDataColumns = ['index','grnId','grnStatus'];

    const savedStartDate = localStorage.getItem('savedStartDate');  
    const savedEndDate = localStorage.getItem('savedEndDate'); 
    if (savedStartDate && savedEndDate) {
      this.purchaseStatusOrderForm.get('startDate').setValue(savedStartDate);
      this.purchaseStatusOrderForm.get('endDate').setValue(savedEndDate);
    }

  }

  filterByBranch(restId) {

    if(this.sharedFilterData != ''){ 
      if(this.sharedFilterData.restaurantId == restId){
        this.purchaseStatusOrderForm.get('rest').setValue(this.sharedFilterData.restaurantId);
        this.branchesData = this.getBranchData;
        this.purchaseStatusOrderForm.get('startDate').setValue(this.sharedFilterData.selectedStartDate);
        this.purchaseStatusOrderForm.get('endDate').setValue(this.sharedFilterData.selectedEndDate);
        this.currentPage = this.sharedFilterData.currentPage;
      }else{
        this.purchaseStatusOrderForm.get('rest').setValue(restId);
        this.purchaseStatusOrderForm.get('startDate').setValue(null)
        this.purchaseStatusOrderForm.get('endDate').setValue(null)
      }
    }else{
      this.purchaseStatusOrderForm.get('startDate').setValue(null)
      this.purchaseStatusOrderForm.get('endDate').setValue(null)
    }
    this.getPurchaseStatus(); 
  }

  ngOnDestroy() {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
    this._onDestroy.next();
    this._onDestroy.complete();
    if (!this.router.url.includes(this.detailedGrnUrl) && !this.router.url.includes(this.receivePOUrl) && !this.router.url.includes(this.detailedPrUrl)) {
      localStorage.removeItem('savedStartDate');
      localStorage.removeItem('savedEndDate');
      localStorage.removeItem('savedItemNames');
      localStorage.removeItem('savedWorkAreas');
    }
  }

  filterByDate() {
    if (this.purchaseStatusOrderForm.value.startDate && this.purchaseStatusOrderForm.value.endDate) {
      this.getPurchaseStatus();
    }
    else {
      this.utils.snackBarShowError('Please select start date and end date')
    }
  }

  getPurchaseStatus(){
    let obj = {}
    obj['tenantId'] = this.user.tenantId
    obj['restaurantId'] = this.purchaseStatusOrderForm.value.rest.restaurantIdOld

    const savedStartDate = localStorage.getItem('savedStartDate');  
    const savedEndDate = localStorage.getItem('savedEndDate'); 

    if(this.purchaseStatusOrderForm.value.startDate && this.purchaseStatusOrderForm.value.endDate){
      obj['startDate'] = this.utils.dateCorrection(this.purchaseStatusOrderForm.value.startDate);
      obj['endDate'] = this.utils.dateCorrection(this.purchaseStatusOrderForm.value.endDate);
    } else if (savedStartDate && savedEndDate && !this.resetDate) {
      obj['startDate'] = savedStartDate;
      obj['endDate'] = savedEndDate;
    }else{
      obj['startDate'] = null;
      obj['endDate'] = null;
    }
    obj['page'] = this.currentPage;
    obj['per_page'] = this.pageSize;

    if(this.searchFilter){
      if(this.searchFilter.filter.prId){
      obj['filter'] = this.searchFilter
      }
    }
    
    this.purchases.getPurchaseStatus(obj).subscribe(res => {
      if (res){
        this.dataSource = new MatTableDataSource<any>();
        this.tempData = res.data
        this.dataForSearch = res.data
        this.totalItems = res.total;
        this.checkData();
      }
    })     
    this.showTable = true;

  }

  onPageChange(page: number): void {
    this.roundTotalItems = this.utils.truncateNew(this.totalItems / this.pageSize);
    this.currentPage = page;
    this.getPurchaseStatus();
  }

  searchData(){
      let obj ={
        filter : {
          prId : this.searchText.toUpperCase()
        }
      }
      this.searchFilter = obj
    this.getPurchaseStatus();
  }

  callApi(text: string) {
    let temp = this.dataSource.data;
    let filteredData
    if(text){
      filteredData = this.dataSource.data.filter(item => {
        const prIdMatch = item.prData.prId.toLowerCase().includes(text.toLowerCase());
        return prIdMatch;
      });
      this.cd.detectChanges();
      this.dataSource.data = filteredData.length > 0 ? filteredData : [];
    }else{
      this.dataSource.data = this.tempData;
    }
}

  getPageNumbers(): number[] {
    const totalPages = this.utils.truncateNew(this.totalItems / this.pageSize);
    return Array.from({ length: totalPages }, (_, i) => i + 1);
  }

  checkData(){
    this.tempData.forEach(async (el)=>{
      let status = (el['prData'] && el['prData']['approvalDetail'].length > 0) ?  this.getStatus(el['prData']['approvalDetail']) : '-' ;
      el['prData']['approvalStatus'] = status ;
    })
    // this.tempData.forEach(async (el)=>{
    //   let status = ( el.hasOwnProperty('poData') && el['poData'].length > 0 && el['poData'][0]['poApprovalDetail'] && el['poData'][0]['poApprovalDetail'].length > 0 ) ?  this.getStatus(el['poData'][0]['poApprovalDetail']) : null ;
    //   el.hasOwnProperty('poData') && el['poData'].length > 0 ? (el['poData'][0]['approvalStatus'] = status) : null ;
    // })

    const uniqueItemNames = new Set();
    this.tempData.forEach(item => {
      item.prData.itemNameList.forEach(element => uniqueItemNames.add(element));
    });
    this.itemNameList = Array.from(uniqueItemNames); 

    const savedItemNames = JSON.parse(localStorage.getItem('savedItemNames') || '[]');            
    if (savedItemNames.length > 0) {
      this.itemName.setValue(savedItemNames);
      this.itemNameChange(savedItemNames);
    } else {
      this.itemName.setValue(this.itemNameList.slice()); 
      this.itemNameChange(this.itemName.value);
    }
    this.itemBank = this.itemNameList
    this.itemBanks.next(this.itemBank.slice());
    this.itemFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
        this.itemNameFilter();
    });

    const uniqueWorkAreas = new Set();
    this.tempData.forEach(item => {
      item.prData.workAreaMapping.forEach(element => uniqueWorkAreas.add(element));
    });
    this.workAreaList = Array.from(uniqueWorkAreas);     

    const savedWorkAreas = JSON.parse(localStorage.getItem('savedWorkAreas') || '[]');            
    if (savedWorkAreas.length > 0) {
      this.workArea.setValue(savedWorkAreas);
      this.workAreaChange(savedWorkAreas);
    } else {
      this.workArea.setValue(this.workAreaList.slice()); 
      this.workAreaChange(this.workArea.value);
    }
    this.workAreaBank = this.workAreaList
    this.workAreas.next(this.workAreaBank.slice());
    this.workAreaFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
        this.workAreaFilter();
    });
    // this.dataSource.data = this.tempData;
    this.displayedColumnsPrData = [ 'prId','status','approvalStatus'];
    this.displayedColumnsPoData = [ 'po'];
    this.displayedColumnsGrnData = [ 'grn'];
    this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
    this.dataSource.paginator = this.paginator;
  }

  filteredData() {
    this.dataSource.data = this.tempData.filter(element => {
      const matchesWorkArea = element.prData.workAreaMapping.some(item => this.selectedWorkAreas.includes(item));  
      const matchesItemName = element.prData.itemNameList.some(item => this.selectedItemNames.includes(item));  
      return matchesWorkArea && matchesItemName;
    });
  }

  workAreaChange(selectedWorkAreas: string[]) {
    if (selectedWorkAreas && selectedWorkAreas.length > 0) {      
      this.selectedWorkAreas = selectedWorkAreas;
      this.filteredData();
    } else {
      this.dataSource.data = [];
    }
  }
  
  itemNameChange(selectedItemNames: string[]) {
    if (selectedItemNames && selectedItemNames.length > 0) {      
      this.selectedItemNames = selectedItemNames;
      this.filteredData();
    } else {
      this.dataSource.data = [];
    }
  }  

  toggleSelectAllWorkAreas() {
    this.allWorkAreaSelected = !this.allWorkAreaSelected;
    if (this.allWorkAreaSelected) {
      this.workArea.setValue(this.workAreaBank.slice());
    } else {
      this.workArea.setValue([]);
    }
    this.workAreaChange(this.workArea.value);
  }

  protected workAreaFilter() {
    if (!this.workAreaBank) {
      return;
    }
    let search = this.workAreaFilterCtrl.value;
    if (!search) {
      this.workAreas.next(this.workAreaBank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.workAreas.next(
      this.workAreaBank.filter(item => item.toLowerCase().indexOf(search) > -1)
    );
  }

  toggleSelectAllItems() {
    this.allItemSelected = !this.allItemSelected;
    if (this.allItemSelected) {
      this.itemName.setValue(this.itemBank.slice());
    } else {
      this.itemName.setValue([]);
    }
    this.itemNameChange(this.itemName.value);
  }

  protected itemNameFilter() {
    if (!this.itemBank) {
      return;
    }
    let search = this.itemFilterCtrl.value;
    if (!search) {
      this.itemBanks.next(this.itemBank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.itemBanks.next(
      this.itemBank.filter(item => item.toLowerCase().indexOf(search) > -1)
    );
  }

  detailedPr(element){
    let reqObj = {}
    reqObj['tenantId'] = this.user.tenantId;
    reqObj['restaurantId'] = this.purchaseStatusOrderForm.value.rest.restaurantIdOld
    reqObj['prId'] = element.prData.prId; 
    reqObj['type'] = 'pr';
  
    this.purchases.getDetails(reqObj).subscribe(res => {
      const prData = res.data; 
      this.sharedData.changePr(prData);
      this.router.navigate(['/home/<USER>',{purchaseStatus : "purchase Status"}]);
    }, err => { })   
  }

  receiveOrder(element){      
    let reqObj = {}
    reqObj['tenantId'] = this.user.tenantId;
    reqObj['restaurantId'] = this.purchaseStatusOrderForm.value.rest.restaurantIdOld
    reqObj['poId'] = element.poId; 
    reqObj['type'] = 'po'; 
  
    this.purchases.getDetails(reqObj).subscribe(res => {      
      const poData = res.data;       
      this.sharedData.changeOrder(poData[0]);
      this.close();
      this.router.navigate(['/home/<USER>',{purchaseStatus : "purchase Status"}]);      
    }, err => { })   
  }

  detailedGrn(element) {
    let reqObj = {}
    reqObj['tenantId'] = this.user.tenantId;
    reqObj['restaurantId'] = this.purchaseStatusOrderForm.value.rest.restaurantIdOld
    reqObj['grnId'] = element.grnId; 
    reqObj['type'] = 'grn'; 
  
    this.purchases.getDetails(reqObj).subscribe(res => {      
      const grnData = res.data;       
      this.sharedData.changeGrn(grnData[0])
      this.close();
      this.router.navigate(['/home/<USER>',{purchaseStatus : "purchase Status"}])
    }, err => { })       
    
  }

  clear(){
    this.resetDate = true; 
    this.purchaseStatusOrderForm.get('startDate').setValue(null);
    this.purchaseStatusOrderForm.get('endDate').setValue(null);
    this.refresh();  
  }

  refresh(){
    if (localStorage.getItem('savedItemNames')) {
      localStorage.removeItem('savedItemNames');
    }
    if (localStorage.getItem('savedWorkAreas')) {
      localStorage.removeItem('savedWorkAreas');
    }
    this.getPurchaseStatus();
  }

  openDi(){
    let dialogRefTemplate = this.dialog.open(this.openD);
    dialogRefTemplate.afterClosed().subscribe(result => {
  })
  }

  getStatus(data){
    const levelOrder = data.map(item => item.level);
    let levelWithStatus = "";
    for (const currentLevel of levelOrder) {
      const matchingData = data.find(item => item.level === currentLevel);
      
      if (matchingData) {
        const { level, status } = matchingData;
        
        if (status === "rejected") {
          levelWithStatus = ` ${status.charAt(0).toUpperCase() + status.slice(1)}`;
          break;
        } else if (status === "pending" && !levelWithStatus.includes("rejected")) {
          levelWithStatus = ` ${status.charAt(0).toUpperCase() + status.slice(1)}`;
        } else if (status === "approved" && !levelWithStatus.includes("rejected") && !levelWithStatus.includes("pending")) {
          levelWithStatus = status.charAt(0).toUpperCase() + status.slice(1);
        }
      }
    }
    return levelWithStatus
  }

  public poFilter = (value: string) => {
    this.poDataSource.filter = value.trim().toLocaleLowerCase();
  }

  resetPO() {
    this.searchPO = ''
    this.searchPOValue = ''
    this.poFilter(this.searchPOValue)
  }

  public grnFilter = (value: string) => {
    this.grnDataSource.filter = value.trim().toLocaleLowerCase();
  }

  resetGRN() {
    this.searchGRN = ''
    this.searchGRNValue = ''
    this.grnFilter(this.searchGRNValue)
  }

  viewPO(element){
    this.dialog.open(this.poDialog, {
      width: '700px', 
      disableClose: true, 
    });
    this.getPOData(element);
  }

  getPOData(element){
    this.poLoader = true;
    let obj = {}
    obj['tenantId'] = this.user.tenantId
    obj['restaurantId'] = this.purchaseStatusOrderForm.value.rest.restaurantIdOld
    obj['prId'] = element.prData.prId
    obj['type'] = 'po'
    this.purchases.getData(obj).subscribe(res => {         
      this.poData = res.data; 
      if (this.poData.length > 0) {
        for (const el of this.poData) {
          let status = el.hasOwnProperty('poApprovalDetail') && el['poApprovalDetail'].length > 0 ? this.getStatus(el['poApprovalDetail']) : null;
          el['approvalStatus'] = status;
        }
      }
      this.poDataSource.data = this.poData; 
      this.pageSizes = this.utils.getPageSizes(this.poDataSource.data);
      this.poDataSource.paginator = this.paginator;
      this.poLoader = false;
      }, err => { })
  }

  viewGRN(element){
    this.dialog.open(this.grnDialog, {
      width: '700px', 
      disableClose: true, 
    });
    this.getGRNData(element);
  }

  getGRNData(element){
    this.grnLoader = true;
    let obj = {}
    obj['tenantId'] = this.user.tenantId
    obj['restaurantId'] = this.purchaseStatusOrderForm.value.rest.restaurantIdOld
    obj['prId'] = element.prData.prId
    obj['type'] = 'grn'
    this.purchases.getData(obj).subscribe(res => {         
      const grnData = res.data; 
      this.grnDataSource.data = grnData; 
      this.pageSizes = this.utils.getPageSizes(this.grnDataSource.data);
      this.grnDataSource.paginator = this.paginator;
      this.grnLoader = false;
      }, err => { })
  }

  close() {    
    this.dialog.closeAll();
  }
}
