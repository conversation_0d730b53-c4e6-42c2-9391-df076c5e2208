<mat-card>
  <!-- <span mat-card-title class="headTag">Update Master Data </span> -->
  <br>
  <div class="container">
    <div fxLayout="row" fxLayoutAlign="center">
      <div class="each">  
        <form [formGroup]="masterDataForm"> 
        <!-- <mat-form-field appearance="outline">
          <mat-label>Select Client </mat-label> -->
          <!-- [(ngModel)]="SelectedClinets" -->
          <!-- <mat-select [(value)]="selectedClient" placeholder="Client" formControlName="clientsSelection">
            <mat-option *ngFor="let client of clients" [value]="client">
              {{client.full}}
            </mat-option>
          </mat-select>
        </mat-form-field> -->

        <mat-form-field appearance="outline">
          <mat-label>Select Client </mat-label>
          <mat-select placeholder="Clients" [(value)]="selectedClient" formControlName="clientsSelection" [ngModelOptions]="{standalone: true}"
            (selectionChange)="selectClient($event.value)">
            <mat-option>
              <ngx-mat-select-search placeholderLabel="Clients..." noEntriesFoundLabel="'no Clients found'"
                [formControl]="clientFilterCtrl"></ngx-mat-select-search>
            </mat-option>
            <mat-option *ngFor="let client of clientBanks | async" [value]="client">
              {{ client.full }}
            </mat-option>
          </mat-select>
        </mat-form-field>

      </form>    
      </div>
      <div class="each">
        <mat-form-field appearance="outline">
          <mat-label>Select Scenario</mat-label>
          <mat-select [(value)]="selectedSheets" placeholder="Scenario" [disabled]="!this.masterDataForm.value.clientsSelection">
            <mat-option *ngFor="let sheet of scenarios" [value]="sheet">
              {{sheet.scenario}}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div class="each" *ngIf="this.selectedSheets.scenario == 'Check System Error Log'">
        <mat-form-field appearance="outline">
          <mat-label>Enter Email</mat-label>
          <input matInput placeholder="Enter Mail" [(ngModel)]="email" type="text" required>
        </mat-form-field>
      </div>
      <div class="each"  *ngIf="this.selectedSheets.scenario == 'Check System Error Log'">
        <button mat-button class="button3 sndBtn" (click)="sendReq()"> Send </button>
      </div>
    </div>
    <br>
    <div style="text-align: center;">
      <p>Do you need a help? <a [routerLink]="" (click)="openHelpFile()">Click here</a></p>
      <div id="outer">
        <div class="inner">
          <button mat-stroked-button class="btn-block" (click)="createJob()"
            [disabled]="selectedClient=='' || selectedSheets.length==0 || this.selectedSheets.scenario == 'Check System Error Log'">
            Request Update
          </button>
        </div>
        <div class="inner">
          <button mat-stroked-button class="btn-block" (click)="reset()">
            Reset Selection
          </button>
        </div>
      </div>
    </div>
  </div>
  
<mat-card>
  <div class="example-container mat-elevation-z8">
    <!-- <br>
    <span mat-card-title class="headTag">Master Data Update Status</span> -->
    <div class="example-header mb-2">
      <mat-form-field appearance="fill" class="mr-2">
        <input matInput (keyup)="applyFilter($event)" placeholder="search here.." #input>
      </mat-form-field>
      <button mat-button class="buttonForRefresh" (click)="retrieveUpdates()">Refresh</button>
    </div>
     <div>
      <!-- mode="determinate" -->
    <div style="height: 1px;margin-bottom: -7px;">
      <mat-progress-bar mode="indeterminate" [value]="loadingPercentage" [ngClass]="{ 'disableLoading': !loading }" *ngIf="loading"></mat-progress-bar>
    </div>
    <table #table mat-table matSort [dataSource]="dataSource">
      <ng-container matColumnDef="id">
        <th mat-header-cell *matHeaderCellDef> Update Id </th>
        <td mat-cell *matCellDef="let element"> {{element.id}} </td>
      </ng-container>
      <ng-container matColumnDef="client">
        <th mat-header-cell *matHeaderCellDef> Client </th>
        <td mat-cell *matCellDef="let element"> {{element.client}} </td>
      </ng-container>      
      <ng-container matColumnDef="createTs">
        <th mat-header-cell *matHeaderCellDef> Created Date </th>
        <!-- <td mat-cell *matCellDef="let element"> {{element.createTs | date: 'medium' : 'UTC'}} </td> -->
        <td mat-cell *matCellDef="let element"> {{ element.createTs | date:'MMM d,h:mm:ss a' }} </td>
      </ng-container>
      <ng-container matColumnDef="category">
        <th mat-header-cell *matHeaderCellDef> Category </th>
        <td mat-cell *matCellDef="let element"> {{element.category}} </td>
      </ng-container>
      <ng-container matColumnDef="status">
        <th mat-header-cell *matHeaderCellDef> Status </th>
        <td mat-cell *matCellDef="let element"> {{element.status}}
        
          <div class="tableStatusIcons">
						<mat-icon class="check_circle"
							*ngIf="element.status == 'Completed'">check_circle
						</mat-icon>
						<div *ngIf="element.status == 'Pending'" class="spinner-border spinner_class" role="status" >
							<span class="sr-only">Loading...</span>
						</div>
					</div>
        
        </td>
      </ng-container>
      <ng-container matColumnDef="errorLog">
        <th mat-header-cell *matHeaderCellDef> Error Log </th>
        <td mat-cell *matCellDef="let element">
          <span>
            <mat-icon (click)="getErrorLog(element)" matTooltip="click to view error">preview</mat-icon>
            <!-- <div *ngIf="isLoadTableLoader" class="loaderSpinDiv">
              <div class="spinner-border" role="status">
                <span class="sr-only">Loading...</span>
              </div>
            </div> -->
          </span>
        </td>
      </ng-container>

      <!-- <ng-container matColumnDef="progressBar">
        <th mat-header-cell  *matHeaderCellDef colspan="6" class="progressBarCell">
          <mat-progress-bar mode="indeterminate" *ngIf="loading"></mat-progress-bar>
        </th>
      </ng-container>-->
      <!-- <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true" class="mainHeader"></tr>
      <tr mat-header-row *matHeaderRowDef="['progressBar']; sticky: true" class="progressBarRow" style="max-height: 6px !important;"></tr> -->
      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>
    <div class="dataMessage" *ngIf="dataSource.data.length === 0">
      No Data Found
    </div>
    <mat-paginator [showTotalPages]="5" [pageSize]="10" [pageSizeOptions]="pageSizes"></mat-paginator>
  </div>
  </div>
</mat-card>