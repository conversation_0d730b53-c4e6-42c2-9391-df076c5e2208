.title-center {
  flex: 1 1 auto;
  text-align: center;
}

#refButton{
  float: right; 
  background-color: brown;
}

#outer{
  width:100%;
  text-align: center;
}

.inner{
  display: inline-block;
  padding: 0px 10px 0px 10px;
}

.each{
  padding: 0px 5px;
}

.example-header{
  display: flex;
}

.sndBtn{
  margin-top: 13px !important;
}

.check_circle_error{
  color: #A52A2A;
  font-size: 19px;
  position: absolute;
  margin-top: -10px;
  margin-left: 50px;
}

.check_circle{
  color:green;
  font-size: 18px;
  position: absolute;
  margin-top: -17px;
  margin-left: 62px;
}

.spinner_class{
  position: absolute;
  margin-top: -16px;
  margin-left: 65px;
}

.mainHeader {
  border-bottom: none;
}

// .progressBarRow .progressBarCell {
//   padding: 0;
// }

// .mat-progress-bar {
//   display: block;
//   height: 4px;
//   overflow: hidden;
//   position: relative;
//   // transition: opacity 250ms linear;
//   width: 100%;
// }

.disableLoading{
  visibility: hidden;
}

::ng-deep .mat-progress-bar-buffer.mat-progress-bar-element {
  background: #7f7f7f !important;
}

::ng-deep .mat-progress-bar-fill::after {
  background-color: #006a4e !important;
}