import { AuthService } from '../_services/auth.service';
import { NotificationService } from '../_services/notification.service';
import { Component, OnInit, ViewChild } from '@angular/core';
import { MatDialog, MatPaginator, MatTableDataSource } from '@angular/material';
import { MasterdataupdateService } from '../_services/masterdataupdate.service';
import { UtilsService } from '../_utils/utils.service';
import * as moment from 'moment';
import { FormControl, FormGroup, FormBuilder, Validators  } from '@angular/forms';
import { ReplaySubject, Subject } from 'rxjs';
import {takeUntil } from 'rxjs/operators';
@Component({
  selector: 'app-python-script',
  templateUrl: './python-script.component.html',
  styleUrls: ['./python-script.component.scss', './../../common-dark.scss']
})
export class PythonScriptComponent implements OnInit {
  serverControl = new FormControl();
  clientControl = new FormControl();
  sheetsControl = new FormControl();
  servers :any;
  clients : any = [];
  scenarios : any = [];
  selectedClient ={}
  selectedSheets: any = [];
  pageSizes = []
  user: any;
  displayedColumns: string[];
  dataSource = new MatTableDataSource();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  dbName: any;
  email: any;
  SelectedClinets: any;
  masterDataForm: FormGroup;
  public Bank: any[] = [];
  public clientFilterCtrl: FormControl = new FormControl();
  public clientBanks: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  protected _onDestroy = new Subject<void>();
  fileOutput: any;
  loading = true;
  isLoadTableLoader : boolean = false;
  loadingPercentage = 20;
  tempData: any[];
  constructor(
    private notifyService: NotificationService,
    private auth: AuthService,
    public dialog: MatDialog,
    private utils: UtilsService,
    private fb: FormBuilder,
    private masterDataService: MasterdataupdateService,) {   
      this.user = this.auth.getCurrentUser(); 
      this.masterDataForm = this.fb.group({
        clientsSelection : [null, Validators.required]
      });
      // setTimeout(() => (this.loading = false), 10000);
  }


  applyFilter(event: Event) {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) {
      this.dataSource.paginator.firstPage();
    }
  }
  

  ngOnInit() {
    this.displayedColumns = ['id','client','createTs','category','status','errorLog'];
    this.masterDataUpdateConfig();    
    this.SelectedClinets
  }

  openHelpFile(){
    window.open('../../assets/help.txt', '_blank');
  }

  reset(){
    this.selectedClient = {};
    this.selectedSheets = [];
  }

  masterDataUpdateConfig() {
    let obj = this.user;
    this.masterDataService.masterDataUpdateConfig(obj).subscribe((response: any) => {
      if (response.success) {
        this.servers = response.data.servers
        if(this.user.tenantId == "100000"){
          this.clients = response.data.clients
        }else{
          const clients = response.data.clients.filter(obj => obj.tenantId === this.user.tenantId);
          this.SelectedClinets = clients
          this.masterDataForm.get('clientsSelection').setValue(this.SelectedClinets[0]);
          this.clients = clients ? clients : []
        }
        this.clients.unshift({full : 'All'})
        this.Bank = this.clients
        this.clientBanks.next(this.Bank.slice());
        this.clientFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
            this.vendorfilterBanks();
        });
        this.scenarios = response.data.sheets
        this.dbName = response.data.dbName
        // this.clients.length > 0 ? this.retrieveUpdates() : this.loading = false;
        this.retrieveUpdates();
      }
    });
  }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  retrieveUpdates() {
    this.loading = true;
    let obj = this.user;
    if(this.user.tenantId != "100000"){
      obj['client'] = this.masterDataForm.value.clientsSelection ? this.masterDataForm.value.clientsSelection.full : this.user.role;
    }
    this.masterDataService.retrieveUpdates(obj).subscribe((response: any) => {
      if (response.success) {
        this.dataSource.data = response.data
        this.tempData = this.dataSource.data;
        this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
        this.dataSource.paginator = this.paginator;
        this.loading = false;
      }
    });
  }

  getErrorLog(eleObj:any) {
    // this.isLoadTableLoader = true;
    if (this.selectedSheets.scenario =="Check System Error Log" || eleObj['category'] =="Check System Error Log"){
      let obj = this.user;
      let currentDate = moment();
      let yesterday = currentDate.subtract(1, 'days');
      let formattedDate = yesterday.toISOString().slice(0, 10);
      obj["selectedClientId"]= eleObj['selectedClientId']
      obj["date"]= formattedDate
      this.masterDataService.getSystemErrorLog(obj).subscribe((response: any) => {
        if (response.success) {
          // var downloadLink = document.createElement("a");
          // downloadLink.href = 'data:application/zip;base64,' + response.eFile;
          // downloadLink.download = "system_error_log_"+ eleObj['selectedClientId']+".zip";
          // document.body.appendChild(downloadLink);
          // downloadLink.click();
          // document.body.removeChild(downloadLink);

          var textData = atob(response.eFile); // Decode the Base64-encoded data to text
          const newWindow = window.open('', '_blank');
          if (newWindow) {
            newWindow.document.open();
            newWindow.document.write('<pre>' + textData + '</pre>');
            newWindow.document.close();
          }

        }else{
          this.utils.snackBarShowError(response.message)
        }
      });
    }else{
      let obj = this.user;
      obj["masterDataUpdateId"]= eleObj["id"]
      this.masterDataService.getErrorLog(obj).subscribe((response: any) => {
        if (response.success) {
          // var downloadLink = document.createElement("a");
          // downloadLink.href = 'data:application/txt;base64,' + response.eFile;
          // downloadLink.download = "error_log_"+eleObj["id"]+".txt";
          // document.body.appendChild(downloadLink);
          // downloadLink.click();
          // document.body.removeChild(downloadLink);

          var textData = atob(response.eFile); // Decode the Base64-encoded data to text
          const newWindow = window.open('', '_blank');
          if (newWindow) {
            newWindow.document.open();
            newWindow.document.write('<pre>' + textData + '</pre>');
            newWindow.document.close();
          }
        }else{
          this.utils.snackBarShowError(response.message)
        }
      });
    }
    // this.isLoadTableLoader = false;
  }  

  createJob(){

    if(this.selectedSheets.scenario != "Check System Error Log"){
      let obj = this.user;
      obj["tenantId"]= this.user.tenantId
      obj['selectedClientId'] = this.selectedClient['tenantId']
      obj["email"] = this.user.email
      obj["server"]= this.servers
      obj["dbName"]= this.dbName
      // obj["client"]= this.selectedClient['full']
      obj["client"]= this.masterDataForm.value.clientsSelection.full
      obj["sheets"]= this.selectedSheets.value
      obj["category"] = this.selectedSheets.scenario
      obj["status"] = "Pending"
      this.masterDataService.createUpdateJob(obj).subscribe((response: any) => {
        if (response.success) {
          this.utils.snackBarShowSuccess(response.message)
          this.retrieveUpdates();
        }else{
          this.utils.snackBarShowError(response.message)
        }
      });  
    }
  }

  sendReq(){
    let obj = {}
    obj["tenantId"]= this.user.tenantId
    obj['selectedClientId'] = this.selectedClient['tenantId']
    // obj["client"]= this.selectedClient['full']
    obj["client"]= this.masterDataForm.value.clientsSelection.full
    obj["category"] = this.selectedSheets.scenario
    obj["email"] = this.email
    obj["event"] = "systemErrorLog"
    this.masterDataService.checkSystemLog(obj).subscribe((response: any) => {
      if (response.success) {
        this.utils.snackBarShowSuccess("Updated Successfully")
      }else{
        this.utils.snackBarShowError("Something Went Wrong")
      }
    });  
  }

  protected vendorfilterBanks() {
    if (!this.Bank) {
      return;
    }
    let search = this.clientFilterCtrl.value;
    if (!search) {
      this.clientBanks.next(this.Bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    
    this.clientBanks.next(
      this.Bank.filter(data => data.full.toLowerCase().indexOf(search) > -1)
    );
  }

  selectClient(val){
    if(val.full == 'All'){
      this.dataSource.data = this.tempData
    }else{
      const filteredData = this.tempData.filter(item => item['client'] === val.full);
      this.dataSource.data = filteredData
    }
  }

}
