<div class="title">
  <button mat-raised-button class="button" style="float: left; margin-left: 0px;" (click)="goBack()">
    <mat-icon>keyboard_backspace</mat-icon> Back
  </button>
  <div *ngIf="user.uType === 'restaurant' && !purchaseStatus">
    <div style="float: right;" [matTooltip]="(permission)  ? 'Po approval required' : '' ">
      <div style="float: right;" [matTooltip]="(purOrder.status.orderStatus === 'closed')  ? 'PO closed' : '' ">
    <button mat-button mat-raised-button id="save-btn" class="button3" style="float: right;"
[disabled]="permission || purOrder.status.orderStatus === 'complete' || purOrder.status.orderStatus === 'closed' || isGrnDone || !this.invoiceDate.value || !this.invoiceNum.value"
      (click)="receiveAll()">
      Generate GRN
    </button>
  </div>
</div>
    <button mat-button mat-raised-button id="save-btn" class="button3" style="float: right;"
    (click)="save()" [disabled]="purOrder.status.orderStatus === 'complete' && !editAccess">
      Save
    </button>
    <div style="float: right;" [matTooltip]="(permission)  ? 'Po approval required' : '' ">
      <div style="float: right;" [matTooltip]="showPoEmail  ? '' : 'Vendor email not available'">
        <button mat-button mat-raised-button id="save-btn" class="button" style="float: right;" (click)="poEmail()"
          [disabled]='(!showPoEmail) || permission || purOrder?.purchaseStatus'>
          Send PO Copy
        </button>
      </div>
    </div>
    <div style="float: right;" [matTooltip]="(permission)  ? 'Po approval required' : '' ">
      <button mat-button mat-raised-button id="save-btn" class="button" style="float: right;" (click)="printpdf('Po')"
        [disabled]='permission || purOrder?.purchaseStatus'>
        Print
      </button>
    </div>
    <div *ngIf = 'this.selectedWorkArea.length > 0' style="float: right;" [matTooltip]="(permission)  ? 'Po approval required' : '' ">
      <button mat-button mat-raised-button id="save-btn" class="button" style="float: right;" (click)="printpdf('DetailedPo')"
        [disabled]='permission || purOrder?.purchaseStatus'>
        Detailed Print
      </button>
    </div>
    <button mat-button mat-raised-button id="save-btn" class="button" style="float: right;" (click)="exportToExcel()" [disabled]="purOrder?.purchaseStatus">
      Export
    </button>
  </div>
</div>

<mat-card>
  <div class="row">
    <div class="col">
      <table class="table">
        <tbody>
          <tr>
            <th class="topItemkey" scope="row">PO No.</th>
            <td>{{ purOrder.poId }}</td>
          </tr>
          <tr>
            <th class="topItemkey" scope="row">Status</th>
            <td>{{ purOrder.status.orderStatus }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="col">
      <table class="table">
        <tbody>
          <tr>
            <th class="topItemkey" scope="row">PR No.</th>
            <td>{{ prId ? prId : purOrder.prDetails.prId }}</td>
          </tr>
          <tr>
            <th class="topItemkey" scope="row">Receiving Branch</th>
            <td>{{ restaurantBranch | titlecase }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="col">
      <table class="table">
        <tbody>
          <tr>
            <th class="topItemkey" scope="row">PO Created Date</th>
            <td>{{ this.utils.formatDateToUTC(purOrder.createTs) }}</td>
          </tr>
          <tr>
            <th class="topItemkey" scope="row">Vendor</th>
            <!-- <td>{{ purOrder.hasOwnProperty('vendorDetails') && purOrder.vendorDetails['vendorName'] ? purOrder.vendorDetails['vendorName'] : '-' }}</td> -->
            <td>{{ purOrder.vendorDetails['vendorName'] }}</td>
          </tr>
        </tbody>
      </table>
    </div>

  </div>

  <div class="topItemsInputs">
    <div>
      <mat-form-field *ngIf="this.branches.length > 1" appearance="outline" class="rightInputs m-2" style="padding-right: 10px;">
        <label>Select Location</label>
        <mat-select placeholder="Select Location" [formControl]="locationSelection" (selectionChange)="filterByBranch($event.value)"
        [disabled] = "purOrder.status.orderStatus === 'complete' || purchaseStatus">            
          <mat-option *ngFor="let rest of branches" [value]="rest">
            {{ rest.branchName }}
          </mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field appearance="outline" class="rightInputs m-2" style="width: 230px !important;">
        <mat-label>Add work area for direct indent</mat-label>
        <mat-select multiple placeholder="Select Work Area" (selectionChange)="selectDestinationIndentArea($event.value,'Manual')"
        [(ngModel)]="selectedWorkAreas" [disabled] = "purOrder.status.orderStatus === 'complete' || purchaseStatus">
          <mat-option>
            <ngx-mat-select-search placeholderLabel="Select Work Area..." noEntriesFoundLabel="'no Work Area found'"
              [formControl]="bankFilterCtrl"></ngx-mat-select-search>
          </mat-option>
          <mat-option *ngFor="let area of filteredWorkArea | async" [value]="area">
            {{ area }}
          </mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field appearance="outline" class="rightInputs m-2" style="padding-right: 10px;">
        <mat-label>Goods Received Date</mat-label>
        <input matInput [matDatepicker]="picker2" placeholder="Goods Received Date" [formControl]="documentDate"
          [(ngModel)]="grnDocumentDate" [max]="today"
          [disabled] = "purOrder.status.orderStatus === 'complete' || purchaseStatus" [readonly] = "purOrder.status.orderStatus === 'complete' || purchaseStatus"/>
        <mat-datepicker-toggle matSuffix [for]="picker2">
          <mat-icon matDatepickerToggleIcon>
            <img class="datepickIcon" src="./../../assets/calender.png" />
          </mat-icon>
        </mat-datepicker-toggle>
        <mat-datepicker #picker2></mat-datepicker>
      </mat-form-field>

      <mat-form-field appearance="outline" class="rightInputs m-2" style="padding-right: 10px;">
        <mat-label>Vendor Invoice Date</mat-label>
        <input matInput [matDatepicker]="picker" placeholder="Vendor Invoice Date" [formControl]="invoiceDate"
          [max]="today" [disabled] = "purOrder.status.orderStatus === 'complete' || purchaseStatus" 
          [readonly] = "purOrder.status.orderStatus === 'complete' || purchaseStatus"/>
        <mat-datepicker-toggle matSuffix [for]="picker">
          <mat-icon matDatepickerToggleIcon><img class="datepickIcon" src="./../../assets/calender.png" /></mat-icon>
        </mat-datepicker-toggle>
        <mat-datepicker #picker></mat-datepicker>
      </mat-form-field>

      <mat-form-field appearance="outline" class="rightInputs m-2" style="padding-right: 10px;">
        <mat-label>Invoice Number</mat-label>
        <input matInput  placeholder="Invoice Number" [formControl]="invoiceNum" [(ngModel)]="invoiceNumber"  [readonly] = "purOrder.status.orderStatus === 'complete' || purchaseStatus" [disabled]="purchaseStatus"/>
        <!-- <mat-icon matSuffix  matTooltip="Click to search"  class="invoiceNumSearchIcon" [readonly] = "purOrder.status.orderStatus === 'complete'" [disabled]="purOrder?.purchaseStatus">search</mat-icon> -->
      </mat-form-field>
        <!-- <mat-icon *ngIf ="!existingInvoice && isShowInvoiveNum" class="check_circle " >check_circle
        </mat-icon>
        <mat-icon *ngIf = "existingInvoice && isShowInvoiveNum" class="check_close" matTooltip="Invoice Number Already exists" >close
        </mat-icon> -->
    </div>
  </div>
</mat-card>

<mat-card>
  <div class="fieldboxToggle mt-2 mb-3">

    <mat-form-field appearance="none">
      <!-- <label>Search</label> -->
      <input matInput type="text" class="outline"  placeholder="Search" [(ngModel)]='searchText' (keyup)="doFilter($event.target.value)"/>
      <mat-icon matSuffix (click)="resetForm()" class="closebtn">close</mat-icon>
    </mat-form-field>

    <mat-slide-toggle style="float: right !important;margin-top: 25px !important;" class="ml-2" [(ngModel)]="allExtraFieldFlag"
      (change)="addExtraFields()" [disabled]="purOrder?.purchaseStatus">
      <span *ngIf="purOrder.status.orderStatus === 'complete'">Show Cess/Disct</span>
      <span *ngIf="purOrder.status.orderStatus != 'complete'">Add Cess/Disct</span>
    </mat-slide-toggle>

    <mat-slide-toggle class="ml-3" style="float: right !important;margin-top: 25px !important;" [(ngModel)]="otherChargesFieldFlag"
      (change)="addOtherCharges()" [disabled]="purOrder?.purchaseStatus">
      <span>Show Other Charges</span>
    </mat-slide-toggle>

    <mat-slide-toggle style="float: right !important;margin-top: 25px !important;" class="ml-2" [(ngModel)]="makeZeroOfReceivedQnt"
    (change)="makeZeroOfTableData()" [disabled]="purOrder?.purchaseStatus">
    <span>Make Zero</span>
  </mat-slide-toggle>

  </div>
  <mat-card-content>
  <section class="example-container-1 mat-elevation-z8">
    <table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort>
      <ng-container matColumnDef="select">
        <th mat-header-cell *matHeaderCellDef>
          <mat-checkbox *ngIf="user.uType === 'restaurant'" #selectAll (change)="$event ? masterToggle() : null"
            [checked]="selection.hasValue() && isAllSelected()"
            [indeterminate]="selection.hasValue() && !isAllSelected()" [aria-label]="checkboxLabel()"
            [(ngModel)]="checkSelection">
          </mat-checkbox>
        </th>
        <td mat-cell *matCellDef="let row">
          <mat-checkbox *ngIf="user.uType === 'restaurant'"
            (change)="$event ? selection.toggle(row) : null; getTotal($event)" [checked]="selection.isSelected(row)"
            [aria-label]="checkboxLabel(row)" [disabled]="row.disabled && !editAccess">
          </mat-checkbox>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="index">
        <th mat-header-cell *matHeaderCellDef class="tableId"><b>#</b></th>
        <td mat-cell *matCellDef="let element; let i = index" class="name-cell tableId">
          {{ i + 1 }}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="actionBtns">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Actions</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <button mat-icon-button>
            <mat-icon>done</mat-icon>
          </button>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="itemName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Inventory Item</b>
        </th>
        <td mat-cell *matCellDef="let element">
          {{ element.itemName | titlecase }}
          <div style="font-size: 10px;" *ngIf="element.priceType">
            ( {{element.priceType}} - {{element.priceTypeValue}} )
          </div>
        </td>
        <td mat-footer-cell *matFooterCellDef>Total</td>
      </ng-container>

      <ng-container matColumnDef="itemCode">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Item Code</b>
        </th>
        <td mat-cell *matCellDef="let element">
          {{ element.itemCode | uppercase }}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="hsnCode">
        <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell"><b>Hsn Code</b> </th>
        <td mat-cell *matCellDef="let element" class="name-cell"> {{element.hsnCode}} </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="pkgName">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Pkg Name</b></th>
        <td mat-cell *matCellDef="let element"> {{element.packages[0].packageName | titlecase}} </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="orderQty">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Order Quantity</b>
        </th>
        <td mat-cell *matCellDef="let element">
          {{ element.quantity }}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="itemStatus">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Status</b></th>
        <td mat-cell *matCellDef="let element">
          {{ element.itemStatus }}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="receivedQty">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Total - Received Qty</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <input *ngIf="element.itemStatus != 'complete' && user.uType === 'restaurant'"
            (keyup)="validateReceviedQty(element)" class="input1" type="number" step="0.01" min="0"
            [disabled]="this.selectedWorkArea.length > 0" [(ngModel)]="element.receivedQty"
            (focus)="focusFunctionWithOutForm(element,'receivedQty')" (focusout)="focusOutFunctionWithOutForm(element,'receivedQty')"
            onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)"/>
          <span *ngIf="element.itemStatus == 'complete' || user.uType === 'vendor'">{{ element.receivedQty }}
          </span>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="taxRate">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>tax Rate</b></th>
        <td mat-cell *matCellDef="let element">
          <input *ngIf="(element.itemStatus != 'complete' && editAccess) && user.uType === 'restaurant'"
           class="input1" type="number" step="0.01" min="0" [readonly]="element.priceType"
            [(ngModel)]="element.taxRate" (focus)="focusFunctionWithOutForm(element,'taxRate')" (focusout)="focusOutFunctionWithOutForm(element,'taxRate')" [ngStyle]="{'opacity': (element.priceType) ? '0.5' : '1'}" [disabled]="element.priceType" 
            onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)"/>
            <span *ngIf="element.itemStatus == 'complete' || user.uType === 'vendor'">{{ element.taxRate }}</span>
          <!-- {{element.taxRate}}  -->
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="pendingQty">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Pending Quantity</b>
        </th>
        <td mat-cell *matCellDef="let element">
          {{ this.utils.truncateNew(element.pendingQty) }}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="vendor">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Brand</b></th>
        <td mat-cell *matCellDef="let element">{{ element.brand.name }}</td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="unitPrice">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Unit Cost</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <input *ngIf="(element.itemStatus != 'complete' && editAccess) && user.uType === 'restaurant'" class="input1" type="number" step="0.01" min="0"
            [(ngModel)]="element.packages[0].packagePrice" (keyup)="changeAmounts($event.target.value , element)" [readonly]="element.priceType"
            [ngStyle]="{'opacity': (element.priceType) ? '0.5' : '1'}"
            [disabled]="element.priceType"
            (focus)="focusFunctionWithOutForm(element,'packages')" (focusout)="focusOutFunctionWithOutForm(element,'packages')"
            onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)"/>
            <!-- || element.itemStatus == 'complete' -->
            <span *ngIf="element.itemStatus == 'complete' || user.uType === 'vendor'">{{ this.utils.truncateNew(element.packages[0].packagePrice) }}</span>
          <!-- {{ this.utils.truncateNew(element.packages[0].packagePrice)}}</td> (keyup)="makeZero($event , element)"-->
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="totalValue">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Total (incl.tax,etc)</b></th>
        <td mat-cell *matCellDef="let element">
          {{this.utils.truncateNew(
          ((element.receivedQty * element.packages[0].packagePrice) -
          element.discAmt) + element.cessAmt +element.extraAmt +((element.receivedQty *
          element.packages[0].packagePrice) -
          element.discAmt)* (element.taxRate /100))
          }}
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getTotal())}}</td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="subTotal">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Total (excl.tax)</b></th>
        <td mat-cell *matCellDef="let element">
          {{this.utils.truncateNew(
          ((element.packages[0].packagePrice ) *
          element.receivedQty) )
          }}
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getSubTotal())}}</td>
      </ng-container>

      <ng-container matColumnDef="taxbleValue">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Taxable Amount</b></th>
        <td mat-cell *matCellDef="let element">
          {{this.utils.truncateNew(
          ((element.receivedQty * element.packages[0].packagePrice) -
          element.discAmt))
          }}
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getTaxbleTotal())}}</td>
      </ng-container>


      <ng-container matColumnDef="taxAmt">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Tax Amt</b></th>
        <td mat-cell *matCellDef="let element">
          {{this.utils.truncateNew(
          ((element.taxAmount/element.quantity ) *
          element.receivedQty))
          }}
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getTaxTotal())}}</td>
      </ng-container>

      <ng-container matColumnDef="status">
        <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Unit</b></th>
        <td mat-cell *matCellDef="let element">{{ element.brand.unit }}</td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>
      <ng-container matColumnDef="Action">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b> Action</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <button *ngIf="
              element.itemStatus != 'complete' && user.uType === 'restaurant'
            " class="mat-icon-button" matTooltip="Add or Edit Packaging Sizes" (click)="displayPackages(element)">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
              class="bi bi-pencil-square svgEditIcon" viewBox="0 0 16 16">
              <path
                d="M15.502 1.94a.5.5 0 0 1 0 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 0 1 .707 0l1.293 1.293zm-1.75 2.456-2-2L4.939 9.21a.5.5 0 0 0-.121.196l-.805 2.414a.25.25 0 0 0 .316.316l2.414-.805a.5.5 0 0 0 .196-.12l6.813-6.814z" />
              <path fill-rule="evenodd"
                d="M1 13.5A1.5 1.5 0 0 0 2.5 15h11a1.5 1.5 0 0 0 1.5-1.5v-6a.5.5 0 0 0-1 0v6a.5.5 0 0 1-.5.5h-11a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 .5-.5H9a.5.5 0 0 0 0-1H2.5A1.5 1.5 0 0 0 1 2.5v11z" />
            </svg>
          </button>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="cessAmt">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Cess Amt</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <input *ngIf="element.itemStatus != 'complete' && editAccess" class="input1" type="number" step="any" min="0"
            [(ngModel)]="element.cessAmt"
            onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)" />
          <span *ngIf="(element.itemStatus === 'complete')">{{ element.cessAmt }}</span>
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getFieldTotal('cessAmt'))}}</td>
      </ng-container>

      <ng-container matColumnDef="extraAmt">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Extra Charge</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <input *ngIf="element.itemStatus != 'complete' && editAccess" class="input1" type="number" step="any" min="0"
            [(ngModel)]="element.extraAmt"
            onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)" />
          <span *ngIf="(element.itemStatus === 'complete')">{{ element.extraAmt }}</span>
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getFieldTotal('extraAmt'))}}</td>
      </ng-container>

      <ng-container matColumnDef="discnt">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Discount</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <input *ngIf="element.itemStatus != 'complete' && editAccess" class="input1" type="number" step="any" min="0"
            [(ngModel)]="element.discAmt"
            onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)" />
          <span *ngIf="(element.itemStatus === 'complete')">{{ element.discAmt }}</span>
        </td>
        <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getFieldTotal('discAmt'))}}</td>
      </ng-container>

<!-- 
  <div  *ngIf='this.grn.directIndentAreas'>
    <ng-container  *ngFor="let area of this.grn.directIndentAreas" matColumnDef="{{ area }}">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>  {{ area }}</b>
      </th>
      <td mat-cell *matCellDef="let element">
        <span>
          {{element.splitUp[area]}}
        </span>
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>
  </div> -->

      <ng-container *ngFor="let area of selectedWorkArea" matColumnDef="{{ area }}">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b> {{ area }}</b>
        </th>
        <td mat-cell *matCellDef="let element">
          <div style="float: right;" [matTooltip]="(!element.workAreas.includes(area))  ? 'workArea not mapped for current item' : '' ">
            <input 
              *ngIf="element.itemStatus != 'complete'&& user.uType === 'restaurant'" 
              min="0" 
              type="number"
              class="input1"
              (keypress)="onKeyPress($event)"
              [(ngModel)]="element[area]" 
              (keyup)="receivedQuantityValue($event,area, element)" 
              [disabled]="!element.workAreas.includes(area)"
              [ngClass]="{'disabled-input': !element.workAreas.includes(area)}"
              (focus)="focusFunctionWithOutForm(element,area)" (focusout)="focusOutFunctionWithOutForm(element,area)"
            />
            <span *ngIf="element.itemStatus == 'complete' || user.uType === 'vendor'">{{ element.splitUp[area] }}
            </span>
          </div>
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container  *ngFor="let area of this.split()" matColumnDef="{{ area }}">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>{{ area }}</b>
        </th>
        <td mat-cell *matCellDef="let element">{{ element.splitUp[area] }}</td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <ng-container matColumnDef="description">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>
          <b>Description</b>
        </th>
        <td mat-cell *matCellDef="let element">
          {{ element.itemDescription }}
        </td>
        <td mat-footer-cell *matFooterCellDef></td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky:true;"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns" [ngClass]="{ 'disabled-row': row.disabled && !editAccess }"></tr>
      <tr mat-footer-row *matFooterRowDef="displayedColumns"></tr>
      <div class="dataMessage" *ngIf="dataSource?.data.length == 0"> No Data Available </div>
    </table>
  </section>

    <div *ngIf="otherChargesFieldFlag" class="mt-3 mb-3">
      <div>
        <mat-form-field appearance="none" style="float: right !important; margin-right: 0px !important;">
          <mat-select placeholder="Select Tax" [formControl]="taxCtrl"  
          class="outline" (selectionChange)="addTax($event.value)" multiple>
            <mat-option>      
              <ngx-mat-select-search [formControl]="taxFilterCtrl" placeholderLabel="Select Tax..."></ngx-mat-select-search>  
            </mat-option>    
            <mat-option class="hide-checkbox" (click)="toggleSelectAll()">
              Select All / Deselect All
            </mat-option>           
            <mat-option *ngFor="let tax of filteredTaxArray" [value]="tax">
              {{ tax }}
            </mat-option>
          </mat-select>
        </mat-form-field> 
      </div>
      <br>
        <span class="otherchargeHeading topItemkey">ADD TAX</span>
      <br><br><br>

        <div *ngFor="let tax of otherTaxes; let i = index" style="padding-bottom: 22px !important;">
          <span class="otherchargeHeading topItemkey">{{ tax.taxName }}</span>
          <input matInput class="outline otherTax" [(ngModel)]="tax.value" type="number" step="0.01" min="0"
          (focus)="focusFn(tax)" (focusout)="focusOutFn(tax)" (ngModelChange)="adjustGrandTotal(tax, i)"
          [disabled]="purOrder.status.orderStatus === 'complete' || purOrder.status.orderStatus === 'closed' || isGrnDone"
          onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)"/>
        </div>

        <div>
          <span class="otherchargeHeading topItemkey">Freight/Transportation charges ₹</span>
          <input matInput type="number" min="0" autocomplete="off" class="outline otherTax" (keyup)="grandTotalCalc()"
            [(ngModel)]="otherCharges" placeholder="charges ₹"
            [disabled]="purOrder.status.orderStatus === 'complete' || purOrder.status.orderStatus === 'closed' || isGrnDone"
            (focus)="focusFunction('otherCharges')" (focusout)="focusOutFunction('otherCharges')"
            onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)" />
        </div><br>

        <div>
          <span class="otherchargeHeading topItemkey">Labour charges ₹</span>
          <input matInput type="number" min="0" autocomplete="off" class="outline otherTax"
            (keyup)="grandTotalLabourChargesCalc()" [(ngModel)]="labourCharges" placeholder="charges ₹"
            [disabled]="purOrder.status.orderStatus === 'complete' || purOrder.status.orderStatus === 'closed' || isGrnDone"
            (focus)="focusFunction('labourCharges')" (focusout)="focusOutFunction('labourCharges')"
            onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)" />
        </div><br>

        <div>
          <span class="otherchargeHeading topItemkey">Grand total ₹</span>
          <input matInput class="outline otherTax" [value]="getGrandTotal()" placeholder="Total ₹" disabled />
          <!-- <input *ngIf="purOrder.status.orderStatus === 'complete' || purOrder.status.orderStatus === 'closed' || isGrnDone"
          matInput class="outline othercharge" [(ngModel)]="grn.grandTotal" placeholder="Total ₹" disabled/> -->
        </div>      
    </div>
  </mat-card-content>
</mat-card>



<ng-template #openStatusDialog>
	<button mat-icon-button matDialogClose="yes" matTooltip="Close" class="CloseBtn">
		<mat-icon>close</mat-icon>
	</button>
	<h2 mat-dialog-title>
		<b>Approve Status</b>
	</h2>

	<mat-dialog-content class="mat-typography">
    <div class="col">
      <table class="table">
        <tbody>
          <tr *ngFor="let item of this.purOrder.rejectReason">
            <th class="topItemkey" style="width: 65px !important; background: none;" scope="row">Level</th>
            <td>{{ item.level }}</td>
            <th class="topItemkey" style="width: 65px !important; background: none;" scope="row">Reason</th>
            <td>{{ item.reason }}</td>
          </tr>
        </tbody>
      </table>
    </div>

	</mat-dialog-content>

	<mat-dialog-actions align='center'>
		<div class="reqBtn">
			<button mat-raised-button class="button3 mb-2 mx-2" matDialogClose="yes" (click)="okDialog()">
				Ok
			</button>
		</div>
	</mat-dialog-actions>
</ng-template>