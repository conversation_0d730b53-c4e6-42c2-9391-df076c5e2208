input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}

  // .fieldboxToggle {
  //   float: right;
  // }

  // .dataAndInputs{
  //   display: flex;
  // }

.othercharge{
  width: 120px;
  float: right;
  margin-right: 38px;
}

.otherchargeHeading{
  margin-left: 600px;
}

.otherTax{
  // width: 120px;
  width: 145px;
  float: right;
  margin-right: 33px;
}

.deleteForeverBtn{
  width: 30px;
  height: 38px;
  border-radius: 4px;
  float: right;
  padding-top: 7px;
}

.topInputsFieldsMatCard{
  position: relative;
}

.topInputsFields{
  display: flex;
}

::ng-deep .mat-form-field-appearance-outline .mat-form-field-infix {
  padding: 0.1em 0 0.8em 0 !important;
}

.topItemsInputs{
  display: flex;
  justify-content: space-between;
}

.requiredField {
  color: #ff0000;
}

.sndreqBtn{
  margin-top: 0.8rem!important
}

.commomForIcons{
  // margin-top: -41px;
  // margin-left: 101%;
  // margin-top: 16px;
  // margin-left: 838px;
  margin-top: -48px;
  margin-left: 924px;
}

.check_circle{
  color:green;
  font-size: 27px;
  margin-top: 16px;
}

.check_close{
  color:rgb(255, 8, 8);
  font-size: 27px;
  margin-top: 16px;
}

.dataText{
  font-family: Public Sans;
  font-size: 12px;
}

.CloseBtn{
  float: right;
  margin-bottom: -1px;
}

.example-container-1{
  overflow: auto;
  width: 100%;
  max-height: 300px;
}

.invoiceNumSearchIcon{
  margin-top: 8px;
  margin-right: 8px;
  cursor: grab;
}

.disabled-row {
  opacity: 0.5; /* styling to indicate disabled state */
  pointer-events: none; /* disables pointer events on the row */
}


.disabled-input {
  opacity: 0.6; /* You can adjust the opacity value as needed */
}
