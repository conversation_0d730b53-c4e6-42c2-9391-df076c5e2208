import { Component, OnInit, AfterContentChecked, ChangeDetectorRef, ViewChild } from '@angular/core';
import { ShareDataService, PurchasesService, AuthService, BranchTransferService } from '../_services/';
import { MatTableDataSource } from '@angular/material';
import { SelectionModel } from '@angular/cdk/collections';
import { GlobalsService } from '../_services/globals.service';
import { Location } from '@angular/common';
import { SimpleDialogComponent } from '../_dialogs/simple-dialog/simple-dialog.component';
import { InvoiceDialogComponent } from '../_dialogs/invoice-dialog/invoice-dialog.component';
import { MatDialog } from '@angular/material';
import { UtilsService } from '../_utils/utils.service';
import { PackageDialogComponent } from '../_dialogs/package-dialog/package-dialog.component';
import { FormControl } from '@angular/forms';
import { NotificationService } from '../_services/notification.service'
import { environment } from '../../environments/environment';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material';
import { MomentDateModule, MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS, MAT_MOMENT_DATE_FORMATS } from '@angular/material-moment-adapter';
import { TemplateRef } from '@angular/core';
import { ReplaySubject, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ActivatedRoute, Router, Params } from '@angular/router';
import { first } from 'rxjs/operators';
import { SharedFilterService } from '../_services/shared-filter.service';
export function provideMomentDateAdapter(): any {
  return [
    { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS] },
    { provide: MAT_DATE_FORMATS, useValue: MAT_MOMENT_DATE_FORMATS }
  ];
}


// export const MY_FORMATS = {
//   parse: {
//     dateInput: 'LL'
//   },
//   display: {
//     dateInput: 'DD-MM-YYYY',
//     monthYearLabel: 'YYYY',
//     dateA11yLabel: 'LL',
//     monthYearA11yLabel: 'YYYY'
//   }
// };

@Component({
  selector: 'app-receive-order',
  templateUrl: './receive-order.component.html',
  styleUrls: ['./receive-order.component.scss', "./../../common-dark.scss"],
  providers: [
    {provide: MAT_DATE_LOCALE, useValue: 'en'},
    provideMomentDateAdapter(),
  ],
  // providers: [{ provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
  // { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }
  // ]
})
export class ReceiveOrderComponent implements OnInit, AfterContentChecked {
  isShowInvoiveNum: boolean = false;
  searchText: any;
  existingWorkArea: any;
  selectedWorkArea = [];
  filteredWorkAreasList: any;
  workAreas = [];
  purOrder: any;
  dataSource: MatTableDataSource<any>;
  displayedColumns = [];
  restaurantBranch: any;
  selectedWorkAreas = [];
  dialogRefr: any;
  selection = new SelectionModel<any>(true, []);
  user: any;
  reason: String
  allExtraFieldFlag: any
  otherChargesFieldFlag: boolean = false;
  makeZeroOfReceivedQnt: boolean = false;
  request: boolean = true;
  isGrnDone: boolean = false;
  showRequest: boolean;
  permission: boolean = false;
  invoiceDate = new FormControl();
  documentDate = new FormControl();
  invoiceNum = new FormControl();
  element: any;
  otherCharges: any = 0;
  grnDocumentDate: any;
  today: any;
  grandTotal: number;
  forSelectRole: boolean = true;
  showPoEmail: boolean = false;
  checkSelection: boolean;
  existingInvoice: boolean = true;
  invoiceNumber: any;
  labourCharges: any = 0;
  @ViewChild('openStatusDialog') openStatusDialog: TemplateRef<any>;
  openStatus: boolean = false;
  private specialKeys: Array<string> = ['Backspace', 'Tab', 'End', 'Home', 'ArrowLeft', 'ArrowRight', 'Del', 'Delete'];
  private regex: RegExp = new RegExp(/^\d*\.?\d{0,2}$/g);

  public Bank: any[] = [];
  public VendorBank: any[] = [];
  public bankFilterCtrl: FormControl = new FormControl();
  public filteredWorkArea: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  protected _onDestroy = new Subject<void>();
  private unsubscribe$ = new Subject<void>();
  tempData: any[];
  currentInvoiceId: any;
  zeroData: any[];
  windowCurrentUrl: string;
  purchaseStatus: any;
  prId: any;
  access: string;
  checkAccess: boolean;
  deleteAccess: boolean;
  editAccess: boolean;
  dialogRef: any;
  addItem: number;
  addItems: any[] = [];
  totalAmount: number;
  otherTax: any;
  otherTaxes: any;
  taxArray = ["AROED", "EXCISE DUTY", "MISCELLANEOUS", "TCS", "VAT"];
  taxCtrl = new FormControl();
  taxFilterCtrl = new FormControl();
  filteredTaxArray: any[] = [];
  allSelected: boolean = false;
  todayDate: Date = new Date();
  locationSelection = new FormControl();
  getBranchData: any[];
  branches: any[] = [];
  multiBranchUser; branchSelected: boolean;

  constructor(
    private notifyService: NotificationService,
    private shareData: ShareDataService,
    private location: Location,
    private dialog: MatDialog,
    private router: Router,
    public utils: UtilsService,
    private purchases: PurchasesService,
    private auth: AuthService,
    private cdr: ChangeDetectorRef,
    private activateRoute: ActivatedRoute,
    private sharedFilterService: SharedFilterService,
    private branchTransfer: BranchTransferService
    ) {
      this.user = this.auth.getCurrentUser();
      this.multiBranchUser = this.user.multiBranchUser;
      this.access = sessionStorage.getItem('access');
      let dataArray = this.access.split(',');
      this.checkAccess = dataArray.includes(this.user.role)      
      this.activateRoute.params.subscribe((params: Params) => {
        if (params.purchaseStatus) {
          var windowLocation = window.location.href;
          this.windowCurrentUrl = windowLocation.split('/')[4].split(';')[0]
          this.purchaseStatus = params.purchaseStatus
        } else {
          this.tenantDetail();
        }
      });
  }

  filterWorkArea() {
    this.filteredWorkAreasList = this.workAreas.filter(
      (wa) =>
        wa.toLowerCase().includes(this.searchText.toLowerCase())
    );
  }

  selectDestinationIndentArea(val,action) {    
    val.forEach(area => {
      if (!this.selectedWorkArea.includes(area)) {
          this.selectedWorkArea.push(area); 
      }
    });
    this.selectedWorkArea = this.selectedWorkArea.filter(area => val.includes(area));        
    if (this.allExtraFieldFlag) {
      this.displayedColumns = Object.create(GlobalsService.receivePoColumns.concat(['extraAmt', 'discnt', 'cessAmt']).concat(this.selectedWorkArea));
    }
    else {
      this.displayedColumns = Object.create(GlobalsService.receivePoColumns.concat(this.selectedWorkArea));
    }
    if (this.selectedWorkArea.length > 0) {
      this.dataSource.data.forEach(element => {
          if (element.itemStatus != 'complete' && action == 'Manual') {
          element['splitUp'] = {};
          this.selectedWorkArea.forEach(function (value, i) {
            if (i == 0) {
              if (element['workAreas'].includes(value)) {
                element['splitUp'][value] = element.pendingQty;
                element[value] = element.pendingQty;
                element['receivedQty'] = element.pendingQty
              } else {
                element['splitUp'][value] = 0;
                element[value] = 0;
                element['receivedQty'] = 0;
              }
            }
            else if (i > 0) {
              element['splitUp'][value] = 0;
              element[value] = 0;
              // element['receivedQty'] = element.pendingQty;
            }
            else {
              element['splitUp'][value] = 0
              element[value] = 0
            }
          })
        } else {
            let addedWorkAreas = [] ;
            if (element['splitUp'].hasOwnProperty('STORE') && !this.selectedWorkArea.includes('STORE')) {
              this.selectedWorkArea.push('STORE');
              this.displayedColumns = Object.create(GlobalsService.receivePoColumns.concat(this.selectedWorkArea));
            } 
            this.selectedWorkArea.forEach(function (value, i) {
            let totalSum = 0;
            addedWorkAreas.forEach(wa => {
              if (element.hasOwnProperty(wa)){
                totalSum = totalSum + element[wa]
              }
            });
            if (element['workAreas'].includes(value)) {
              addedWorkAreas.push(value)
              element['splitUp'][value] = element['splitUp'][value] ? element['splitUp'][value] : 0 ;
              if ((element.pendingQty - totalSum) <= element['splitUp'][value]){
                element['splitUp'][value] = element.pendingQty - totalSum;
                element[value] = element.pendingQty - totalSum;
              } else {
                element[value] = element['splitUp'][value];
              }
            } else {
              element['splitUp'][value] = 0;
              element[value] = 0;
              element['receivedQty'] = 0;
            }
          })
        }
      });
    }
  }

  filterByBranch(restId) {        
    const rId = restId.restaurantIdOld;
    this.user.restaurantAccess.forEach(element => {
      if (element.restaurantIdOld == rId) {
        const workAreas = element.workAreas ? ['STORE', ...element.workAreas] : ['STORE'];
        this.workAreas = workAreas;
        this.filteredWorkAreasList = workAreas;
        this.Bank = workAreas;
        this.filteredWorkArea.next(this.Bank.slice());
        this.bankFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
          this.filterBanks();
        });
      }
    });
    if (this.selectedWorkArea){ 
      this.displayedColumns = this.displayedColumns.filter(
        col => !this.selectedWorkArea.includes(col)
      );    
    }    
    this.workAreaMapping(rId);
  }

  ngOnInit() {
    this.shareData.currPurOrder.pipe(first()).subscribe(order => {
            if (!order) {
        this.utils.snackBarShowInfo("data not available")
        this.router.navigate(['/home/<USER>'])
      } else {
        this.purOrder = order; 

        let grn = null;
        if (this.purOrder && Array.isArray(this.purOrder.grnData) && this.purOrder.grnData.length > 0) {
          grn = this.purOrder.grnData[0];
        }
        let invoiceDate = (grn && grn.invoiceDate) ? grn.invoiceDate : this.purOrder.invoiceDate;
        let invoiceNumber = (grn && grn.invoiceId) ? grn.invoiceId : this.purOrder.invoiceNumber;
        let grnDocumentDate = (grn && grn.grnDocumentDate) ? grn.grnDocumentDate : this.purOrder.grnDocumentDate;
        let selectedLocation = (grn && grn.selectedLocation) ? grn.selectedLocation : null;

        this.otherTaxes = this.purOrder.hasOwnProperty('otherTax') && this.purOrder.otherTax ? this.purOrder.otherTax : [];          
        if (this.purOrder.status.orderStatus === "complete") {
          this.displayedColumns = Object.create(GlobalsService.receivePoColumns.concat(this.split()));
        } else {
          this.displayedColumns = Object.create(GlobalsService.receivePoColumns);
        }
        if (this.purOrder.status) {
          if (this.purOrder.status.orderStatus === "complete") {
            this.invoiceDate.setValue(invoiceDate);
            this.invoiceNumber = invoiceNumber;
            this.grnDocumentDate = grnDocumentDate;
            this.locationSelection.setValue(selectedLocation);
          } else {
            this.activateRoute.params.subscribe((params: Params) => {
              if (!params.purchaseStatus) {
                this.shareData.sharedBranchData.pipe(takeUntil(this.unsubscribe$)).subscribe((val) => {
                  this.getBranchData = val;
                  this.branches = this.user.restaurantAccess;
                  const toSelect = this.getBranchData.find(data => data.branchName == this.getBranchData[0].branchName);
                  this.locationSelection.setValue(toSelect);
                  this.filterByBranch(this.locationSelection.value);
                });       
                this.checkPoEmail();
              } 
            });
            let date = new Date();
            date.setHours(0, 0, 0, 0);
            this.today = date ; 
            this.grnDocumentDate = date ; 
          }
        }
        this.purOrder.invoiceDate ? (this.invoiceDate.setValue(this.purOrder.invoiceDate)) : null;
        this.purOrder.invoiceNumber ? (this.invoiceNumber = this.purOrder.invoiceNumber) : null;
        this.otherCharges = this.purOrder.otherCharges ? this.utils.truncateNew(this.purOrder.otherCharges) : 0;
        this.labourCharges = this.purOrder.labourCharges ? this.utils.truncateNew(this.purOrder.labourCharges) : 0;

        if ("poMadeBy" in this.purOrder) {
        } else {
          this.purOrder.poMadeBy = "NA";
        }
        if (this.purOrder != null) {
          if ("mId" in this.purOrder && (!this.purOrder.mId))
            this.router.navigate(['/home/<USER>'])
        }
      }
          });

    if (this.purOrder) {
      this.dataSource = new MatTableDataSource<any>();
      this.processInputData(this.purOrder);
      let totalPrice;
      this.dataSource.data.forEach(element => {
        if (element.pendingQty === 0) {
          element.receivedQty = element.quantity;
        }
        let afterDiscount = 0;
        let tax = 0;
        afterDiscount = (element.receivedQty * element.packages[0].packagePrice) - element.discAmt
        tax = afterDiscount * (element.taxRate / 100)
        element.subTotal = afterDiscount;
        element.taxAmount = tax;
        totalPrice += afterDiscount + element.cessAmt + element.extraAmt + tax;
        totalPrice ? this.grandTotalCalc(totalPrice) : null;
      });
      this.tempData = this.dataSource.data;
      this.otherChargesFieldFlag = this.purOrder.status.orderStatus === 'complete' ? true : false;

      if (Array.isArray(this.purOrder.PrId)) {
        let result = this.purOrder.PrId.join(", ");
        this.prId = result;
      } else {
        this.prId = this.purOrder.PrId;
      }
    }
    this.getGrandTotal();
    this.filteredTaxArray = this.taxArray.slice(); 
    this.taxCtrl.setValue(this.otherTaxes.map(tax => tax.taxName));
    this.taxFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
      this.filterTaxArray();
    });
    }

  workAreaMapping(rId) {
    let obj = {
      poId: this.purOrder.poId,
      restaurantId : rId
    }
    this.purchases.workAreaMapping(obj).subscribe(data => {
      if (data.success) {
        this.dataSource.data.forEach((el) => {
          let workAreaMappings = data.workAreaMapping.find((item) => item.itemCode == el.itemCode && item.packageName == el.packages[0].packageName)
          el['workAreas'] = workAreaMappings ? workAreaMappings['workArea'] : []
        })

      } else {
        this.utils.snackBarShowError(data.message)
      }
      this.existingWorkArea = this.purOrder.selectedWorkArea ? this.purOrder.selectedWorkArea : null;
      this.existingWorkArea ? this.selectDestinationIndentArea(this.existingWorkArea,'Auto') : null;
      this.selectedWorkAreas = this.existingWorkArea ? this.existingWorkArea : [];
      if (this.purOrder.hasOwnProperty('grnData') && this.purOrder.grnData.length > 0 && this.purOrder.grnData[0].directIndentAreas) {
        this.existingWorkArea = (this.purOrder.status.orderStatus === "complete" && this.purOrder.grnData && this.purOrder.grnData[0].directIndentAreas.length > 0) ? this.purOrder.grnData[0].directIndentAreas[0] : null;
      }
    })
  }

  checkPoEmail() {
    let obj = {
      tenantId: this.purOrder.hasOwnProperty('tenantDetails') && this.purOrder.tenantDetails.tId ? this.purOrder.tenantDetails.tId : this.purOrder.tenantId,
      rId: this.purOrder.restaurantId,
      vendorId: this.purOrder.vendorDetails.vendorId
    }
    this.purchases.checkPoEmail(obj).subscribe(data => {
        if (data.hasOwnProperty('branch')){
          this.branches = data['branch'];
          this.locationSelection.setValue(data['branch'][0]);
        }
        if (data.data[0]) {
          this.showPoEmail = (data.data[0].email && data.data[0].email.length > 0) ? true : false;
        }
    })
    this.checkPermission(this.purOrder)
  }


  checkPermission(data) {
    if (data.hasOwnProperty('poApprovalDetail') && Array.isArray(data.poApprovalDetail)) {
      if (data.poApprovalDetail) {
        let approvedObjects = data.poApprovalDetail.filter((el) => {
          return el.status == 'approved'
        })
        this.permission = (approvedObjects.length === data.poApprovalDetail.length) ? false : true;
      }
    } else {
      this.permission = false;
    }
  }

  sumValues(obj) {
    var sum = 0;
    for (var el in obj) {
      if (obj.hasOwnProperty(el)) {
        sum += this.utils.truncateNew(obj[el]);
      }
    }
    if (sum) {
      return sum;
    } else {
      return 0;
    }

  }

  receivedQuantityValue(event , area, element) {
    element['splitUp']['store'] = 0
    element['splitUp'][area] = element[area] ? element[area] : 0
    let overallSum = this.sumValues(element['splitUp']);
    if (overallSum <= element.pendingQty) {
      element.receivedQty = (this.utils.truncateNew(element.receivedQty));
      element.receivedQty = overallSum;
    }
    else if (overallSum > element.pendingQty) {
      element[area] = 0;
      element['splitUp'][area] = 0;
      overallSum = this.sumValues(element['splitUp']);
      element.receivedQty = this.utils.truncateNew(overallSum);
    }
    else {
      element[area] = 0;
      element['splitUp'][area] = 0;
      this.utils.snackBarShowWarning("Please balance the qty")
    }
  }
  
  onKeyPress(event: any) {
    const currentValue: string = event.target.value;
    const pattern = /^\d*\.?\d{0,2}$/;
    const inputChar: string = String.fromCharCode(event.charCode);
    const isDot: boolean = inputChar === '.';
    const hasDot: boolean = currentValue.includes('.');

    // Check if the input is a valid number with up to two decimal places
    if (!pattern.test(currentValue + inputChar) || (isDot && hasDot)) {
        event.preventDefault();
    }
}

  poEmail() {
    this.dialog.open(SimpleDialogComponent, {
      data: {
        title: 'Send Email',
        msg: 'Are you sure you want to send Email ?',
        ok: function () {
          this.purOrder['userEmail'] = this.auth.getCurrentUser().email,
            this.purchases.poEmail(this.purOrder).subscribe((response: any) => {
              if (response.success) {
                this.utils.snackBarShowSuccess(response.message)
              } else {
                this.utils.snackBarShowError(response.message)
              }
            });
        }.bind(this)
      }
    });
  }


  processInputData(purOrder) {
    this.dataSource.data = purOrder.poItems;
    this.restaurantBranch = this.purOrder.restaurantId.split('@')[1]
    this.dataSource.data.forEach(item => {
      if (!item.hasOwnProperty('cessAmt')) {
        item.cessAmt = 0
      }
      if (!('splitUp' in item)) {
        item['splitUp'] = {}
        item['splitUp']['store'] = item.pendingQty
      }
      if (item.hasOwnProperty('receivedQty') && item.itemStatus != 'complete') {
        if (item.hasOwnProperty('savedQty')) {
          if (item['pendingQty'] < item['savedQty']) {
            item.receivedQty = item.pendingQty
          } else {
            item.receivedQty = item.savedQty
          }
        } else {
          item.receivedQty = item.pendingQty
        }
      }
      if (!item.hasOwnProperty('discAmt')) {
        item.discAmt = 0
      }
      if (!item.hasOwnProperty('extraAmt')) {
        item.extraAmt = 0
      }
    });
    this.dataSource.data = [...this.dataSource.data];
    let filteredItems
    let filteredDataSource
    if(!this.editAccess){
      filteredItems = this.dataSource.data.filter((el) => el.itemStatus != 'complete');
    }else{
      filteredItems = this.dataSource.data;
    }
    this.dataSource.data.forEach((el) => {
      el.disabled = (el.itemStatus == 'complete') ? true : false;
    })
    if(!this.editAccess){
      filteredDataSource = this.dataSource.data.filter((el) => (el.disabled == false))
    }else{
      filteredDataSource = this.dataSource.data
    }
    this.checkSelection = (filteredItems.length == filteredDataSource.length) ? true : false;
    this.selection = new SelectionModel<any>(true, filteredItems);
  }

  cancel() {
    this.router.navigate(['/home/<USER>'])
  }

  receiveAll() {
    this.isGrnDone = true;
    this.selection.selected.forEach(element => {
      element.totalPrice = element.subTotal + element.taxAmount;
    });
    if (this.selection.selected.length === 0) {
      this.utils.snackBarShowWarning("You have not selected any items")
    }
    else if (this.utils.getTotal(this.selection.selected, 'receivedQty') <= 0) {
      this.utils.snackBarShowWarning("Received Quantity of selected items is zero")
    }
    else if (!this.invoiceDate.value) {
      this.utils.snackBarShowWarning("Please select invoice date")
    }
    else if (!this.invoiceNum.value) {
      this.utils.snackBarShowWarning("Please enter invoice number")
    }
    else if (!this.documentDate.value) {
      this.utils.snackBarShowWarning("Please select GRN document date")
    }
    else {
      let obj = {
        tenantId: this.auth.getCurrentUser().tenantId,
        vendorId: this.purOrder.vendorDetails.vendorId,
        invoiceId: this.invoiceNumber.replace(/\s+/g, ''),
      }
      this.purchases.getInvoice(obj).subscribe(data => {
        if(this.purOrder['status'].orderStatus === "partial" && ((data.data.length > 0 && data.data[0]['details'].poId) === this.purOrder['poId'])){
          this.save()
          this.receiveAllFn()
        }else{
          data.exist ? (this.utils.snackBarShowWarning('Invoice number already exist'), this.isGrnDone = false) : (this.save(), this.receiveAllFn());
        }
        // data.exist ? (this.utils.snackBarShowWarning('Invoice number already exist'), this.isGrnDone = false) : (this.save(), this.receiveAllFn());
      })
    }
    this.isShowInvoiveNum = false;
  }
  createPackage(obj) {
    this.purchases.addPkge(obj).subscribe(data => {
      return data;
    }, err => console.error(err));
  }

  checkInvoiceDate() {
    if (this.invoiceDate.value) {
      return true
    } else {
      this.utils.snackBarShowWarning("Please select invoice date")
      return false
    }
  }

  displayPackages(data) {
    const dialogRef = this.dialog.open(PackageDialogComponent, {
      height: '400px',
      width: '800px',
      data: data
    });
    dialogRef.afterClosed().subscribe(
      data => {
        let obj = {};
        obj['newPackage'] = data.newPackagingSizes;
        obj['tenantId'] = this.purOrder.tenantDetails.tId;
        obj['vendorId'] = this.purOrder.vendorDetails.vendorId;
        if (Object.keys(this.purOrder.vendorDetails).length === 0) {
          obj['vendorId'] = '';
        }
        let addedPackages = this.createPackage(obj);
        data.packagingSizes = data.packagingSizes.concat(data.newPackagingSizes)
        let dialogReceivedQty = 0;
        let otherPackages = parseInt(data.otherPackages, 10) || 0;
        data.packagingSizes.forEach((item) => {
          item.orderedPackages = parseInt(item.orderedPackages, 10) || 0;
          dialogReceivedQty = dialogReceivedQty + ((item.orderedPackages * item.pkgQty))
        });
        let newQty = this.utils.truncateNew(dialogReceivedQty + otherPackages);
        data.receivedQty = newQty
        this.selection.selected.push(data);
      });
  }

  ngAfterContentChecked(): void {
    this.cdr.detectChanges();
  }

  emailToVendor() {
    this.purOrder['emailToVendor'] = true;
    this.purchases.printpdfs(this.purOrder, 'Po').subscribe(data => {
      if (data['success']) {
        this.utils.snackBarShowSuccess(data['message'])
      } else {
        this.utils.snackBarShowError(data['message'])
      }
    });
  }

  receiveAllFn() {
    this.sharedFilterService.getFilteredPurOrder['_value'] = ''
    let totalPrice = 0;
    this.dataSource.data.forEach(element => {
      let afterDiscount = 0;
      let tax = 0;
      afterDiscount = (element.receivedQty * element.packages[0].packagePrice) - element.discAmt
      tax = afterDiscount * (element.taxRate / 100)
      element.subTotal = afterDiscount;
      element.taxAmount = tax;
      totalPrice += afterDiscount + element.cessAmt + element.extraAmt + tax;
    });

    this.totalAmount = this.utils.truncateNew(this.otherCharges + this.labourCharges + totalPrice)
    let hasPartiallyReceived = false;
    this.selection.selected.forEach(selection => {
      const { pendingQty, receivedQty } = selection;
      selection.pendingQty = selection.quantity - receivedQty;
      selection.itemStatus = receivedQty > 0 ? (selection.pendingQty === 0 ? 'complete' : 'partial') : selection.itemStatus;

      if (!hasPartiallyReceived && pendingQty !== receivedQty && pendingQty !== 0 && receivedQty >= 0) {
        hasPartiallyReceived = true;
      }
    });
    if (hasPartiallyReceived) {
      this.utils.snackBarShowWarning("Items have been partially received. Please proceed with QDN process");
      this.purOrder.status.orderStatus = 'Partially Received';
    } else {
      this.purOrder.status.orderStatus = 'close';
    }

    if (this.purOrder.status.orderStatus != 'pending') {
      const currentUser = this.auth.getCurrentUser();
      const grnReq: any = {
        poId: this.purOrder.poId,
        uId: currentUser.mId,
        items: this.selection.selected,
        mId: this.purOrder.mId,
        InvNo: this.invoiceNum.value.replace(/\s+/g, ''),
        invoiceDate: this.utils.dateCorrection(this.invoiceDate.value),
        grnDocumentDate: this.utils.dateCorrection(this.documentDate.value),
        extraFieldFlag: this.allExtraFieldFlag,
        vId: this.purOrder.vendorDetails.vendorId,
        tId: this.purOrder.tenantDetails.tId,
        restaurantId:  this.purOrder.restaurantId,
        tenantId: this.purOrder.tenantDetails.tId,
        userEmail: currentUser.email,
        baseUrl: environment.baseUrl,
        grandTotal: this.grandTotal,
        otherCharges: this.otherCharges,
        labourCharges: this.labourCharges,
        destination: this.locationSelection.value ? this.locationSelection.value.restaurantIdOld : undefined,
        directIndentAreas: this.selectedWorkArea.length > 0 ? this.selectedWorkArea : undefined,
        poMadeBy: 'poMadeBy' in this.purOrder ? this.purOrder.poMadeBy : 'NA',
        grnMadeBy: 'userName' in currentUser ? currentUser.userName : 'NA',
        isContract: 'isContract' in this.purOrder ? this.purOrder.isContract : undefined,
        remarks: 'remarks' in this.purOrder ? this.purOrder.remarks : null,
        paymentTerms: 'paymentTerms' in this.purOrder ? this.purOrder.paymentTerms : null,
        poTerms: 'poTerms' in this.purOrder ? this.purOrder.poTerms : null,
        paymentMethod: 'paymentMethod' in this.purOrder ? this.purOrder.paymentMethod : null
      };
      if (this.otherTaxes != null && this.otherTaxes != undefined){   
        const otherTaxNames = [...this.otherTaxes];
        grnReq.otherTax =  otherTaxNames
      }
      this.purchases.createGrn(grnReq).subscribe(data => {
        this.isGrnDone = true;
                if (data['status'] == 'success') {
          if (data.hasOwnProperty('data')) {
            this.utils.snackBarShowError("Invoice number already exists for this vendor")
            this.isGrnDone = false;
          }
          if (data.hasOwnProperty('skippedItemList')) {
            this.utils.snackBarShowInfo('InStock update has been skipped for these items.' + data['skippedItemList'])
          }
          if (data.newGrn != null || data.newGrn != undefined) {
            this.generateInvoice(data.newGrn)
          }
          let stockConversionItems = this.selection.selected.filter((el) => el.stockConversion == true)
          stockConversionItems.length > 0 ? this.utils.snackBarShowInfo('Convertible stock items found.') : undefined;
        } else {
          this.utils.snackBarShowError(data['message'])
        }
      }, err => console.error(err))
    }
  }

  receiveItem(item) {
    this.dialog.open(SimpleDialogComponent, {
      data: {
        title: 'Receive Item',
        msg: 'Receive item?',
        ok: function () {
        }.bind(this)
      }
    });

  }

  generateInvoice(data) {    
    this.router.navigate(['/home'])
    this.dialog.open(InvoiceDialogComponent, {
      data: {
        id: data.grnId,
        title: 'GRN',
        items: data.grnItems,
        labourCharges: parseInt(data.labourCharges),
        otherCharges: parseInt(data.otherCharges),
        otherTax: data.otherTax,
        grandTotal: data.grandTotal,
        stockConversion: data.stockConversion,
        order: {
          id: data.details.poId,
          InvNo: data.invoiceId
        },
        ok: function () {
          window.location.reload();
        }.bind(this),
      }
    });
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    let filteredData = this.dataSource.data.filter((el) => (el.disabled == false))
    const numRows = filteredData.length;
    return numSelected === numRows;
  }

  /** Selects all rows if they are not all selected; otherwise clear selection. */
  masterToggle() {
    this.isAllSelected() ?
      this.selection.clear() :
      this.dataSource.data.forEach(row => {
        if (!row.disabled) {
          this.selection.select(row);
        }
      })
  }

  checkboxLabel(row?: any): string {
    
    if (!row) {
      return `${this.isAllSelected() ? 'select' : 'deselect'} all`;
    }
    return `${this.selection.isSelected(row) ? 'deselect' : 'select'} row ${row.itemCode + 1}`;
  }

  validateReceviedQty(element) {
    element.pendingQty < this.utils.truncateNew(element.receivedQty) ? element.receivedQty = this.utils.truncateNew(element.pendingQty) : element;
  }



  printpdf(action) {
    if (this.otherCharges != undefined) {
      this.purOrder['otherCharges'] = this.otherCharges;
    } else {
      this.purOrder['otherCharges'] = 0;
    }
    if (this.labourCharges != undefined) {
      this.purOrder['labourCharges'] = this.labourCharges
    } else {
      this.purOrder['labourCharges'] = 0;
    }
    this.purchases.printpdfs(this.purOrder, action).subscribe(data => {
      // window.open('data:application/pdf;base64,' + data.eFile);
      this.purchases.globalPrintPdf(data.eFile);
    });
  }

  exportToExcel() {
    this.purchases.exportToExcel(this.purOrder, 'Po').subscribe(data => {
      window.open('data:text/csv;base64,' + data.eFile);
    });
  }

  goBack() {
    if (this.purchaseStatus) {
      this.location.back();
    } else {
      this.router.navigate(['/home/<USER>'])
    }
  }

  getTotal() {
    let totalPrice = 0
    if (this.purOrder.status.orderStatus != 'open') {
      this.dataSource.data.forEach(element => {
        let afterDiscount = 0;
        let tax = 0;
        afterDiscount = (element.receivedQty * element.packages[0].packagePrice) - element.discAmt
        tax = afterDiscount * (element.taxRate / 100)
        element.subTotal = afterDiscount;
        element.taxAmount = tax;
        totalPrice += afterDiscount + element.cessAmt + element.extraAmt + tax;
      });
    }
    else {
      this.selection.selected.forEach(element => {
        let afterDiscount = 0;
        let tax = 0;
        afterDiscount = (element.receivedQty * element.packages[0].packagePrice) - element.discAmt
        tax = afterDiscount * (element.taxRate / 100)
        element.subTotal = afterDiscount;
        element.taxAmount = tax;
        totalPrice += afterDiscount + element.cessAmt + element.extraAmt + tax;
      });
    }
    totalPrice ? this.grandTotalCalc(totalPrice) : null;
    return totalPrice
  }

  changeAmounts(value , element){

    this.selection.selected.forEach(element => {
      let afterDiscount = 0;
      let tax = 0;
      let totalPrice = 0
      afterDiscount = (element.receivedQty * element.packages[0].packagePrice) - element.discAmt
      tax = afterDiscount * (element.taxRate / 100)
      element.subTotal = afterDiscount;
      element.taxAmount = tax;
      totalPrice += afterDiscount + element.cessAmt + element.extraAmt + tax;
      element.totalPrice = totalPrice
      element.unitPrice = element.packages[0].packagePrice
    });
  }


  getTaxbleTotal() {
    let taxableSum = 0
    if (this.purOrder.status.orderStatus != 'open') {
      this.dataSource.data.forEach(element => {
        taxableSum += (element.receivedQty * element.packages[0].packagePrice) - element.discAmt
      });
    }
    else {
      this.selection.selected.forEach(element => {
        taxableSum += (element.receivedQty * element.packages[0].packagePrice) - element.discAmt
      });
    }
    return taxableSum
  }

  getSubTotal() {
    let subTotalSum = 0
    if (this.purOrder.status.orderStatus != 'open') {
      this.dataSource.data.forEach(element => {
        subTotalSum += element.receivedQty * element.packages[0].packagePrice
      });
    }
    else {
      this.selection.selected.forEach(element => {
        subTotalSum += element.receivedQty * element.packages[0].packagePrice
      });
    }

    return subTotalSum
  }

  getTaxTotal() {
    let taxTotal = 0
    if (this.purOrder.status.orderStatus != 'open') {
      this.dataSource.data.forEach(element => {
        let afterDiscount = 0;
        let tax = 0;
        afterDiscount = (element.receivedQty * element.packages[0].packagePrice) - element.discAmt
        tax = afterDiscount * (element.taxRate / 100)
        element.subTotal = afterDiscount;
        element.taxAmount = tax;
        taxTotal += element.receivedQty * (element.taxAmount / element.quantity)
      });
    }
    else {
      this.selection.selected.forEach(element => {
        let afterDiscount = 0;
        let tax = 0;
        afterDiscount = (element.receivedQty * element.packages[0].packagePrice) - element.discAmt
        tax = afterDiscount * (element.taxRate / 100)
        element.subTotal = afterDiscount;
        element.taxAmount = tax;
        taxTotal += element.receivedQty * (element.taxAmount / element.quantity)
      });
    }

    return taxTotal
  }

  addExtraFields() {
    if (this.allExtraFieldFlag) {
      this.displayedColumns.splice(10, 0, 'extraAmt', 'discnt', 'cessAmt',)
    } else {
      this.displayedColumns = Object.create(GlobalsService.receivePoColumns.concat(this.selectedWorkArea));
    }
  }

  makeZeroOfTableData() {
    let data = this.tempData;
    this.makeZeroOfReceivedQnt = this.makeZeroOfReceivedQnt;
    if (this.makeZeroOfReceivedQnt == true) {
      this.zeroData = data.map((obj) => {
        const newObj = { ...obj, receivedQty: 0 };
        for (const column of this.selectedWorkAreas) {
          if (obj.workAreas.includes(column)) {
            newObj[column] = 0;
            newObj['splitUp'][column] = 0;
          }
        }
        return newObj;
      });
      this.dataSource.data = this.zeroData;
    } else {
      const differentReceivedQtyObjects = this.zeroData.filter(obj => obj.receivedQty > 0);
      const itemCodesToRemove = differentReceivedQtyObjects.map(item => item.itemCode);
      this.tempData = this.tempData.filter(item => !itemCodesToRemove.includes(item.itemCode));
      this.tempData = this.tempData.concat(differentReceivedQtyObjects);
      this.dataSource.data = this.tempData;
      this.zeroData = [];
    }
    this.selection = new SelectionModel<any>(true, this.dataSource.data);
  }

  getFieldTotal(key: string) {
    if (this.purOrder.status.orderStatus != 'open')
      return this.utils.getTotal(this.dataSource.data, key);
    else
      return this.utils.getTotal(this.selection.selected, key);
  }

  addOtherCharges() {
    this.otherChargesFieldFlag = this.otherChargesFieldFlag
  }

  grandTotalCalc(total) {
    let totalPrice = total
    this.totalAmount = this.utils.truncateNew(this.otherCharges + this.labourCharges + totalPrice)
  }

  grandTotalLabourChargesCalc() {
    let totalPrice = 0
    this.dataSource.data.forEach(element => {
      let afterDiscount = 0;
      let tax = 0;
      afterDiscount = (element.receivedQty * element.packages[0].packagePrice) - element.discAmt
      tax = afterDiscount * (element.taxRate / 100)
      element.subTotal = afterDiscount;
      element.taxAmount = tax;
      totalPrice += afterDiscount + element.cessAmt + element.extraAmt + tax;
    });
    this.totalAmount = this.utils.truncateNew(this.otherCharges + this.labourCharges + totalPrice)
  }

  openFilter() {
    if (this.openStatus == false) {
      this.openStatus = true;
    } else {
      this.openStatus = false;
    }

    let dialogRefTemplate = this.dialog.open(this.openStatusDialog);
    dialogRefTemplate.afterClosed().subscribe(result => {
    })
  }

  okDialog() {
    this.openStatus = false;
  }

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  protected filterBanks() {
    if (!this.Bank) {
      return;
    }
    let search = this.bankFilterCtrl.value;
    if (!search) {
      this.filteredWorkArea.next(this.Bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.filteredWorkArea.next(
      this.Bank.filter(bank => bank.toLowerCase().indexOf(search) > -1)
    );
  }

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }

  resetForm() {
    this.searchText = ''
    this.dataSource = new MatTableDataSource<any>();
    if (this.zeroData.length > 0) {
      this.dataSource.data = this.zeroData;
    } else {
      this.dataSource.data = this.tempData;
    }
  }

  save() {
    let obj = {
      tenantId: this.purOrder.tenantDetails.tId,
      restaurantId:  this.purOrder.restaurantId,
      items: this.selection.selected,
      poId: this.purOrder.poId,
      InvNo: this.invoiceNum.value.replace(/\s+/g, ''),
      invoiceDate:this.invoiceDate.value ?  this.utils.dateCorrection(this.invoiceDate.value) : undefined,
      grnDocumentDate:this.documentDate.value ?  this.utils.dateCorrection(this.documentDate.value) : undefined,
    }
    this.purchases.savePurchaseOrder(obj).subscribe(data => {
      data.result ? this.utils.snackBarShowSuccess(data.message) : this.utils.snackBarShowError(data.message)
    })
  }

  split() {
    if (this.purOrder.status.orderStatus === "complete") {
      return Object.keys(this.purOrder.poItems[0].splitUp)
    }
  }

  focusFunction(data){
    if(Number(this[data]) === 0){
      this[data] = null;
    }
  }
  
  focusOutFunction(data){
    if(this[data] === null){
      this[data] = 0
    }
  }

  focusFunctionWithOutForm(element , value){
    if(value === 'packages'){
      if(element['packages'][0].packagePrice === 0){
        element['packages'][0].packagePrice  = null;
      }
    }
    else {
      if (Number(element[value]) === 0){
        element[value] = null;
      }
    }
   
  }
  
  focusOutFunctionWithOutForm(element , value){
    if(value === 'packages'){
      if(element['packages'][0].packagePrice === null){
        element['packages'][0].packagePrice  = 0;
      }
    }
    else {
      if(element[value] === null){
        element[value] = 0
      }
    }
  }

  tenantDetail() {
    let obj = {
      tenantId: this.user.tenantId
    }
    this.branchTransfer.tenantDetails(obj).subscribe(res => {
      if (res.result == 'success') {
        let tenantDetails = res.data[0].permission 
        let deleteAccess = res.data[0] && res.data[0].permission && res.data[0].permission.POAccess ? res.data[0].permission.POAccess.delete : false;
        let editAccess = res.data[0] && res.data[0].permission && res.data[0].permission.POAccess ? res.data[0].permission.POAccess.edit : false;
        if (deleteAccess) {
          let rolesList = (res.data[0] && res.data[0].permission && res.data[0].permission.POAccess) ? res.data[0].permission.POAccess.deleteAccess : [];
          this.deleteAccess = (rolesList.some(role => role.toLowerCase() === this.user.role.toLowerCase())) ? true : false ;
        } else {
          this.deleteAccess = false ;
        }
        if (editAccess) {
          let rolesList = (res.data[0] && res.data[0].permission && res.data[0].permission.POAccess) ? res.data[0].permission.POAccess.editAccess : [];
          this.editAccess = (rolesList.some(role => role.toLowerCase() === this.user.role.toLowerCase())) ? true : false ;
        } else {
          this.editAccess = false ;
        }
      } else {
        this.deleteAccess = false ;
        this.editAccess = false ;
      }
    });
  }

  focusFn(tax: any){
    if(Number(tax.value) === 0){
      tax.value = null;
    }
  }
  
  focusOutFn(tax: any){
    if (tax.value < 0) {
      tax.value = 0;
    }
    if(tax.value === null){
      tax.value = 0
    }
  }

  getGrandTotal() {
    let itemTotal = this.getTotal()
    let taxTotal =  this.otherTaxes.reduce((acc, item) => acc + item.value, 0);  
    this.grandTotal =  (this.utils.truncateNew(itemTotal) + this.utils.truncateNew(taxTotal) + this.otherCharges + this.labourCharges);  
    return this.utils.truncateNew(this.grandTotal)
  }

  adjustGrandTotal(tax: any, index: number) {    
    const previousTaxValue = parseFloat(this.otherTaxes[index].oldValue) || 0;
    const newTaxValue = parseFloat(tax.value) || 0;  
    this.grandTotal += (newTaxValue - previousTaxValue);  
    this.otherTaxes[index].oldValue = newTaxValue;
    this.getGrandTotal();
  }

  private filterTaxArray() {
    const search = this.taxFilterCtrl.value ? this.taxFilterCtrl.value.toLowerCase() : '';
    this.filteredTaxArray = this.taxArray.filter(tax => tax.toLowerCase().includes(search));
  }

  toggleSelectAll() {
    this.allSelected = !this.allSelected;
    if (this.allSelected) {
      this.taxCtrl.setValue(this.taxArray);
    } else {
      this.taxCtrl.setValue([]);
    }
    this.addTax(this.taxCtrl.value || []);  
    this.cdr.detectChanges();
  }

  addTax(selectedTaxes: string[]) {
    const updatedTaxes = [];  
    selectedTaxes.forEach((tax) => {
      const existingTax = this.otherTaxes.find(item => item.taxName === tax);
      if (existingTax) {
        updatedTaxes.push(existingTax);  
      } else {
        updatedTaxes.push({ taxName: tax, value: 0 });
      }
    });  
    this.otherTaxes = updatedTaxes;
  }

}