<mat-card>
  <div class="infoMessage ">
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
      class="bi bi-info-circle-fill infoSvgIcon" viewBox="0 0 16 16">
      <path
        d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm.93-9.412-1 4.705c-.07.34.029.533.304.533.194 0 .487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703 0-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381 2.29-.287zM8 5.5a1 1 0 1 1 0-2 1 1 0 0 1 0 2z" />
    </svg>
    <p class="ml-2 mb-0"> Hey, the list contains items that are mapped to the source workArea</p>
  </div>

  <div class="row topInputs">
    <div>
      <form [formGroup]="intraBranchForm">
        <mat-form-field *ngIf="multiBranchUser" appearance="none">
          <label class="title-palce">Select Branch</label>
          <mat-select placeholder="Select Branch" class="outline" formControlName="branchSelection"
            (selectionChange)="filterByBranch($event.value)">
            <mat-option *ngFor="let rest of branches" [value]="rest">
              {{ rest.branchName }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </form>
    </div>

    <div>
      <mat-form-field appearance="none" style="margin-left: 10px;"
        *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser">
        <label>Source Work Area</label>
        <mat-select placeholder="Select Work Area" (selectionChange)="selectSourceIndentArea($event)" class="outline">
          <mat-option>
            <ngx-mat-select-search placeholderLabel="Select Source Work Area..."
              noEntriesFoundLabel="'no Source Work Area found'"
              [formControl]="sourceBankFilterCtrl"></ngx-mat-select-search>
          </mat-option>
          <mat-option *ngFor="let area of sourceFilteredWorkArea | async" [value]="area"
            [disabled]="area == destinationIndentArea">
            {{ area }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </div>

    <div class="d-flex align-items-center">
        <mat-form-field *ngIf="isShow" appearance="none" style="margin-left: 10px;">
          <label>Document Date</label>
          <input matInput class="outline" [matDatepicker]="picker2" [formControl]="documentDate"
            placeholder="Document Date"  [max]="today" />
          <mat-datepicker-toggle matSuffix [for]="picker2">
            <mat-icon matDatepickerToggleIcon>
              <img src="./../../assets/calender.png" />
            </mat-icon>
          </mat-datepicker-toggle>
          <mat-datepicker #picker2></mat-datepicker>
        </mat-form-field>
    </div>


    <div class="grep" *ngIf="isShow">
      <mat-slide-toggle class="mr-2" *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser"
        [(ngModel)]="transferWorkAreaPreview" (change)="preview()">Preview
      </mat-slide-toggle>

      <button mat-raised-button *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" class="button"
        (click)="transferCheck()">
        Transfer
      </button>
    </div>

  </div>
</mat-card>

<div *ngIf="((branchSelected && multiBranchUser) || !multiBranchUser) && isShow" class="datacontainer">
  <mat-card>
    <mat-card-content>
      <div class="search-table-input">
        <mat-form-field appearance="none">
          <label>Search</label>
          <input matInput type="text" (keyup)="doFilter($event.target.value)" placeholder="Search" autocomplete="off"
            class="outline" [(ngModel)]='searchText' />
          <mat-icon matSuffix (click)="clearFilter()" class="closebtn">close</mat-icon>
        </mat-form-field>

        <mat-form-field id="branch-select" appearance="none">
          <label>Item Type</label>
          <mat-select placeholder="Item Type" [formControl]="ItemType" class="outline">
            <mat-option *ngFor="let itemType of ItemTypeList" [value]="itemType" (click)="selectItemType(itemType)">
              {{ itemType | titlecase }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="none">
          <label>Category</label>
          <mat-select placeholder="Category" [formControl]="Category" class="outline">
            <mat-option *ngFor="let cat of categoryList" [value]="cat" (click)="selectCategory(cat)">
              {{ cat | titlecase }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="none">
          <label>Sub Category</label>
          <mat-select placeholder="Subcategory" [formControl]="Subcategory" class="outline">
            <mat-option *ngFor="let subCat of subCategoryList" [value]="subCat" (click)="selectSubCat(subCat)">
              {{ subCat | titlecase }}
            </mat-option>
          </mat-select>
        </mat-form-field>

      </div>
      <div class="table-responsive">
        <table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort>
          <ng-container matColumnDef="itemName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Item Name</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.itemName | titlecase }}
            </td>
            <td mat-footer-cell *matFooterCellDef > Total </td>
          </ng-container>

          <ng-container matColumnDef="index">
            <th mat-header-cell *matHeaderCellDef mat-sort-header class="tableId"><b>#</b></th>
            <td mat-cell *matCellDef="let element; let i = index" class="tableId">
              {{ i + 1 + paginator.pageIndex * paginator.pageSize }}
            </td>
            <td mat-footer-cell *matFooterCellDef >  </td>
          </ng-container>

          <ng-container matColumnDef="pkgName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Pkg Name</b>
            </th>
            <td mat-cell *matCellDef="let element">
              <ng-container *ngIf='element.packageName != null'>
                {{element.packageName | titlecase}}
              </ng-container>
              <ng-container *ngIf='element.packageName == null'>
                {{ element.uom | titlecase}}
              </ng-container>
            </td>
            <td mat-footer-cell *matFooterCellDef >  </td>
          </ng-container>

          <ng-container matColumnDef="entryType">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Entry Type</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.entryType | titlecase }}
            </td>
            <td mat-footer-cell *matFooterCellDef >  </td>
          </ng-container>

          <ng-container matColumnDef="totalPrice">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Total Price</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ this.utils.truncateNew(element.withTaxPrice * element.transferQty) }}
            </td>
            <td mat-footer-cell *matFooterCellDef > {{ getTotalPrice() }} </td>
          </ng-container>

          <ng-container matColumnDef="UnitPrice">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>WAC(incl.tax,etc)</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ this.utils.truncateNew(element.withTaxPrice,2 ) }}
            </td>
            <td mat-footer-cell *matFooterCellDef >  </td>
          </ng-container>

          <ng-container matColumnDef="uom">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> UOM</b>
            </th>
            <td mat-cell *matCellDef="let element">{{ element.uom }}</td>
            <td mat-footer-cell *matFooterCellDef >  </td>
          </ng-container>

          <ng-container matColumnDef="transferQty">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Transfer Qty</b>
            </th>
            <td mat-cell *matCellDef="let element">
              <input class="input1" type="number" step="0.01" min="0"
                [(ngModel)]="element.transferQty" (focus)="focusFunctionWithOutForm(element)" (focusout)="focusOutFunctionWithOutForm(element)" (keyup)="checkStock($event.target.value,element)"
                />
            </td>
            <td mat-footer-cell *matFooterCellDef >  </td>
          </ng-container>

          <ng-container *ngFor="let area of indentAreas" matColumnDef="{{ area }}">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> {{ area }}</b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ this.utils.truncateNew(getIndentAreaVal(element.workArea, area)) }}
            </td>
            <td mat-footer-cell *matFooterCellDef >  </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky : true"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
          <tr mat-footer-row *matFooterRowDef="displayedColumns" [class.hidden]="!showFooter" ></tr>
        </table>
        <div class="dataMessage" *ngIf="dataSource?.data.length == 0"> No Data Available </div>
      </div>
    </mat-card-content>
  </mat-card>
  <mat-paginator [showTotalPages]="5" [pageSize]="10" [pageSizeOptions]="pageSizes"></mat-paginator>
</div>