import { Component, OnInit, ViewChild } from '@angular/core';
import { AuthService, PurchasesService, BranchTransferService, ShareDataService } from '../_services';
import { UtilsService } from '../_utils/utils.service';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE, MatDialog } from '@angular/material';
import { MatTableDataSource, MatPaginator, MatSort } from '@angular/material';
import { SimpleDialogComponent } from '../_dialogs/simple-dialog/simple-dialog.component';
import { NotificationService } from '../_services/notification.service';
import { FormBuilder, FormControl, FormGroup, Validators } from "@angular/forms";
import { ReplaySubject, Subject } from 'rxjs';
import {takeUntil } from 'rxjs/operators';
import { MomentDateAdapter } from '@angular/material-moment-adapter';
export const MY_FORMATS = {
  parse: {
      dateInput: 'LL'
  },
  display: {
      dateInput: 'DD-MM-YYYY',
      monthYearLabel: 'YYYY',
      dateA11yLabel: 'LL',
      monthYearA11yLabel: 'YYYY'
  }
};

@Component({
  selector: 'app-return-to-store',
  templateUrl: './return-to-store.component.html',
  styleUrls: ['./return-to-store.component.scss', './../../common-dark.scss'],
  providers:[{ provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
  { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }]
})
export class ReturnToStoreComponent implements OnInit {
  all = "ALL";
  curDataSource: any[];
  transferWorkAreaPreview: boolean = false;
  searchValue: any = ''
  categoryList = ['All'];
  subCategoryList = ['All'];
  documentDate = new FormControl();
  ItemTypeList = ['All'];
  initCategoryList = [];
  initSubCategoryList = [];
  workAreasColumn = [];
  subCatList = [];
  title: String = 'Inventory List';
  filterKeys = { ItemType: 'All', category: 'All', subCategory: 'All' }
  indentAreas: string[] = [];
  user: any;
  inventoryItems: any[] =[];
  displayedColumns: any[];
  dataSource: MatTableDataSource<any>;
  showFooter: boolean = false;
  categories: any[];
  category = new FormControl('', [Validators.required]);
  pageSizes = []
  ItemType = new FormControl();
  Category = new FormControl();
  Subcategory = new FormControl();
  restaurantId: any;
  searchText: any;
  filteredWorkAreasList: any;
  filteredDestinationWorkAreasList: any;
  multiBranchUser; branchSelected: boolean;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  sourceIndentArea: any;
  destinationIndentArea: any;
  branches: any[];
  getBranchData: any[]
  intraBranchForm: FormGroup;
  searchFilterWorkAreaText: any;
  searchFilterDestinationWorkAreaText: any;
  showTableDatas: boolean = false;
  private specialKeys: Array<string> = ['Backspace', 'Tab', 'End', 'Home', 'ArrowLeft', 'ArrowRight', 'Del', 'Delete'];
  private regex: RegExp = new RegExp(/^\d*\.?\d{0,2}$/g);
  isShow : boolean = false;

  public Bank: any[] = [];
  public VendorBank: any[] = [];
  public sourceBankFilterCtrl: FormControl = new FormControl();
  public destinationBankFilterCtrl: FormControl = new FormControl();
  public sourceFilteredWorkArea: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public destinationFilteredWorkArea: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  protected _onDestroy = new Subject<void>();
  today: Date;
  
  constructor(
    private notifyService: NotificationService,
    private auth: AuthService, private purchases: PurchasesService,
    private branchTransfer: BranchTransferService,
    private dialog: MatDialog, private utils: UtilsService,
    private sharedData: ShareDataService,
    private fb: FormBuilder) {
    this.user = this.auth.getCurrentUser()
    this.intraBranchForm = this.fb.group({
      branchSelection: [null, Validators.required]
    });

    this.sharedData.sharedBranchData.subscribe((val) => {
      this.getBranchData = val;
      if(this.getBranchData.length == 0 ){
        this.branches = this.user.restaurantAccess;
      }else if(this.getBranchData.length == 1){        
        const toSelect = this.getBranchData.find(data => data.branchName == this.getBranchData[0].branchName);
        this.intraBranchForm.get('branchSelection').setValue(toSelect);
        this.branches = this.getBranchData
        this.filterByBranch(this.intraBranchForm.value.branchSelection);
      }else{
        this.branches = this.getBranchData
      }
  });
  }

  ngOnInit() {
    this.dataSource = new MatTableDataSource()
    this.multiBranchUser = this.user.multiBranchUser
    if (!this.user.multiBranchUser) {
      this.restaurantId = this.user.restaurantAccess[0].restaurantIdOld
    }
    if(this.dataSource.data.length == 0){
      // this.displayedColumns = ['index', 'itemName', 'entryType', 'pkgName','uom','UnitPrice','transferQty' ]
      this.displayedColumns = ['index', 'itemName', 'entryType', 'pkgName','uom','UnitPrice','transferQty','totalPrice' ]
    }
    let date = new Date();
    date.setHours(0, 0, 0, 0);
    this.today = date ; 
    this.documentDate.setValue(this.today);
  }


  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
  }

  getWorkAreasForBranch(){
    this.branchTransfer.getWorkAreasForBranch({
      tenantId: this.user.tenantId,
      restaurantId: this.restaurantId           
    }
    ).subscribe(data => {
      this.user.restaurantAccess.forEach(element => {
        if (element.restaurantIdOld == this.restaurantId) {
          this.indentAreas = element.workAreas
          if (element.workAreas.length == 0) {
            this.indentAreas = data.workAreas;
          }
        }
      });
      this.indentAreas = [...new Set(this.indentAreas)];
      this.filteredWorkAreasList = this.indentAreas
      this.Bank = this.filteredWorkAreasList;
      this.sourceFilteredWorkArea.next(this.Bank.slice());
      this.sourceBankFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
        this.filterBanks();
    });

      this.filteredDestinationWorkAreasList = this.indentAreas
      this.Bank = this.filteredDestinationWorkAreasList;
      this.destinationFilteredWorkArea.next(this.Bank.slice());
      this.destinationBankFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
        this.destinationFilterBanks();
    });
    },
      err => console.error(err))    
  }

  preview() {
    this.showFooter = !this.showFooter;
    if (this.transferWorkAreaPreview == true) {
    this.curDataSource = this.dataSource.data
    this.dataSource.data = this.inventoryItems.filter(item => item.transferQty > 0)
    this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
    }
    else {
      this.dataSource.data = this.curDataSource
      this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
    }    
  }  


  validateAndDcrsReqQty(element) {
    if(element.transferQty > 0){ 
      element.transferQty = element.transferQty - 1;
    }
    else if (element.transferQty < 0){
      element.transferQty = 0
    }
  }

  transferCheck(){
    let returnItems = this.dataSource.data.filter((el) => el['transferQty'] > 0 ) 
    let obj = {
      tenantId: this.user.tenantId,
      restaurantId: this.restaurantId,
      workArea: this.sourceIndentArea,
      items: returnItems,
      documentData: this.documentDate.value,
      uId: this.user.mId
    }
    this.branchTransfer.returnToStore(obj).subscribe(data => {
      if (data['success']){
        this.utils.snackBarShowSuccess('Returned Successfully');
        this.getBranchInv() ;
      } else {
        this.utils.snackBarShowError('Something went wrong, Please try again');
      }
    })
  }

  getBranchInv() {
    this.dataSource = new MatTableDataSource<any> ();
    this.branchTransfer.getWorkAreaInv({
      tenantId: this.user.tenantId,
      restaurantId: this.restaurantId,
      sourceWorkArea: this.sourceIndentArea,
    }
    ).subscribe(data => {
      if (data['success']) {
        this.categoryList = ['All'] ;
        this.subCategoryList = ['All'] ;
        this.inventoryItems = data.invItems;
        this.inventoryItems.map((item: any) => {
          item.transferQty = 0
          return item
        });         
      }else{
        this.inventoryItems = []
        this.utils.snackBarShowInfo(data['message']);
        this.categoryList = []
        this.subCategoryList = []
      }
      this.showTableDatas = true;
      // this.displayedColumns = ['index', 'itemName', 'entryType', 'pkgName','uom','UnitPrice',].concat([this.sourceIndentArea,'transferQty'])
      this.displayedColumns = ['index', 'itemName', 'entryType', 'pkgName','uom','UnitPrice',].concat([this.sourceIndentArea,'transferQty','totalPrice' ])
      this.dataSource.data = this.inventoryItems
      this.categories = []
      this.inventoryItems.forEach(item => {
        this.categories.push(item.invCategory)
        if (item.category == null) {
          item.category = 'N/A'
        }
        if (item.ItemType == null) {
          item.ItemType = 'N/A'
        }
        if (item.subCategory == null) {
          item.subCategory = 'N/A'
        }
        this.ItemTypeList.push(item.ItemType)
        this.categoryList.push(item.category)
        this.subCategoryList.push(item.subCategory)
        if (!item.uom)
          item.uom = "units"
      })
      this.categoryList = this.categoryList.filter((k, i, ar) => ar.indexOf(k) === i);
      this.ItemTypeList = this.ItemTypeList.filter((p, q, arrr) => arrr.indexOf(p) === q);
      this.subCategoryList = this.subCategoryList.filter((j, l, arr) => arr.indexOf(j) === l);
      this.initCategoryList = this.categoryList;
      this.initSubCategoryList = this.subCategoryList;
      this.pageSizes = this.utils.getPageSizes(this.inventoryItems)
      this.dataSource.paginator = this.paginator;     
    },
      err => console.error(err))
      this.isShow = true;
  }

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }

  getIndentAreaVal(obj, area) {
    if (obj.hasOwnProperty(area))
      return obj[area]
  }

  
  getPlatesVal(obj) {
    var t1 = obj.transferQty
    if ("totalWeightInKg" in obj ){
      var t2 = obj.totalWeightInKg
    }else{
      t2 = 1 
    }
    obj["transferQtyPlates"] = this.utils.truncateNew(t1 / t2)
    return obj["transferQtyPlates"]

  }

  allFilter() {
    let tmp = this.inventoryItems
    let prev = this.inventoryItems
    Object.keys(this.filterKeys).forEach(element => {
      if (this.filterKeys[element] != 'All') {
        tmp = prev.filter(item =>
          item[element].toLowerCase() === this.filterKeys[element].toLowerCase())
        prev = tmp
      }
    });
    this.dataSource.data = tmp
  }

  selectItemType(itemType) {
    let filteredCategoryList = []
    let filteredSubCategoryList = []
    if (itemType != 'All') {
      let filteredItem = this.inventoryItems.filter(item => item['ItemType'].toUpperCase() === itemType.toUpperCase());
      filteredItem.forEach(element => {
        filteredCategoryList.push(element.category)
        filteredSubCategoryList.push(element.subCategory)
      });
      this.categoryList = filteredCategoryList.filter((k, i, ar) => ar.indexOf(k) === i);
      this.subCategoryList = filteredSubCategoryList.filter((k, i, ar) => ar.indexOf(k) === i);
      this.categoryList.splice(0, 0, 'All')
      this.subCategoryList.splice(0, 0, 'All')
      this.subCatList = this.subCategoryList
    }
    else {
      this.categoryList = this.initCategoryList;
      this.subCategoryList = this.initSubCategoryList;
    }
    this.filterKeys = { ItemType: itemType, category: 'All', subCategory: 'All' }
    this.allFilter()
  }

  selectCategory(cat) {
    let filteredSubCategoryList = []
    let filteredItem = []
    if (cat != 'All') {
      if (this.filterKeys.ItemType != 'All') {
        filteredItem = this.inventoryItems.filter(item => item['category'].toUpperCase() === cat.toUpperCase()
          && item['ItemType'].toUpperCase() === this.filterKeys.ItemType.toUpperCase());
      }
      else {
        filteredItem = this.inventoryItems.filter(item => item['category'].toUpperCase() === cat.toUpperCase());
      }
      filteredItem.forEach(element => {
        filteredSubCategoryList.push(element.subCategory)
      });
      this.subCategoryList = filteredSubCategoryList.filter((k, i, ar) => ar.indexOf(k) === i);
      this.subCategoryList.splice(0, 0, 'All')
    }
    else if (this.filterKeys.ItemType != 'All') {
      this.subCategoryList = this.subCatList
    }
    else {
      this.subCategoryList = this.initSubCategoryList
    }
    this.filterKeys.category = cat;
    this.filterKeys.subCategory = 'All'
    this.allFilter()
  }

  selectSubCat(subCat) {
    this.filterKeys.subCategory = subCat;
    this.allFilter()
  }
  printpdf() {
    let inventoryList = {}
    inventoryList['inventoryItems'] = this.dataSource.data;
    inventoryList['user'] = this.user;
    this.purchases.printpdfs(inventoryList, 'Inventory').subscribe(data => {
      // window.open('data:application/pdf;base64,' + data.eFile);
      this.purchases.globalPrintPdf(data.eFile);
    });

  }
  exportToExcel() {
    let inventoryList = {}
    inventoryList['inventoryItems'] = this.dataSource.data;
    inventoryList['user'] = this.user;
    this.purchases.exportToExcel(inventoryList, 'Inventory').subscribe(data => {
      window.open('data:text/csv;base64,' + data.eFile);
    });
  }

  filterByBranch(restId) {
    this.restaurantId = restId.restaurantIdOld;
    this.ItemType.setValue('')
    this.Category.setValue('')
    this.Subcategory.setValue('')
    this.branchSelected = true;
    this.dataSource = new MatTableDataSource();
    this.getWorkAreasForBranch();
  }

  clearFilter() {
    this.ItemType.setValue('')
    this.Category.setValue('')
    this.Subcategory.setValue('')
    this.searchValue = ''
    this.searchText = ''
    this.doFilter(this.searchValue)
    this.dataSource.data = this.inventoryItems
  }

  clearSearchText() {
    this.searchText = ''
    this.doFilter(this.searchText)
  }

  selectSourceIndentArea(val) {
    this.displayedColumns = []
    this.sourceIndentArea = val.value ;
    this.getBranchInv()
  }

  checkStock(val,el) {
    if (val > el['workArea'][this.sourceIndentArea]) {
      el['transferQty'] = this.utils.truncateNew(el['workArea'][this.sourceIndentArea])
    } 
    val < 0 ?  el['transferQty'] = 0 : undefined ; 
  }
  
  selectDestinationIndentArea(val){
    this.destinationIndentArea = val.value
  }

  filterWorkArea() {
    this.filteredWorkAreasList = this.indentAreas.filter(
      (wa) =>
        wa.toLowerCase().includes(this.searchFilterWorkAreaText.toLowerCase())
    );
  }

  filterDestinationWorkArea() {
    this.filteredDestinationWorkAreasList = this.indentAreas.filter(
      (wa) =>
        wa.toLowerCase().includes(this.searchFilterDestinationWorkAreaText.toLowerCase())
    );
  }
  //  = this.indentAreas

  ngOnDestroy() {
    this._onDestroy.next();
    this._onDestroy.complete();
  }

  protected filterBanks() {
    if (!this.Bank) {
      return;
    }
    let search = this.sourceBankFilterCtrl.value;
    if (!search) {
      this.sourceFilteredWorkArea.next(this.Bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.sourceFilteredWorkArea.next(
      this.Bank.filter(bank => bank.toLowerCase().indexOf(search) > -1)
    );
  }
  protected destinationFilterBanks() {
    if (!this.Bank) {
      return;
    }
    let search = this.destinationBankFilterCtrl.value;
    if (!search) {
      this.destinationFilteredWorkArea.next(this.Bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.destinationFilteredWorkArea.next(
      this.Bank.filter(bank => bank.toLowerCase().indexOf(search) > -1)
    );
  }

  focusFunctionWithOutForm(element){
    if(Number(element.transferQty) === 0){
      element.transferQty = null;
    }
  }
  
  focusOutFunctionWithOutForm(element){
    if(element.transferQty === null){
      element.transferQty = 0
    }
  }

  getTotalPrice(){
    return this.utils.truncateNew(this.dataSource.data.map(element => element.withTaxPrice * element.transferQty).reduce((acc, value) => acc + value, 0) )
  }
  
}
