<div class="title">
  <button mat-raised-button class="button" style="float: left; margin-left: 0px;" (click)=goBack()>
    <mat-icon>keyboard_backspace</mat-icon> Back to Rts List
  </button>

  <button *ngIf='!rtsWorkAreaViewFlag' mat-button mat-raised-button class="button3" style="float: right;"
    (click)="acceptReturnItem()" [disabled]="disableRts || rts.status != 'pending'">
    Update InStock
  </button>
  <button mat-button mat-raised-button class="button" (click)="exportToExcel()" style="float: right;">
    Export
  </button>
  <button mat-button mat-raised-button class="button" (click)="printToPdf()" style="float: right;">
    Print
  </button>
</div>
<div class="search-table-input fieldcontainer">
  <div class="fieldbox">
    <label>Indent# </label> <span class="label5">{{ rts.rtsId }}</span>
  </div>

  <div class="fieldbox4-a">
    <label style="font-size: 16px;">Sender Area </label>
    <span class="label5">{{ rts.workArea }}</span>
  </div>
  <div class="fieldbox4-a">
    <label style="font-size: 16px;">Req Date </label>
    <span class="label5">{{ rts.createTs | date: "EEEE, MMMM d, y" }}</span>
  </div>

</div>

<mat-card class="matcontent">
  <table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort>
    <!-- Index Column -->
    <ng-container matColumnDef="index">
      <th mat-header-cell *matHeaderCellDef class="tableId"><b>#</b></th>
      <td mat-cell *matCellDef="let element; let i = index" class="name-cell tableId">
        {{ i + 1 }}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <!-- Name Column -->
    <ng-container matColumnDef="itemName" sticky>
      <th mat-header-cell *matHeaderCellDef mat-sort-header class="name-cell">
        <b> Item Name</b>
      </th>
      <td mat-cell *matCellDef="let element" class="name-cell">
        {{ element.itemName | titlecase }}
      </td>
      <td mat-footer-cell *matFooterCellDef>Total</td>
      <!-- <td mat-footer-cell *matFooterCellDef> Total </td> -->

      <!-- <mat-divider></mat-divider> -->
    </ng-container>

    <!-- difference Percent Column -->
    <ng-container matColumnDef="pkgName">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b>Pkg Name</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ element.packageName |titlecase}}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="unitPrice">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b> Unit Price</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ this.utils.truncateNew(element.price)}}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="totalPrice">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b> Total Price</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ this.utils.truncateNew(element.price * element.receivedQty)}}
      </td>
      <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(getTotal())}}</td>
    </ng-container>

    <ng-container matColumnDef="returnQty">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b> Return Qty</b>
      </th>
      <td mat-cell *matCellDef="let element">
        {{ element.returnQty }}
      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <ng-container matColumnDef="receivedQty">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>
        <b> Received Qty</b>
      </th>
      <td mat-cell *matCellDef="let element">
        <div *ngIf="rts.status == 'pending'">
          <input style="text-align: center;" type="number" min="0" class="input1" (keyup)="validateRecivedQty($event , element)"
            [(ngModel)]="element.receivedQty" 
            (focus)="focusFunctionWithOutForm(element,'receivedQty')" (focusout)="focusOutFunctionWithOutForm(element,'receivedQty')"/>
        </div>
        <div *ngIf="rts.status != 'pending'">
          <span>{{element.receivedQty}}</span>
        </div>

      </td>
      <td mat-footer-cell *matFooterCellDef></td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
    <tr mat-footer-row *matFooterRowDef="displayedColumns"></tr>
  </table>

  <mat-card-actions> </mat-card-actions>
</mat-card>