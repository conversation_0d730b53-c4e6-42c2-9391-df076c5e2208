import { Component, OnInit } from '@angular/core';
import { GlobalsService } from '../_services/globals.service';
import { AuthService, BranchTransferService, PurchasesService } from '../_services/';
import { MatTableDataSource, MatPaginator, MatSort } from '@angular/material';
import { ShareDataService } from '../_services/share-data.service';
import { RtsItem } from '../_models';
import { Location } from '@angular/common';
import { NotificationService } from '../_services/notification.service';
import { UtilsService } from '../_utils/utils.service';
@Component({
  selector: 'app-rts-detail',
  templateUrl: './rts-detail.component.html',
  styleUrls: ['./rts-detail.component.scss','./../../common-dark.scss']
})
export class RtsDetailComponent implements OnInit {

  rts: any
  displayedColumns: any
  dataSource: MatTableDataSource<RtsItem>;
  disableRts: any = false
  rtsWorkAreaViewFlag: boolean;

  constructor(private shareData: ShareDataService,private notifyService: NotificationService,
    private auth: AuthService,
    private branchTransfer: BranchTransferService,private purchases: PurchasesService,
    private utils: UtilsService,
    private loc : Location,) { }

  ngOnInit() {

    this.shareData.currRts.subscribe(
      values => {
        let rts = values[0]
        this.rtsWorkAreaViewFlag = values[1]
        if(!rts.rtsId)
          this.loc.back()
        else{
          this.rts = rts
          if (this.rts.status === 'pending'){
            this.rts.rtsItems.map(
              (item:any) => {item.receivedQty = 0;return item}
            )
          }
          
          this.dataSource = new MatTableDataSource<RtsItem>();
          this.dataSource.data = this.rts.rtsItems;
          if (this.rtsWorkAreaViewFlag){
            if (rts.status == 'pending')
              this.displayedColumns = GlobalsService.workAreaRtsPendingColoumns;
            else
              this.displayedColumns = GlobalsService.workAreaRtsCompleteColoumns;
          }
          else{
              this.displayedColumns = GlobalsService.storeRtsColoumns;
          }
        }

      });

  }

  goBack(){
    this.loc.back()
  }

  acceptReturnItem(){
      this.branchTransfer.acceptReturnItem({
        tenantId: this.rts.tenantId,
        restaurantId: this.rts.restaurantId,
        workArea: this.rts.workArea,
        rtsId: this.rts.rtsId,
        rtsItems: this.dataSource.data
      }).subscribe(res => {
        if (res.result == 'success'){
          this.disableRts = true
          this.rts.status = res.status 
          this.utils.snackBarShowSuccess('Store Updated Successfully');
        }
      })
  }

  validateRecivedQty(event , element){
    this.utils.truncateNew(element.receivedQty) > this.utils.truncateNew(element.returnQty) ? element.receivedQty = this.utils.truncateNew(element.returnQty) : this.utils.truncateNew(element.receivedQty);
  }

  validateAndIncrmntRecivedQty(element){
    element.receivedQty = element.receivedQty + 1
    element.receivedQty > element.returnQty ? element.receivedQty = element.returnQty : element.receivedQty;
  }

  validateAndDcrsDispatchQty(element){
    element.receivedQty = element.receivedQty - 1
    if (element.receivedQty < 0)
      element.receivedQty = 0
  }

  getTotal(){
    let totalPrice = 0
    this.dataSource.data.forEach(element => {
      totalPrice += element.receivedQty * element.price
    });
    return totalPrice
  }

  printToPdf(){
    let inventoryList = {};
    inventoryList['inventoryItems'] = [];
    inventoryList['inventoryItems'] = this.dataSource.data
    if (inventoryList['inventoryItems'].length == 0){
      this.utils.snackBarShowWarning('please enter rts values');
      return;
    }
    inventoryList['user'] = this.auth.getCurrentUser();
    inventoryList['rtsListView'] = true
    inventoryList['status'] = this.rts.status
    inventoryList['recipientArea'] = this.rts.workArea
    inventoryList['rtsId'] = this.rts.rtsId
    this.purchases.printpdfs(inventoryList,'Indent').subscribe(data => {
      // window.open('data:application/pdf;base64,'+data.eFile);
      this.purchases.globalPrintPdf(data.eFile);
    });
  }

  exportToExcel() {
    let inventoryList = {}
    inventoryList['indentArea'] = this.rts.indentArea;
    inventoryList['inventoryItems'] = this.dataSource.data;
    inventoryList['user'] = this.auth.getCurrentUser();
    this.purchases.exportToExcel(inventoryList,'Indent').subscribe(data => {
      window.open('data:text/csv;base64,'+data.eFile);
    });
  }

  focusFunctionWithOutForm(element , value){
    if(Number(element[value]) === 0){
      element[value] = null;
    }
  }
  
  focusOutFunctionWithOutForm(element , value){
    if(element[value] === null){
      element[value] = 0
    }
  }

}
