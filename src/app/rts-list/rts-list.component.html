<div *ngIf="multiBranchUser" class="title">
  <mat-form-field appearance="none" class="topitem">
    <label>Select Branch</label>
    <mat-select placeholder="Restaurant" (selectionChange)="filterByBranch($event.value)" class="outline">
      <mat-option *ngFor="let rest of this.user.restaurantAccess" [value]="rest.restaurantIdOld">
        {{ rest.branchName }}
      </mat-option>
    </mat-select>
  </mat-form-field>
  <mat-form-field *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" appearance="none"
    style="margin-left: 10px;" class="topitem">
    <label>Start Date</label>
    <input matInput class="outline" [matDatepicker]="picker1" placeholder="Start Date" [formControl]="startDate" />
    <mat-datepicker-toggle matSuffix [for]="picker1">
      <mat-icon matDatepickerToggleIcon>
        <img src="./../../assets/calender.png" />
      </mat-icon>
    </mat-datepicker-toggle>
    <mat-datepicker #picker1></mat-datepicker>
  </mat-form-field>

  <mat-form-field *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" appearance="none"
    style="margin-left: 10px;" class="topitem">
    <label>End Date</label>
    <input matInput class="outline" [matDatepicker]="picker2" [readonly] = "!startDate.value" [formControl]="endDate"  [disabled]="!startDate.value" placeholder="End Date" [min]="startDate.value"/>
    <mat-datepicker-toggle matSuffix [for]="picker2">
      <mat-icon matDatepickerToggleIcon>
        <img src="./../../assets/calender.png" />
      </mat-icon>
    </mat-datepicker-toggle>
    <mat-datepicker #picker2></mat-datepicker>
  </mat-form-field>
  <button *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" mat-button mat-raised-button id="save-btn"
    class="button2 button3" style="margin-top: 24px;padding-left: 0%;padding-right: 0%;"
    (click)="filterByDate()">Find</button>
</div>

<div *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" class="datacontainer">
  <mat-card>
    <mat-card-content>
      <div class="search-table-input">
        <mat-form-field appearance="none">
          <label>Search</label>
          <input matInput type="text" class="outline" (keyup)="doFilter($event.target.value)" placeholder="Search" />
          <mat-icon matSuffix class="closebtn">close</mat-icon>
        </mat-form-field>

      </div>

      <table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort>
        <ng-container matColumnDef="rtsId">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Rts Id</b>
          </th>
          <td mat-cell *matCellDef="let element" (click)="detailedRts(element)" class="links">
            {{ element.rtsId }}
          </td>
        </ng-container>

        <ng-container matColumnDef="date">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Date</b>
          </th>
          <td mat-cell *matCellDef="let element">{{ element.createTs | date: "EEEE, MMMM d, y":"UTC" }}</td>
        </ng-container>

        <ng-container matColumnDef="senderArea">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Sender Area</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.workArea }}
          </td>
        </ng-container>

        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Status</b>
          </th>
          <td mat-cell *matCellDef="let element">{{ element.status }}</td>
        </ng-container>


        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>
      <mat-paginator [showTotalPages]="5" [pageSize]="10" [pageSizeOptions]="pageSizes"></mat-paginator>
    </mat-card-content>
  </mat-card>
</div>