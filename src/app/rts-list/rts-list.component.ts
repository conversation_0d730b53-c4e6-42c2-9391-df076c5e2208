import { Component, OnInit, ViewChild } from '@angular/core';
import { GlobalsService } from '../_services/globals.service';
import { AuthService, BranchTransferService } from '../_services/';
import { MatTableDataSource, MatPaginator, MatSort } from '@angular/material';
import { UtilsService } from '../_utils/utils.service';
import { ShareDataService } from '../_services/share-data.service';
import { Router } from '@angular/router';
import { FormControl } from '@angular/forms';
import { IndentItem } from '../_models/';
import { NotificationService } from '../_services/notification.service';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material';
import { MomentDateModule, MomentDateAdapter } from '@angular/material-moment-adapter';

export const MY_FORMATS = {
  parse: {
      dateInput: 'LL'
  },
  display: {
      dateInput: 'DD-MM-YYYY',
      monthYearLabel: 'YYYY',
      dateA11yLabel: 'LL',
      monthYearA11yLabel: 'YYYY'
  }
};
@Component({
  selector: 'app-rts-list',
  templateUrl: './rts-list.component.html',
  styleUrls: ['./rts-list.component.scss',"./../../common-dark.scss"],
  providers:[{ provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
            { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }
          ]
})
export class RtsListComponent implements OnInit {

  user: any;
  @ViewChild(MatPaginator) set paginator(value: MatPaginator) {
    if (this.dataSource){
      this.dataSource.paginator = value;
    }
  };
  pageSizes=[];
  multiBranchUser: any
  restaurantId: any
  branchSelected: any
  indentsListFlag: boolean
  rtsWorkAreaViewFlag: boolean
  rtsListUrl = encodeURI(GlobalsService.rtsList)
  rtsReqUrl = encodeURI(GlobalsService.workAreaRts)
  dataSource: MatTableDataSource<IndentItem>;
  displayedColumns: any = GlobalsService.rtsListColumns
  private startDate = new FormControl();
  private endDate = new FormControl();

  constructor(private auth : AuthService,private branchTransfer: BranchTransferService,private notifyService: NotificationService,
    private utils: UtilsService, private router : Router,
      private sharedData : ShareDataService) { }

  ngOnInit() {
    this.indentsListFlag = this.router.url.includes(this.rtsListUrl);
    this.rtsWorkAreaViewFlag = this.router.url.includes(this.rtsReqUrl);
    this.user= this.auth.getCurrentUser();
    this.multiBranchUser = this.user.multiBranchUser
    if (!this.user.multiBranchUser) {
      this.restaurantId = this.user.restaurantId
      let obj = {
        tenantId: this.user.tenantId,
        restaurantId: this.restaurantId,
        userEmail: this.user.email,
        rtsWorkAreaViewFlag: this.rtsWorkAreaViewFlag
      }
      this.getRtsList(obj)
    }

  }

  getRtsList(obj){
    this.branchTransfer.getRtsList(obj).subscribe(data => {
      if (data){
        this.dataSource = new MatTableDataSource<IndentItem>();
        this.dataSource.data = data
        this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
        this.dataSource.paginator = this.paginator;
      }
    });

  }

  filterByBranch(restId){
    this.restaurantId = restId;
    this.branchSelected = true
    let obj = {
      tenantId: this.user.tenantId,
      restaurantId: this.restaurantId,
      userEmail: this.user.email,
      indentReqFlag: this.rtsWorkAreaViewFlag
    }
    this.getRtsList(obj)
  }

  detailedRts(obj){
    this.sharedData.changeRts(obj,this.rtsWorkAreaViewFlag);
    this.router.navigate(['/home/<USER>']);

  }

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }

  filterByDate(){
    if(this.startDate.value && this.endDate.value){
      let obj = {
        tenantId : this.user.tenantId,
        restaurantId: this.restaurantId,
        startDate: this.startDate.value,
        endDate: this.endDate.value,
        userEmail: this.user.email,
        indentReqFlag: this.rtsWorkAreaViewFlag
      }
      this.getRtsList(obj)
      
    }
    else{
      this.utils.snackBarShowWarning('please select start date and date both');
    }
  }

}
