<div class="title">
  <mat-form-field *ngIf="multiBranchUser" appearance="none" class="topitem">
    <label class="title-palce">Select Branch</label>
    <mat-select placeholder="Select Branch" class="outline" (selectionChange)="filterByBranch($event.value)">
      <mat-option *ngFor="let rest of this.user.restaurantAccess" [value]="rest.restaurantIdOld">
        {{ rest.branchName }}
      </mat-option>
    </mat-select>
  </mat-form-field>

  <mat-form-field *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" appearance="none"
    style="margin-left: 10px;" class="topitem">
    <label>Start Date</label>
    <input matInput class="outline" [matDatepicker]="picker1" placeholder="Start Date" [formControl]="startDate" />
    <mat-datepicker-toggle matSuffix [for]="picker1">
      <mat-icon matDatepickerToggleIcon>
        <img src="./../../assets/calender.png" />
      </mat-icon>
    </mat-datepicker-toggle>
    <mat-datepicker #picker1></mat-datepicker>
  </mat-form-field>

  <mat-form-field *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" appearance="none"
    style="margin-left: 10px;" class="topitem">
    <label>End Date</label>
    <input matInput class="outline" [matDatepicker]="picker2" [formControl]="endDate" placeholder="End Date" [readonly] = "!startDate.value" [min]="startDate.value" [disabled]="!startDate.value"/>
    <mat-datepicker-toggle matSuffix [for]="picker2">
      <mat-icon matDatepickerToggleIcon>
        <img src="./../../assets/calender.png" />
      </mat-icon>
    </mat-datepicker-toggle>
    <mat-datepicker #picker2></mat-datepicker>
  </mat-form-field>
  <button *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" mat-button mat-raised-button id="save-btn"
    class="button2 button3" style="margin-top: 24px;padding-left: 0%;padding-right: 0%;"
    (click)="filterByDate()">Find</button>

  <button mat-raised-button class="button" style="float: right;" (click)="exportToExcel()">
    Export
  </button>
  <button mat-raised-button (click)="printpdf()" class="button" style="float: right;">
    Print
  </button>
</div>
<div *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" class="datacontainer">
  <mat-card>
    <mat-card-content>
      <div class="search-table-input">
        <mat-form-field appearance="none">
          <label>Search</label>
          <input matInput type="text" (keyup)="doFilter($event.target.value)" placeholder="Search" autocomplete="off"
            class="outline" />
          <mat-icon matSuffix class="closebtn">close</mat-icon>
          <mat-icon matSuffix class="searchbtn">search</mat-icon>
        </mat-form-field>

        <mat-form-field appearance="none">
          <label>Vendor Name</label>
          <mat-select placeholder="Vendor Name" [formControl]="vendorName" class="outline">
            <mat-option *ngFor="let vendor of vendorList" [value]="vendor" (click)="selectVendor(vendor)">
              {{ vendor | titlecase }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <table #table mat-table [dataSource]="dataSource" matSortDirection="desc" matSort>

        <ng-container matColumnDef="rtvId">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> RTV Id</b>
          </th>
          <td mat-cell *matCellDef="let element" class="links" (click)="detailedRtv(element)">
            {{ element.rtvId }}
          </td>
        </ng-container>
        <ng-container matColumnDef="grnId">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> GRN Id</b>
          </th>
          <td mat-cell *matCellDef="let element" class="links">
            {{ element.grnId }}
          </td>
        </ng-container>

        <ng-container matColumnDef="poId">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Request Id </b>
          </th>
          <td mat-cell *matCellDef="let element">
            <span *ngIf="element.details.ibtId">{{
              element.details.ibtId
              }}</span>
            <span *ngIf="element.details.poId">{{ element.details.poId }}</span>
          </td>
        </ng-container>

        <ng-container matColumnDef="vendorName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Vendor Name </b>
          </th>
          <td mat-cell *matCellDef="let element">{{ element.vendorName }}</td>
        </ng-container>
        <ng-container matColumnDef="type">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Type </b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.grnType | titlecase }}
          </td>
        </ng-container>

        <ng-container matColumnDef="date">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Date</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.createTs | date: "EEEE, MMMM d, y" }}
          </td>
        </ng-container>

        <ng-container matColumnDef="invId">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b> Invoice Id </b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.invoiceId }}
          </td>
        </ng-container>

        <ng-container matColumnDef="action">
          <th mat-header-cell *matHeaderCellDef mat-sort-header>
            <b>Actions</b>
          </th>
          <td mat-cell *matCellDef="let element">
            <button mat-icon-button (click)="rtvProcess(element)" class="action-print" matTooltip="Return Items"
              matTooltipPosition="right">
              <mat-icon class="action-print-icon">swap_vertical_circle</mat-icon>
            </button>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>
      <mat-paginator [showTotalPages]="5" [pageSize]="10" [pageSizeOptions]="pageSizes"></mat-paginator>
    </mat-card-content>
  </mat-card>
</div>