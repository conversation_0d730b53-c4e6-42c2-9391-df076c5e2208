import { Component, ViewChild, OnInit } from '@angular/core';
import { PurchasesService, AuthService, ShareDataService } from '../_services';
import { MatSort, Sort, MatTableDataSource, MatPaginator } from '@angular/material';
import { GlobalsService } from '../_services/globals.service';
import { Location } from '@angular/common';
import { Router } from '@angular/router';
import { FormControl } from '@angular/forms';
import { UtilsService } from '../_utils/utils.service';
import { NotificationService } from '../_services/notification.service';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material';
import { MomentDateModule, MomentDateAdapter } from '@angular/material-moment-adapter';

export const MY_FORMATS = {
  parse: {
      dateInput: 'LL'
  },
  display: {
      dateInput: 'DD-MM-YYYY',
      monthYearLabel: 'YYYY',
      dateA11yLabel: 'LL',
      monthYearA11yLabel: 'YYYY'
  }
};
@Component({
  selector: 'app-rtv-list',
  templateUrl: './rtv-list.component.html',
  styleUrls: ['./rtv-list.component.scss', "./../../common-dark.scss"],
  providers:[{ provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
            { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }
          ]
})
export class RtvListComponent implements OnInit {

  @ViewChild(MatPaginator) set paginator(value: MatPaginator) {
    if (this.dataSource) {
      this.dataSource.paginator = value;
    }
  };
  pageSizes= [];
  startDate = new FormControl();
  endDate = new FormControl();
  filterKeys = { vendorName: 'All', grnType: 'All' }
  restaurantId: any;
  multiBranchUser; branchSelected: boolean;
  grnType = new FormControl();
  vendorName = new FormControl();
  rtvList = [];
  vendorList = ['All'];
  grnTypeList = ['All', 'Po', 'Ibt'];
  user: any;
  dataSource: MatTableDataSource<any>;
  displayedColumns: string[];
  @ViewChild(MatSort) sort: MatSort;
  constructor(private purchases: PurchasesService, private auth: AuthService,
    private notifyService: NotificationService, private router: Router,
    private utils: UtilsService,
    private sharedData: ShareDataService) { }

  ngOnInit() {
    this.displayedColumns = GlobalsService.rtvListColumns;

    this.user = this.auth.getCurrentUser()
    this.multiBranchUser = this.user.multiBranchUser
    if (!this.user.multiBranchUser) {
      this.restaurantId = this.user.restaurantAccess[0].restaurantIdOld
      let reqObj: any = {
        tenantId: this.user.tenantId,
        restaurantId: this.restaurantId
      }
      this.getRtvs(reqObj)
    }
  }
  exportToExcel(){

  }
  printpdf(){
    
  }

  getRtvs(reqObj) {
    this.purchases.getRtvs(reqObj).subscribe(data => {
      if (!this.dataSource)
        this.dataSource = new MatTableDataSource<any>();
      this.dataSource.data = data;
      this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
      this.dataSource.paginator = this.paginator;
      this.rtvList = data;
      this.rtvList.forEach(element => {
        this.vendorList.push(element.vendorName);
      });
      this.vendorList = this.vendorList.filter((k, i, ar) => ar.indexOf(k) === i);
      this.dataSource.sort = this.sort;
    }, err => { })

  }

  detailedRtv(obj) {
    this.sharedData.changeRtv(obj)
    this.router.navigate(['/home/<USER>'])
  }

  public doFilter = (value: string) => {
    this.dataSource.filter = value.trim().toLocaleLowerCase();
  }

  allFilter() {
    let tmp = this.rtvList
    let prev = this.rtvList
    Object.keys(this.filterKeys).forEach(element => {
      if (this.filterKeys[element] != 'All') {
        tmp = prev.filter(item =>
          item[element].toLowerCase() === this.filterKeys[element].toLowerCase())
        prev = tmp
      }
    });
    this.dataSource.data = tmp
  }

  selectVendor(vendor) {
    this.filterKeys.vendorName = vendor;
    this.allFilter()
  }

  filterByBranch(restId) {
    this.restaurantId = restId;
    this.branchSelected = true
    this.vendorName.setValue('')
    let reqObj: any = {
      tenantId: this.user.tenantId,
      restaurantId: this.restaurantId
    }
    this.getRtvs(reqObj)
  }

  filterByDate() {
    if (this.startDate.value && this.endDate.value) {
      if (this.startDate.value <= this.endDate.value) {
        let obj = {
          tenantId: this.user.tenantId,
          restaurantId: this.restaurantId,
          startDate: this.startDate.value,
          endDate: this.endDate.value
        }
        this.getRtvs(obj)
        this.grnType.setValue('')
        this.vendorName.setValue('')
      }
      else {
      this.utils.snackBarShowError("End date is earlier than the Start start date!");
      }

    }
    else {
      this.utils.snackBarShowError("Please select both start&end dates");

    }
  }

}
