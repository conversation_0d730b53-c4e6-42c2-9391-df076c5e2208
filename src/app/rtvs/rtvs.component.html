<div class="title row ">
  <div>
    <form [formGroup]="rtvForm">
      <mat-form-field *ngIf="multiBranchUser" appearance="none" class="topitem">
        <label class="title-palce">Select Branch</label>
        <mat-select placeholder="Select Branch" class="outline" formControlName="branchSelection"
          (selectionChange)="filterByBranch($event.value)">
          <mat-option *ngFor="let rest of branches" [value]="rest">
            {{ rest.branchName }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </form>
  </div>

  <div>
    <mat-form-field *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" appearance="none"
      style="margin-left: 10px;" class="topitem">
      <label>Start Date</label>
      <input matInput class="outline" [matDatepicker]="picker1" placeholder="Start Date" [formControl]="startDate" />
      <mat-datepicker-toggle matSuffix [for]="picker1">
        <mat-icon matDatepickerToggleIcon>
          <img src="./../../assets/calender.png" />
        </mat-icon>
      </mat-datepicker-toggle>
      <mat-datepicker #picker1></mat-datepicker>
    </mat-form-field>
  </div>

  <div>
    <mat-form-field *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" appearance="none"
      style="margin-left: 10px;" class="topitem">
      <label>End Date</label>
      <input matInput class="outline" [matDatepicker]="picker2" [formControl]="endDate" placeholder="End Date"
        [readonly]="!startDate.value" [disabled]="!startDate.value" [min]="startDate.value" />
      <mat-datepicker-toggle matSuffix [for]="picker2">
        <mat-icon matDatepickerToggleIcon>
          <img src="./../../assets/calender.png" />
        </mat-icon>
      </mat-datepicker-toggle>
      <mat-datepicker #picker2></mat-datepicker>
    </mat-form-field>
  </div>

  <div class=" mr-2">
    <button *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" mat-stroked-button
      class="btn-block findButton button3" (click)="filterByDate()">
      Find
    </button>
  </div>

  <div>
    <button *ngIf="(branchSelected && multiBranchUser) || !multiBranchUser" mat-stroked-button
      class="btn-block findButton button3" (click)="clearDates()">
      Clear
    </button>
  </div>

</div>

<div class="datacontainer">
  <mat-card>
    <mat-card-content>
      <div class="search-table-input">
        <mat-form-field appearance="none">
          <label>Search</label>
          <div class="search-container">
            <input matInput type="text" class="outline" (keyup)="doFilter($event.target.value)" 
            [(ngModel)]='searchText' placeholder="Search" />
            <mat-icon matSuffix class="closebtn" (click)="resetForm()">close</mat-icon>
          </div>
        </mat-form-field>

        <mat-form-field appearance="none">
          <label>Vendor Name</label>
          <mat-select placeholder="Vendor Name" [formControl]="vendorName"
            (selectionChange)="vendorChange($event.value)" class="outline" [multiple]="true" #multiSelect>
            <mat-option>
              <ngx-mat-select-search placeholderLabel="Vendor Items..." noEntriesFoundLabel="'no Vendor Item found'"
                [formControl]="vendorMultiFilterCtrl"></ngx-mat-select-search>
            </mat-option>
            <mat-option (click)="toggleAllVendors()" class="hide-checkbox">
              Deselect All / Select All
            </mat-option>
            <mat-option *ngFor="let vendor of grnvendorsMulti | async" [value]="vendor">
              {{ vendor }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="none">
          <label>Item Name</label>
          <mat-select placeholder="Item Name" [formControl]="itemName" (selectionChange)="itemNameChange($event.value)"
            class="outline" [multiple]="true" #multiSelect>
            <mat-option>
              <ngx-mat-select-search placeholderLabel="Items..." noEntriesFoundLabel="'no Item found'"
                [formControl]="itemFilterCtrl"></ngx-mat-select-search>
            </mat-option>
            <mat-option (click)="toggleAllItems()" class="hide-checkbox">
              Deselect All / Select All
            </mat-option>
            <mat-option *ngFor="let item of itemBanks | async" [value]="item">
              {{ item }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <button mat-button class="buttonForRefresh refreshBtn" (click)="refreshdata()">Refresh</button>

      </div>
      <section class="example-container-1 mat-elevation-z8">    
        <table #table mat-table [dataSource]="dataSource" matSortDirection="desc" matSort>

          <ng-container matColumnDef="index">
            <th mat-header-cell *matHeaderCellDef><b>S.No</b></th>
            <td mat-cell *matCellDef="let element; let i = index" style="padding-right: 30px;">
              {{ i + 1 }}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>

          <ng-container matColumnDef="returnDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> RTV Date </b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.returnDate | date: "EEEE, MMMM d, y" }}
            </td>
          </ng-container>

          <ng-container matColumnDef="vendorName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header style="width: 200px !important">
              <b> Vendor Name </b>
            </th>
            <td mat-cell *matCellDef="let element" style="width: 200px !important">
              {{ element.vendorName }}
            </td>
          </ng-container>

          <ng-container matColumnDef="rtvId">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> RTV Id </b>
            </th>
            <td mat-cell *matCellDef="let element" class="links" (click)="detailedRtvs(element)">
              {{ element.rtvId }}
            </td>
          </ng-container>

          <ng-container matColumnDef="grnId">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> GRN Id </b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.grnId }}
            </td>
          </ng-container>

          <ng-container matColumnDef="invId">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Invoice Id </b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.invoiceId }}
            </td>
          </ng-container>

          <ng-container matColumnDef="creditNo">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Credit Note No </b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.creditNo }}
            </td>
          </ng-container>

          <ng-container matColumnDef="creditDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Credit Note Date </b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.creditDate | date: "EEEE, MMMM d, y" }}
            </td>
          </ng-container>

          <ng-container matColumnDef="createdAt">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Created At </b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ this.utils.formatDateToUTC(element.createdAt) || '-' }}
            </td>
          </ng-container>

          <ng-container matColumnDef="createdBy">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b> Created By </b>
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.createdBy || '-' }}
            </td>
          </ng-container>

          <ng-container matColumnDef="action">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              <b>Action</b>
            </th>
            <td mat-cell *matCellDef="let element">
                <button mat-icon-button class="delete-button" matTooltip="Delete RTV" (click)="deleteRtv(element)" matTooltipPosition="left">
                  <mat-icon>delete_outline</mat-icon>
                </button>
            </td>
          </ng-container>
          
          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        </table>
      </section>
    </mat-card-content>
  </mat-card>
</div>
