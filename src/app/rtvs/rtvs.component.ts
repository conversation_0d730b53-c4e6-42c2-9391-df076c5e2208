import { Component, ViewChild, OnInit } from '@angular/core';
import { PurchasesService, AuthService, ShareDataService, BranchTransferService } from '../_services';
import { MatSort, Sort, MatTableDataSource, MatPaginator, MatOption } from '@angular/material';
import { GlobalsService } from '../_services/globals.service';
import { ActivatedRoute, NavigationStart, Params, Router } from '@angular/router';
import { UtilsService } from '../_utils/utils.service';
import { NotificationService } from '../_services/notification.service'
import { FormBuilder, FormControl, FormGroup, Validators } from "@angular/forms";
import { ReplaySubject, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material';
import { MomentDateModule, MomentDateAdapter } from '@angular/material-moment-adapter';
import { SharedFilterService } from '../_services/shared-filter.service';
import { SimpleDialogComponent } from '../_dialogs/simple-dialog/simple-dialog.component';
import { MatDatepickerInputEvent, MatDialog } from '@angular/material';
import { InvoiceDialogComponent } from '../_dialogs/invoice-dialog/invoice-dialog.component';

export const MY_FORMATS = {
  parse: {
    dateInput: 'LL'
  },
  display: {
    dateInput: 'DD-MM-YYYY',
    monthYearLabel: 'YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'YYYY'
  }
};

@Component({
  selector: 'app-rtvs',
  templateUrl: './rtvs.component.html',
  styleUrls: ['./rtvs.component.scss', "./../../common-dark.scss"],
  providers: [{ provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
  { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }
  ]
})

export class RtvsComponent implements OnInit {

  grn: any;
  rtvForm: FormGroup;
  dataSource: MatTableDataSource<any> = new MatTableDataSource<any>();
  displayedColumns: string[];
  multiBranchUser; branchSelected: boolean;
  actualData: any;
  user: any;
  restaurantId: any;
  branches: any[];
  getBranchData: any[]
  sharedFilterData: any = {};
  clearDate: null;
  prevBranchId: string;
  vendorName = new FormControl();
  itemName = new FormControl();
  vendorNameList: any;
  itemNameList: any;
  dataRefresh: boolean = false;
  selectAllVendors: boolean = true;
  selectAll: boolean = true;
  searchText: string;
  public searchValue: any = ''
  public startDate = new FormControl();
  public endDate = new FormControl();
  private unsubscribe$ = new Subject<void>();
  protected _onDestroy = new Subject<void>();

  public grnvendorsMulti: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public vendorBank: any[] = [];
  public vendorMultiFilterCtrl: FormControl = new FormControl();

  public itemBank: any[] = [];
  public itemBanks: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public itemFilterCtrl: FormControl = new FormControl();

  constructor(private purchases: PurchasesService, 
    private sharedData: ShareDataService,
    private router: Router, 
    private fb: FormBuilder,
    private utils: UtilsService,
    public dialog: MatDialog,
    private auth: AuthService,
    private sharedFilterService: SharedFilterService,
  ) {
    this.user = this.auth.getCurrentUser()
    this.multiBranchUser = this.user.multiBranchUser;
    this.rtvForm = this.fb.group({
      branchSelection: [null, Validators.required]
    });
    this.sharedFilterService.getFilteredGrn.subscribe(obj =>
      this.sharedFilterData = obj
    );
    this.sharedData.sharedBranchData.pipe(takeUntil(this.unsubscribe$)).subscribe((val) => {
      this.getBranchData = val;      
      if (this.getBranchData.length == 0) {
        this.branches = this.user.restaurantAccess;
      } else if (this.getBranchData.length == 1) {
        const toSelect = this.getBranchData.find(data => data.branchName == this.getBranchData[0].branchName);
        if (toSelect != this.sharedFilterData.restaurantId) {
          this.sharedFilterData = '';
          this.clearDate = null;
          this.startDate.setValue(null);
          this.endDate.setValue(null);
        }
        this.rtvForm.get('branchSelection').setValue(toSelect);
        this.branches = this.getBranchData
        this.filterByBranch(this.rtvForm.value.branchSelection);
        if (this.sharedFilterData) {
          this.sharedFilterData.branchFlag = false;
        }
      } else {
        if (this.sharedFilterData.branchFlag == true) {
          this.filterByBranch(this.sharedFilterData.restaurantId);
          this.sharedFilterData.branchFlag = false;
        }
        this.branches = this.getBranchData
      }
    });

    this.router.events.subscribe(event => {
      if (event instanceof NavigationStart) {
        if (this.router.url.includes('/home/<USER>')) {
          localStorage.setItem('savedItemNames', JSON.stringify(this.itemName.value));
          localStorage.setItem('savedVendorNames', JSON.stringify(this.vendorName.value));
        }
      }
    }); 
   }

  ngOnInit() {
    if (this.branchSelected) {
    } else {
      if (!this.user.multiBranchUser) {
        this.restaurantId = this.user.restaurantAccess[0].restaurantIdOld;        
        this.branchSelected = true;
        this.getRtvList();
      }
    }
    this.displayedColumns = ['index','rtvId','vendorName','grnId','invId', 'creditNo', 'creditDate', 'returnDate','createdBy', 'action'];   
  }

  ngOnDestroy() {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
    this._onDestroy.next();
    this._onDestroy.complete();
    if (!this.router.url.includes('/home/<USER>')) {
      this.sharedFilterService.getFilteredGrn['_value'] = ''
      localStorage.removeItem('savedItemNames');
      localStorage.removeItem('savedVendorNames');
    }
  }

  filterByBranch(restId) {
    this.prevBranchId = window.sessionStorage.getItem("restaurantId");
    this.restaurantId = restId.restaurantIdOld;    
    this.branchSelected = true
    window.sessionStorage.setItem("restaurantId", this.restaurantId)
    if (this.sharedFilterData != '' && this.sharedFilterData.restaurantId == restId) {
      this.rtvForm.get('branchSelection').setValue(this.sharedFilterData.restaurantId);
      this.branches = this.getBranchData
      this.startDate.setValue(this.sharedFilterData.selectedStartDate)
      this.endDate.setValue(this.sharedFilterData.selectedEndDate)
      this.clearDate = this.sharedFilterData.clearDate
    }
    this.getRtvList()
  }

  getRtvList() {        
  let obj = {}
  obj['tenantId'] = this.user.tenantId;
  obj['restaurantId'] = this.restaurantId;
  if (this.startDate.value && this.endDate.value) {
    obj['startDate'] = new Date(this.startDate.value).toISOString();
    obj['endDate'] = new Date(this.endDate.value).toISOString();
  } else {
    obj['startDate'] = null;
    obj['endDate'] = null;
  }
  this.purchases.getRtvList(obj).subscribe(res => {   
    this.actualData = res.data
    // this.dataSource.data = res.data; 
    const data = res.data;

    const uniqueItemNames = new Set();
    data.forEach(element => {
      element.itemList.forEach(element => uniqueItemNames.add(element));
    });
    this.itemNameList = Array.from(uniqueItemNames); 
    const savedItemNames = JSON.parse(localStorage.getItem('savedItemNames') || '[]');      
    if (savedItemNames.length > 0 && this.restaurantId === this.prevBranchId && !this.dataRefresh) {
      this.itemName.setValue(savedItemNames);
      this.itemNameChange(savedItemNames);
    } else {
      this.itemName.setValue(this.itemNameList.slice()); 
      this.itemNameChange(this.itemName.value);
    }

    this.itemBank = this.itemNameList
    this.itemBanks.next(this.itemBank.slice());
    this.itemFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
        this.vendorfilterBanks();
    });
        
    const uniqueVendorNames = new Set();
    data.forEach((element) => {          
      if (element.restaurantId === this.restaurantId) {
        uniqueVendorNames.add(element.vendorName);
      }
    });
    const vendorList = Array.from(uniqueVendorNames); 
    this.vendorNameList = vendorList.filter(vendor => vendor != null);    
    const savedVendorNames = JSON.parse(localStorage.getItem('savedVendorNames') || '[]');      
    if (savedVendorNames.length > 0 && this.restaurantId === this.prevBranchId && !this.dataRefresh) {
      this.vendorName.setValue(savedVendorNames);
    } else {
      this.vendorName.setValue(this.vendorNameList.slice()); 
    }
    this.vendorBank = this.vendorNameList;
    this.grnvendorsMulti.next(this.vendorNameList.slice());
    this.vendorMultiFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
      this.filterBanksMulti();
    });
    this.dataRefresh = false;
 }, err => { })
}

itemNameChange(selectedItemNames: string[]) {                   
  if (selectedItemNames && selectedItemNames.length > 0) {
    this.dataSource.data = this.actualData.filter(element =>
      element.itemList.some(item => selectedItemNames.includes(item))
    );
  } else {
    this.dataSource.data = [];
  }
}

vendorChange(selectedVendors: string[]) {    
  this.itemName.setValue([]); 
  this.dataSource.data = [];   
  const filteredItemNames = new Set<string>();    
  if (selectedVendors && selectedVendors.length > 0) {
    this.actualData.forEach(grn => {
      if (selectedVendors.includes(grn.vendorName)) {
        grn.itemList.forEach(item => filteredItemNames.add(item));
      }
    });
  } else {
    this.itemNameList.forEach(item => filteredItemNames.add(item));
  }    
  this.itemBank = Array.from(filteredItemNames);
  this.itemBanks.next(this.itemBank.slice());
}

toggleAllVendors() {
  this.selectAllVendors = !this.selectAllVendors;
  if (this.selectAllVendors) {
    this.vendorName.setValue(this.vendorBank.slice());
  } else {
    this.vendorName.setValue([]);
  }
  this.vendorChange(this.vendorName.value);
}

toggleAllItems() {
  this.selectAll = !this.selectAll;
  if (this.selectAll) {
    this.itemName.setValue(this.itemBank.slice());
  } else {
    this.itemName.setValue([]);
  }
  this.itemNameChange(this.itemName.value);
}

protected vendorfilterBanks() {
  if (!this.itemBank) {
    return;
  }
  let search = this.itemFilterCtrl.value;
  if (!search) {
    this.itemBanks.next(this.itemBank.slice());
    return;
  } else {
    search = search.toLowerCase();
  }
  this.itemBanks.next(
    this.itemBank.filter(item => item.toLowerCase().indexOf(search) > -1)
  );
}

protected filterBanksMulti() {
  if (!this.vendorBank) {
    return;
  }
  let search = this.vendorMultiFilterCtrl.value;
  if (!search) {
    this.grnvendorsMulti.next(this.vendorBank.slice());
    return;
  } else {
    search = search.toLowerCase();
  }

  this.grnvendorsMulti.next(
    this.vendorBank.filter(item => item.toLowerCase().indexOf(search) > -1)
  );
}

refreshdata() {
  this.dataRefresh = true;
  this.getRtvList();
}

filterByDate() {
  if (this.startDate.value && this.endDate.value) {
    this.getRtvList()
  }
  else {
    this.utils.snackBarShowError('Please select start date and end date')
  }
}

clearDates() {
  this.clearDate = null;
  this.startDate.setValue(null);
  this.endDate.setValue(null);
  this.vendorName.setValue(null);
  this.getRtvList()
}

deleteRtv(obj) {  
  let reqObj = {}
    reqObj['grnId'] = obj['grnId']
    reqObj['rtvId'] = obj['rtvId']
    reqObj['tenantId'] = this.user.tenantId;
    reqObj['user'] = this.user.mId;

  this.dialog.open(SimpleDialogComponent, {
    data: {
      title: 'Delete RTV',
      msg: 'Are you sure you want to delete?',
      ok: function () {
        this.purchases.deleteRtv(reqObj).subscribe(res => {
          if (res['result'] === true) {
            this.utils.snackBarShowSuccess('RTV deleted successfully!');
          } else {
            this.utils.snackBarShowError(`${res['message']}`);
          }
          this.getRtvList();
        })
      }.bind(this)
    }
  });
}

public doFilter = (value: string) => {
  this.dataSource.filter = value.trim().toLocaleLowerCase();
}

resetForm() {
  this.searchText = ''
  this.searchValue = ''
  this.doFilter(this.searchValue)
  this.getRtvList();
}

detailedRtvs(obj){  
  this.sharedData.changeRtvs(obj)
  this.router.navigate(['/home/<USER>'])
}

}
