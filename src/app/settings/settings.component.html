<mat-card>
  <h3 class="headTag">Settings</h3>

  <mat-button-toggle-group class="buttonToggleGroup" aria-label="Font Style">
    <!-- <mat-button class="toggleButtons" (click)="bold()">Personal Info</mat-button>
    <mat-button class="toggleButtons" (click)="Italic()">Notifications</mat-button>
    <mat-button class="toggleButtons" (click)="Underline()">Underline</mat-button> -->
    <button mat-raised-button class="toggleButtons" (click)="personalInfoFunc()">Personal Info</button>
    <button mat-raised-button class="toggleButtons" (click)="notificationsFunc()">Scheduler</button>
    <button mat-raised-button class="toggleButtons" (click)="underlineFunc()">Underline</button>
  </mat-button-toggle-group>


  <!-- PERSONAL INFO -->

  <div *ngIf="personalInfo" class="mt-3">
    <h4>Personal Info</h4>

    <!-- <div class="imageDiv mt-3">
      <img src="./../../assets/{{this.user.tenantId}}.png" class="image-{{this.user.tenantId}}"/>
    </div> -->

    <div class="mt-3">
      <div class="row">
        <div class="col">
          <table class="table">
            <tbody>
              <tr>
                <th class="topItemkey" scope="row">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-list-ol mr-1" viewBox="0 0 16 16">
                    <path fill-rule="evenodd" d="M5 11.5a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h9a.5.5 0 0 1 0 1h-9a.5.5 0 0 1-.5-.5z"/>
                    <path d="M1.713 11.865v-.474H2c.217 0 .363-.137.363-.317 0-.185-.158-.31-.361-.31-.223 0-.367.152-.373.31h-.59c.016-.467.373-.787.986-.787.588-.002.954.291.957.703a.595.595 0 0 1-.492.594v.033a.615.615 0 0 1 .569.631c.003.533-.502.8-1.051.8-.656 0-1-.37-1.008-.794h.582c.008.178.186.306.422.309.254 0 .424-.145.422-.35-.002-.195-.155-.348-.414-.348h-.3zm-.004-4.699h-.604v-.035c0-.408.295-.844.958-.844.583 0 .96.326.96.756 0 .389-.257.617-.476.848l-.537.572v.03h1.054V9H1.143v-.395l.957-.99c.138-.142.293-.304.293-.508 0-.18-.147-.32-.342-.32a.33.33 0 0 0-.342.338v.041zM2.564 5h-.635V2.924h-.031l-.598.42v-.567l.629-.443h.635V5z"/>
                  </svg>
                  Tenant Id</th>
                <td>{{ user.tenantId }}</td>
              </tr>
              <tr>
                <th class="topItemkey" scope="row">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-person-workspace mr-1" viewBox="0 0 16 16">
                    <path d="M4 16s-1 0-1-1 1-4 5-4 5 3 5 4-1 1-1 1H4Zm4-5.95a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Z"/>
                    <path d="M2 1a2 2 0 0 0-2 2v9.5A1.5 1.5 0 0 0 1.5 14h.653a5.373 5.373 0 0 1 1.066-2H1V3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v9h-2.219c.554.654.89 1.373 1.066 2h.653a1.5 1.5 0 0 0 1.5-1.5V3a2 2 0 0 0-2-2H2Z"/>
                  </svg>
                  Restaurant Id</th>
                <td>{{ user.restaurantId }}</td>
              </tr>
              <tr>
                <th class="topItemkey" scope="row">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-buildings-fill mr-1" viewBox="0 0 16 16">
                    <path d="M15 .5a.5.5 0 0 0-.724-.447l-8 4A.5.5 0 0 0 6 4.5v3.14L.342 9.526A.5.5 0 0 0 0 10v5.5a.5.5 0 0 0 .5.5h9a.5.5 0 0 0 .5-.5V14h1v1.5a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 .5-.5V.5ZM2 11h1v1H2v-1Zm2 0h1v1H4v-1Zm-1 2v1H2v-1h1Zm1 0h1v1H4v-1Zm9-10v1h-1V3h1ZM8 5h1v1H8V5Zm1 2v1H8V7h1ZM8 9h1v1H8V9Zm2 0h1v1h-1V9Zm-1 2v1H8v-1h1Zm1 0h1v1h-1v-1Zm3-2v1h-1V9h1Zm-1 2h1v1h-1v-1Zm-2-4h1v1h-1V7Zm3 0v1h-1V7h1Zm-2-2v1h-1V5h1Zm1 0h1v1h-1V5Z"/>
                  </svg>
                  Name</th>
                <td>{{ user.name }}</td>
              </tr>
              <tr>
                <th class="topItemkey" scope="row">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-envelope mr-1" viewBox="0 0 16 16">
                    <path d="M0 4a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V4Zm2-1a1 1 0 0 0-1 1v.217l7 4.2 7-4.2V4a1 1 0 0 0-1-1H2Zm13 2.383-4.708 2.825L15 11.105V5.383Zm-.034 6.876-5.64-3.471L8 9.583l-1.326-.795-5.64 3.47A1 1 0 0 0 2 13h12a1 1 0 0 0 .966-.741ZM1 11.105l4.708-2.897L1 5.383v5.722Z"/>
                  </svg>
                  Email</th>
                <td>{{ user.email }}</td>
              </tr>
              <tr>
                <th class="topItemkey" scope="row">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-telephone mr-1" viewBox="0 0 16 16">
                    <path d="M3.654 1.328a.678.678 0 0 0-1.015-.063L1.605 2.3c-.483.484-.661 1.169-.45 1.77a17.568 17.568 0 0 0 4.168 6.608 17.569 17.569 0 0 0 6.608 4.168c.601.211 1.286.033 1.77-.45l1.034-1.034a.678.678 0 0 0-.063-1.015l-2.307-1.794a.678.678 0 0 0-.58-.122l-2.19.547a1.745 1.745 0 0 1-1.657-.459L5.482 8.062a1.745 1.745 0 0 1-.46-1.657l.548-2.19a.678.678 0 0 0-.122-.58L3.654 1.328zM1.884.511a1.745 1.745 0 0 1 2.612.163L6.29 2.98c.329.423.445.974.315 1.494l-.547 2.19a.678.678 0 0 0 .178.643l2.457 2.457a.678.678 0 0 0 .644.178l2.189-.547a1.745 1.745 0 0 1 1.494.315l2.306 1.794c.829.645.905 1.87.163 2.611l-1.034 1.034c-.74.74-1.846 1.065-2.877.702a18.634 18.634 0 0 1-7.01-4.42 18.634 18.634 0 0 1-4.42-7.009c-.362-1.03-.037-2.137.703-2.877L1.885.511z"/>
                  </svg>
                  Mobile</th>
                <td>{{ user.mobile }}</td>
              </tr>
              <tr>
                <th class="topItemkey" scope="row">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-person-vcard mr-1" viewBox="0 0 16 16">
                    <path d="M5 8a2 2 0 1 0 0-4 2 2 0 0 0 0 4Zm4-2.5a.5.5 0 0 1 .5-.5h4a.5.5 0 0 1 0 1h-4a.5.5 0 0 1-.5-.5ZM9 8a.5.5 0 0 1 .5-.5h4a.5.5 0 0 1 0 1h-4A.5.5 0 0 1 9 8Zm1 2.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5Z"/>
                    <path d="M2 2a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H2ZM1 4a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1H8.96c.026-.163.04-.33.04-.5C9 10.567 7.21 9 5 9c-2.086 0-3.8 1.398-3.984 3.181A1.006 1.006 0 0 1 1 12V4Z"/>
                  </svg>
                  Role</th>
                <td>{{ cardDesc }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <button mat-button>
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-person-lock mr-1" viewBox="0 0 16 16">
        <path d="M11 5a3 3 0 1 1-6 0 3 3 0 0 1 6 0ZM8 7a2 2 0 1 0 0-4 2 2 0 0 0 0 4Zm0 5.996V14H3s-1 0-1-1 1-4 6-4c.564 0 1.077.038 1.544.107a4.524 4.524 0 0 0-.803.918A10.46 10.46 0 0 0 8 10c-2.29 0-3.516.68-4.168 1.332-.678.678-.83 1.418-.832 1.664h5ZM9 13a1 1 0 0 1 1-1v-1a2 2 0 1 1 4 0v1a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1v-2Zm3-3a1 1 0 0 0-1 1v1h2v-1a1 1 0 0 0-1-1Z"/>
      </svg>
      Change Password</button>
  </div>

  <!-- NOTIFICATION -->

  <div *ngIf="notifications" class="mt-3">
    <h4>Scheduler</h4>

    <!-- <form [formGroup]="settingsForm">
    <mat-form-field appearance="outline" class="mr-2">
      <input matInput placeholder="Event Start Datetime" matTooltip="Event Start Datetime"
        [matDatetimepicker]="dts"  required autocomplete="false" formControlName="dateAndTime">
        <mat-error >Start time is required</mat-error>
      <mat-datetimepicker-toggle [for]="dts" matSuffix></mat-datetimepicker-toggle>
      <mat-datetimepicker #dts type="datetime" openOnFocus="true" timeInterval="5"></mat-datetimepicker>
    </mat-form-field>
    <mat-form-field appearance="outline" class="mr-2">
      <mat-label>Mail</mat-label>
      <input matInput placeholder="Email" formControlName="mail" required>
    </mat-form-field>
    <mat-form-field appearance="outline" class="mr-2">
      <mat-label>Sending Days</mat-label>
      <mat-select placeholder="Select day" formControlName="selectedDay" (selectionChange)="selectedDayFunc($event.value)">
        <mat-option *ngFor="let days of selectDay" [value]="days">
          {{ days }}
        </mat-option>
      </mat-select>
    </mat-form-field>
    <button mat-button class="button3 mr-2" (click)="sendRequest()"> Send Request </button>
  </form> -->
 

  <div class="control-section">
    <div class="col content-wrapper">
      <ejs-schedule
        #schedule
        height="650px"
        [(selectedDate)]="selectedDate"
        [eventSettings]="eventSettings"
        currentView="Agenda" 
        (popupOpen)="onPopupOpen($event);"
        (actionComplete)="onActionComplete($event)"
        [views]='views'
      >
      
      <ng-template #editorTemplate>
        <table class="custom-event-editor" width="100%" cellpadding="5">
            <tbody>

              <tr>
                <td class="e-textlabel">Title </td>
                <td colspan="4">
                    <input id="Title" class="e-field e-input" type="text" value="1234" name="Title" placeholder="Enter Title" style="width: 100%" />
                </td>
            </tr>
                <tr>
                  <td class="e-textlabel">Email</td>
                  <td colspan="4">
                      <input id="email" class="e-field e-input" type="text" name="email" placeholder="Enter Mail ID" style="width: 100%" />
                  </td>
                </tr>
                <tr>
                    <td class="e-textlabel">select Report</td>
                    <td colspan="4">
                        <input type="text" id="ReportType" name="ReportType" class="e-field" placeholder="Select Reports" style="width: 100%" />
                    </td>
                </tr>
                <tr>
                  <td class="e-textlabel">Repeat</td>
                  <td colspan="4">
                      <input type="text" id="RecurrenceRule" name="RecurrenceRule" class="e-field" style="width: 100%" />
                  </td>
              </tr>
                <tr>
                    <td class="e-textlabel">Start Date</td>
                    <td colspan="4">
                        <input id="StartTime" class="e-field" type="text" name="StartTime" />
                    </td>
                </tr>
                <tr>
                    <td class="e-textlabel">End Date</td>
                    <td colspan="4">
                        <input id="EndTime" class="e-field" type="text" name="EndTime" />
                    </td>
                </tr>
                <tr>
                  <td class="e-textlabel">Time</td>
                  <td colspan="4">
                      <input type="time" class="e-field" id="Time" name="Time" required>
                  </td>
                </tr>

                
            </tbody>
        </table>
    </ng-template>


    <!-- <ng-template #eventSettingsTemplate let-data>
      <div>
        <div class="subject">{{data.Subject}}</div>
        <div class="time">
          Time: {{data.StartTime.toISOString()}} -
          {{data.EndTime.toISOString()}}
        </div>
      </div>
    </ng-template> -->

    <!-- <ng-template #eventSettingsTemplate *ngFor="let data of dataSource" >
      <div>
        <div class="subject">{{data.title}}</div>
        <div class="time">
          Time: {{data.StartTime.toISOString()}} -
          {{data.EndTime.toISOString()}}
        </div>
      </div>
    </ng-template> -->


      </ejs-schedule>
    </div>
  </div> 

                      <!-- <tr>
                    <td class="e-textlabel">Reason</td>
                    <td colspan="4">
                        <textarea id="Description" class="e-field e-input" name="Description" rows="3" cols="50" style="width: 100%; height: 60px !important; resize: vertical"></textarea>
                    </td>
                </tr> -->

  </div>


</mat-card>



