.buttonToggleGroup{
    border: none;
    display: flex;
    justify-content: center;
}

.toggleButtons{
    width: 145px;
    margin-right: 15px;
    border-radius: 25px;
}

.mat-button-toggle-group-appearance-standard .mat-button-toggle + .mat-button-toggle {
    border-left: none !important;
}

::ng-deep .mat-form-field-appearance-outline .mat-form-field-infix {
    padding: 0.7em 0 0.8em 0 !important;
}

.imageDiv{
    display: flex;
    justify-content: center;
}

table td {
    color: black;
}

// ::ng-deep .e-popup.e-popup-close {
//     display: none !important;
// }

// ::ng-deep .e-popup.e-popup- {
//     display: none !important;
// }

// ::ng-deep .e-quick-popup-wrapper {
//     display: none !important;
// }