import { Component, OnInit, ViewChild } from '@angular/core';
import { AuthService, MenuItemService } from '../_services';
import { User } from "../_models";
import {  FormBuilder, FormGroup, Validators  } from '@angular/forms';
import { MasterdataupdateService } from '../_services/masterdataupdate.service';
import { NotificationService } from '../_services/notification.service';

@Component({
  selector: 'app-settings',
  templateUrl: './settings.component.html',
  styleUrls: ['./settings.component.scss']
})
export class SettingsComponent implements OnInit {
  personalInfo: boolean = false
  notifications: boolean = false
  isCustom: boolean = false
  datasofData: any = [];
  fetchedData: any = [];
  user: User;
  cardDesc: any;
  public settingsForm: FormGroup;

  selectDays: any;
  reportList: any = [];
  reportListArr: any;
  displayArr: any = [];
  
  
  constructor(
    private auth: AuthService,
    public fb: FormBuilder,
    private masterDataService: MasterdataupdateService,
    private notifyService: NotificationService,
    private menuItems: MenuItemService,
  ) {
    this.user = this.auth.getCurrentUser();
    this.cardDesc = "";
    this.cardDesc += this.user.role;

    this.settingsForm = fb.group({
      dateAndTime: ['', Validators.required],
      mail: ['', Validators.required],
      selectedDay: ['', Validators.required]
    });

   }

  ngOnInit() {
    this.fetchReportList();
    this.fetchScheduler();
  }

  // PERSONAL INFO
  personalInfoFunc(){
    this.personalInfo = true
    this.notifications = false
  }

  // NOTIFICATIONS
  notificationsFunc(){
    this.personalInfo = false
    this.notifications = true
  }

  fetchReportList() {
    let obj = {}
    obj['tenantId'] = this.user.tenantId
    this.menuItems.fetchReportList(obj).subscribe(res => {
      if (res.result == 'success') {
        this.reportList = res['reportList']
        this.reportList.forEach(element => {
          this.reportListArr = element.displayName  
          this.displayArr.push(this.reportListArr)
         });
      }
      else if (res.result == 'failure') {
          this.notifyService.showError(res.desc, "");
      }
    },
      err => {
        console.log(err)
      });
  }

  fetchScheduler(){
    let obj = {}
    obj['tenantId'] = this.user.tenantId;
    obj['restaurantId'] = this.user.restaurantId;
    // this.masterDataService.fetchScheduler(obj).subscribe((res: any) => {
    //   if (res.result == 'success') {
    //       this.fetchedData = res['data']
    //       // console.log(this.fetchedData[0])
    //     }
    // });
  }

  selectedDayFunc(value){
    console.log("OOOOOOOOOOOOOOOOOOOOOOOOOOOooo")
    console.log(this.selectDays)
    console.log(value)

  }

  sendRequest(){
    let inputObj = {}
    inputObj['tenantId'] = this.user.tenantId;
    inputObj['restaurantId'] = this.user.restaurantId;
    inputObj['dateAndTime'] = this.settingsForm.value.dateAndTime;
    inputObj['mail'] = this.settingsForm.value.mail;
    inputObj["selectedDay"] = this.settingsForm.value.selectedDay;

    // console.log(inputObj)

    // this.masterDataService.createVendor(inputObj).subscribe((response: any) => {
    //   if (response.success === true ) {
    //     this.notifyService.showSuccess("Contract Pricing created successfully",'')
    //     this.getVendorList();
    //   }
    //   else {
    //     this.notifyService.showError("Something Worng, please try again later",'')
    //   }
    // });
  }


  // onPopupOpen(args) {
  //   console.log("popUp args", args.data);
  //   console.log("getEvent result", this.scheduleObj.getEvents(args.data));
  // }


  // OTHER
  underlineFunc(){
    console.log("underlineFunc")
  }

}
