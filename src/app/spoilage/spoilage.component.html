<div class="title">
  <form [formGroup]="adjustWorkAreaInvForm" class="topHeadInputs">
    <mat-form-field *ngIf="multiBranchUser" appearance="none" class="topitem">
      <label>Select Branch</label>
      <mat-select placeholder="Select Branch" formControlName="branchSelection" class="outline" (selectionChange)="filterByBranch($event.value)">
        <mat-option *ngFor="let rest of branches" [value]="rest">
          {{ rest.branchName }}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </form>
    <mat-form-field appearance="none"  style="margin-left: 10px;" class="topitem">
      <label>Select workArea</label>
      <mat-select placeholder="Select Work Area" [(ngModel)]="selectedWorkArea" (selectionChange)="selectWorkArea($event.value)" class="outline">
        <mat-option *ngFor="let area of IndentAreas" [value]="area">
          {{ area }}
        </mat-option>
      </mat-select>
    </mat-form-field>

      <mat-form-field appearance="none" *ngIf = "selectedWorkArea" style="margin-left: 10px;" class="topitem">
        <label>Document Date</label>
        <input matInput class="outline" [matDatepicker]="picker2" [formControl]="documentDate"
          placeholder="Document Date"  [max]="today" />
        <mat-datepicker-toggle matSuffix [for]="picker2">
          <mat-icon matDatepickerToggleIcon>
            <img src="./../../assets/calender.png" />
          </mat-icon>
        </mat-datepicker-toggle>
        <mat-datepicker #picker2></mat-datepicker>
      </mat-form-field>

  <button *ngIf="(branchSelected && multiBranchUser) ||
    (branchSelected && multiBranchUser && workAreaSelected) ||
    (!multiBranchUser && workAreaSelected) || (!multiBranchUser)"
    mat-button mat-raised-button class="uIbutton button3 adjustInvBtnAlign" (click)="adjustReq()" [disabled] ='!reqSelected'>
    Create
  </button>

  <!-- <button *ngIf="(branchSelected && multiBranchUser ) || 
    (branchSelected && multiBranchUser && workAreaSelected) ||  
    (!multiBranchUser  && workAreaSelected) || (!multiBranchUser )"
    mat-button mat-raised-button class="exprbutton button adjustInvBtnAlign" (click)="exportToExcel()" >
    Export
  </button>
  <button *ngIf="(branchSelected && multiBranchUser ) || 
    (branchSelected && multiBranchUser && workAreaSelected) ||  
    (!multiBranchUser && workAreaSelected) || (!multiBranchUser)"
    mat-button mat-raised-button class="exprbutton button adjustInvBtnAlign" (click)="printToPdf()" >
    Print
  </button> -->
    <mat-slide-toggle *ngIf="(branchSelected && multiBranchUser) || 
    (branchSelected && multiBranchUser && workAreaSelected) ||  
    (!multiBranchUser && workAreaSelected) || (!multiBranchUser)"
    class="togglebutton" [(ngModel)]="adjInvPreview" (change)="preview()">Preview</mat-slide-toggle>
    <!-- <mat-slide-toggle class="togglebutton mr-2" [(ngModel)]="highQnty" (change)="showAll()">All Item</mat-slide-toggle> -->
</div>



<div *ngIf="(branchSelected && multiBranchUser) || 
  (branchSelected && multiBranchUser && workAreaSelected) ||  
  (!multiBranchUser && workAreaSelected) || 
  (!multiBranchUser)" class="datacontainer">
  <mat-card>
    <mat-card-content>
      <div class="search-table-input">
        <mat-form-field appearance="none">
          <label>Search</label>
          <input matInput type="text" (keyup)="doFilter($event.target.value)" placeholder="Search" class="outline"
            [(ngModel)]='searchText' />
          <mat-icon matSuffix (click)="clearSearchText()" class="closebtn">close</mat-icon>
        </mat-form-field>
        <mat-form-field appearance="none">
          <label>Type</label>
          <mat-select placeholder="Item Type" [(ngModel)]="itemtpe" [formControl]="ItemType" class="outline">
            <mat-option *ngFor="let itemType of ItemTypeList" [value]="itemType" (click)="selectItemType(itemType)">
              {{ itemType | titlecase }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field appearance="none">
          <label>Category</label>
          <mat-select placeholder="Category" class="outline" [(ngModel)]="cat" [formControl]="category">
            <mat-option *ngFor="let cat of categoryList" [value]="cat" (click)="selectCategory(cat)">
              {{ cat | titlecase}}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field appearance="none">
          <label>Sub Category</label>
          <mat-select placeholder="Subcategory" [(ngModel)]="subCat" [formControl]="Subcategory" class="outline">
            <mat-option *ngFor="let subCat of subCategoryList" [value]="subCat" (click)="selectSubCat(subCat)">
              {{ subCat | titlecase }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <button mat-button class="buttonForRefresh refreshBtnForAI" (click)="refreshdata()">Refresh</button>
        
        <!-- <div class="search-table-input"> refreshBtn-->
          <!-- <span class="amountValue">
            TOTAL (in Rs): {{ this.utils.truncateNew(totalIndentCost)}}
          </span> -->
        <!-- </div> -->
        <!-- <button mat-stroked-button class="clrButton button3" (click)=clear()>Clear</button> -->
      </div>
      <section class="example-container-1 mat-elevation-z8">
      <table #table mat-table [dataSource]="dataSource">
        <!-- matSortActive="itemName" matSortDirection="asc" matSort -->
        <ng-container matColumnDef="itemName">
          <th mat-header-cell *matHeaderCellDef>
            <b> Item Name</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.itemName | titlecase }}
          </td>
          <td mat-footer-cell *matFooterCellDef>Total</td>
        </ng-container>

        <!-- <ng-container matColumnDef="index">
          <th mat-header-cell *matHeaderCellDef class="tableId"><b>#</b></th>
          <td mat-cell *matCellDef="let element; let i = index" class="tableId">
            {{ i + 1 + paginator.pageIndex * paginator.pageSize }}
          </td>
        </ng-container> -->

        <ng-container matColumnDef="workAreaStock">
          <th mat-header-cell *matHeaderCellDef>
            <b *ngIf="this.indentArea != 'STORE'"> WorkArea Stock</b>
            <b *ngIf="this.indentArea == 'STORE'"> In Store</b>
          </th>
          <td mat-cell *matCellDef="let element">
            <span *ngIf="this.indentArea != 'STORE'">{{ this.utils.truncateNew(element.workArea[this.indentArea]) }}</span>
            <span *ngIf="this.indentArea == 'STORE'">{{ this.utils.truncateNew(element.inStock) }}</span>

          </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="unitPrice">
          <th mat-header-cell *matHeaderCellDef>
            <b> WAC(incl.tax,etc) </b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ this.utils.truncateNew(element.withTaxPrice,2) }}
          </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="totalPrice">
          <th mat-header-cell *matHeaderCellDef>
            <b> Total Price </b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ this.utils.truncateNew((element.adjustQty * element.withTaxPrice),2) }}
          </td>
          <td mat-footer-cell *matFooterCellDef>{{this.utils.truncateNew(totalIndentCost,2)}}</td>
        </ng-container>

        <ng-container matColumnDef="pkgName">
          <th mat-header-cell *matHeaderCellDef>
            <b> Pkg Name</b>
          </th>
          <td mat-cell *matCellDef="let element">
            <ng-container *ngIf='element.packageName != null'>
              {{element.packageName | titlecase}}
            </ng-container>
            <ng-container *ngIf='element.packageName == null'>
              {{ element.uom | titlecase}}
            </ng-container>
          </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="entryType">
          <th mat-header-cell *matHeaderCellDef>
            <b> Entry Type</b>
          </th>
          <td mat-cell *matCellDef="let element">
            {{ element.entryType }}
          </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="adjustType">
          <th mat-header-cell *matHeaderCellDef>
            <b>Adjust Type</b>
          </th>
          <td mat-cell *matCellDef="let element">
            <button (click)="changeButtonText(element)" class="typeButton" [matTooltip]="element.adjustType"
              matTooltipPosition="right" mat-mini-fab>
              <mat-icon class='myIcon'>{{element.icon}}</mat-icon>
            </button>
          </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="adjustQty">
          <th mat-header-cell *matHeaderCellDef>
            <b>Adjust Qty</b>
          </th>
          <td mat-cell *matCellDef="let element">
            <input class="input1" type="number" step="0.01" min="0" (keyup)="getTotalReturnCost(element)" (keydown)="restrictDecimal($event, element)" 
              [(ngModel)]="element.adjustQty" (focus)="focusFunctionWithOutForm(element,'adjustQty')" (focusout)="focusOutFunctionWithOutForm(element,'adjustQty')"
              onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)"/>
          </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <ng-container matColumnDef="reason">
          <th mat-header-cell *matHeaderCellDef>
            <b> Reason </b>
          </th>
          <td mat-cell *matCellDef="let element">
              <textarea matInput rows="3" maxlength="200" wrap="soft" cols="50"
              [(ngModel)]=element.reason (keyup)="permissionCall($event.target.value,element)" [disabled] = "!element.adjustQty" ></textarea>
              <div *ngIf = "element.adjustQty && !element.reason" class = "requiredField"  >Reason is required!</div>
          </td>
          <td mat-footer-cell *matFooterCellDef></td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        <tr mat-footer-row *matFooterRowDef="displayedColumns" [class.hidden]="!showFooter" ></tr>

      </table>
    <div class="dataMessage" *ngIf="dataSource?.data.length == 0"> No Data Available </div>
    </section>
    </mat-card-content>
  </mat-card>
  <mat-paginator [showTotalPages]="5" [pageSize]='10' [pageSizeOptions]="pageSizes"></mat-paginator>
</div>