<div class="title row ">
  <form [formGroup]="stockConversionForm">
    <mat-form-field appearance="none" class="topitem">
      <label class="title-palce">Select Branch</label>
      <mat-select placeholder="Select Branch" class="outline" formControlName="branchSelection"
        (selectionChange)="filterByBranch($event.value)">
        <mat-option *ngFor="let rest of branches" [value]="rest">
          {{ rest.branchName }}
        </mat-option>
      </mat-select>
    </mat-form-field>
  </form>
</div>
<div>
    <app-invenotry-list [stockConversionValue]="stockConversion" ></app-invenotry-list>
</div>

<mat-accordion>
  <mat-expansion-panel>
    <mat-expansion-panel-header>
      <mat-panel-title class="topItemkey"><b>
        History
      </b></mat-panel-title>
    </mat-expansion-panel-header>
 
    <div class="title row">   
      <div>
        <mat-form-field appearance="none" class="topitem">
          <label>Start Date</label>
          <input matInput class="outline" [matDatepicker]="picker1" placeholder="Start Date"
            [formControl]="startDate" />
          <mat-datepicker-toggle matSuffix [for]="picker1">
            <mat-icon matDatepickerToggleIcon>
              <img src="./../../assets/calender.png" />
            </mat-icon>
          </mat-datepicker-toggle>
          <mat-datepicker #picker1></mat-datepicker>
        </mat-form-field>
      </div>
    
      <div>
        <mat-form-field appearance="none" style="margin-left: 10px;" class="topitem">
          <label>End Date</label>
          <input matInput class="outline" [matDatepicker]="picker2" [formControl]="endDate"
            placeholder="End Date" [readonly]="!startDate.value" [disabled]="!startDate.value" [min]="startDate.value" />
          <mat-datepicker-toggle matSuffix [for]="picker2">
            <mat-icon matDatepickerToggleIcon>
              <img src="./../../assets/calender.png" />
            </mat-icon>
          </mat-datepicker-toggle>
          <mat-datepicker #picker2></mat-datepicker>
        </mat-form-field>
      </div>
    
      <div class=" mr-2">
        <button mat-stroked-button class="btn-block findButton button3" (click)="filterByDate()">
          Find
        </button>
      </div>
    
      <div>
        <button mat-stroked-button class="btn-block findButton button3" (click)="clearDates()">
          Clear
        </button>
      </div>
    </div>
  
    <div class="row expansionInnerContent">
    <mat-card *ngIf="isShow">
    
        <table #table mat-table [dataSource]="dataSource" matSortActive="itemName" matSortDirection="asc" matSort>
          <ng-container matColumnDef="id">
            <th mat-header-cell *matHeaderCellDef mat-sort-header><b> Id </b></th>
            <td mat-cell *matCellDef="let element" class="links" matTooltip="click to view Details"
              (click)="showDetails(element)">{{ element.stockConversionId }}</td>
          </ng-container>
      
          <ng-container matColumnDef="createdDate">
            <th mat-header-cell *matHeaderCellDef class="tableRole"><b>Created Date</b></th>
            <td mat-cell *matCellDef="let element">{{ element.stockConvertionDate | date: 'MMM d, y'}}</td>
          </ng-container>
      
          <ng-container matColumnDef="itemCategory">
            <th mat-header-cell *matHeaderCellDef mat-sort-header><b> Category </b></th>
            <td mat-cell *matCellDef="let element">{{ element.items[0].itemCategory | titlecase }}</td>
          </ng-container>
      
          <ng-container matColumnDef="itemCode">
            <th mat-header-cell *matHeaderCellDef mat-sort-header><b> Item Code</b></th>
            <td mat-cell *matCellDef="let element">{{ element.items[0].itemCode }}</td>
          </ng-container>
      
          <ng-container matColumnDef="itemName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header><b> Item Name</b></th>
            <td mat-cell *matCellDef="let element;">{{ element.items[0].itemName | titlecase }}</td>
          </ng-container>
      
          <ng-container matColumnDef="packageName">
            <th mat-header-cell *matHeaderCellDef class="tableRole"><b>Package Name</b></th>
            <td mat-cell *matCellDef="let element;">{{ element.items[0].packageName | titlecase }}</td>
          </ng-container>
      
          <ng-container matColumnDef="action">
            <th mat-header-cell *matHeaderCellDef class="tableAmount"><b>Show Details</b></th>
            <td mat-cell *matCellDef="let element;">
              <div class="links" matTooltip="click to view Details" (click)="showDetails(element)">View Details</div>
            </td>
          </ng-container>
      
          <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky : true"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        </table>
      <mat-paginator [showTotalPages]="5" [pageSize]="10" [pageSizeOptions]="pageSizes"></mat-paginator>
    </mat-card>
  </div>
    
  
  </mat-expansion-panel>

</mat-accordion>


