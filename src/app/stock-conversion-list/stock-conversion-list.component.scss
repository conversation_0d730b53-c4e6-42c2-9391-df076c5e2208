.parentOfCards{
    margin-left: 1%;
    margin-right: 1%;
    justify-content: space-between;
}

.childCard{
    border-radius: 0.25rem;
    height: 120px;
    margin: 10px;
    max-width: 380px;
    // max-width: 325px;
    overflow: hidden;
    position: relative;
}

.child1{
    // (green and blue) background: linear-gradient(to right, #9CCC65, #4DD0E1);
    background: linear-gradient(to right, #479299, #00184a);
}

.child2{
    background: linear-gradient(to right, #884295, #26004a);
}

.child3 {
    background: linear-gradient(to right, #477a56, #063f03);
  }

.insideDatas{
    // margin: 12px;
    margin: 5px;
    justify-content: space-around;
    margin-top: 30px;
}

.closeBtnSc{
    float: right;
}
.findButton{
    margin-top: 24px
}

mat-expansion-panel{
    margin: 20px !important;
}