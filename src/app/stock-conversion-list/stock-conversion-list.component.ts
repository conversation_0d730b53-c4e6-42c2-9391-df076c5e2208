import { ChangeDetector<PERSON><PERSON>, Component, OnInit, ViewChildren,Query<PERSON>ist, ViewChild } from '@angular/core';
import { ShareDataService } from '../_services/share-data.service';
import { PurchasesService } from '../_services/purchases.service';
import { AuthService } from '../_services/auth.service';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog, MatTable } from '@angular/material';
import { NotificationService } from '../_services/notification.service';
import { UtilsService } from '../_utils/utils.service';
import { Router } from '@angular/router';
// import { QueryList } from '@angular/core/src/render3/query';
import { FormBuilder, FormControl, FormGroup, Validators } from "@angular/forms";
import {takeUntil } from 'rxjs/operators';
import { ReplaySubject, Subject } from 'rxjs';

@Component({
  selector: 'app-stock-conversion-list',
  templateUrl: './stock-conversion-list.component.html',
  styleUrls: ['./stock-conversion-list.component.scss', './../../common-dark.scss']
})
export class StockConversionListComponent implements OnInit {
  data: any;
  user: any;
  dataSource = new MatTableDataSource();
  displayedColumns: string[];
  pageSizes = [];
  @ViewChild(MatPaginator) paginator: MatPaginator;
  startDate = new FormControl();
  endDate = new FormControl();
  stockConversionForm: FormGroup;
  branches: any[];
  getBranchData: any[]
  restaurantId: any;
  private unsubscribe$ = new Subject<void>();
  isShow : boolean = false;
  stockConversion: boolean = false;
  constructor(
    private sharedData: ShareDataService,
    private purchases: PurchasesService,
    private auth: AuthService,
    private utils: UtilsService,
    public router: Router,
    private cd: ChangeDetectorRef,
    private fb: FormBuilder,
    private notifyService : NotificationService
  ) { 
    this.user = this.auth.getCurrentUser()
    this.stockConversionForm = this.fb.group({
      branchSelection: [null, Validators.required]
    });


    this.sharedData.sharedBranchData.pipe(takeUntil(this.unsubscribe$)).subscribe((val) => {
      this.getBranchData = val;
      if(this.getBranchData.length == 0 ){
        this.branches = this.user.restaurantAccess;
      }else if(this.getBranchData.length == 1){        
        const toSelect = this.getBranchData.find(data => data.branchName == this.getBranchData[0].branchName);
        this.stockConversionForm.get('branchSelection').setValue(toSelect);
        this.branches = this.getBranchData
        this.filterByBranch(this.stockConversionForm.value.branchSelection);
      }else{
        this.branches = this.getBranchData
      }
  });

  }

  ngOnInit() {
    this.stockConversion = true;
    this.displayedColumns = ['id','createdDate','itemCategory','itemCode', 'itemName', 'packageName','action'];
    // this.stockConversionList();
  }

  ngOnDestroy() {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
  

  filterByBranch(val){
    this.restaurantId = val.restaurantIdOld
    let reqObj = {
      restaurantId : this.restaurantId
    }
    if (this.startDate.value && this.endDate.value) {
      reqObj['startDate'] = this.utils.dateCorrection(this.startDate.value),
      reqObj['endDate'] = this.utils.dateCorrection(this.endDate.value)
    }else{
      reqObj['startDate'] = null,
      reqObj['endDate'] = null
    }
    this.purchases.stockConversionList(reqObj).subscribe(res => {
      if(res.success == true){
        this.dataSource.data = res.data
        this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
        this.dataSource.paginator = this.paginator;
      }else{
        this.dataSource.data = res.data
        this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
        this.dataSource.paginator = this.paginator;
        // this.notifyService.showInfo("Stock conversion converted successfully","") ;
      }
    })
    this.isShow = true;
  }

  showDetails(element){
    this.sharedData.DetailedStockConversionList(element);
    this.router.navigate(['/home/<USER>'])
  }

  filterByDate() {
    this.filterByBranch(this.stockConversionForm.value.branchSelection);
  }

  clearDates() {
    this.startDate.setValue(null);
    this.endDate.setValue(null);
    this.filterByBranch(this.stockConversionForm.value.branchSelection);
  }

}
