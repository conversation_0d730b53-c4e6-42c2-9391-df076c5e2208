<mat-card>
  <span mat-card-title class="topItemkey" style="font-size: 16px; padding-top: 10px;"><b>Stock Conversion</b></span>

      <table #table mat-table [dataSource]="grnDataSource" matSortActive="itemName" matSortDirection="asc" matSort>
        <ng-container matColumnDef="grnId">
          <th mat-header-cell *matHeaderCellDef mat-sort-header><b>GRN ID</b></th>
          <td mat-cell *matCellDef="let element">{{ element.grnId }}</td>
        </ng-container>

        <ng-container matColumnDef="date">
          <th mat-header-cell *matHeaderCellDef mat-sort-header><b> Date </b></th>
          <td mat-cell *matCellDef="let element">{{ element.createTs | date: 'MMM d, y' }}</td>
        </ng-container>

        <ng-container matColumnDef="vendor">
          <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Vendor Name</b></th>
          <td mat-cell *matCellDef="let element">{{ element.vendorDetails.vendorName[0] }}</td>
        </ng-container>

        <ng-container matColumnDef="itemCode">
          <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Item Code</b></th>
          <td mat-cell *matCellDef="let element">{{ element.grnItems.itemCode }}</td>
        </ng-container>

        <ng-container matColumnDef="itemName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Item Name</b></th>
          <td mat-cell *matCellDef="let element">{{ element.grnItems.itemName }}</td>
        </ng-container>

        <ng-container matColumnDef="packageName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header><b>package Name</b></th>
          <td mat-cell *matCellDef="let element">{{ element.grnItems.packageName }}</td>
        </ng-container>

        <ng-container matColumnDef="quantity">
          <th mat-header-cell *matHeaderCellDef mat-sort-header><b>Quantity</b></th>
          <td mat-cell *matCellDef="let element">{{ element.grnItems.stockConversionQty }}</td>
        </ng-container>
  
        <ng-container matColumnDef="price">
          <th mat-header-cell *matHeaderCellDef class="tableRole"><b>Price</b></th>
          <td mat-cell *matCellDef="let element;">{{ element.grnItems.unitPrice }}</td>
        </ng-container>

        <ng-container matColumnDef="action">
          <th mat-header-cell *matHeaderCellDef class="tableRole"><b>Action</b></th>
          <td mat-cell *matCellDef="let element; let rowIndex = index">
            <span style="cursor: pointer">
              <mat-icon [ngClass]="{'highlighted-row': rowIndex === selectedRowIndex}" (click)="getChild(element); selectRow(rowIndex);">
                play_circle_outline
              </mat-icon>
            </span>
            
          </td> 
        </ng-container>
  
        <tr mat-header-row *matHeaderRowDef="grnDisplayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: grnDisplayedColumns"></tr>
      </table>
    </mat-card>
    
    <mat-card *ngIf="isShow">
      <mat-card-content>
      <div class="datacontainer" style="padding-top: 10px;">
      <mat-form-field appearance="outline" class="mt-2">
        <mat-label>Document Date</mat-label>
        <input matInput [matDatepicker]="picker4" [formControl]="date" placeholder="Document Date" [(ngModel)]="documentDate"/>
        <mat-datepicker #picker4></mat-datepicker>
              <mat-datepicker-toggle matSuffix [for]="picker4">
          <mat-icon matDatepickerToggleIcon>
            <img src="./../../assets/calender.png" />
          </mat-icon>
        </mat-datepicker-toggle>
        <mat-datepicker #picker4></mat-datepicker>
      </mat-form-field>
  
      <mat-form-field appearance="outline" class="mt-2" style="padding-left: 10px;">
        <mat-label>Remark</mat-label>
        <input matInput  placeholder="Remark" [formControl]="remarks"/>
      </mat-form-field>
    </div>
      <br><br><br>
      <div class="m-1">
        <br>
        <span class="topItemkey" style="font-size: 16px;"><b>Items</b></span>
      </div>

      <table #table mat-table matSort [dataSource]="dataSource">

        <ng-container matColumnDef="itemCode">
          <th mat-header-cell *matHeaderCellDef> Item Code </th>
          <td mat-cell *matCellDef="let element">{{element.itemCode }} </td>
        </ng-container>
    
        <ng-container matColumnDef="itemName">
          <th mat-header-cell *matHeaderCellDef> Item Name </th>
          <td mat-cell *matCellDef="let element">{{element.itemName }} </td>
        </ng-container>
    
        <ng-container matColumnDef="uom">
          <th mat-header-cell *matHeaderCellDef> UOM </th>
          <td mat-cell *matCellDef="let element"> {{element.uom }} </td>
        </ng-container>
    
        <ng-container matColumnDef="stock">
          <th mat-header-cell *matHeaderCellDef> Current Stock </th>
          <td mat-cell *matCellDef="let element"> {{ inStoreStock }} </td>
          <!-- <td mat-cell *matCellDef="let element"> {{ element.stockConversionQty }} </td> -->
        </ng-container>
    
        <ng-container matColumnDef="inStock">
          <th mat-header-cell *matHeaderCellDef>Quantity</th>
          <td mat-cell *matCellDef="let element"> 
            <div >
              <input class="input1" type="number" step="0.01" min="0" (keyup)="getRequestedQty($event.target.value,element)" [(ngModel)]="element.stockConversionQty"
              (input)="checkNumericInput($event)" onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)"
              (focus)="focusFunctionWithOutForm($event , element,'stockConversionQty')" (focusout)="focusOutFunctionWithOutForm(element,'stockConversionQty')"/>
            </div>
          </td>
        </ng-container>
     
        <ng-container matColumnDef="price">
          <th mat-header-cell *matHeaderCellDef> Unit Price </th>
          <td mat-cell *matCellDef="let element"> {{element.unitPrice }} </td>
        </ng-container>

        <ng-container matColumnDef="totalPrice">
          <th mat-header-cell *matHeaderCellDef> Total Price </th>
          <!-- <td mat-cell *matCellDef="let element"> {{ element.price * requestedStock}} </td> -->
          <td mat-cell *matCellDef="let element"> {{ element.unitPrice * requestedStock}} </td>
        </ng-container>
        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
      </table>

      <div class="m-1">
        <br>
        <span class="topItemkey" style="font-size: 16px;"><b>Converted Items</b></span>
      </div>

      <table #table mat-table [dataSource]="scDataSource" matSortActive="itemName" matSortDirection="asc" matSort>

        <ng-container matColumnDef="itemCode">
          <th mat-header-cell *matHeaderCellDef mat-sort-header><b> Item Code</b></th>
          <td mat-cell *matCellDef="let element">{{ element.itemCode }}</td>
          <td mat-footer-cell *matFooterCellDef class="name-cell"></td>
        </ng-container>
  
        <ng-container matColumnDef="itemName">
          <th mat-header-cell *matHeaderCellDef mat-sort-header><b> Item Name</b></th>
          <td mat-cell *matCellDef="let element">{{ element.itemName | titlecase }}</td>
          <td mat-footer-cell *matFooterCellDef class="name-cell">Total</td>
        </ng-container>
  
        <ng-container matColumnDef="uom">
          <th mat-header-cell *matHeaderCellDef mat-sort-header><b> UOM </b></th>
          <td mat-cell *matCellDef="let element">{{ element.uom }}</td>
          <td mat-footer-cell *matFooterCellDef class="name-cell"></td>
        </ng-container>
  
        <ng-container matColumnDef="inStock">
          <th mat-header-cell *matHeaderCellDef class="tableRole"><b>Current Stock</b></th>
          <td mat-cell *matCellDef="let element;">{{ this.utils.truncateNew(element.inStock) }}</td>
          <td mat-footer-cell *matFooterCellDef class="name-cell"></td>
        </ng-container>
  
        <ng-container matColumnDef="weight">
          <th mat-header-cell *matHeaderCellDef class="tableAmount"><b>Quantity</b></th>
          <td mat-cell *matCellDef="let element;">
              <div>
                <input class="input1" type="number" step="0.01" min="0" (keyup)="makeZero($event.target.value, element)" [(ngModel)]="element.weight"
                onkeypress="return (event.charCode != 45 && (event.charCode>=48 &&event.charCode<=57)|| event.charCode == 46)"
                (focus)="focusFunctionWithOutForm($event ,element,'weight')" (focusout)="focusOutFunctionWithOutForm(element,'weight')"/>
              </div>
          </td>
          <td mat-footer-cell *matFooterCellDef class="name-cell">{{this.utils.truncateNew(getTotal('weight'))}}</td>
        </ng-container>
  
        <ng-container matColumnDef="price">
          <th mat-header-cell *matHeaderCellDef><b>Unit Price</b></th>
          <td mat-cell *matCellDef="let element;">{{ this.utils.truncateNew(element.price) }}</td>
          <td mat-footer-cell *matFooterCellDef class="name-cell"></td>
        </ng-container>
  
        <ng-container matColumnDef="totalAmount">
          <th mat-header-cell *matHeaderCellDef><b>Total Price</b></th>
          <td mat-cell *matCellDef="let element;">{{this.utils.truncateNew(element.totalPrice)}}</td>
          <td mat-footer-cell *matFooterCellDef class="name-cell">{{ this.utils.truncateNew(getTotal('totalPrice')) }}</td>
        </ng-container>
  
        <tr mat-header-row *matHeaderRowDef="scDisplayedColumns; sticky : true"></tr>
        <tr mat-row *matRowDef="let row; columns: scDisplayedColumns"></tr>
        <tr mat-footer-row *matFooterRowDef="scDisplayedColumns"></tr>
      </table>


          <!-- <mat-card style="flex: 1 1 auto;">
            <mat-card-header>
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-currency-rupee svgIcons" viewBox="0 0 16 16">
                <path d="M4 3.06h2.726c1.22 0 2.12.575 2.325 1.724H4v1.051h5.051C8.855 7.001 8 7.558 6.788 7.558H4v1.317L8.437 14h2.11L6.095 8.884h.855c2.316-.018 3.465-1.476 3.688-3.049H12V4.784h-1.345c-.08-.778-.357-1.335-.793-1.732H12V2H4v1.06Z"/>
              </svg>
              <mat-card-title class="topItemkey">Wastage Value</mat-card-title>
              <mat-card-subtitle class="bottomValues">₹ {{ this.utils.truncateNew(wastageAmount) }}</mat-card-subtitle>
            </mat-card-header>
          </mat-card> -->
        <div class="bottom-section">

          <div class="bottom-details">
            <!-- <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" fill="currentColor" class="bi bi-trash3-fill svgIcons" viewBox="0 0 16 16">
                <path d="M11 1.5v1h3.5a.5.5 0 0 1 0 1h-.538l-.853 10.66A2 2 0 0 1 11.115 16h-6.23a2 2 0 0 1-1.994-1.84L2.038 3.5H1.5a.5.5 0 0 1 0-1H5v-1A1.5 1.5 0 0 1 6.5 0h3A1.5 1.5 0 0 1 11 1.5Zm-5 0v1h4v-1a.5.5 0 0 0-.5-.5h-3a.5.5 0 0 0-.5.5ZM4.5 5.029l.5 8.5a.5.5 0 1 0 .998-.06l-.5-8.5a.5.5 0 1 0-.998.06Zm6.53-.528a.5.5 0 0 0-.528.47l-.5 8.5a.5.5 0 0 0 .998.058l.5-8.5a.5.5 0 0 0-.47-.528ZM8 4.5a.5.5 0 0 0-.5.5v8.5a.5.5 0 0 0 1 0V5a.5.5 0 0 0-.5-.5Z"/>
              </svg> -->
              <h3 style="font-size: 16px;"><b>Wastage Qty</b></h3>
              <span style="font-size: 16px;">{{ this.utils.truncateNew(wastageQuantity)}} {{uomForRemainingUom}}</span>
          </div>

          <div class="buttons-container">
            <button mat-raised-button color="warn" (click)="back()" [disabled]="this.data.inStock == 0">Cancel</button>
            <button mat-raised-button class="button3" *ngIf="isShow" (click)="save()" [disabled]="this.data.inStock == 0">Convert</button>
          </div>
        </div>
</mat-card-content>
</mat-card>

