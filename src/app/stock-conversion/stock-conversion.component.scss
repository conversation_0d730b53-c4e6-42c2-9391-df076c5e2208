.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  // margin: 0 10%;

  .cards {
    display: flex;
    justify-content: space-between;
    width: 100%;
    // margin: 10px 0;
  }

  mat-icon.mat-card-avatar {
    width: 50px;
    height: 40px;
    font-size: 50px;
  }
}
.bottom-section {
  display: flex;
  justify-content: space-between;
  padding-top: 10px;
}

.bottom-details {
  width: 200px;
  padding-top: 15px;
}

.buttons-container {
  display: flex;
  gap: 10px;
  padding-top: 20px;
}

.svgIcons{
  margin-top: 7px;
}

.bottomValues{
  font-size: large;
}

.highlighted-row {
  // background-color: #FFFFE0;
  color: green;
}

.example-container-1{
  // max-height: 135px;
  overflow-y: auto;
}