import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ShareDataService } from '../_services/share-data.service';
import { Location } from '@angular/common';
import { PurchasesService } from '../_services/purchases.service';
import { MatTableDataSource, MatPaginator, MatSort, MatDialog } from '@angular/material';
import { FormBuilder, FormGroup, Validators , FormControl, FormArray, AbstractControl} from '@angular/forms';
import { Router } from '@angular/router';
import { NotificationService } from '../_services/notification.service';
import { UtilsService } from '../_utils/utils.service';
import { InvoiceDialogComponent } from '../_dialogs/invoice-dialog/invoice-dialog.component';

@Component({
  selector: 'app-stock-conversion',
  templateUrl: './stock-conversion.component.html',
  styleUrls: ['./stock-conversion.component.scss', "./../../common-dark.scss"]
})
export class StockConversionComponent implements OnInit {
  dataSource = new MatTableDataSource();
  scDataSource = new MatTableDataSource();
  grnDataSource = new MatTableDataSource();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  data: any;
  displayedColumns: string[];
  scDisplayedColumns: string[];
  grnDisplayedColumns: string[];
  conversionNumber = new FormControl();
  remarks = new FormControl();
  date = new FormControl();
  totalAmount : any;
  remainingAmount : any;
  totalWeight : any;
  remainingWeight : any;
  wastageQuantity: any;
  wastageAmount: any = 0;
  requestedStock: any;
  inStoreStock: any;
  documentDate: any;
  uomForRemainingUom: any;
  tempStock: any;
  isShow : boolean = false;
  selectedRowIndex: number = -1;
  grnId: any;
  requestedWeight: any;
  constructor(
    private sharedData: ShareDataService,
    private loc: Location,
    public purchases: PurchasesService,
    public router: Router,
    private notifyService: NotificationService,
    private utils: UtilsService,
    private dialog: MatDialog,

  ) { 
  }

  ngOnInit() {
    this.displayedColumns = ['itemCode','itemName','uom','stock','inStock','price','totalPrice']
    this.sharedData.stockConversionData.subscribe(stockConversionData => {
      this.data = stockConversionData;
    });
    // if(Object.keys(this.data).length === 0){
    //   this.router.navigate(['/home/<USER>'])
    // }
    this.documentDate = new Date(); 
    this.scDisplayedColumns = ['itemCode','itemName','uom','inStock','weight','price','totalAmount']
    this.grnDisplayedColumns = ['grnId','date','vendor','itemCode','itemName','packageName','quantity','price','action']

    let reqObj = {
      restaurantId : this.data.restaurantId,
      itemCode : this.data.itemCode,
      packageName : this.data.uom
    }

    this.purchases.getGrnsforStockConversion(reqObj).subscribe(res => {
      if(res.success == true){
        this.grnDataSource.data = res.data;
      }
    })
  }

  getChild(element){
    this.data.unitPrice = element.grnItems.unitPrice;
    if(this.data.inStock >= element.grnItems.stockConversionQty){
      this.data.stockConversionQty = element.grnItems.stockConversionQty;
    }else{
      this.data.stockConversionQty = this.data.inStock;
    }
    this.uomForRemainingUom = this.data.uom
    this.requestedStock = this.data.stockConversionQty;
    this.inStoreStock = this.data.inStock;
    this.totalAmount = this.data.unitPrice;
    this.totalWeight = this.data.stockConversionQty;
    this.remainingAmount = this.data.unitPrice;
    this.remainingWeight = this.data.stockConversionQty;
    this.tempStock = this.data.stockConversionQty
    this.dataSource.data = [this.data];
    this.grnId = element.grnId;
    let reqObj = {
      restaurantId : this.data.restaurantId,
      itemCode : this.data.itemCode,
      packageName : this.data.uom
    }
    this.purchases.getChild(reqObj).subscribe(res => {
      if(res.success == true){
        res.data.forEach(ele => {
          ele.weight = 0;
          ele.totalPrice = 0;
        })
        this.scDataSource.data = res.data;
      }
    })
    this.isShow = true;
  }

  back(){
    this.router.navigate(['/home/<USER>'])
  }

  save(){
    const hasWeightGreaterThanZero = this.scDataSource.data.some(item => item['weight'] > 0);
    if (hasWeightGreaterThanZero) {
      let reqObject = [{
        itemName : this.data.itemName,
        itemCode : this.data.itemCode,
        itemCategory : this.data.category,
        packageName : this.data.packageName,
        quantity : this.data.quantity,
        scItems : this.scDataSource.data,
      }]
      let obj = {
        restaurantId : this.data.restaurantId,
        tenantId : this.data.tenantId,
        grnId : this.grnId,
        quantity : this.utils.truncateNew(this.requestedStock),
        stockConversion: reqObject,
        remarks : this.remarks.value,
        conversionNumber : this.conversionNumber.value,
        date : this.date.value,
        totalAmount : this.data.unitPrice * this.data.stockConversionQty,
        totalQuantity : this.data.stockConversionQty,
        wastageAmount : this.wastageAmount,
        wastageQuantity : this.wastageQuantity,
      }
      this.purchases.startConversion(obj).subscribe(res => {
        if(res.success == true) {
          // this.notifyService.showSuccess("Stock conversion converted successfully","") ;
          // this.router.navigate(['/home']);
          // this.router.navigate(['/home/<USER>'])
          this.dialog.open(InvoiceDialogComponent, {
            data: {
            title: 'Stock Conversion',
            items: this.scDataSource.data,
            parentItems : this.dataSource.data,
            wastageQuantity : this.wastageQuantity,
            wastageAmount : this.wastageAmount,
            uom : this.data.uom,
            ok: function () {
              window.location.reload();
            }.bind(this),
          }
        });
        } else {
          this.utils.snackBarShowError("Something went wrong")
        }
      })
    } else {
      this.utils.snackBarShowInfo("Please enter a quantity of at least one item.")
    }
  }

  makeZero(event,element){
    let totalWeight = this.checkTotalWeight(this.scDataSource.data);
    totalWeight > this.requestedStock ? element.weight = 0 : undefined ;

    this.requestedWeight = this.scDataSource.data.reduce((total,el) => total + el['weight'],0)
    let currentUnitPrice = ((this.data.unitPrice * this.requestedStock) / this.requestedWeight) ;
    for (let i = 0; i < this.scDataSource.data.length; i++) {
      const el = this.scDataSource.data[i];
      if (el['weight'] > 0) {
        el['price'] = currentUnitPrice;
        el['totalPrice'] = el['weight'] * el['price'];
      } else {
        el['price'] = 0;
        el['totalPrice'] = el['weight'] * el['price'];
      }
    }
  }

  getTotal(key: string) {
    return this.utils.truncateNew(this.utils.getTotal(this.scDataSource.data, key)); 
  }

checkTotalWeight(element){    
  let total = element.reduce((key, value) => key + value.weight, 0);
  if((this.requestedStock - total) >= 0){
    this.wastageQuantity = this.utils.truncateNew(this.requestedStock - total)
  }else{
      this.utils.snackBarShowInfo("Enter a Weight below parent quantity");
  }
  return total
}


getRequestedQty(event,element){
  this.requestedStock = event ;
  element.stockConversionQty > this.inStoreStock ? (element.stockConversionQty = this.inStoreStock,this.requestedStock = this.inStoreStock) : null ;
  element.stockConversionQty < 0 ? (element.stockConversionQty = this.inStoreStock,this.requestedStock = this.inStoreStock) : null ;
  this.scDataSource.data.forEach((el)=>{
    el['weight'] = 0;
    el['totalPrice'] = 0; 
  })
  this.totalAmount = element.unitPrice;
  this.tempStock = element.stockConversionQty;
  this.checkTotalWeight(this.scDataSource.data) ;
}

selectRow(rowIndex: number) {
  this.selectedRowIndex = rowIndex;
}

focusFunctionWithOutForm(event , element , value){
  if(Number(element[value]) === 0){
    element[value] = null;
  }else if (event.target.value && element[value]){
    const inputValue = event.target.value;
    const formattedValue = inputValue.replace(/^0+/, '');
    element[value] = formattedValue;
  }
}

focusOutFunctionWithOutForm(element , value){
  if(element[value] === null){
    element[value] = 0
  }
}

checkNumericInput(event: any , element) {     
  if (element.grnItems.packageName === 'NOS') {
    const input = event.target.value;
    event.target.value = input.replace(/[^0-9]/g, ''); 
    element.stockConversionQty = event.target.value;
  }
}

}
