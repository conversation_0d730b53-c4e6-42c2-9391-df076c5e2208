<mat-card>
  <div class="row">
    <div class="col-xl-3 center-block actionbtn temp">
      <mat-form-field id="branch-select">
        <mat-select [disabled]="(!date) || (session.value == '')" placeholder="Branch" [formControl]="branch" multiple>
          <mat-option *ngFor="let bran of branchesData" [value]="bran.restaurantIdOld" (click)="uncheckAllSelect(1)">
            {{bran.branchLocation}}
          </mat-option>
        </mat-select>
        <input matInput [disabled]='true' *ngIf="branchesData.length == 1" style="display: none;">
      </mat-form-field>
    </div>
    <div class="col-xl-3 center-block actionbtn">
      <mat-form-field class="temp">
        <input matInput autocomplete="off" ngxDaterangepickerMd [minDate]="pickerDateRange.startDate"
          [maxDate]="pickerDateRange.endDate" *ngIf="true" placeholder="Choose a date" class="dateSelector"
          [ranges]="dateRanges" alwaysShowCalendars="true" [(ngModel)]="date">
          <mat-icon matDatepickerToggleIcon>
            <img src="./../../assets/calender.png" style="padding: 0px 15px 0px 15px;"/>
          </mat-icon>
      </mat-form-field>

      <mat-form-field class="dateSelector temp" *ngIf="false">
        <input matInput [min]="pickerDateRange.startDate" [max]="pickerDateRange.endDate" (click)="myDatepicker.open()"
          placeholder="Choose a Date" [matDatepicker]="myDatepicker" [(ngModel)]="date">
        <mat-datepicker-toggle matSuffix [for]="myDatepicker"></mat-datepicker-toggle>
        <mat-datepicker #myDatepicker></mat-datepicker>
      </mat-form-field>
    </div>
    <div class="col-xl-5 ">
      <div class="row">
        <div class="col-md-3 center-block actionbtn">
          <button mat-stroked-button 
            class="btn-block" (click)="find(3)"
            [disabled]="(!date) || (session.value == '') || (branch.value == '')">
            Find
          </button>        
        </div>
        <div class="col-md-3 center-block actionbtn">
          <button mat-stroked-button 
            class="btn-block" (click)="save()"
            [disabled]=" (!date) || (session.value == '') || !allowEdit || !(session.value.length === 1) || !(branch.value.length === 1) || !(this.date.startDate.format('DD-MM-YYYY') == this.date.endDate.format('DD-MM-YYYY'))">
            Save
          </button>   
        </div>
        <div class="col-md-3 center-block actionbtn">
          <button mat-stroked-button 
            class="btn-block" (click)="print()"
            [disabled]="(!date) || (session.value == '') || !allowActions">
            Print
          </button>             
        </div>
        <div class="col-md-3 center-block actionbtn">
          <button mat-stroked-button 
            class="btn-block" (click)="exportToExcel()"
            [disabled]=" (!date) || (session.value == '') || !allowActions">
            Export
          </button>             
        </div>
        <div class="col-md-2 center-block actionbtn" *ngIf="user.isCentralKitchen">
          <button mat-stroked-button 
            class="btn-block" (click)="reverseIbt()">
            Ibt
          </button>             
        </div>
      </div>
    </div>
  </div>
</mat-card>