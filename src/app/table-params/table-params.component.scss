mat-card {
  margin: 10px auto;
  min-height: 10vh;
  min-width: 90%;
  padding-bottom: 50px;
}

.temp{
  max-width: 100%;
  min-width: 280px;
  line-height: 1.125 !important;
}

.mat-form-field-appearance-fill .mat-form-field-subscript-wrapper {
  padding: 0 !important;
}

 .mat-stroked-button {
  border: 1px solid currentColor !important;
  padding: 0 15px !important;
  line-height: 42.5px !important;
}

.mat-icon {
  background-repeat: no-repeat;
  display: inline-block;
  fill: currentColor;
  height: auto !important;
  width: auto !important;
}

 .material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: unset !important;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-smoothing: antialiased;
}

.actionbtn {
  padding: 0;
  display: inline-block;
  line-height: 42.5px;
  border-color: darkgrey;
  text-align: center;
}

.center-block{
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30px;
}

mat-form-field, input, button{
  width: 80%;
}

