import { Component, OnInit, EventEmitter, Output, Input, ViewChild } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { DateUtilsService } from '../_services/date-utils.service';
import { AuthService, GlobalsService, ShareDataService } from '../_services';
import { Branch } from '../_models';
import { MatOption } from '@angular/material'
import * as moment from 'moment';
import { ChangeDetectorRef} from '@angular/core';
import { NotificationService } from '../_services/notification.service';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material';
import { MomentDateModule, MomentDateAdapter } from '@angular/material-moment-adapter';
import {takeUntil } from 'rxjs/operators';
import { ReplaySubject, Subject } from 'rxjs';
import { UtilsService } from '../_utils/utils.service';

export const MY_FORMATS = {
  parse: {
      dateInput: 'LL'
  },
  display: {
      dateInput: 'DD-MM-YYYY',
      monthYearLabel: 'YYYY',
      dateA11yLabel: 'LL',
      monthYearA11yLabel: 'YYYY'
  }
};
@Component({
  selector: 'app-table-params',
  templateUrl: './table-params.component.html',
  styleUrls: ['./table-params.component.scss'],
  providers:[{ provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
            { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS }
          ]
})
export class TableParamsComponent implements OnInit {
  private unsubscribe$ = new Subject<void>();
  @Output() getItems: EventEmitter<any> = new EventEmitter();
  @Output() updateItems: EventEmitter<any> = new EventEmitter();
  @Output() reverseIbtEvent: EventEmitter<any> = new EventEmitter();
  @Output() printItems: EventEmitter<any> = new EventEmitter();
  @Output() xportToExcel: EventEmitter<any> = new EventEmitter();
  @Input() allowEdit: boolean;
  @Input() isKitchen: boolean;
  @Input() title: string;
  @Input() isForecastReport: boolean;
  @ViewChild('branchSelectAll') branchSelectAll: MatOption;
  @ViewChild('sessSelectAll') sessSelectAll: MatOption;
  user: any;
  branches: Branch[];
  isAdmin: boolean;
  session: any;
  branch = new FormControl();
  branchValue: string;
  date = null;
  dateRange: any = null;
  pickerDateRange: any = {};
  @Input() allowActions: boolean = false;

  public readonly dateRanges: any = {
    'Today': [moment(), moment()],
    'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
    'Last 7 Days': [moment().subtract(6, 'days'), moment()],
    'Last 30 Days': [moment().subtract(29, 'days'), moment()],
    'This Month': [moment().startOf('month'), moment().endOf('month')],
    'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
  }
  forcastRestaurantId: any;
  branchArray: any;
  getBranchData: any[]
  branchesData: any[]
  selectedBranch: any[];
  constructor(
    private notifyService: NotificationService,
    private dateUtils: DateUtilsService,
    private utils: UtilsService,
    private sharedData: ShareDataService, 
    private auth: AuthService,
    private cdref: ChangeDetectorRef) {
    this.user = this.auth.getCurrentUser()
    this.dateRange = JSON.parse(sessionStorage.getItem(GlobalsService.dateRange));
    this.branches = JSON.parse(sessionStorage.getItem(GlobalsService.branches));

    this.sharedData.sharedBranchData.pipe(takeUntil(this.unsubscribe$)).subscribe((val) => {
      this.getBranchData = val;
      if(this.getBranchData.length == 0 ){
        this.branchesData = this.user.restaurantAccess;
      }else if(this.getBranchData.length == 1){
        const toSelect = this.getBranchData.find(data => data.branchLocation == this.getBranchData[0].branchLocation);
        this.branchesData = this.getBranchData
        this.branch.setValue([toSelect.restaurantIdOld])
        this.branchValue = toSelect.branchLocation
        this.uncheckAllSelect(1)
      }else{
        this.branchesData = this.getBranchData
      }
  });
  }

  ngAfterContentChecked() {
    this.cdref.detectChanges();
  }

  ngOnInit() {
    this.session = {
      'value': ['Morning']
    }

    this.branches.sort((b1, b2) => {
      if (b1.branchLocation > b2.branchLocation) return 1;
      else return -1;
    });
    if (this.branches.length == 1) {
      this.branch.setValue([this.branches[0].restaurantIdOld])
      this.branchValue = this.branches[0].branchLocation
    }
    this.isAdmin = (this.auth.getCurrentUser().role === GlobalsService.admin);
    this.pickerDateRange.startDate = this.dateRange.startDate;
    this.dateRange.startDate = moment(this.dateRange.startDate);
    this.dateRange.endDate = moment(this.dateRange.endDate);
    this.dateRange.actuals = moment(this.dateRange.actuals);
    this.pickerDateRange.startDate = this.dateRange.startDate;
    if (!this.isForecastReport)
      this.pickerDateRange.endDate = this.dateRange.endDate;
    else
      this.pickerDateRange.endDate = this.dateRange.actuals;
  }

  find(i) {
    if (!this.date.startDate) {
      this.utils.snackBarShowWarning("Please select date");
    }
    else {
      let date;
      switch (i) {
        case 0:
          date = new Date(Date.now() - 864e5);
          break;
        case 1:
          date = new Date();
          break;
        case 2:
          date = this.date;
          break;
      }
      let obj: any = {};
      obj.session = this.session.value;
      obj.date = this.dateUtils.getDateParam(this.date);
      obj.branchArray = this.branch.value;
      this.forcastRestaurantId = obj.branchArray
      this.sharedData.changeForcastRes(obj)
      this.getItems.emit(obj);
      this.allowActions = false;
    }
  }

  save() {
    this.updateItems.emit();
  }

  print() {
    let obj: any = {};
    obj.date = this.dateUtils.getDateParam(this.date);
    obj.branchArray = this.branch.value;
    obj.session = this.session.value;
    this.printItems.emit(obj);
  }

  exportToExcel() {
    let obj: any = {};
    let date = this.dateUtils.getDateParam(this.date);
    obj.dateRange = `${moment(date.startDate).format('DD-MMMM-YYYY')}_to_${moment(date.endDate).format('DD-MMMM-YYYY')}`;
    obj.branch = '';
    this.branch.value.forEach(item => {
      obj.branch += ` ${item},`;
    });
    obj.branch = obj.branch.substr(0, obj.branch.length - 1).trim();
    obj.session = '';
    this.session.value.forEach(item => {
      obj.session += ` ${item},`;
    });
    obj.session = obj.session.substr(0, obj.session.length - 1).trim();
    this.xportToExcel.emit(obj);
  }

  uncheckAllSelect(num) {
    switch (num) {
      case 0:
        break;
      case 1:
        if (this.branchSelectAll && this.branchSelectAll.selected)
          this.branchSelectAll.deselect();
        else if (this.branch.value.length === this.branches.length)
          this.branchSelectAll.select()
        break;
    }
  }

  generateTableParamObj() {
    let obj: any = {};
    if (this.session.value && this.session.value.length > 0) {
      obj.session = this.removeAllSeclectVal(this.session.value);
    }
    if (this.branch.value && this.branch.value.length > 0) {
      obj.branchArr = this.removeAllSeclectVal(this.branch.value);
    }
    obj.date = this.dateUtils.getDateParam(this.date);
    return obj;
  }

  removeAllSeclectVal(arr: any[]) {
    if (arr && arr.length > 0 && arr.indexOf(0) !== -1)
      arr.pop()
    return arr;

  }

  reverseIbt() {
    let obj = {
      date: this.dateUtils.getDateParam(this.date),
      branchArr: this.branches,
      tenantId: this.user.tenantId,
      uId: this.user.mId,
      session: this.removeAllSeclectVal(this.session.value),
    }
    this.reverseIbtEvent.emit(obj)
  }
}

