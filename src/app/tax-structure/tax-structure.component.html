<mat-card>
  <div class="headTag">
    Tax Structure
  </div>
</mat-card>

<mat-card *ngIf="!showAddTax">

  <button mat-raised-button *ngIf="!showAddTax" class="button3 taxButton mt-3" (click)="addNewTax()">
    Add
  </button>

  <table #table mat-table matSort [dataSource]="dataSource">

    <ng-container matColumnDef="digiId">
      <th mat-header-cell *matHeaderCellDef> Digi Id </th>
      <td mat-cell *matCellDef="let element" (click)="edit(element)" class="links" matTooltip="Show Details"> {{ element.taxStructureId }}</td>
    </ng-container>

    <ng-container matColumnDef="tenantId">
      <th mat-header-cell *matHeaderCellDef> Tenant Id </th>
      <td mat-cell *matCellDef="let element" matTooltip="Show Details"> {{ element.tenantId }}</td>
    </ng-container>

    <ng-container matColumnDef="location">
      <th mat-header-cell *matHeaderCellDef> Location </th>
      <td mat-cell *matCellDef="let element" style="width: 200px;"> 
        <mat-form-field appearance="outline" style="margin: 5px; width: 100px;">
            <mat-select placeholder="Location" [(ngModel)]="element.location[0]">
              <mat-option *ngFor="let rest of restaurant(element)" [value]="rest">
                {{ rest }}
              </mat-option>
            </mat-select>
          </mat-form-field>
      </td>
    </ng-container>

    <ng-container matColumnDef="name">
      <th mat-header-cell *matHeaderCellDef> Name </th>
      <td mat-cell *matCellDef="let element">  {{ element.name | uppercase }} </td>
    </ng-container>

    <ng-container matColumnDef="description">
      <th mat-header-cell *matHeaderCellDef> Description </th>
      <td mat-cell *matCellDef="let element"> {{ element.description }}  </td>
    </ng-container>

    <ng-container matColumnDef="effectiveDate">
      <th mat-header-cell *matHeaderCellDef> Effective Date</th>
      <td mat-cell *matCellDef="let element">  {{ element.effectiveDate | date: 'MMM d, y, h:mm:ss a' }} </td>
    </ng-container>

    <ng-container matColumnDef="taxCode">
      <th mat-header-cell *matHeaderCellDef> Tax Code </th>
      <td mat-cell *matCellDef="let element"> {{ element.taxCode }} </td>
    </ng-container>

    <ng-container matColumnDef="type">
      <th mat-header-cell *matHeaderCellDef> Type </th>
      <td mat-cell *matCellDef="let element"> {{ element.type }} </td>
    </ng-container>

  <ng-container matColumnDef="percentage">
    <th mat-header-cell *matHeaderCellDef> Percentage </th>
    <td mat-cell *matCellDef="let element"> {{ element.percentage }} </td>
  </ng-container>

  <ng-container matColumnDef="calculationType">
    <th mat-header-cell *matHeaderCellDef> Calculation Type </th>
    <td mat-cell *matCellDef="let element"> {{ element.calculationType }} </td>
  </ng-container>

  <ng-container matColumnDef="taxNoc">
    <th mat-header-cell *matHeaderCellDef> Tax Noc </th>
    <td mat-cell *matCellDef="let element">  {{ element.taxNoc }}</td>
  </ng-container>

  <ng-container matColumnDef="action">
    <th mat-header-cell *matHeaderCellDef> Action </th>
    <td mat-cell *matCellDef="let element"  matTooltip="delete"> 
      <mat-icon (click)="deleteTax(element)">delete</mat-icon>
    </td>
  </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
  </table>

  <mat-paginator [showTotalPages]="5" [pageSize]="10"[pageSizeOptions]="pageSizes"></mat-paginator>

</mat-card>

<mat-card class="p-5" *ngIf="showAddTax">
  <div class="container">

    <div class="d-flex justify-content-end">
      <button mat-raised-button *ngIf="showAddTax" class="button3 mr-2 mt-3 mb-2" (click)="back()">
        Back
      </button>
      <button mat-raised-button *ngIf="!showUpdate" class="button3 mt-3 mb-2" (click)="createTax()">
        Create
      </button>
      <button mat-raised-button *ngIf="showUpdate" class="button3 mt-3 mb-2" (click)="updateTax()">
        Update
      </button>
    </div>

    <form [formGroup]="taxStructureForm" style="display: grid;">
        <div>
          <mat-form-field appearance="outline" style="width: 100% !important;">
            <mat-label>Tenant ID</mat-label>
            <input matInput placeholder="Tenant ID" formControlName="tenantId" readonly>
          </mat-form-field>
        </div>

        <div>
          <mat-form-field appearance="outline" style="width: 100% !important;">
            <mat-label>Name</mat-label>
            <input matInput placeholder="Name" formControlName="name">
          </mat-form-field>
        </div>
  
        <div>
          <mat-form-field appearance="outline" style="width: 100% !important;">
            <mat-label>Description</mat-label>
            <!-- <input matInput placeholder="Description" formControlName="description"> -->
            <textarea matInput placeholder="Description" formControlName="description" maxlength="30"></textarea>
          </mat-form-field>
        </div>

        <!-- <div>
          <mat-form-field appearance="outline" style="width: 100% !important;">
            <mat-label>Type</mat-label>
            <mat-select placeholder="Type" formControlName="type">
              <mat-option *ngFor="let type of type" [value]="type">
                {{ type }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div> -->
  
        <!-- <div>
          <mat-form-field appearance="outline" style="width: 100% !important;">
            <mat-label>Effective Date</mat-label>
            <input matInput [matDatepicker]="picker3"
              formControlName="effectiveDate" placeholder="Start Date" (dateChange)="effectiveDateFunc($event)"/>
            <mat-datepicker-toggle matSuffix [for]="picker3"  >
              <mat-icon matDatepickerToggleIcon>
                <img src="./../../assets/calender.png"/>
              </mat-icon>  
            </mat-datepicker-toggle>
            <mat-datepicker #picker3 ></mat-datepicker>
          </mat-form-field>
        </div> -->
  
        <div>
          <!-- <mat-form-field appearance="outline" style="width: 100% !important;">
            <mat-label>Tax Code</mat-label>
            <mat-select placeholder="Select TaxCode" formControlName="taxCode">
              <mat-option *ngFor="let tax of taxCode" [value]="tax">
                {{ tax }}
              </mat-option>
            </mat-select>
          </mat-form-field> -->

            <mat-form-field appearance="outline" style="width: 100% !important;">
              <mat-label>Tax Code</mat-label>
              <input matInput placeholder="Tax Code" aria-label="Category" [matAutocomplete]="auto1"
                formControlName="taxCode" (keyup.enter)="addOption()"
                oninput="this.value = this.value.toUpperCase()">
              <mat-autocomplete #auto1="matAutocomplete" (optionSelected)="optionSelected($event.option)">
                <mat-option *ngFor="let cat of taxCodeSBank | async" [value]="cat">
                  <span>{{ cat }}</span>
                </mat-option>
              </mat-autocomplete>
            </mat-form-field>
        </div>

        <div>
          <mat-form-field appearance="outline" style="width: 100% !important;">
          <mat-label>Select Location</mat-label>
            <mat-select placeholder="Select Location" formControlName="location" multiple>
              <mat-option>
                <ngx-mat-select-search placeholderLabel="Location..." noEntriesFoundLabel="'no Location found'"
                  [formControl]="locationFilterCtrl"></ngx-mat-select-search>
              </mat-option>
              <mat-option *ngFor="let rest of locationBank | async" [value]="rest.restaurantIdOld">
                {{ rest.branchName }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <div *ngIf="showCheckBox || (taxStructureForm.value.centralGST || taxStructureForm.value.stateGST || taxStructureForm.value.interStateGST )">
          <div class="d-flex align-items-end m-2">
            <mat-checkbox class="example-margin" style="min-width: 140px;" [(ngModel)]="centralGst" (change)="toggleCentralGst()" [ngModelOptions]="{standalone: true}">Central GST</mat-checkbox>
            <input *ngIf="centralGst" matInput type="number" class="outline othercharge ml-5" formControlName="centralGST"
            (keyup)="checkTotalValue($event.target.value)" placeholder=" %"/>
          </div>
           <div class="d-flex align-items-end m-2">
            <mat-checkbox class="example-margin" style="min-width: 140px;" [(ngModel)]="stateGst" (change)="toggleStateGst()" [ngModelOptions]="{standalone: true}">State GST</mat-checkbox>
            <input *ngIf="stateGst" matInput type="number" class="outline othercharge ml-5" formControlName="stateGST"
            (keyup)="checkTotalValue($event.target.value)" placeholder=" %"/>
          </div>
           <div class="d-flex align-items-end m-2">
            <mat-checkbox class="example-margin" style="min-width: 140px;" [(ngModel)]="interGst" (change)="toggleIGst()" [ngModelOptions]="{standalone: true}">InterState GST</mat-checkbox>              
            <input *ngIf="interGst" matInput type="number" class="outline othercharge ml-5" formControlName="interStateGST" 
            (keyup)="checkTotalValue($event.target.value)" placeholder=" %"/>
          </div>
        </div> 
  
        <!-- <div class="col-md-6">
          <mat-form-field appearance="outline" style="width: 100% !important;">
            <mat-label>Percentage</mat-label>
            <input matInput placeholder="Percentage" formControlName="percentage">
          </mat-form-field>
        </div> -->
  
        <!-- <div class="col-md-6">
          <mat-form-field appearance="outline" style="width: 100% !important;">
            <mat-label>Calculation Type</mat-label>
            <mat-select placeholder="Calculation Type" formControlName="calculationType">
              <mat-option *ngFor="let cal of calculationType" [value]="cal">
                {{ cal }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div> -->
  
        <!-- <div class="col-md-6">
          <mat-form-field appearance="outline" style="width: 100% !important;">
            <mat-label>Tax NOC</mat-label>
            <mat-select placeholder="Tax NOC" formControlName="taxNoc">
              <mat-option *ngFor="let taxNoc of taxNoc" [value]="taxNoc">
                {{ taxNoc }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div> -->
    </form>
  </div>

</mat-card>
