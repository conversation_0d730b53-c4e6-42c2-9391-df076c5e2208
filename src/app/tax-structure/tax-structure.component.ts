import { Component, OnInit, ViewChild } from '@angular/core';
import { AuthService, ShareDataService, VendorsService } from '../_services';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NotificationService } from '../_services/notification.service';
import { MasterdataupdateService } from '../_services/masterdataupdate.service';
import { MatDatepickerInputEvent, MatDialog, MatPaginator, MatTableDataSource } from '@angular/material';
import { UtilsService } from "../_utils/utils.service";
import { Vendor } from '../_models';
import { map, startWith, takeUntil } from 'rxjs/operators';
import { Observable, ReplaySubject, Subject } from 'rxjs';
import { FormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { SimpleDialogComponent } from '../_dialogs/simple-dialog/simple-dialog.component';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material';
import { MomentDateModule, MomentDateAdapter, MAT_MOMENT_DATE_ADAPTER_OPTIONS, MAT_MOMENT_DATE_FORMATS } from '@angular/material-moment-adapter';
export function provideMomentDateAdapter(): any {
  return [
    { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS] },
    { provide: MAT_DATE_FORMATS, useValue: MAT_MOMENT_DATE_FORMATS }
  ];
}

@Component({
  selector: 'app-tax-structure',
  templateUrl: './tax-structure.component.html',
  styleUrls: ['./tax-structure.component.scss', './../../common-dark.scss'],
  providers: [
    {provide: MAT_DATE_LOCALE, useValue: 'fr'},
    provideMomentDateAdapter(),
  ],
})
export class TaxStructureComponent implements OnInit {
  displayedColumns: any;
  dataSource = new MatTableDataSource();
  @ViewChild(MatPaginator) paginator: MatPaginator;
  pageSizes = [];
  showAddTax : boolean = false;
  public taxStructureForm: FormGroup;
  public location: any[] = [];
  public locationBank: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public locationFilterCtrl: FormControl = new FormControl();
  public taxCode: any[] = [];
  // public taxCodeBank: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  // public taxCodeFilterCtrl: FormControl = new FormControl();
  public type: any[] = [];
  public typeBank: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public typeFilterCtrl: FormControl = new FormControl();
  public calculationType: any[] = [];
  public calculationTypeBank: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public calculationTypeFilterCtrl: FormControl = new FormControl();
  public taxNoc: any[] = [];
  public taxNocBank: ReplaySubject<any[]> = new ReplaySubject<any[]>(1);
  public taxNocFilterCtrl: FormControl = new FormControl();
  protected _onDestroy = new Subject<void>();
  user: any;
  showUpdate : boolean = false;
  updateTaxData: any = {};
  question = 'Would you like to add "';
  taxCodeSBank: Observable<string[]>;

  centralGst: boolean = false;
  stateGst: boolean = false;
  interGst: boolean = false;
  showCheckBox: boolean = false;
  restaurantLocation: any;
  constructor(
    public fb: FormBuilder,
    private auth: AuthService,
    private masterDataService: MasterdataupdateService,
    private utils: UtilsService,
    private dialog: MatDialog,

  ) { 
    this.user = this.auth.getCurrentUser();

    this.taxStructureForm = fb.group({
      tenantId: ['', Validators.required],
      location: ['', Validators.required],
      name: ['', Validators.required],
      description: [''],
      // effectiveDate: ['', Validators.required],
      taxCode: ['', Validators.required],
      // type: ['', Validators.required],
      // percentage: ['', Validators.required],
      // calculationType: ['', Validators.required],
      // taxNoc: ['', Validators.required],
      centralGST: [0, Validators.required],
      stateGST: [0, Validators.required],
      interStateGST: [0, Validators.required],
      tax : ['', Validators.required],
    });
    this.getTaxStructure();

    this.location = this.user.restaurantAccess
    this.locationBank.next(this.location.slice());
    this.locationFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
        this.locationfilterBanks();
    });

    this.taxCode = ['GST', 'CST' , 'TCS' , 'VAT' , 'TDS' , 'FBT']
    this.taxCodeSBank = this.taxStructureForm.get('taxCode').valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), this.taxCode)));

    // this.taxCodeBank.next(this.taxCode.slice());
    // this.taxCodeFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
    //   this.Filter(this.taxCodeBank, this.taxCodeFilterCtrl, this.taxCode);
    // });

    this.type = ['Percentage' , 'Amount']
    this.typeBank.next(this.type.slice());
    this.typeFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
      this.Filter(this.typeBank, this.typeFilterCtrl, this.type);
    });

    this.calculationType = ['no' , 'data']
    this.calculationTypeBank.next(this.calculationType.slice());
    this.calculationTypeFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
      this.Filter(this.calculationTypeBank, this.calculationTypeFilterCtrl, this.calculationType);
    });

    this.taxNoc = ['no' , 'data']
    this.taxNocBank.next(this.taxNoc.slice());
    this.taxNocFilterCtrl.valueChanges.pipe(takeUntil(this._onDestroy)).subscribe(() => {
      this.Filter(this.taxNocBank, this.taxNocFilterCtrl, this.taxNoc);
    });
   }

   protected locationfilterBanks() {
    if (!this.location) {
      return;
    }
    let search = this.locationFilterCtrl.value;
    if (!search) {
      this.locationBank.next(this.location.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    this.locationBank.next(
      this.location.filter(loc => loc.branchName.toLowerCase().indexOf(search) > -1)
    );
  }

  protected Filter(bank:any, form:any, data:any) {
    if (!bank) {
      return;
    }
    let search = form.value;
    if (!search) {
      data.next(bank.slice());
      return;
    } else {
      search = search.toLowerCase();
    }
    bank.next(
      data.filter(data => data.toLowerCase().indexOf(search) > -1)
    );
  }

  ngOnInit() {
    this.displayedColumns = ['digiId','location','name', 'description','taxCode','action'];
  }

  addNewTax(){
    this.taxStructureForm.get('tenantId').setValue(this.user.tenantId)
    this.showAddTax = true;
    this.showUpdate = false;
  }

  back(){
    this.showAddTax = false;
    this.showCheckBox = false;
    this.taxStructureForm.reset();
    this.getTaxStructure();
  }

  edit(element){
    this.updateTaxData = element;
    // this.taxStructureForm.get('tenantId').setValue(this.user.tenantId)
    this.showAddTax = true;
    this.showUpdate = true;
    this.centralGst = true;
    this.stateGst = true;
    this.interGst = true;
    this.taxStructureForm.get('tenantId').setValue(element.tenantId);
    this.taxStructureForm.get('location').setValue(element.location);
    this.taxStructureForm.get('name').setValue(element.name);
    this.taxStructureForm.get('description').setValue(element.description);
    //  this.taxStructureForm.get('effectiveDate').setValue(element.effectiveDate);
    this.taxStructureForm.get('taxCode').setValue(element.taxCode);
    //  this.taxStructureForm.get('type').setValue(element.type);
    //  this.taxStructureForm.get('percentage').setValue(element.percentage);
    //  this.taxStructureForm.get('calculationType').setValue(element.calculationType);
    //  this.taxStructureForm.get('taxNoc').setValue(element.taxNoc);
    const centralGST = element.centralGST ? element.centralGST : 0;
    const stateGST =  element.stateGST ? element.stateGST : 0;
    const interStateGST =  element.interStateGST ? element.interStateGST : 0;
    this.taxStructureForm.get('centralGST').setValue(centralGST);
    this.taxStructureForm.get('stateGST').setValue(stateGST);
    this.taxStructureForm.get('interStateGST').setValue(interStateGST);

  }

  effectiveDateFunc(){

  }

  optionSelected(option: any) {
    this.showCheckBox = true;    
    if (option.value.indexOf(this.question) === 0) {      
      this.addOption();
    } else {
      // this.getSubCategories(this.taxStructureForm.value.taxCode);
    }
    this.taxCodeSBank = this.taxStructureForm.get('taxCode').valueChanges.pipe(startWith(''), map(value => this._filter((value || ''), this.taxCode)));
  }

  addOption() {
    this.showCheckBox = true;
    this.taxStructureForm.controls['taxCode'].patchValue(this.removePromptFromOption(this.taxStructureForm.value.taxCode));
    // this.getSubCategories(this.taxStructureForm.value.taxCode);
  }

  removePromptFromOption(option) {
    if (option.startsWith(this.question)) {
      option = option.substring(this.question.length, option.length - 1);
    }
    return option;
  }

  protected _filter(value: string, input: string[]): string[] {
    let filterValue = value.toLowerCase();
    let filtered = input.filter(option => option.toLowerCase().includes(filterValue));
        if (filtered.length == 0) {
      filtered = [this.question + value + '"'];
    }
        return filtered
  }

  getTaxStructure(){
    let obj = {}
    obj['tenantId'] = this.user.tenantId;
    this.masterDataService.getTaxStructure(obj).subscribe((response: any) => {
      if (response.success === true) {
        this.displayedColumns = ['digiId','location','name', 'description', 'taxCode','action'];
        this.dataSource.data = response.data
        this.pageSizes = this.utils.getPageSizes(this.dataSource.data)
        this.dataSource.paginator = this.paginator;
      }
    }); 
  }

  createTax(){
    let inputObj = {}
    inputObj['tenantId'] = this.taxStructureForm.value.tenantId;
    inputObj['location'] = this.taxStructureForm.value.location;
    inputObj['name'] = this.taxStructureForm.value.name;
    inputObj["description"] = this.taxStructureForm.value.description;
    // inputObj['effectiveDate'] = this.taxStructureForm.value.effectiveDate;
    inputObj['taxCode'] = this.taxStructureForm.value.taxCode;
    // inputObj["type"] = this.taxStructureForm.value.type;
    // inputObj["percentage"] = this.taxStructureForm.value.percentage;
    // inputObj["calculationType"] = this.taxStructureForm.value.calculationType;
    // inputObj["taxNoc"] = this.taxStructureForm.value.taxNoc;
    inputObj["centralGST"] = this.taxStructureForm.value.centralGST;
    inputObj["stateGST"] = this.taxStructureForm.value.stateGST;
    inputObj["interStateGST"] = this.taxStructureForm.value.interStateGST;
    inputObj["tax"] =  this.taxStructureForm.value.centralGST + this.taxStructureForm.value.stateGST +this.taxStructureForm.value.interStateGST
    this.taxStructureForm.get('tax').setValue(inputObj["tax"])
    this.masterDataService.createTaxStructure(inputObj).subscribe((response: any) => {
      if (response.success === true ) {
        this.utils.snackBarShowSuccess("created successfully")
        this.back();
      }
      else {
        this.utils.snackBarShowError("Something Wrong, please try again later")
      }
    });
  }

  updateTax(){
    let inputObj = {}
    let tax =  this.taxStructureForm.value.centralGST + this.taxStructureForm.value.stateGST +this.taxStructureForm.value.interStateGST
    this.taxStructureForm.get('tax').setValue(tax)
    inputObj['tenantId'] = this.updateTaxData['tenantId'];
    inputObj['taxStructureId'] = this.updateTaxData['taxStructureId'];
    inputObj['data'] = this.taxStructureForm.value;
 
    this.masterDataService.updateTaxStructure(inputObj).subscribe((response: any) => {
      if (response['result'] === true ) {
        this.utils.snackBarShowSuccess("updated successfully")
        this.back();
      }
      else {
        this.utils.snackBarShowError("Something Wrong, please try again later")
      }
    });
  }

  deleteTax(element){
    this.dialog.open(SimpleDialogComponent, {
      data: {
        title: 'Delete Tax',
        msg: 'Are you sure you want to Delete Tax ?',
        ok: function () {
          let inputObj = {}
          inputObj['taxStructureId'] = element['taxStructureId'];
          this.masterDataService.deleteTaxStructure(inputObj).subscribe((response: any) => {
            if (response['result'] === true ) {
              this.utils.snackBarShowSuccess("Deleted successfully")
              this.back();
            }else {
              this.utils.snackBarShowError("Something Wrong, please try again later")
            }
          });
        }.bind(this)
      }
    });
  }

  toggleCentralGst() {
    // this.centralGst = !this.centralGst;
  }

  toggleStateGst() {
    // this.stateGst = !this.stateGst;
  }

  toggleIGst() {
    // this.iGst = !this.iGst;
  }

  restaurant(element){
    return element.location;
  }

  checkTotalValue(value){
    let taxValue = this.taxStructureForm.value.centralGST + this.taxStructureForm.value.stateGST + this.taxStructureForm.value.interStateGST
    // console.log(taxValue);
    // console.log(this.taxStructureForm.value.centralGST);
    // console.log(this.taxStructureForm.value.stateGST);
    // console.log(this.taxStructureForm.value.interStateGST);
    // console.log(this.taxStructureForm.value.taxCode);
  }

}
