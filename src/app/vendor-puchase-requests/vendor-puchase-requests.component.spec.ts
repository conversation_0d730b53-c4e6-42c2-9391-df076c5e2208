import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { VendorPuchaseRequestsComponent } from './vendor-puchase-requests.component';

describe('VendorPuchaseRequestsComponent', () => {
  let component: VendorPuchaseRequestsComponent;
  let fixture: ComponentFixture<VendorPuchaseRequestsComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ VendorPuchaseRequestsComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(VendorPuchaseRequestsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
