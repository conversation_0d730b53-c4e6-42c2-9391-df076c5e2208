import { Component, OnInit } from '@angular/core';
import { GlobalsService } from '../_services';
@Component({
  selector: 'app-vendor-puchase-requests',
  templateUrl: './vendor-puchase-requests.component.html',
  styleUrls: ['./vendor-puchase-requests.component.scss']
})
export class VendorPuchaseRequestsComponent implements OnInit {
  dataObj: any = {};
  constructor() {
  }

  ngOnInit() {
    this.dataObj.displayedColumns = GlobalsService.purchasheRequestsColumns;
    this.dataObj.title = "Purchase Requests";
    this.dataObj.data = [
      {
        onHand: 12,
        optStock: 122,
        openOrders: 32,
        itemName: 'Heiniken',
        orderQty: 22,
        openToBuy: 344,
        leadTime: 32,
        unitPrice: 87,
        unit: 'each',
        itemCode: "909929",
        customerName: "Bob's Bar",
        supplyDate: '24-05-2019'
      },
      {
        onHand: 12,
        optStock: 122,
        openOrders: 32,
        itemName: 'Amstel',

        orderQty: 234,
        unitPrice: 87,
        unit: 'each',
        itemCode: "909929",
        openToBuy: 344,
        leadTime: 32,
        customerName: "Bob's Bar",

        customer: {
          name: "Bob's Bar"
        },
        supplyDate: '26-05-2019'
      },
      {
        onHand: 12,
        optStock: 122,
        openOrders: 32,
        itemName: 'Kingfisher',
        customerName: "Nandhana",

        orderQty: 54,
        openToBuy: 344,
        leadTime: 32,
        unitPrice: 87,
        unit: 'each',
        itemCode: "909929",
        customer: {
          name: "Nandhana"
        },
        supplyDate: '24-05-2019'

      },
      {
        onHand: 12,
        optStock: 122,
        openOrders: 32,
        itemName: 'Budweiser',
        customerName: "CP",

        orderQty: 23,
        openToBuy: 344,
        leadTime: 32,
        unitPrice: 87,
        unit: 'each',
        itemCode: "909929",
        customer: {
          name: "CP"
        },
        supplyDate: '24-06-2019'

      },
      {
        onHand: 12,
        optStock: 122,
        openOrders: 32,
        itemName: 'Becks Ice',
        orderQty: 666,
        openToBuy: 344,
        leadTime: 32,
        unitPrice: 87,
        unit: 'each',
        itemCode: "909929",
        customerName: "Pizzonomy",

        customer: {
          name: "Pizzonomy"
        },
        supplyDate: '24-05-2018'

      },
    ];
    this.dataObj.isVendor = true;

  }

}
