import { Component, OnInit } from '@angular/core';
import { GlobalsService } from '../_services/globals.service';
@Component({
  selector: 'app-vendor-purchase-orders',
  templateUrl: './vendor-purchase-orders.component.html',
  styleUrls: ['./vendor-purchase-orders.component.scss']
})
export class VendorPurchaseOrdersComponent implements OnInit {
  dataObj : any = {};
  constructor() {
  }

  ngOnInit() {
    this.dataObj.displayedColumns = GlobalsService.VendorpurchaseOrdersReqColumns;
    this.dataObj.title = "Purchase Orders";
    this.dataObj.data =  [
      {
        id : '192000000043',
        onHand: 12,
        optStock: 122,
        openOrders: 32,
        itemName: 'Heiniken',
        reqQty: 22,
        openToBuy: 344,
        leadTime: 32,
        unitPrice: 87,
        unit: 'each',
        itemCode: "909929",
        orderQty: 22,

        customer : {
          name : "<PERSON>'s bar"
        },
        vendor : 'abc',
        supplyDate: '24-05-2019',
        status : 'pending'

      },
      {
        id : '192000000453',
        onHand: 12,
        optStock: 122,
        openOrders: 32,
        itemName: 'Heinken',
        vendor : 'xyz',
        reqQty: 234,
        unitPrice: 83,
        unit: 'each',
        itemCode: "909929",
        openToBuy: 344,
        leadTime: 32,
        customer : {
          name : "Bob's bar"
        },
        orderQty: 22,

        supplyDate: '24-05-2019',
        status : 'pending',

      },
      {
        id : '192000000876',
        status : 'partially delivered',

        onHand: 12,
        optStock: 122,
        openOrders: 32,
        vendor : 'pqr',
        itemName: 'Kingfisher',

        reqQty: 54,
        openToBuy: 344,
        leadTime: 32,
        unitPrice: 87,
        unit: 'each',
        itemCode: "909929",
        orderQty: 22,

        customer : {
          name : "Nandhana"
        },
        supplyDate: '24-05-2019'

      },
      {
        id : '192000087056',
        status : 'delivered',

        onHand: 12,
        optStock: 122,
        openOrders: 32,
        itemName: 'Budweiser',
        vendor : 'def',
        reqQty: 23,
        openToBuy: 344,
        leadTime: 32,
        unitPrice: 87,
        unit: 'each',
        itemCode: "909929",
        customer : {
          name : "CP"
        },
        supplyDate: '24-05-2019',
        orderQty: 22,

      },
      {
        status : 'rejected',
        id : '192000000126',

        onHand: 12,
        optStock: 122,
        openOrders: 32,
        itemName: 'Becks Ice',
        reqQty: 666,
        vendor : 'qwerty',
        openToBuy: 344,
        leadTime: 32,
        unitPrice: 87,
        unit: 'each',
        itemCode: "909929",
        orderQty: 22,

        customer : {
          name : "Pizzonomy"
        },
        supplyDate: '24-05-2019'

      },
    ];
    this.dataObj.data.forEach( item => {
      item.customerName = item.customer.name
    });
    this.dataObj.isVendor = true;
    this.dataObj.tableType = 1;
  }

}
