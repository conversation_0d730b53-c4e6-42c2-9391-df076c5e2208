$primary: #3586ca;

.title button,
.title2 button {
  margin-left: 10px;
}

.datacontainer {
  margin-bottom: 20px;
}

.deletebtn {
  margin-top: 42px;
  background-color: $primary !important;
  font-size: 14px;
  padding: 0px;
}
.deletebtn2 {
  margin-top: 20px;
  background-color: $primary !important;
  font-size: 14px;
  padding: 0px;
}
.item {
  margin-top: -20px;
  margin-left: -10px;
}
.item2 {
  margin-top: -30px;
  margin-left: -10px;
}
.searchbtn {
  padding-top: 11px;
  padding-left: 10px;
  padding-right: 25px;
  background-color: $primary !important;
  height: 37px;
  border-radius: 0px 3px 3px 0;
  font-size: 16px;
  display: inline-block !important;
  margin-top: 1px;
  margin-top: 1px !important;
  display: none !important;
}

.closebtn {
  padding-top: 14px !important;
  padding-left: 10px;
  padding-right: 25px;
  bottom: 0px;
  right: 8px;
  padding-top: 8px;
  background-color: #464646;
  height: 38.6px;
  margin-left: -10px;
  font-size: 14px;
  font-weight: bold;
  display: inline-block !important;
  margin-top: 0px !important;
}

.closebtn3 {
  margin-bottom: 12px !important;
}

.closebtn4 {
  padding-top: 12px !important;
  padding-left: 10px;
  padding-right: 10px;
  bottom: 0px;
  right: 8px;
  background-color: #464646;
  height: 38.6px;
  margin-left: -10px;
  font-size: 14px;
  font-weight: bold;
  display: inline-block !important;
  margin-top: 0px !important;
}

.mat-form-field:not(.mat-form-field-appearance-legacy)
  .mat-form-field-prefix
  .mat-icon,
.mat-form-field:not(.mat-form-field-appearance-legacy)
  .mat-form-field-suffix
  .mat-icon {
  //display: inline-block !important;
  display: block;
  float: left;
  width: 180px !important;
}

.mat-icon img {
  height: 16px;
  width: 16px;
}

.topitem {
  ::ng-deep .mat-form-field-infix {
    margin-top: -25px !important;
    padding: 0px !important;
  }
}

.title-palce {
  font-size: 10px !important;
}

.outline-light {
  background-color: #2f2f2f !important;
  height: 40px;
  border: solid 1px #2f2f2f;
  padding-left: 10px !important;
  border-radius: 5px 0 0 5px;
  font-size: 12px;
  ::ng-deep .mat-select-value {
    padding: 12px 0 !important;
  }

  ::ng-deep .mat-select-arrow-wrapper {
    height: 38px !important;
    border-radius: 0px 3px 3px 0 !important;
    background-color: #464646 !important;
  }

  ::ng-deep .mat-select-arrow {
    width: 12px;
    height: 16px;
    border: none !important;
    margin: 0 4px;
    background-image: url(./assets/if_chevron-right.png);
    padding: 0 12px;
    background-repeat: no-repeat;
    background-position-x: center;
  }
}

.outline {
  background-color: #191919 !important;
  height: 40px;
  border: solid 1px #2f2f2f;
  padding-left: 10px !important;
  border-radius: 5px 0 0 5px;
  font-size: 12px;
  ::ng-deep .mat-select-value {
    padding: 12px 0 !important;
  }

  ::ng-deep .mat-select-arrow-wrapper {
    height: 38px !important;
    border-radius: 0px 3px 3px 0 !important;
    background-color: #464646 !important;
  }

  ::ng-deep .mat-select-arrow {
    width: 12px;
    height: 16px;
    border: none !important;
    margin: 0 4px;
    background-image: url(./assets/if_chevron-right.png);
    padding: 0 12px;
    background-repeat: no-repeat;
    background-position-x: center;
  }
}

.mat-slide-toggle {
  margin-top: 8px !important;
}

.mat-form-field {
  ::ng-deep .mat-form-field-suffix {
    position: absolute;
    bottom: 8px;
    right: -5px;
    display: inherit;
  }
}

.mat-form-field2 {
  ::ng-deep .mat-form-field-suffix {
    position: absolute;
    bottom: 16px;
    right: 0px;
    display: inherit;
  }
}

.mat-form-field3 {
  ::ng-deep .mat-form-field-suffix {
    position: absolute;
    bottom: 8px;
    right: -35px;
    display: inherit;
  }
}

.outline mat-form-field {
  .mat-form-field-wrapper {
    border-bottom: 0 !important;
  }
}

.mat-slide-toggle {
  ::ng-deep .mat-slide-toggle-content {
    font-size: 14px !important;
  }
}

.fieldcontainer {
  margin-bottom: 10px;
  overflow: hidden;
  padding: 10px;
  border-bottom: 1px solid #191919;
  margin: 20px auto;
  width: 96%;
  background-color: #292929;
  margin-bottom: 0px;
  border-radius: 3px;
  display: block !important;
}

.matcontent {
  margin-top: 0px !important;
  padding-top: 20px !important;
}

.fieldbox {
  width: 10%;
  float: left;
  margin-right: 10px;
}

.fieldbox2 {
  width: 15%;
  float: left;
  margin-right: 10px;
}

.fieldbox3 {
  width: 20%;
  float: left;
  margin-right: 10px;
}
.fieldbox3-a {
  width: 25%;
  float: left;
  margin-right: 10px;
}
.fieldbox4 {
  width: 15%;
  float: left;
  margin-right: 10px;
}
.fieldbox4-a {
  width: 40%;
  float: left;
  margin-right: 10px;
}
.fieldbox5 {
  margin-top: 15px;
}

.fieldbox5b {
  width: 85%;
  float: left;
}

.fieldbox label,
.fieldbox2 label,
.fieldbox3 label,
.fieldbox3-a label,
.fieldbox4 label,
.fieldbox4-a label {
  width: 100%;
  font-size: 12px;
}

.fieldbox span,
.fieldbox2 span,
.fieldbox3 span,
.fieldbox3-a span,
.fieldbox4 span,
.fieldbox4-a span {
  width: 100%;
  font-size: 12px;
}

.action-print {
  background-color: $primary !important;
  border-radius: 50%;
  height: 24px !important;
  width: 24px !important;
  vertical-align: middle;
}

.action-print-icon {
  height: 12px !important;
  width: 12px !important;
  font-size: 17px;
  margin-top: -30px;
  margin-left: -5px;
}

.action-btn {
  height: 24px !important;
  width: 24px !important;
  vertical-align: middle;
}

.topitem {
  ::ng-deep .mat-form-field-infix {
    margin-top: -25px !important;
    padding: 0px !important;
  }
}

.title-palce {
  font-size: 10px !important;
}

.links {
  color: #ffffff;
  cursor: pointer;
  text-decoration: underline;
}
.links:hover {
  text-decoration: underline;
  cursor: grab;
}
td.mat-cell:first-of-type,
td.mat-footer-cell:first-of-type,
th.mat-header-cell:first-of-type {
  border-left: none !important;
}

th.tableId,td.tableId  {
  width: 5px !important;
}

label {
  font-size: 12px !important;
  opacity: 0.55 !important;
}
.label5 {
  font-size: 16px !important;
  font-weight: 300 !important;
}

.input1 {
  text-align: center;
  width: 73px;
  height: 25px;
  background: white;
  border: #191919;
  margin-left: 5px;
  margin-right: 5px;
  color: black;
  font-size: 12px !important;
}
.select1 {
  height: 25px;
  background: #191919;
  border: #191919;
  margin-left: 5px;
  margin-right: 5px;
  color: white;
  font-size: 12px !important;
  padding-left: 10px !important;
  font-size: 18px;
  ::ng-deep .mat-select-value {
    padding: 2px 0 !important;
  }

  ::ng-deep .mat-select-arrow-wrapper {
    opacity: 0.5;
    height: 25px !important;
    border-radius: 0px 3px 3px 0 !important;
    background-color: #191919 !important;
  }

  ::ng-deep .mat-select-arrow {
    width: 12px;
    height: 16px;
    border: none !important;
    margin: 0 4px;
    background-image: url(./assets/if_chevron-right.png);
    padding: 0 12px;
    background-repeat: no-repeat;
    background-position-x: center;
  }
}
.value-button {
  display: inline-block;
  border: 1px solid #191919;
  margin: 0px;
  margin-top: -2px;
  width: 25px;
  height: 25px;
  color: white;
  text-align: center;
  vertical-align: middle;
  font-size: 12px;
  background: #2f2f2f;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.value-button:hover {
  cursor: pointer;
}

.mat-table thead, .mat-table tbody, .mat-table tfoot, mat-header-row, mat-row, mat-footer-row, [mat-header-row], [mat-row], [mat-footer-row]{
  background: #2f2f2f;
}