import { enableProdMode } from '@angular/core';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { AppModule } from './app/app.module';
import { environment } from './environments/environment';
import 'core-js/es6/reflect';
import 'core-js/es7/reflect';
import 'zone.js'
if (environment.production) {
  enableProdMode();
  if (window) {
    window.console.log = function() { };
  }
}

platformBrowserDynamic().bootstrapModule(AppModule)
  .catch(err => console.error(err));
