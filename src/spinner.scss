* {
  margin: 0px;
  padding: 0px;
  box-sizing: border-box;
}

body {
  background: #191919 !important;
  height: 100%;
  margin: 0% !important;
  padding: 0% !important;
}

.spinner {
  color: #29b6f6;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
}

.spinner:after {
  animation: changeContent 0.8s linear infinite;
  display: block;
  content: "⠋";
  font-size: 80px;
}

@keyframes changeContent {
  10% {
    content: "⠙";
  }
  20% {
    content: "⠹";
  }
  30% {
    content: "⠸";
  }
  40% {
    content: "⠼";
  }
  50% {
    content: "⠴";
  }
  60% {
    content: "⠦";
  }
  70% {
    content: "⠧";
  }
  80% {
    content: "⠇";
  }
  90% {
    content: "⠏";
  }
}
