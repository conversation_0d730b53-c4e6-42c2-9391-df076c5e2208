/* You can add global styles to this file, and also import other style files */
@import "~@angular/material/prebuilt-themes/indigo-pink.css";
@import "spinner.scss";
@import "styles-variables";
// @import "~bootstrap/scss/bootstrap-reboot";
// @import "~bootstrap/scss/bootstrap-grid";
@import url("https://fonts.googleapis.com/icon?family=Material+Icons");
@import '~@mat-datetimepicker/core/datetimepicker/datetimepicker-theme.scss';
@import "../node_modules/ngx-toastr/toastr.css";
@include mat-core();
// @import "~font-awesome/scss/font-awesome";
// $fa-font-path: "~font-awesome/fonts";
$app-primary: mat-palette($mat-indigo);
$app-accent:  mat-palette($mat-pink);
$app-warn:    mat-palette($mat-red);
$primary: #3586ca;
$accent: mat-color($app-accent);
$warn: mat-color($app-warn);
$theme: mat-light-theme($app-primary, $app-accent, $app-warn);
@include angular-material-theme($theme);
@include mat-datetimepicker-theme($theme);
$dialogContainerColor : #262626;
$dialogBtnHoverColor : #6ca7ff;
$basicBtnColor : #3586ca;
$basicBtnTextColor :black;
$refreshButton : #A52A2A;

.cdk-overlay-pane.my-dialog {
  position: relative !important;
}

.close.mat-button {
  position: absolute;
  top: 0;
  right: 0;
  padding: 5px;
  line-height: 14px;
  min-width: auto;
  float: right;
}

.table {
  width: 100%;
  margin-bottom: 1rem;
  color: white !important;
}

*::-webkit-scrollbar {
  width: 0.5em;
}

html,
body {
  height: 100%;
}

body {
  margin: 0;
  font-family: Roboto, "Helvetica Neue", sans-serif;
  background-color: #131714 !important;
  color: white;
  overflow-x: hidden;
  overflow-y: scroll;
}

.headTag {
  text-align: center !important;
  font-weight: bolder !important;
  color: $primary !important;
  padding-top: 10px !important;
}

.mat-toolbar.mat-primary {
    background: $primary  !important;
    color: #fff;
}

.mat-select-value {
  color: white !important;
}

.mat-select-arrow {
  color: white !important;
}

.mat-tab-label {
  color: white !important;
  // min-width: 30% !important;
}

.submit-button {
  width: 40%;
  border-radius: 20px;
}

.mat-tab-body-content {
  overflow-x: hidden !important;
}

// digicell changes
*::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}

*::-webkit-scrollbar-thumb {
  box-shadow: 0 0 3px 0 rgba(0, 0, 0, 0.6);
  background-color: rgba(255, 255, 255, 0.51);
}

.mat-form-field-wrapper {
  padding-bottom: 0 !important;
}

html {
  scroll-behavior: smooth;
}

button.mat-primary,
button.mat-icon-button h1 {
  color: #fff !important;
  background-color: $primary !important;
  mat-icon {
    color: $primary !important;
  }
}

.mat-option {
  border: solid 1px #2f2f2f !important;
  background-color: #000000;
}

.btn-margin {
  float: right;
  margin-top: 37px !important;
}

.button {
  background-color: #191919 !important;
  border: 1px solid $primary !important;
  color: $primary !important;
  min-width: 78px !important;
}

.buttonWhiteOutline {
  background-color: #191919 !important;
  border: 1px solid #8f8f8f !important;
  color: #e7e7e7 !important;
  min-width: 78px !important;
}

.button2 {
  background-color: #464646 !important;
}

.button3 {
  background-color : $basicBtnColor !important;
  color: $basicBtnTextColor !important;
}

.button3:disabled {
  background-color: #c2c2a3 !important;
  color: black !important;
}

mat-toolbar {
  mat-icon {
    color: white !important;
  }
}

button.btn,
button.active,
td.active {
  background-color: $primary !important;
}

.name-cell {
  button.mat-sort-header-button {
    margin: 0 !important;
  }
}

mat-list {
  text-transform: full-width;
}

mat-card-title {
  font-size: 18px;
  color: $primary  !important;
}

.name-cell.mat-sort-header-container {
  align-items: flex-start;
}

.name-cell {
  text-align: left !important;
  padding-left: 30px !important;
  position: sticky;
  // background-color: transparent;
}

.estimated-cell {
  font-size: 0;

  input,
  div {
    font-size: 14px;
  }
}

.mat-drawer-container {
  background-color: #191919 !important;
}

mat-card {
  width: 96%;
  margin: 10px auto;
  border-collapse: collapse;
  margin-bottom: 0px;
  background-color: #292929 !important;
}

th.mat-header-cell {
  // background-color: #191919;
  // background-color: #1919195e;
  // background-color: #424242 !important;
  // background-color: #2f2f2f!important;
  // background-color: $primary !important;
  background-color: #343b3e !important;

}

table {
  color: white;
  width: 100%;
  margin: 10px auto;
  border-collapse: collapse;
  margin-bottom: 0px;

  tr {
    border: solid #e0e0e0;
    border-width: 1px 0;
    padding: 10%;
    min-height: 5vh;
    height: 7vh;
    height: 30px !important;
  }

  td {
    text-align: left;
    border-left: 1px solid #262626;
    padding-left: 10px !important;
  }

  th {
    font-size: 12px !important;
    color: white;
    background-color: #1919195e;
    padding-left: 10px !important;
    border-left: 1px solid #464646 !important;
  }

  td {
    font-size: 12px !important;
  }

  tr:first-child {
    border-top: none;
  }

  tr:last-child {
    border-bottom: none;
  }

  .mat-row:nth-child(even) {
    background-color: #191919 !important;
  }

  .mat-row:nth-child(odd) {
    background-color: #191919 !important;
  }
}

mat-paginator {
  width: 96%;
  margin: 0 auto;
}

h3 {
  color: $primary  !important;
  margin-top: 1%;
  margin-left: 0.5%;
}

mat-calendar {
  height: inherit !important;
}

app-root {
  width: 95%;
}

.md-button.md-raised.md-primary:not([disabled]) {
  background-color: black;
}

fieldset {
  border: 1px solid $primary  !important;
  margin: 0 auto;

  legend {
    background-color: $primary  !important;
    color: #fff;
    font-size: 1rem;
    position: relative;
    left: 1%;
    width: inherit;
    padding: 0.5rem;
  }
}

.search-table-input {
  margin: 0 auto;
  margin-bottom: 15px;
  overflow: hidden;

  .mat-form-field {
    margin-right: 15px;
  }
}

.title {
  overflow: hidden;
  width: 96%;
  margin: 15px auto 20px auto !important;
}

.title-heading {
  float: left;
  margin-top: 8px;
}

.mat-card {
  box-shadow: none !important;
  padding-top: 0px !important;
}

.mat-paginator-navigation-first,
.mat-paginator-navigation-previous,
.mat-paginator-navigation-next,
.mat-paginator-navigation-last,
.mat-custom-page {
  border-radius: 5px !important;
  height: 34px !important;
  width: 28px !important;
}

.mat-paginator {
  background-color: #292929 !important;
}

.mat-paginator-range-actions {
  display: flex;
  margin: 0px auto !important;
}

.mat-paginator-range-label {
  margin: 0 32px 0 24px;
  display: none;
}

.mat-mini-fab .mat-button-wrapper {
  padding: 0 0;
  display: table-footer-group !important;
  line-height: 1px;
}

.mat-paginator-container {
  display: flex;
  align-items: center;
  min-height: 56px;
  padding: 0 8px;
  flex-wrap: wrap-reverse;
  width: auto !important;
  margin: 0 auto !important;
}

.mat-form-field-underline {
  bottom: 1.34375em;
  display: none !important;
}

.mat-form-field-appearance-fill .mat-form-field-flex {
  border-radius: 4px 4px 0 0;
  padding: 0px 9px 0px 12px !important;
}

.mat-select-arrow {
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid;
  margin: 7px 5px -4px !important;
}

.active-link {
  background: #ffffff !important;
  color: #000000 !important;
}

.mat-form-field-flex {
  margin-top: -5px !important;
}
label {
  font-size: 12px !important;
  opacity: 0.55 !important;
}

.mat-button .mat-primary {
  height: 40px !important;
}

.mat-button {
  height: 40px !important;
}

.mat-raised-button {
  height: 40px !important;
}

.md-drppicker .btn {
  position: relative;
  overflow: hidden;
  border-width: 0;
  outline: 0;
  border-radius: 2px;
  box-shadow: none !important;
  background-color: $primary  !important;
  color: #ecf0f1;
  transition: background-color 0.4s;
  height: 33px;
  text-transform: uppercase;
  border: none;
  font-family: sans-serif;
}

.md-drppicker .ranges ul li button {
  padding: 8px 12px;
  width: 100%;
  background: 0 0;
  border: none;
  text-align: left;
  color: white !important;
  cursor: pointer;
}

.md-drppicker .ranges ul li {
  color: black !important;
  cursor: pointer;
}

.md-drppicker.ltr {
  background-color: #292929 !important;
}

.md-drppicker {
  background-color: #292929 !important;
}

.md-drppicker .calendar-table {
  border: 1px solid #424242;
  padding: 4px;
  border-radius: 4px;
  background-color: #424242 !important;
}

.md-drppicker th {
  color: #000 !important;
  background: #c7c7c7;
}

.md-drppicker.ltr .calendar.left .calendar-table {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  padding-right: 12px;
  border: 1px solid white !important;
}

.md-drppicker.ltr .calendar.right .calendar-table {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  padding-right: 12px;
  margin-left: 5px;
  border: 1px solid white !important;
}

.mat-tooltip {
  overflow: visible;
  position: relative;
  &.right {
    margin-left: 5px;
    &::before {
      position: absolute;
      content: "";
      display: inline-block;
      clip-path: polygon(50% 0, 0 50%, 50% 100%);
      left: -12px;
      width: 15px;
      height: 15px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}

.stepperNextBtn {
  display: flex;
  justify-content: center;
}

  .snackbar-show{
    font-weight: bolder;
  }
  
.spinner-border {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  vertical-align: -0.125em;
  border: 2px solid currentcolor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: .75s linear infinite spinner-border;
}

.mat-sort-header-button {
  border: none;
  background: 0 0;
  display: flex;
  align-items: center;
  padding: 0;
  cursor: inherit;
  outline: 0;
  font: inherit;
  color: currentColor;
  font-weight: bold !important;
}

.mat-header-cell {
  color: rgba(255, 255, 255, 0.7);
  font-weight: bolder !important;
}


.successtoast .errortoast .warningtoast .infotoast{
  margin: 0 !important;
  background-image: none !important;
}
.successtoast::after{
  height: 100%;
  width: 15%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #039b42 !important;
  border-bottom: 5px solid #037444 !important;
  content: '\f00c' !important;
  font-family: 'FontAwesome' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  color: #fff !important;
  font-size: 1.2rem !important;
}

.successtoast .toast-message{
  margin: 0 !important;
  padding: 0 !important;
  margin-right: 1rem !important;
}

.successtoast .toast-close-button{
  top: auto;
  right: auto;
}


.errortoast::after{
  height: 100%;
  width: 15%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f74b2d !important;
  border-bottom: 5px solid #ff0000 !important;
  content: '\f00c' !important;
  font-family: 'FontAwesome' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  color: #fff !important;
  font-size: 1.2rem !important;
}


.errortoast .toast-message{
  margin: 0 !important;
  padding: 0 !important;
  margin-right: 1rem !important;
}

.errortoast .toast-close-button{
  top: auto;
  right: auto;
}



.warningtoast::after{
  height: 100%;
  width: 15%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffbb21 !important;
  border-bottom: 5px solid #ff8800 !important;
  content: '\f00c' !important;
  font-family: 'FontAwesome' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  color: #fff !important;
  font-size: 1.2rem !important;

}


.warningtoast .toast-message{
  margin: 0 !important;
  padding: 0 !important;
  margin-right: 1rem !important;
}

.warningtoast .toast-close-button{
  top: auto;
  right: auto;
}



.infotoast::after{
  height: 100%;
  width: 15%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #05c9e2 !important;
  border-bottom: 5px solid #00e7ff !important;
  content: '\f00c' !important;
  font-family: 'FontAwesome' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  color: #fff !important;
  font-size: 1.2rem !important;
}


.infotoast .toast-message{
  margin: 0 !important;
  padding: 0 !important;
  margin-right: 1rem !important;
}

.infotoast .toast-close-button{
  top: auto;
  right: auto;
}

.mat-dialog-title {
	padding: 8px !important;
	background: $primary  !important;
  border-radius: 5px;
}

.CloseBtn:hover{
  background-color: $dialogBtnHoverColor;
}

.dialogTitle{
  color: $primary  !important;
  margin-right: 10px;
  font-weight: bolder;
	display: flex;
}

.dialogTitleData{
  display: flex;
  font-size: 12px;
}

 .mat-dialog-container {
  display: inline-table !important;
  padding: 0px !important; 
  // border-radius: 4px; 
  box-sizing: border-box;
  overflow: none;
  outline: 0;
  width: 100%;
  height: 100%;
  min-height: inherit;
  max-height: inherit;
  padding: none;
  overflow: inherit !important;
  border-radius: 5px !important;
  max-width: 750px !important;
}

.mat-dialog-container {
  background: $dialogContainerColor !important;
}

.mat-dialog-actions{
  display: contents !important;
}

.table th, .table td {
  border-top: none !important;
}

table tr {
  border: none !important;
}

.topItemkey{
  color: $primary !important;
}

.topItemkeyPs{
  color: $primary !important;
}

th.topItemkey {
  width: 125px;
}

.buttonForRefresh{
  background-color: $refreshButton !important;
}

.svgEditIcon{
  margin-top: -19px;
}

.infoMessage{
  display: flex;
  border: 1px solid $primary;
  background-color: #1919195e;
  margin-right: -16px;
  margin-left: -16px;
  padding: 4px;
  font-size: 13px;
  font-style: italic;
}

.infoSvgIcon{
  color : $primary;
}
mat-expansion-panel{
  background: #292929 !important;
}

.mat-form-field-appearance-outline .mat-form-field-infix {
  padding: 0.7em 0 0.8em 0 !important;
}

.matExpansionTitle{
  color : $primary !important;
}



#stepperSubmitBtn {
  float: right;
  // background-color: #c2c2a3 !important;
  // color: black !important;
  background-color: #191919 !important;
  border: 1px solid #1e88e5 !important;
  color: #1e88e5 !important;
  min-width: 78px !important;
}

#stepperSubmitBtn:disabled {
  // background-color: #727267 !important;
  background-color: #191919 !important;
  border: 1px solid #1e88e5 !important;
  color: #1e88e5 !important;
  min-width: 78px !important;
  opacity: 0.5;
}

#stepperSubmitBtnWhite {
  float: right;
  // background-color: #c2c2a3 !important;
  // color: black !important;
}

#createNewUserBtn {
  // background-color: #c2c2a3 !important;
  // color: black !important;
  background-color: #191919 !important;
  border: 1px solid #1e88e5 !important;
  color: #1e88e5 !important;
  min-width: 78px !important;
}

#createNewUserBtn:disabled {
  // background-color: #727267 !important;
  background-color: #191919 !important;
  border: 1px solid #0f5797 !important;
  color: #1e88e5 !important;
  min-width: 78px !important;
}

#cancelUpdateBtn {
  // background-color: #85857d !important;
  // color: black !important;
  margin-right: 25px;
  background-color: #191919 !important;
  border: 1px solid #1e88e5 !important;
  color: #2278c4 !important;
  min-width: 78px !important;
}

.createDataPopup mat-form-field {
  font-size: small;
}

.secondaryFieldSpan mat-form-field {
  font-size: 12px;
}

::ng-deep .secondaryFieldSpan .mat-option {
  font-size: x-small !important;
}

// .secondaryFieldSpan {
//   box-shadow: 0px 0px 12px black;
// }
.packaging-mat-card {
  ::ng-deep th.mat-header-cell {
    white-space: nowrap !important;
  }
}

.preFilledLabel {
  display: flex;
}

.preFilledLabel #head {
  font-weight: normal;
  font-size: smaller !important;
  color: #b7b7b7;
}

.preFilledLabel #value {
  font-size: medium !important;
}

.userDefaultData table tr {
  border: none;
}

.userDefaultData table td {
  border-left: none;
  padding-left: 0px !important;
  padding-right: 10px;
}

.mainTable td:last-of-type {
  text-align: center;
}

.textColorWhite {
  color: white !important;
  opacity: 1 !important;
}

#editDetailsArea {
  margin-right: 1rem;
}

#editDetailsArea2 {
  margin-right: 1rem;
}

.warningMessageLabel {
  display: flex;
  flex-direction: row;
  align-items: center;
  color: red;
  opacity: 0.9 !important;
  font-size: 13px !important;
}

.loading-screen {
  z-index: 999;
  position: absolute;
  background-color: rgba(255, 255, 255, 0.6);
  width: 100%;
  height: 100%;
  display: block;
}

.loading-screen-icon1 {
  position: absolute;
  top: 40%;
  left: 55%;
  transform: translate(-50%, -50%);
}

#refButton {
  float: right;
  color: black;
  font-weight: bold;
  background-color: #c2c2a3 !important;
}
#refRedButton {
  float: right;
  background-color: brown !important;
}

#addButton {
  float: right;
}

.noBorderTable tr {
  border-width: 0px !important;
}

.noBorderTable td {
  border-color: transparent !important;
}

.workAreaMasterInput .workAreaInputField {
  border-radius: 5px 0px 0px 0px !important;
}

.workAreaMasterInput .addWorkAreBtn {
  border-radius: 0px 5px 0px 0px !important;
}

.workAreaMasterInput .addWorkAreBtnBtn {
  border-radius: 0px 5px 0px 0px !important;
  background-color: #505050 !important;
  color: white;
  font-weight: bold;
  border: 0px !important;
  border-bottom: 0px !important;
}

.disableInputArrow[type="number"]::-webkit-inner-spin-button,
.disableInputArrow[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.noPointerEvent {
  pointer-events: none;
}

.dmTableDiv .mat-paginator-range-label {
  display: contents !important;
}

.paginatorFullWidth {
  width: auto;
}

.makeElementHidden {
  display: none !important;
}

.boxShadowNone {
  box-shadow: none !important;
}

.padding16px{
  padding: 16px;
}

.downloadIcon{
  transform: rotate(90deg);
}

.uploadIcon{
  transform: rotate(270deg);
}

.matGroupAuditLogModule .mat-tab-labels{
  display: flex !important;
  justify-content: center !important;
}

.rotate{
  animation: rotation 2s infinite linear;
}
@keyframes rotation {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(359deg);
  }
}
#outer {
  width: 100%;
  text-align: center;
}
.inner {
  display: inline-block;
  padding: 0px 0px 0px 10px;
}
.topHeadInputs{
  display: contents !important;
}

// .mat-snack-bar-container {
//   max-width: 800px !important;
// }

.dataMessage{
  text-align: center !important;
  font-size: 13px !important;
  margin-top: 20px;
}

// ::ng-deep td.tableId.mat-cell.cdk-column-index.mat-column-index.ng-star-inserted {
//   position: relative;
//   width: 20px;
// }

th.tableId,
td.tableId {
  width: 5px !important;
}

.refreshBtn{
  float: right;
  margin-top: 35px !important;
}

.cdk-overlay-pane {
  display: block !important;
}

.parentIdClass{
  float: right;
  /* background: radial-gradient(#ffff00, transparent); */
  color: black;
  background: yellow;
}

.findAndClearBtn{
  // margin-top: 15px;
  padding-left: 0%;
  padding-right: 0%;
  height: 39px;
  width: 73px;
}

.matFormFieldTopItems{
  margin-top: -15px;
}

// --------- SNACKBAR STYLES -----------------------
// ::ng-deep .mat-snack-bar-container {
//     max-width: none !important;
// }

snack-bar-container.mat-snack-bar-container.ng-trigger.mat-snack-bar-center.mat-snack-bar-top {
    max-width: none !important;
}

.success-snackbar {
  // background-color: #2196F3 !important;
  background-color: #28a745 !important;
  color: white !important;
}

.error-snackbar {
  // background-color: #2196F3 !important;
  background-color: #B30202 !important;
  color: white !important;
}


.warning-snackbar {
  // background-color: #2196F3 !important;
  background-color: #ff7900 !important;
  color: white !important;
}

.info-snackbar {
  // background-color: #2196F3 !important;
  background-color: #54B4D3 !important;
  color: white !important;
}
// --------- SNACKBAR STYLES -----------------------

