{"compileOnSave": false, "compilerOptions": {"resolveJsonModule": true, "noUnusedLocals": false, "noUnusedParameters": false, "baseUrl": "./", "outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "module": "es2015", "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowJs": true, "importHelpers": true, "target": "ES2017", "paths": {"@/*": ["app/*"]}, "typeRoots": ["node_modules/@types"], "lib": ["es2018", "dom"]}, "angularCompilerOptions": {"disableTypeScriptVersionCheck": true}}